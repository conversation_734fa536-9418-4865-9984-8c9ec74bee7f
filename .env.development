
# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS = gzip


# 页面标题
VITE_APP_TITLE = 农业生产管理平台

# 开发环境配置
VITE_APP_ENV = 'development'

# 如果本地调试微前端，则注释下面这一行
VITE_APP_PUBLIC_PATH='/'

# 农业生产管理平台/开发环境

#----k8s环境配置（使用新环境时打开）
#配置说明
#1、因本周需要项目开发和测试环境进行k8s切换，为保证不影响大家日常开发工作目前已将swarm的开发环境和k8s开发环境配置写在本配置文件中
#2、swagger访问地址：http://************:30404/stage-api/doc.html
#3、swagger接口调试需要设置-swagger页面-页面目录>文档管理>个性化设置>host设置为：http://************:30404/stage-api
#如果存在无法启动或提示token过期问题，请清理浏览器缓存后重试，如仍无法处理，请联系陈立伟进行处理
# #开发环境-统一认证平台地址
# VITE_APP_JUMP_AMP = 'http://************:30007'
# #开发环境-sso地址
# VITE_APP_SSO_API_IP = 'http://************:30084'
# # 业务后台-后台接口服务地址
# VITE_APP_BASE_API_IP = 'http://************:31335/bdh-portal/api'
# VITE_APP_BASE_API_IP = 'http://************:30696/stage-api'
# 测试环境下
#测试环境-统一认证平台地址
VITE_APP_JUMP_AMP = 'http://************:31007'
#测试环境-sso地址
VITE_APP_SSO_API_IP = 'http://************:30084'
# 业务后台-后台接口服务地址
VITE_APP_BASE_API_IP = 'http://************:31696/stage-api'

# 系统配置 -- （修改为系统注册时提供的systemCode）
VITE_APP_SYSTEM_CODE = 'bdh-front-group'
# VITE_APP_USER_NAME ='bazzjhnc01'
# # 密码
# VITE_APP_USER_PASSWORD ='123456@a'
# --------------------------------------------------------------------------------------------------------->
# VITE_APP_USER_NAME ='guoying02'
# # 密码
# VITE_APP_USER_PASSWORD ='123456@a'
# 用户名
VITE_APP_USER_NAME ='13104660636'
# 密码
VITE_APP_USER_PASSWORD ='123456@a'
# ---------------------------------------------------------------------- #
# 系统配置 -- （修改为系统注册时提供的systemCode）
# VITE_APP_USER_NAME ='guoying'
# # 密码
# VITE_APP_USER_PASSWORD ='123456@a'
# # ---------------------------------------------------------------------- #

# 后台前缀
VITE_APP_BASE_API = '/dev-api'


# SSO信息 '/sso-api'
VITE_APP_SSO_API_PREFIX = '/sso-redirect'
# 自动获取开发测试 用户信息
VITE_APP_TEST_LOGIN_URL = '/sso/doTestLogin'

# 路由懒加载a
VUE_CLI_BABEL_TRANSPILE_MODULES = true
# 业务相关
# 权限接口(mock开头，走mock并在main.js中引入mock)
# 引入mock会导致文件下载失败！！！
VITE_APP_AUTH_URL='/client/getSysAndMenu'
# sso退出（get）
VITE_APP_SSO_LOGOUT_URL='/bdh-front-group-api/client/logout'

# 导航菜单-系统编码
VITE_SYSTEM_CODE = 'bdh-group-report,sysagriculturalsituation,sysanimalhusbandry,bdh-aviation-mission,bdh-agric-cost-analysis,bdh-samic-confirm,bdh-bpm,
bdh-question-feedback,bdh-info-publish,systemlandcontract,bdh-agric-invest,bdh-trace,systemagriculturalmachineryv2,sysforestresource,syslandresource,bdh-subsidy'

# mqtt 农机地址
VITE_APP_MQTT = 'ws://10.11.14.16:8083/mqtt'
