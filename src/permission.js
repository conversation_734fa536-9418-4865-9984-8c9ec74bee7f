import { ElMessage, ElMessageBox } from "element-plus"
import NProgress from "nprogress"
import "nprogress/nprogress.css"
import { getToken,getCopToken } from "@/utils/auth"
import { isHttp } from "@/utils/validate"
import { isRelogin } from "@/utils/request"
import useUserStore from "@/store/modules/user"
import useSettingsStore from "@/store/modules/settings"
import usePermissionStore from "@/store/modules/permission"
import { getDictList,getUserCurrentDepartment } from '@/api/systemagriculturalmachineryv2/common'
import md5 from "md5"
import axios from "axios"
import useVehiclesStore from "@/store/modules/vehicles"
import useMqtt from '@/views/systemagriculturalmachineryv2/utils/mqtt.js'
const { startMqtt, disconnect } = useMqtt()
import mitt from 'mitt'
import { ElNotification } from 'element-plus'
NProgress.configure({ showSpinner: false })

const whiteList = ["/login", "/register", "/smsLogin"];
const checkToken = ["/checkToken"];
const copToken = ["/copToken"];
const env = import.meta.env;

function createdMqtt() {
  disconnect()
  const mqtt = import.meta.env.VITE_APP_MQTT
  // const mqtt = window.VITE_APP_MQTT
  console.log('import.meta.env',import.meta.env)
  console.log('window',window)
  startMqtt(mqtt, 'machinery', '1234qwer', [`agmachine/async/${useUserStore().currentOrgCode}/${useUserStore().staffId}`], null, (topic, data) => {
    if (data) {
      let { type, msg } = JSON.parse(data)
      window.$bus.$emit('mqtt', JSON.parse(data))
      if (type == "basicinfo_aminfo_exportExcelAsync") {
        ElNotification({
          title: '提示',
          duration: 10000,
          message: msg || ''
        });
      } else if (type == "basicinfo_aminfo_exportExcelWithQrCode") {
        ElNotification({
          title: '提示',
          duration: 10000,
          message: msg || ''
        });
      }
    }
  })
}
function initM2(path){
  if(path.indexOf("systemagriculturalmachineryv2")>-1 && !useVehiclesStore().isInitSystemagriculturalmachineryv2 ){
    getDictList().then((res) => {
      localStorage.setItem("Dicts", JSON.stringify(res.data));
    })
    getUserCurrentDepartment().then(res => {
        const { data } = res;
        useUserStore().setOrg(data)
        createdMqtt()
        useVehiclesStore().getArea()
        useVehiclesStore().SET_INIT_M2()
      }).catch((err) => {})
  }
}
export const executePermission = (router) => {
  router.beforeEach((to, from, next) => {
    NProgress.start();
    /* 处理token */
    console.log(to.path);
    if (checkToken.includes(to.path)) {
      const { accessToken, toUrl } = to.query;
      if (accessToken) {
        if (toUrl) {
          useUserStore()
            .login(accessToken)
            .then(() => {
              next({ path: toUrl });
            });
        } else {
          useUserStore()
            .login(accessToken)
            .then(() => {
              next({ path: "/" });
            });
        }
      } else {
        useUserStore().clearInfo("");
      }
    }
    if (copToken.includes(to.path)) { 
      const { copToken, toUrl,current } = to.query;
      if (copToken) {
        if (toUrl) {
          if(toUrl=='/external'){
            useUserStore()
              .loginCop(copToken)
              .then(() => {
                next({ path: toUrl,query: { current: current }});
              });
          }else{
            useUserStore()
              .loginCop(copToken)
              .then(() => {
                next({ path: toUrl });
              });
          }
        } else {
          useUserStore()
            .loginCop(copToken)
            .then(() => {
              next({ path: "/" });
            });
        }
      } else {
        useUserStore().clearInfo("");
      }
    }
    console.log(getToken(), "000000000000000000000000000000000000000000000000");
    if (getToken()||getCopToken()) {
      to.meta.title && useSettingsStore().setTitle(to.meta.title);
      /* has token*/
      if (to.path === "/login") {
        next({ path: "/" });
        NProgress.done();
      } else {
        initM2(to.path)
        if (useUserStore().roles.length === 0) {
          isRelogin.show = true;
          // 判断当前用户是否已拉取完user_info信息
          useUserStore()
            .getInfo()
            .then((routes) => {
              isRelogin.show = false;
              usePermissionStore()
                .generateRoutes(routes)
                .then((accessRoutes) => {
                  // 根据roles权限生成可访问的路由表
                  console.log(2222, "----accessRoutes-----", accessRoutes);
                  accessRoutes.forEach((route) => {
                    if (!isHttp(route.path)) {
                      router.addRoute(route); // 动态添加可访问路由表
                    }
                  });
                  next({ ...to, replace: true }); // hack方法 确保addRoutes已完成
                });
            })
            .catch((err) => {
              console.log(2222, "catch-logOut");

              useUserStore()
                .logOut()
                .then(() => {
                  ElMessage.error(err);
                  next({ path: "/" });
                });
            });
        } else {
          next();
        }
      }
    } else {
      // 没有token
      if (whiteList.indexOf(to.path) !== -1) {
        // 在免登录白名单，直接进入
        next();
      } else {
        console.log(env);
        if (env.VITE_APP_ENV === "development" || env.VITE_APP_ENV === "tenant") {
          const password = env.VITE_APP_USER_PASSWORD;
          const data = env.VITE_APP_ENV === "development" ? {
            // callbackUrl: env.VITE_APP_CB_URL,
            systemCode: env.VITE_APP_SYSTEM_CODE,
            loginName: env.VITE_APP_USER_NAME,
            password: md5(
              md5(password.toUpperCase()).toUpperCase()
            ).toLowerCase(),
          } : {
            // callbackUrl: process.env.VUE_APP_CB_URL,
            systemCode: env.VITE_APP_SYSTEM_CODE,
            loginName: env.VITE_APP_USER_NAME,
            tenantId: env.VITE_APP_TENANT_ID,
            password: md5(md5(password).toUpperCase()).toLowerCase()
          };
          axios.defaults.withCredentials = true;
          console.log(
            `${env.VITE_APP_SSO_API_PREFIX}${env.VITE_APP_TEST_LOGIN_URL}`
          );
          axios
            .post(
              `${env.VITE_APP_SSO_API_PREFIX}${env.VITE_APP_TEST_LOGIN_URL}`,
              data
            )
            .then((response) => {
              const { code, data, msg } = response.data;
              if (code !== 0) {
                // 提示错误
                ElMessageBox.confirm(
                  msg || "服务器内部错误！",
                  "开发者模式提示",
                  {
                    confirmButtonText: "正确配置开发环境变量",
                    showCancelButton: false,
                    type: "error",
                    closeOnClickModal: false,
                    showClose: false,
                  }
                ).then(() => {
                  window.location = env.VITE_APP_JUMP_AMP;
                });
              } else {
                const { accessToken } = data;
                useUserStore()
                  .login(accessToken)
                  .then(() => {
                    next({ path: "/" });
                  });
              }
            })
            .catch((error) => {
              ElMessage({
                message: error,
                type: "error",
                duration: 5 * 1000,
              });
            });
          NProgress.done();
        } else {
          //cookie为空进行跳转
          useUserStore().clearInfo("");
          NProgress.done();
        }
      }
    }
  });

  router.afterEach(() => {
    NProgress.done();
  });
};
