
import { buildTree } from "@/views/systemagriculturalmachineryv2/utils/tree"
import { fetchSnList, fetchAreaList, fetchSnListByType } from '@/api/systemagriculturalmachineryv2/vehicleManage'
const store = defineStore(
  'vehicles',
  {
    state: () => ({
      sn: [], // 设备号
      snUnfiltered: [], // 未过滤的sn号
      area: [], // 归属地
      areaOriginal: [],
      getAreaLoading: false,
      getAreaFinished: false,
      // 归属地-设备号 组装树形
      tree: [],
      // 当前点击的节点
      clickNode: null,
      snSimple: [], // 另一个接口的设备号，返回内容没有位置信息
      snSimpleUnfiltered: [], // 未过滤的snSimple号
      isInitSystemagriculturalmachineryv2:false,
    }),
    actions: {
      SET_INIT_M2() {
        this.isInitSystemagriculturalmachineryv2 = true
      },
      SET_CLICK_NODE(node) {
        this.clickNode = node
      },
      SET_TREE(tree) {
        this.tree = tree
      },
      SET_SN(sn) {
        this.sn = sn
      },
      PUSH_SN(snItem) {
        this.sn.push(snItem)
      },
      SET_SN_UNFILTERED(sn) {
        this.snUnfiltered = sn
      },
      SET_SN_SIMPLE(snSimple) {
        this.snSimple = snSimple
      },
      SET_SN_SIMPLE_UNFILTERED(snSimple) {
        this.snSimpleUnfiltered = snSimple
      },
      SET_AREA(area) {
        this.area = area
      },
      SET_AREA_ORIGINAL(area) {
        this.areaOriginal = area
      },
      SET_AREA_FINISHED(boolean) {
        this.getAreaFinished = boolean
      },
      SET_AREA_LOADING(boolean) {
        this.getAreaLoading = boolean
      },
      RESET() {
        this.snSimple = []
        this.snSimpleUnfiltered = []
        this.area = []
        this.areaOriginal = []
        this.getAreaLoading = false
        this.getAreaFinished = false
        this.tree = []
      },
      // 获取归属地
      getArea() {
        if (this.getAreaLoading) {
          return
        }
        this.SET_AREA_LOADING(true)
        try {
          return new Promise((resolve, reject) => {
            fetchAreaList().then(response => {
              let { data } = response
              this.SET_AREA_ORIGINAL(JSON.parse(JSON.stringify(data)))
              const option = buildTree(data, 'orgId', 'parentId')
              this.SET_AREA(JSON.parse(JSON.stringify(option)))
              this.SET_AREA_FINISHED(true)
              this.SET_AREA_LOADING(false)
              resolve()
            }).catch(error => {
              this.SET_AREA_LOADING(false)
              reject(error)
            })
          })
        } catch (e) {
          this.SET_AREA_LOADING(false)
        }
      },

      // 获取设备号
      async getSn() {
        if (!this.getAreaFinished) {
          await this.getArea()
        }
        return new Promise((resolve, reject) => {
          fetchSnList().then(response => {
            const { data } = response
            this.SET_SN(JSON.parse(JSON.stringify(data)))
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },
      async getSnByType(type) {
        return new Promise((resolve, reject) => {
          fetchSnListByType(type).then(response => {
            const { data } = response
            this.SET_SN(JSON.parse(JSON.stringify(data)))
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },
    },
  })
export default store