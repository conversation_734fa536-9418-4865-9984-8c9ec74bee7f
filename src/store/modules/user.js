import { login, logout, getInfo } from "@/api/login"
import { getToken, setToken, removeToken, setCopToken } from "@/utils/auth"
import { ElMessageBox } from "element-plus"
import caches from "@/plugins/cache";
import { getUserOrgInfoList } from "@/api/bdh-trace/system/user";
import {getOrgTreeApi} from "@/api/bdh-trace/system/dict/data";
import axios from 'axios'
const env = import.meta.env
import usePermissionStore from '@/store/modules/permission'
const systemCodes = env.VITE_SYSTEM_CODE?.replace(/\n/g, '').split(',')
const topbarRouters = computed(() => usePermissionStore().topbarRouters);
const useUserStore = defineStore("user", {
    state: () => ({
        token: getToken(),
        name: "",
        avatar: "",
        roles: [],
        permissions: [],
        groupPermissions: {}, //项目权限组
        currentOrgMsg:{},
        loginName: '',
        staffId:""
    }),
    getters: {
        currentOrgCode: (state) => state.currentOrgMsg?.orgCode ?? "",
    },
    actions: {
        // 登录
        login(token) {
            return new Promise((resolve, reject) => {
                setToken(token)
                this.token = token
                resolve()
            })
        },
        // app登录
        loginCop(token) {
            return new Promise((resolve, reject) => {
                setCopToken(token)
                this.token = token
                resolve()
            })
        },
        // 获取用户信息
        getInfo(params) {
            return new Promise((resolve, reject) => {
                getInfo().then((res) => {
                        const { code, msg ,data} = res
                        if (
                            [100008, 100017, 200004, 100020, 300001].includes(
                                code
                            )
                        ) {
                            ElMessageBox.confirm(
                                msg || "服务器内部错误！",
                                "系统提示",
                                {
                                    confirmButtonText: "重新登录",
                                    type: "warning",
                                    closeOnClickModal: false,
                                    showClose: false,
                                }
                            ).then(() => {
                                reject(res)
                            })
                        } else {

                            const {
                                systemList = [],
                                staffName,
                                staffId,
                                loginName
                            } = data
                            let avatar = new URL(
                                `../../assets/logo/logo.png`,
                                import.meta.url
                            ).href
                            this.staffId = staffId
                            this.name = staffName
                            this.avatar = avatar
                            this.loginName = loginName
                            this.permissions = systemList.flatMap(m => m.menu).flatMap(p => p.authCode) //权限编码
                            // 各项目权限编码
                            let groupAuthCode = {}
                            systemList.flatMap(m => m.menu).forEach(item => {
                                groupAuthCode[item.system.systemCode] = item.authCode
                            })
                            this.groupPermissions = groupAuthCode

                            let allMenu = systemList.flatMap(m => m.menu).filter(item => systemCodes.includes(item.system.systemCode))
                            const roles = allMenu.map(item => {

                                let ampAuth = item.ampAuth.map(child => {
                                    // console.debug("user.js", child)
                                    let p = child.path?.replace(/\//g, '') || '/'

                                    child.children = child.children?.map(c => {
                                        //图标
                                        let isCustom = false,sidebarClass='';
                                        if(!!c.meta && !!c.meta.icon && c.meta.icon.startsWith('custom-')){
                                            isCustom = true
                                            sidebarClass = `${item.system.systemCode}-`+c.meta.icon.split('custom-')[1]
                                        }else {
                                            isCustom = false
                                            sidebarClass = ''
                                        }
                                        return {
                                            ...c,
                                            isCustom: isCustom, // 图标是自定义图标
                                            sidebarClass: sidebarClass, //图标
                                        }

                                    })
                                    //图标
                                    if(!!child.meta && !!child.meta.icon && child.meta.icon.startsWith('custom-')){
                                        child.isCustom = true
                                        child.sidebarClass = `${item.system.systemCode}-` +child.meta.icon.split('custom-')[1]
                                    }else {
                                        child.isCustom = false
                                        child.sidebarClass = ''
                                    }
                                    return {
                                        ...child,
                                        path: child.children?.length>0 ? p : child.path,
                                        component: child.children?.length>0 ? 'ParentView' : child.component
                                    }
                                })

                                return {
                                    name: item.system.systemCode,
                                    path: `/${item.system.systemCode}`,
                                    hidden: false,//是否隐藏
                                    redirect: "noRedirect",
                                    component: "Layout",
                                    menuResourceType: item.system.systemAggregationType, //分类
                                    alwaysShow: true,
                                    remark: item.system.systemDesc,
                                    meta: {
                                        title: item.system.systemName,
                                        shortTitle: item.system.systemAlias,
                                        icon: item.system.systemStaticIcon, //路由图标
                                        shortIcon: item.system.systemMenuIcon,
                                        noCache: false,
                                        link: null,
                                        navigationIcon: item.system.systemStaticIcon //导航图标
                                    },
                                    children: ampAuth
                                }
                            })
                            console.log(222,'----roles',roles)

                            if (roles && roles.length > 0) {
                                this.roles = ["admin"]
                            } else {
                                this.roles = ["admin"]
                            }

                            resolve(roles)
                        }
                    })
                    .catch((error) => {
                        console.log(error)
                        reject(error)
                    })
            })
        },
        //别人踢
        clearInfo(code) {
            console.log(
                env.VITE_APP_SSO_API_PREFIX,
                "99999999999999999999999999999999999999999999999999999999999999999999999999999999"
            )
            const hostname = location.protocol + "//" + location.host
            if (code == "100008") {
                this.token = ""
                this.roles = []
                this.permissions = []
                removeToken()
                window.location =
                    hostname +
                    `${env.VITE_APP_SSO_API_PREFIX}/sso/login?callbackUrl=` +
                    hostname +
                    `&code=100008`
            } else {
                window.location =
                    hostname +
                    `${env.VITE_APP_SSO_API_PREFIX}/sso/login?callbackUrl=` +
                    hostname
            }
        },
        // 退出系统
        logOut() {
            return new Promise((resolve, reject) => {
                logout()
                    .then((res) => {
                        const { data: staffId } = res
                        this.token = ""
                        this.roles = []
                        this.permissions = []
                        removeToken()
                        const hostname =
                            location.protocol + "//" + location.host
                        //退出登录，回当前系统
                        // let path = location.href.replace(hostname, "")
                        // if(path) {
                        //     const route = topbarRouters.value.find(r=>path.startsWith(r.path))
                        //     if(route && route.path) {
                        //         path = `${route.path}/page${route.path}/index`;
                        //         console.error("path", path)
                        //     } else {
                        //         path = "";
                        //     }
                        //
                        // } else {
                        //     path = "";
                        // }
                        window.location =
                            hostname +
                            `${env.VITE_APP_SSO_API_PREFIX}/sso/logout?staffId=${staffId}&callbackUrl=` +
                            hostname
                            // + path
                        resolve()
                    })
                    .catch((error) => {
                        reject(error)
                    })
            })
        },
        setRoles(roles) {
            this.roles = roles;
        },
        setOrg(msg) {
            this.currentOrgMsg = msg
        },
        setCurrentOrg() {
            getUserOrgInfoList().then(res => {
                const { code, data } = res;
                if (code === 0) {
                    const $orgArr = data || [];
                    if ($orgArr.length > 0 ) {
                        const org = $orgArr[0];
                        org.orgCodes = $orgArr.map(item => item.orgCode);
                        caches.local.setJSON("userOrg", org)
                    }
                }
            })
        },
        // 初始化默认组织结构代码
        initOrgCode() {
            getOrgTreeApi().then(res => {
                if (res.code === 0) {
                    if (res.data && res.data.length > 0) {
                        const org = res.data[0] || {};
                        const $org = {
                            code: org.orgCode,
                            name: org.orgFullName
                        };
                        window.$$.org = $org
                        caches.local.setJSON("org", $org)
                    }
                }
            })
        },
    },
})

export default useUserStore
