import request from '@/utils/request'
const prefix = window.VITE_APP_BASE_API

// 查询设备是否在线
export function getDeviceOnlineStatus(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/getDeviceOnlineStatus`,
    method: 'post',
    showLoading:false,
    data
  })
}
// 查询指令执行情况
export function getCmdStatus(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/getCmdStatus`,
    method: 'post',
    showLoading:false,
    data
  })
}
// 查询设备实时数据
export function getNavData(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/getNavData`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 查询设备历史轨迹
export function getTrajectoryDate(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/getTrajectoryDate`,
    method: 'post',
    showLoading:false,
    data
  })
}
// 查询单个设备控制状态-控制权限
export function getDeviceControlStatus(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/getDeviceControlStatus`,
    method: 'post',
    showLoading:false,
    data
  })
}
// 获取或延期控制权-控制权限
export function getOrExtendControlAuth(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/getOrExtendControlAuth`,
    method: 'post',
    showLoading:false,
    data
  })
}
// 通用配置指令
export function commonSetting(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/commonSetting`,
    method: 'post',
    showLoading:false,
    data
  })
}
// 拖拉机自主作业整机配置指令
export function tractorWorkSetting(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/tractorWorkSetting`,
    method: 'post',
    showLoading:false,
    data
  })
}
// 拖拉机路径规划配置指令
export function tractorWorkLinePlan(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/tractorWorkLinePlan`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 手动遥控通用配置指令
export function commonRemoteControl(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/commonRemoteControl`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 拖拉机手动遥控能用指令
export function tractorRemoteControl(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/tractorRemoteControl`,
    method: 'post',
    showLoading:false,
    data
  })
}
// 拖拉机自主作业及手动遥控通用配置指令
export function tractorWorkOrRemoteCommon(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/tractorWorkOrRemoteCommon`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 无人农机分页列表
export function machineQueryByPage(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/task/machine/queryByPage`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 无人农机任务列表
export function taskQueryByPage(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/task/queryByPage`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 智慧农机下发任务
export function sendTask(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/task/sendTask`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 获取无人驾驶参数信息
export function getCfg(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/task/getCfg`,
    method: 'post',
    showLoading:false,
    data
  })
}
// 无人驾驶参数信息下发
export function cfgSave(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/task/cfgSave`,
    method: 'post',
    showLoading:false,
    data
  })
}
//无人驾驶控制显示下发
export function consoleSave(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/task/consoleSave`,
    method: 'post',
    showLoading:false,
    data
  })
}
//获取无人驾驶控制显示信息
export function getConsole(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/task/getConsole`,
    method: 'post',
    showLoading:false,
    data
  })
}


// 检查360设备是否在线
export function getLookAroundOnlineStatus(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/getLookAroundOnlineStatus`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 播放360设备
export function playLookAroundInfo(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/playLookAroundInfo`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 关闭360设备
export function closeLookAround(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/closeLookAround`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 获取历史任务详情
export function apiGetHistTaskDetail(data) {
  let formData = new FormData();
  formData.append('assignTaskNo', data.assignTaskNo);
  console.log('>>>>>>formData: ', formData)
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/task/getHistTask?assignTaskNo=${data.assignTaskNo}`,
    method: 'post',
    showLoading:false,
  })
}

// TBOX实时查询接口
export function getRealTimeInfo(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/getRealTimeInfo`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 批量获取设备实施数据
export function batchGetNavData(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/batchGetNavData`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 批量获取设备是否在线
export function batchGetDeviceOnlineStatus(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/batchGetDeviceOnlineStatus`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 更新任务结束时间
export function updateTask(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/task/update`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 删除无人驾驶控制权
export function deleteControlAuth(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/deleteControlAuth`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 车辆心跳请求
export function unpilotedHeartbeat(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/unpilotedHeartbeat`,
    method: 'post',
    showLoading:false,
    data
  })
}

// 获取顶部title
export function orgTitle(data) {
  return request({
    url: `${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/lovol/orgTitle`,
    method: 'post',
    showLoading:false,
    data
  })
}
