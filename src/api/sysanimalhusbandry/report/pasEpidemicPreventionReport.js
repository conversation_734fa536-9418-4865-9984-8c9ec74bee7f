import request from '@/utils/request'
const env =
  import.meta.env

// 查询春/秋季防疫周报列表一级数据
export function queryByPageOneLevel(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionReport/queryByPageOneLevel`,
    method: 'post',
    data
  })
}
// 查询春/秋季防疫周报列表  xin 
export function listPasEpidemicPreventionReportNew(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/queryByPage`,
    method: 'post',
    data
  })
}
// 查询春/秋季防疫周报列表
export function listPasEpidemicPreventionReport(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/queryByPage`,
    method: 'post',
    data
  })
}

// 查询春/秋季防疫周报详细
export function getPasEpidemicPreventionReport(statYear) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionReport/info/` + statYear,
    method: 'post'
  })
}

// 新增春/秋季防疫周报
export function addPasEpidemicPreventionReport(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionReport/insert`,
    method: 'post',
    data
  })
}

// 修改春/秋季防疫周报
export function updatePasEpidemicPreventionReport(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionReport/update`,
    method: 'post',
    data
  })
}

// 删除春/秋季防疫周报
export function delPasEpidemicPreventionReport(statYear) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionReport/logicDeleteById/` + statYear,
    method: 'post'
  })
}
// 删除多个smsCode，data为数组
export function delPasEpidemicPreventionReports(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionReport/logicDeleteByIds`,
    method: 'post',
    data
  })
}

//提交报表
export function epidemicPreventionReportSubmit(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionReport/epidemicPreventionReportSubmitByIds`,
    method: 'post',
    data
  })
}
//退回报表
export function epidemicPreventionReportBack(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionReport/epidemicPreventionReportBackByIds`,
    method: 'post',
    data
  })
}
//管理区提交报表
export function submitManageByIds(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/submitManageByIds`,
    method: 'post',
    data
  })
}
//管理区退回报表
export function backManageByIds(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/backManageByIds`,
    method: 'post',
    data
  })
}
//农场提交报表
export function submitFarmByIds(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/submitFarmByIds`,
    method: 'post',
    data
  })
}
//农场退回报表
export function backFarmByIds(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/backFarmByIds`,
    method: 'post',
    data
  })
}
//分公司提交报表
export function submitCompanyByIds(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/submitCompanyByIds`,
    method: 'post',
    data
  })
}
//分公司退回报表
export function backCompanyByIds(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/backCompanyByIds`,
    method: 'post',
    data
  })
}
// 机构提前防疫状态
export function prevFillStatus(statYear) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/prevFillStatus/` + statYear,
    method: 'post'
  })
}
// 提前完成防疫
export function prevFill(statYear) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/prevFill/` + statYear,
    method: 'post'
  })
}
//锁定报表
export function epidemicPreventionReportLock(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionReport/epidemicPreventionReportLockByIds`,
    method: 'post',
    data
  })
}


//查询春/秋季防疫周报列表(汇总)//一级数据
export function listPasEpidemicPreventionReportSumOneLevel(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionReport/queryByPageOneLevelIsCollect`,
    method: 'post',
    data
  })
}

// 查询春/秋季防疫周报列表(汇总)//展示列表
export function listPasEpidemicPreventionReportSum(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/queryByPageIsCollect`,
    method: 'post',
    data
  })
}
// 查询春/秋季防疫周报列表(汇总)//展示列表 新
export function listPasEpidemicPreventionReportSumNew(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/queryByPageIsCollect`,
    method: 'post',
    data
  })
}
//
export function queryWeekByParam(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionReport/queryWeekByParam`,
    method: 'post',
    data
  })
}
export function queryWeekByParamNew(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/queryWeekByParam
`,
    method: 'post',
    data
  })
}

/* 适配 2024 年度新表样 */

// 查询春/秋季防疫周报列表一级数据
export function queryByPageOneLevelGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/queryByPageOneLevel`,
    method: 'post',
    data
  })
}
// 查询春/秋季防疫周报列表一级数据-新得
export function queryByPageOneLevelGrpNew(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/queryByPageOneLevel`,
    method: 'post',
    data
  })
}
// 根据模板查询配置的字段属性
export function queryTemplateColumnByTemplate(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/queryTemplateColumnByTemplate`,
    method: 'post',
    data
  })
}
// 查询春/秋季防疫周报列表
export function listPasEpidemicPreventionReportGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/queryByPage`,
    method: 'post',
    data
  })
}

// 查询春/秋季防疫周报详细
export function getPasEpidemicPreventionReportGrp(statYear) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/info/` + statYear,
    method: 'post'
  })
}

// 新增春/秋季防疫周报
export function addPasEpidemicPreventionReportGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/insert`,
    method: 'post',
    data
  })
}

// 修改春/秋季防疫周报
export function updatePasEpidemicPreventionReportGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/update`,
    method: 'post',
    data
  })
}

// 删除春/秋季防疫周报
export function delPasEpidemicPreventionReportGrp(statYear) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/logicDeleteById/` + statYear,
    method: 'post'
  })
}
// 删除多个smsCode，data为数组
export function delPasEpidemicPreventionReportsGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/logicDeleteByIds`,
    method: 'post',
    data
  })
}

//提交报表
export function epidemicPreventionReportSubmitGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/epidemicPreventionReportSubmitByIds`,
    method: 'post',
    data
  })
}
//退回报表
export function epidemicPreventionReportBackGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/epidemicPreventionReportBackByIds`,
    method: 'post',
    data
  })
}
//管理区提交报表
export function submitManageByIdsGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/submitManageByIds`,
    method: 'post',
    data
  })
}
//管理区退回报表
export function backManageByIdsGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/backManageByIds`,
    method: 'post',
    data
  })
}
//农场提交报表
export function submitFarmByIdsGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/submitFarmByIds`,
    method: 'post',
    data
  })
}
//农场退回报表
export function backFarmByIdsGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/backFarmByIds`,
    method: 'post',
    data
  })
}
//分公司提交报表
export function submitCompanyByIdsGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/submitCompanyByIds`,
    method: 'post',
    data
  })
}
//分公司退回报表
export function backCompanyByIdsGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/backCompanyByIds`,
    method: 'post',
    data
  })
}
// 机构提前防疫状态
export function prevFillStatusGrp(statYear) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/prevFillStatus/` + statYear,
    method: 'post'
  })
}
// 提前完成防疫
export function prevFillGrp(statYear) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/prevFill/` + statYear,
    method: 'post'
  })
}
//锁定报表
export function epidemicPreventionReportLockGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/epidemicPreventionReportLockByIds`,
    method: 'post',
    data
  })
}


//查询春/秋季防疫周报列表(汇总)//一级数据
export function listPasEpidemicPreventionReportSumOneLevelGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/queryByPageOneLevelIsCollect`,
    method: 'post',
    data
  })
}

// 查询春/秋季防疫周报列表(汇总)//展示列表
export function listPasEpidemicPreventionReportSumGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/queryByPageIsCollect`,
    method: 'post',
    data
  })
}

//
export function queryWeekByParamGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/queryWeekByParam`,
    method: 'post',
    data
  })
}