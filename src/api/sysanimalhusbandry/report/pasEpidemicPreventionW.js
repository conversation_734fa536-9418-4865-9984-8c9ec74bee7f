import request from '@/utils/request'
const env = import.meta.env

// 查询春/秋季防疫周报（填报）列表
export function listPasEpidemicPreventionW(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionW/queryByPage`,
    method: 'post',
    data
  })
}

// 查询春/秋季防疫周报（填报）详细
export function getPasEpidemicPreventionW(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/info`,
    method: 'post',
    data
  })
}

// 查询春/秋季防疫周报（填报）详细
export function getPasEpidemicPreventionWGrp(dataId) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpW/info/` + dataId,
    method: 'post'
  })
}

// 新增春/秋季防疫周报（填报）
export function addPasEpidemicPreventionW(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionW/insert`,
    method: 'post',
    data
  })
}

// 修改春/秋季防疫周报（填报）
export function updatePasEpidemicPreventionW(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionW/update`,
    method: 'post',
    data
  })
}

// 修改春/秋季防疫周报（填报）
export function updatePasEpidemicPreventionWGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpW/update`,
    method: 'post',
    data
  })
}

// 修改防疫周报（填报）
export function updatePasEpidemicPrevention(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/update`,
    method: 'post',
    data
  })
}

// 删除春/秋季防疫周报（填报）
export function delPasEpidemicPreventionW(dataId) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionW/logicDeleteById/` + dataId,
    method: 'post'
  })
}
// 删除多个smsCode，data为数组
export function delPasEpidemicPreventionWs(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasEpidemicPreventionW/logicDeleteByIds`,
    method: 'post',
    data
  })
}
