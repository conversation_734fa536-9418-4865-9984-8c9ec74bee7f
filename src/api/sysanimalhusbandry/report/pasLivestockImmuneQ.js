import request from '@/utils/request'
const env = import.meta.env

// 查询春/秋季应免存栏数量统计表（填报）列表
export function listPasLivestockImmuneQ(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneQ/queryByPage`,
    method: 'post',
    data
  })
}

// 查询春/秋季应免存栏数量统计表（填报）详细
export function getPasLivestockImmuneQ(dataId) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneQ/info/` + dataId,
    method: 'post'
  })
}

// 查询春/秋季应免存栏数量统计表（填报）详细
export function getPasLivestockImmuneQGrp(dataId) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpQ/info/` + dataId,
    method: 'post'
  })
}

// 新增春/秋季应免存栏数量统计表（填报）
export function addPasLivestockImmuneQ(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneQ/insert`,
    method: 'post',
    data
  })
}

// 修改春/秋季应免存栏数量统计表（填报）
export function updatePasLivestockImmuneQ(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneQ/update`,
    method: 'post',
    data
  })
}

// 修改春/秋季应免存栏数量统计表（填报）
export function updatePasLivestockImmuneQGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpQ/update`,
    method: 'post',
    data
  })
}

// 删除春/秋季应免存栏数量统计表（填报）
export function delPasLivestockImmuneQ(dataId) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneQ/logicDeleteById/` + dataId,
    method: 'post'
  })
}
// 删除多个smsCode，data为数组
export function delPasLivestockImmuneQs(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneQ/logicDeleteByIds`,
    method: 'post',
    data
  })
}
