import request from '@/utils/request'
const env = import.meta.env


// 查询春/秋季应免存栏数量统计表列表
export function listPasLivestockImmuneReportOneLevel(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasLivestockImmuneReport/queryByPageOneLevel`,
    method: 'post',
    data
  })
}
// 根据模板查询配置的字段属性
export function queryTemplateColumnByTemplate(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/queryTemplateColumnByTemplate`,
    method: 'post',
    data
  })
}

// 查询春/秋季应免存栏数量统计表列表
export function listPasLivestockImmuneReport(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasLivestockImmuneReport/queryByPage`,
    method: 'post',
    data
  })
}

// 查询春/秋季应免存栏数量统计表详细
export function getPasLivestockImmuneReport(livestockBeefSum) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasLivestockImmuneReport/info/` + livestockBeefSum,
    method: 'post'
  })
}

// 新增春/秋季应免存栏数量统计表
export function addPasLivestockImmuneReport(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneReport/insert`,
    method: 'post',
    data
  })
}

// 修改春/秋季应免存栏数量统计表
export function updatePasLivestockImmuneReport(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasLivestockImmuneReport/update`,
    method: 'post',
    data
  })
}

// 删除春/秋季应免存栏数量统计表
export function delPasLivestockImmuneReport(livestockBeefSum) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneReport/logicDeleteById/` + livestockBeefSum,
    method: 'post'
  })
}
// 删除多个smsCode，data为数组
export function delPasLivestockImmuneReports(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneReport/logicDeleteByIds`,
    method: 'post',
    data
  })
}

//提交报表
export function pasLivestockImmuneReportSubmit(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneReport/pasLivestockImmuneReportSubmitByIds`,
    method: 'post',
    data
  })
}
//退回报表
export function pasLivestockImmuneReportBack(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneReport/pasLivestockImmuneReportBackByIds`,
    method: 'post',
    data
  })
}
//锁定报表
export function pasLivestockImmuneReportLock(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneReport/pasLivestockImmuneReportLockByIds`,
    method: 'post',
    data
  })
}

//管理区提交报表
export function submitManageByIds(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasLivestockImmuneReport/submitManageByIds`,
    method: 'post',
    data
  })
}

//管理区退回报表
export function backManageByIds(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasLivestockImmuneReport/backManageByIds`,
    method: 'post',
    data
  })
}
//农场提交报表
export function submitFarmByIds(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasLivestockImmuneReport/submitFarmByIds`,
    method: 'post',
    data
  })
}
//农场退回报表
export function backFarmByIds(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasLivestockImmuneReport/backFarmByIds`,
    method: 'post',
    data
  })
}
//分公司提交报表
export function submitCompanyByIds(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasLivestockImmuneReport/submitCompanyByIds`,
    method: 'post',
    data
  })
}
//分公司退回报表
export function backCompanyByIds(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasLivestockImmuneReport/backCompanyByIds`,
    method: 'post',
    data
  })
}

//分公司锁定报表
export function lockCompanyByIds(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasAntiepidemicTemplate/pasLivestockImmuneReport/lockCompanyByIds`,
    method: 'post',
    data
  })
}

/* 适配 2024 年度新表样 */

// 查询春/秋季应免存栏数量统计表列表
export function listPasLivestockImmuneReportOneLevelGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/queryByPageOneLevel`,
    method: 'post',
    data
  })
}


// 查询春/秋季应免存栏数量统计表列表
export function listPasLivestockImmuneReportGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/queryByPage`,
    method: 'post',
    data
  })
}

// 查询春/秋季应免存栏数量统计表详细
export function getPasLivestockImmuneReportGrp(livestockBeefSum) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/info/` + livestockBeefSum,
    method: 'post'
  })
}

// 新增春/秋季应免存栏数量统计表
export function addPasLivestockImmuneReportGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/insert`,
    method: 'post',
    data
  })
}

// 修改春/秋季应免存栏数量统计表
export function updatePasLivestockImmuneReportGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/update`,
    method: 'post',
    data
  })
}

// 删除春/秋季应免存栏数量统计表
export function delPasLivestockImmuneReportGrp(livestockBeefSum) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/logicDeleteById/` + livestockBeefSum,
    method: 'post'
  })
}
// 删除多个smsCode，data为数组
export function delPasLivestockImmuneReportsGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/logicDeleteByIds`,
    method: 'post',
    data
  })
}

//提交报表
export function pasLivestockImmuneReportSubmitGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/pasLivestockImmuneReportSubmitByIds`,
    method: 'post',
    data
  })
}
//退回报表
export function pasLivestockImmuneReportBackGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/pasLivestockImmuneReportBackByIds`,
    method: 'post',
    data
  })
}
//锁定报表
export function pasLivestockImmuneReportLockGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/pasLivestockImmuneReportLockByIds`,
    method: 'post',
    data
  })
}

//管理区提交报表
export function submitManageByIdsGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/submitManageByIds`,
    method: 'post',
    data
  })
}

//管理区退回报表
export function backManageByIdsGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/backManageByIds`,
    method: 'post',
    data
  })
}
//农场提交报表
export function submitFarmByIdsGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/submitFarmByIds`,
    method: 'post',
    data
  })
}
//农场退回报表
export function backFarmByIdsGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/backFarmByIds`,
    method: 'post',
    data
  })
}
//分公司提交报表
export function submitCompanyByIdsGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/submitCompanyByIds`,
    method: 'post',
    data
  })
}
//分公司退回报表
export function backCompanyByIdsGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/backCompanyByIds`,
    method: 'post',
    data
  })
}

//分公司锁定报表
export function lockCompanyByIdsGrp(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/lockCompanyByIds`,
    method: 'post',
    data
  })
}

