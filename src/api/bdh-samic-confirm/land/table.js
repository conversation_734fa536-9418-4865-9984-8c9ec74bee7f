
import request from "@/utils/request"

const env = import.meta.env
const landResourceApi = '/' + import.meta.env.VITE_APP_GATEWAYPATH_LANDRESOURCE + ''
// 获取图层

// 查询列表
export function getListApi(data) {
    return request({
        url: `${landResourceApi}/ygnx/plot/search`, method: "post", data,
    })
}

// 添加阳关农地块
export function addYgnxAPi(data) {
    return request({
        url: `${landResourceApi}/ygnx/plot/add`, method: "post", data,
    })
}

// 删除阳光农地块
export function deleteYgnxAPi(data) {
    return request({
        url: `${landResourceApi}/ygnx/plot/delete`, method: "post", data,
    })
}
// 查询全部
export function getYgnxAll(data) {
    return request({
        url: `${landResourceApi}/ygnx/plot/search/ygnx`, method: "post", data,
    })
}

// 编辑阳光农地块
export function editYgnxApi(data) {
    return request({
        url: `${landResourceApi}/ygnx/plot/edit`, method: "post", data,
    })
}
