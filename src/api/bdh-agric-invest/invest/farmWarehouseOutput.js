import request from '@/utils/request'
const env = import.meta.env

// 获取企业信息
export function queryAllCompanyInfoInput(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/companyInfo/queryAll`,
    method: 'post',
    data
  })
}

// 查询农场投入品出库列表
export function listfarmWarehouseOutput(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/farmWarehouseOutput/queryByPage`,
    method: 'post',
    data
  })
}

// 查询农场投入品出库商品列表
export function getFarmWareOutDetailList(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/farmWarehouseOutput/getFarmWareOutDetailList`,
    method: 'post',
    data
  })
}

// 新增农场投入品入库选择加工商品列表
export function insertOutput(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/farmWarehouseOutput/insert`,
    method: 'post',
    data
  })
}

// 更新农场投入品入库选择加工商品列表
export function updateOutput(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/farmWarehouseOutput/update`,
    method: 'post',
    data
  })
}

// 获取详情
export function infoOutput(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/farmWarehouseOutput/info`,
    method: 'post',
    data
  })
}

//确认出库
export function confirmWarehouseOut(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/farmWarehouseOutput/confirmWarehouseOut`,
    method: 'post',
    data
  })
}

// 删除农场投入品入库列表
export function logicDeleteByIdsOOutput(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/farmWarehouseOutput/logicDeleteByIds`,
    method: 'post',
    data
  })
}

export function logicDeleteByWarehouseRecordIdOutput(id) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/farmWarehouseOutput/logicDeleteById` + id,
    method: 'post'
  })
}

//根据企业id查询加工商品分类树
export function getProduceProcessTypeTreeByCompanyIdInput(companyId) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/produceProcessInfo/getProduceProcessTypeTreeByCompanyId/` + companyId,
    method: 'get'
  })
}

//根据orgcode查询库房列表
export function getFarmWarehouseAll(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/farmWarehouse/queryAll`,
    method: 'post',
    data
  })
}

// 查询出库单
export function queryOutboundOrder(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/farmWarehouseOutput/queryOutboundOrder`,
    method: 'post',
    data
  })
}

// 打印出库单
export function printOutboundOrder(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/farmWarehouseOutput/printOutboundOrder`,
    method: 'post',
    data
  })
}

// 查询质量监管员
export function queryQualitySupervisors(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/thirdPatry/queryByPageMachineryQualitySupervisors`,
    method: 'post',
    data
  })
}