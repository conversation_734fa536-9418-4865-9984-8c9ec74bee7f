import request from '@/utils/request'
const env = import.meta.env

export function postForExcel(url,data,fileName){
    return request({
        url: `${window.VITE_APP_BASE_API}${url}`,
        responseType:'blob',
        method: 'post',
        data
    }).then(
        response=>{
            let link = document.createElement('a');
            link.href=URL.createObjectURL(response);
            link.download = fileName+'.xlsx';
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            URL.revokeObjectURL(link.herf);
        }
    )
}

export function postForPdf(url,data,fileName){
    return request({
        url: `${window.VITE_APP_BASE_API}${url}`,
        responseType:'blob',
        method: 'post',
        data
    }).then(
        response=>{
            let link = document.createElement('a');
            link.href=URL.createObjectURL(response);
            link.download = fileName+'.pdf';
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            URL.revokeObjectURL(link.herf);
        }
    )
}

export function postForZip(url,data,fileName){
    return request({
        url: `${window.VITE_APP_BASE_API}${url}`,
        responseType:'blob',
        method: 'post',
        data
    }).then(
        response=>{
            let link = document.createElement('a');
            link.href=URL.createObjectURL(response);
            link.download = fileName+'.zip';
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            URL.revokeObjectURL(link.herf);
        }
    )
}

export function postForExcelXls(url,data,fileName){
    return request({
        url: `${window.VITE_APP_BASE_API}${url}`,
        responseType:'blob',
        method: 'post',
        data
    }).then(
        response=>{
            let link = document.createElement('a');
            link.href=URL.createObjectURL(response);
            link.download = fileName+'.xls';
            link.style.display = 'none';
            document.body.appendChild(link);
            link.click();
            URL.revokeObjectURL(link.herf);
        }
    )
}
