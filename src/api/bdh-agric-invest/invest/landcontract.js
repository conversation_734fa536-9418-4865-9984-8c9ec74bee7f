//土地承包系统接口
//bdh-landcontract.* dev,test,pro

import request from '@/utils/request'
const env = import.meta.env

/**
 * 农户信息
 * @param data  {page: 1, rows: 10, yearNo: "", name: 姓名, idNumber: 身份证, type: 1 固定传1 }
 * @returns {*}
 */
export function personList(data) {
    return request({
        url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/landcontract/queryByPage`,
        method: 'post',
        data
    })
}

export function familyFarmList(data) {
    return request({
        url: `${window.VITE_APP_BASE_API}/bdh-agric-invest-api/invest/farmWarehouseOutput/queryPageScaleFamilyFarm`,
        method: 'post',
        data
    })
}


//farmer/landcontractfarmers/getResidentInfo

export function getResidentInfo(data) {
    return request({
        url: `${env.VITE_LANDCONTRACT_API}/farmer/landcontractfarmers/getResidentInfo`,
        method: 'post',
        data
    })
}
