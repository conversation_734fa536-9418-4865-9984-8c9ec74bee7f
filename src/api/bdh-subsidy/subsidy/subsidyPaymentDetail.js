import request from '@/utils/request'
const env = import.meta.env

// 查询补贴信息表列表
export function listSubsidyPaymentDetail(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/queryByPage`,
    method: 'post',
    data
  })
}
// 查询table动态
export function getColumnModel(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/getColumnModel`,
    method: 'post',
    data
  })
}
// 查询补贴信息表详细
export function getSubsidyPaymentDetail(subsidyDetailId) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/info/` + subsidyDetailId,
    method: 'post'
  })
}

// 新增补贴信息表
export function addSubsidyPaymentDetail(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/insert`,
    method: 'post',
    data
  })
}

// 修改补贴信息表
export function updateSubsidyPaymentDetail(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/update`,
    method: 'post',
    data
  })
}

// 删除补贴信息表
export function delSubsidyPaymentDetail(subsidyDetailId) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/logicDeleteById/` + subsidyDetailId,
    method: 'post'
  })
}
// 删除多个smsCode，data为数组
export function delSubsidyPaymentDetails(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/logicDeleteByIds`,
    method: 'post',
    data
  })
}

// 下载模板
export function exportTemplate() {
  return `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/exportTemplate`
}

// 查询补贴信息表列表
export function details(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/details`,
    method: 'post',
    data
  })
}

// 查询补贴信息表列表(按组织分组)
export function groupByOrg(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/groupByOrg`,
    method: 'post',
    data
  })
}

// 查询补贴信息表列表(按组织分组后明细)
export function listGroupByOrgDetail(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/groupByOrgDetail`,
    method: 'post',
    data
  })
}

// 审核
export function auditPass(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/auditPassByIds`,
    method: 'post',
    data
  })
}

// 取消审核
export function auditCancel(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/auditCancelByIds`,
    method: 'post',
    data
  })
}
// 查询账户
export function getBankCard(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/getBankCard`,
    method: 'post',
    data
  })
}
// 保存银行卡信息
export function updateBankCard(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/updateBankCard`,
    method: 'post',
    data
  })
}
// 更新金额
export function updateSub(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/updateSub`,
    method: 'post',
    data
  })
}

export function subsidyStandardManagequeryAll(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyStandardManage/queryAll`,
    method: 'post',
    data
  })
}
export function subsidyStandardManagegetByOrgs(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyStandardManage/getByOrgs`,
    method: 'post',
    data
  })
}
export function subsidyProjectQueryAlls(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyConfig/queryAll`,
    method: 'post',
    data
  })
}


// 全部审核
export function auditPassAll(data){
  return request({
    url:`${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/auditPassAll`,
    method:'post',
    data
  })
}


export function importExcelApply(data){
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/importApply`,
    method:'post',
    data
  })
}

export function importApplylog(data){
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/importApplylog`,
    method:'post',
    data
  })
}

export function updateApplylog(data){
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/subsidyPaymentDetail/updateApplylog`,
    method:'post',
    data
  })
}

export function importNewApplylog(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/log/importApplylog`,
    method: 'post',
    data
  })
}


export function updateNewApplylog(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/log/updateApplylog`,
    method: 'post',
    data
  })
}

// 查询预算名称
export function selectBudget(data) {
  return request({
    url: `${window.VITE_APP_BASE_API}/${import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY}/subsidy/budgetfarm/selectBudget`,
    method: 'post',
    data
  })
}
