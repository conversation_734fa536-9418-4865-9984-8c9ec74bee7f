<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-form
        :model="queryParams"
        ref="queryRef"
        v-show="showSearch"
        label-width="100px"
        class="form-line"
      >
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="年份" prop="statYear">
              <el-select
                v-model="queryParams.statYear"
                placeholder="请选择年份"
                clearable
              >
                <el-option
                  v-for="dict in yearOptions"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所在单位" prop="orgCode">
              <org-select
                ref="orgCodeRef"
                placeholderText="请选择所在单位"
                @handleOrgCode="handleOrgCodeQuery"
                :isInterface="isInterface"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="作物" prop="cropType">
              <el-select
                v-model="queryParams.cropType"
                placeholder="请选择作物"
                clearable
              >
                <el-option
                  v-for="dict in cropTypeOptions"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="组长姓名" prop="familyFarmName">
              <el-input
                v-model="queryParams.familyFarmName"
                placeholder="请输入组长姓名"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="组长身份证号" prop="familyFarmIccid">
              <el-input
                v-model="queryParams.familyFarmIccid"
                placeholder="请输入组长身份证号"
                clearable
                @input="
                  queryParams.familyFarmIccid = queryParams.familyFarmIccid.toUpperCase()
                "
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="农户姓名" prop="farmerName">
              <el-input
                v-model="queryParams.farmerName"
                placeholder="请输入农户姓名"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="农户身份证号" prop="farmerIdNumber">
              <el-input
                v-model="queryParams.farmerIdNumber"
                placeholder="请输入农户身份证号"
                clearable
                @input="
                  queryParams.farmerIdNumber = queryParams.farmerIdNumber.toUpperCase()
                "
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="管理模式" prop="manageType">
              <el-select
                v-model="queryParams.manageType"
                placeholder="请选择管理模式"
                clearable
              >
                <el-option
                  v-for="dict in manageTypeOptions"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['project:acBusiTypeInfo:insert']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['project:acBusiTypeInfo:templateImport']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['project:acBusiTypeInfo:logicDeleteByIds']"
          >删除
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table
      border
      :data="acBusiTypeInfoList"
      :height="tableHeight"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />

      <el-table-column label="年份" prop="statYear" width="70px" />
      <el-table-column label="所在单位" prop="orgName" width="320px" />
      <el-table-column label="管理模式" prop="manageType" :formatter="manageTypeFormat" />
      <el-table-column label="组长姓名" prop="familyFarmName"> </el-table-column>
      <el-table-column label="组长身份证号" prop="familyFarmIccid" width="120px">
      </el-table-column>
      <el-table-column label="农户姓名" prop="farmerName"> </el-table-column>
      <el-table-column label="农户身份证号" prop="farmerIdNumber" width="120px">
      </el-table-column>
      <!--      <el-table-column label="经营类型" align="center" prop="busiType" :formatter="busiTypeFormat"/>-->

      <el-table-column label="作物" prop="cropType" :formatter="cropTypeFormat" />
      <el-table-column label="面积(亩)" prop="plantArea"> </el-table-column>
      <el-table-column
        label="操作"
        class-name="small-padding fixed-width"
        width="150px"
        fixed="right"
        v-hasPermi="[
          'project:acBusiTypeInfo:update',
          'project:acBusiTypeInfo:logicDeleteById',
        ]"
      >
        <template #default="scope">
          <el-button
            type="text"
            @click="handleUpdate(scope.row, $event)"
            v-hasPermi="['project:acBusiTypeInfo:update']"
            >修改</el-button
          >
          <el-button
            type="text"
            @click="handleDelete(scope.row)"
            v-hasPermi="['project:acBusiTypeInfo:logicDeleteById']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.rows"
      @pagination="getList"
    />

    <!-- 添加或修改经营类型管理对话框 -->
    <el-dialog
      title="规模家庭农场组管理"
      v-model="open"
      width="800px"
      append-to-body
      close-on-click-modal="false"
    >
      <el-form
        class="busi-type-form"
        ref="acBusiTypeInfoRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        label-position="top"
      >
        <el-row :gutter="30">
          <el-col :span="8">
            <el-form-item label="年份" prop="statYear">
              <el-select v-model="form.statYear" placeholder="请选择年份" clearable>
                <el-option
                  v-for="dict in yearOptions"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在单位" prop="orgCode">
              <org-select
                placeholderText="请选择所在单位"
                @handleOrgCode="handleOrgCodeEdit"
                ref="orgCodeEditRef"
                :defaultOrgCode="form.orgCode"
                :isCheckStrictly="isCheckStrictly"
                :isInterface="isInterface"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="组长姓名" prop="familyFarmName">
              <el-input
                v-model.trim="form.familyFarmName"
                placeholder="请输入组长姓名"
                clearable
                maxlength="20"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="组长身份证号" prop="familyFarmIccid">
              <el-input
                v-model.trim="form.familyFarmIccid"
                placeholder="请输入组长身份证号"
                clearable
                @input="form.familyFarmIccid = form.familyFarmIccid.toUpperCase()"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="农户姓名" prop="farmerName">
              <el-input
                v-model.trim="form.farmerName"
                placeholder="请输入农户姓名"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="农户身份证号" prop="farmerIdNumber">
              <el-input
                v-model.trim="form.farmerIdNumber"
                placeholder="请输入农户身份证号"
                clearable
                @input="form.farmerIdNumber = form.farmerIdNumber.toUpperCase()"
              />
            </el-form-item>
          </el-col>
          <!--          <el-col :span="8">-->
          <!--            <el-form-item label="经营类型" prop="busiType">-->
          <!--              <el-select-->
          <!--                  v-model="form.busiType"-->
          <!--                  placeholder="请选择经营类型"-->
          <!--                  clearable-->
          <!--                  disabled-->
          <!--              >-->
          <!--                <el-option-->
          <!--                    v-for="dict in busiTypeOptions"-->
          <!--                    :key="dict.code"-->
          <!--                    :label="dict.name"-->
          <!--                    :value="dict.code"-->
          <!--                />-->
          <!--              </el-select>-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
          <el-col :span="8">
            <el-form-item label="作物" prop="cropType">
              <el-select v-model="form.cropType" placeholder="请选择作物" clearable>
                <el-option
                  v-for="dict in cropTypeOptions"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="面积(亩)" prop="plantArea">
              <el-input-number
                v-model="form.plantArea"
                placeholder="请输入面积"
                :precision="4"
                :step="0.0001"
                :title="''"
                :max="99999999.9999"
                :min="0"
                :controls="false"
                @mousewheel.native.prevent
              >
              </el-input-number>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="管理模式" prop="manageType">
              <el-select v-model="form.manageType" placeholder="请选择管理模式" clearable>
                <el-option
                  v-for="dict in manageTypeOptions"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!--          <el-col :span="8">-->
          <!--            <el-form-item label="地号" prop="plotNo">-->
          <!--              <el-input v-model="form.plotNo" placeholder="请选择地号" disabled>-->
          <!--              </el-input>-->
          <!--            </el-form-item>-->
          <!--          </el-col>-->
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm($event)" :disabled="disSubmitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导入对话框 -->
    <el-dialog
      :title="uploadExcel.title"
      v-model="uploadExcel.open"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-upload
        ref="uploadRef"
        v-if="uploadExcel.open"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="uploadExcel.headers"
        :action="uploadExcel.url"
        :disabled="uploadExcel.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload">
          <upload-filled />
        </el-icon>
        <div class="el-upload__text"><em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              v-hasPermi="['project:acBusiTypeInfo:templateDownload']"
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="handleDownLoad"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="uploadExcel.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog title="导入失败" v-model="dialogTableVisible" width="800px">
      <el-table :data="errDataList">
        <!--        <el-table-column property="colName" label="列名" width="150" :formatter="valueFormat"></el-table-column>-->
        <el-table-column
          property="message"
          label="错误详细信息"
          :formatter="valueFormat"
        ></el-table-column>
      </el-table>
    </el-dialog>

    <!--        查询地块弹窗-->
    <el-dialog
      title="查询地块"
      v-model="openPlot"
      width="800px"
      append-to-body
      close-on-click-modal="false"
    >
      <el-form
        class="form-container"
        ref="formPlotRef"
        :model="formPlot"
        :rules="rulesPlot"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="年份" prop="year">
              <el-select v-model="formPlot.year" placeholder="请选择年份" clearable>
                <el-option
                  v-for="dict in yearOptions"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属单位" prop="orgCode">
              <org-select
                placeholderText="请选择所在单位"
                @handleOrgCode="handleOrgCodeForm"
                :defaultOrgCode="formPlot.orgCode"
                ref="orgCodeFormRef"
                :isCheckStrictly="isCheckStrictly"
                :isInterface="isInterface"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="地块名称" prop="plotName">
              <el-input
                v-model="formPlot.plotName"
                clearable
                placeholder="请输入地块名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :push="0">
            <el-button icon="Refresh" @click="resetQueryPlot">重置</el-button>
            <el-button type="primary" icon="Search" @click="handleQueryPlot"
              >搜索</el-button
            >
          </el-col>
        </el-row>
        <el-table border :height="tableConfig.height" ref="tablePlotRef" :data="plotList">
          <el-table-column align="center" label="地块编码" prop="plotNo" />
          <el-table-column align="center" label="地块名称" prop="plotName" />
          <el-table-column align="center" label="地块面积" prop="contrArea" />
          <el-table-column
            align="center"
            class-name="small-padding fixed-width"
            width="150px"
            fixed="right"
            label="操作"
          >
            <template #default="scope">
              <el-button
                type="text"
                icon="Edit"
                @click="selectPlot(scope.row)"
                v-hasPermi="['project:acBusiTypeInfo:queryPlot']"
                >选择</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-dialog>
  </div>
</template>

<script setup name="/project/acBusiTypeInfo/queryByPage">
import {
  listAcBusiTypeInfo,
  getAcBusiTypeInfo,
  delAcBusiTypeInfo,
  delAcBusiTypeInfos,
  addAcBusiTypeInfo,
  updateAcBusiTypeInfo,
  templateImport,
  queryPlot,
} from "@/api/bdh-agric-cost-analysis/project/acBusiTypeInfo";
import { getDicts } from "@/api/bdh-agric-cost-analysis/system/dict/data";
import { postForExcel } from "@/api/bdh-agric-cost-analysis/project/excel";
import orgSelect from "@/views/bdh-agric-cost-analysis/components/OrgSelect/index.vue";
import { getToken } from "@/utils/auth.js";
import { ElMessageBox } from "element-plus";

const { proxy } = getCurrentInstance();

const acBusiTypeInfoList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const farmerNames = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const isInterface = ref("queryOrgStationTreeByUserOrg");
const isCheckStrictly = ref(false); //组织机构是否只能选最后一级

const yearOptions = ref([]); //年份字典
const busiTypeOptions = ref([]); //经营类型字典
const cropTypeOptions = ref([]); //作物字典
const manageTypeOptions = ref([]); //管理模式字典
const searchHeight = ref(0); // 搜索栏高度
const searchDom = ref(); // 搜索栏dom

const dialogTableVisible = ref(false);
const errDataList = ref([]);

const openPlot = ref(false);
const plotList = ref([]);

const disSubmitForm = ref(false); //禁用表单中确定按钮
const tableHeight = ref(window.innerHeight - 350);
const columns = ref([
  { key: 0, label: `经营类型ID`, visible: true },
  { key: 1, label: `填报年份`, visible: true },
  { key: 2, label: `组织机构代码`, visible: true },
  { key: 3, label: `组织机构名称`, visible: true },
  { key: 4, label: `场长姓名`, visible: true },
  { key: 5, label: `场长身份证号`, visible: true },
  { key: 6, label: `农户ID`, visible: true },
  { key: 7, label: `农户姓名`, visible: true },
  { key: 8, label: `农户身份证号`, visible: true },
  { key: 9, label: `经营类型`, visible: true },
  { key: 10, label: `作物类型`, visible: true },
  { key: 11, label: `种植面积(亩)`, visible: true },
  { key: 12, label: `创建者`, visible: true },
  { key: 13, label: `创建时间`, visible: true },
  { key: 14, label: `更新者`, visible: true },
  { key: 15, label: `更新时间`, visible: true },
  { key: 16, label: `数据有效状态`, visible: true },
]);
const tableConfig = {
  height: window.innerHeight - 330,
};
const data = reactive({
  formPlot: {},
  form: {},
  queryParams: {
    rows: 10,
    page: 1,
    statYear: new Date().getFullYear().toString(),
    orgCode: null,
    orgName: null,
    familyFarmName: null,
    familyFarmIccid: null,
    farmerName: null,
    farmerIdNumber: null,
    busiType: null,
    cropType: null,
    plantArea: null,
    manageType: null,
  },
  rules: {
    statYear: [{ required: true, message: "请选择年份", trigger: "change" }],
    orgCode: [{ required: true, message: "请选择所在单位", trigger: "change" }],
    familyFarmName: [{ required: true, message: "请输入组长姓名", trigger: "blur" }],
    familyFarmIccid: [{ required: true, message: "请输入组长身份证号", trigger: "blur" }],
    farmerName: [{ required: true, message: "请输入农户姓名", trigger: "blur" }],
    farmerIdNumber: [{ required: true, message: "请输入农户身份证号", trigger: "blur" }],
    busiType: [{ required: true, message: "请选择统营类型", trigger: "change" }],
    cropType: [{ required: true, message: "请选择作物", trigger: "change" }],
    plantArea: [{ required: true, message: "请输入面积", trigger: "blur" }],
    plotNo: [{ required: true, message: "请选择地号", trigger: "change" }],
    // manageType: [{ required: false, message: "请选择管理模式", trigger: "change" }],
  },
  rulesPlot: {
    plotName: [{ required: true, message: "请输入地块名称", trigger: "blur" }],
    orgCode: [{ required: true, message: "请选择所属单位", trigger: "change" }],
    year: [{ required: true, message: "请选择年份", trigger: "change" }],
  },
});

const { queryParams, form, rules, formPlot, rulesPlot } = toRefs(data);
onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 220
    : window.innerHeight - 220;
});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 220
    : window.innerHeight - 220;
});
/** 获取字典 */
const getOptions = () => {
  getDicts("sys_crop").then((response) => {
    cropTypeOptions.value = response.data;
  });
  getDicts("year_cd").then((response) => {
    yearOptions.value = response.data;
  });
  getDicts("busi_type").then((response) => {
    busiTypeOptions.value = response.data;
  });
  getDicts("manage_type").then((response) => {
    manageTypeOptions.value = response.data;
  });
};

/** 作物翻译 */
const cropTypeFormat = (row, column) => {
  let cropType = null;
  if (cropTypeOptions.value.length > 0) {
    cropTypeOptions.value.forEach((v, i) => {
      if (v.code == row.cropType) {
        cropType = v.name;
      }
    });
  }
  return cropType;
};
/** 管理模式翻译 */
const manageTypeFormat = (row, column) => {
  let manageType = null;
  if (manageTypeOptions.value.length > 0) {
    manageTypeOptions.value.forEach((v, i) => {
      if (v.code == row.manageType) {
        manageType = v.name;
      }
    });
  }
  return manageType;
};

const busiTypeFormat = (row, column) => {
  let busiType = null;
  if (busiTypeOptions.value.length > 0) {
    busiTypeOptions.value.forEach((v, i) => {
      if (v.code == row.busiType) {
        busiType = v.name;
      }
    });
  }
  return busiType;
};

function valueFormat(row, column, cellValue, index) {
  return cellValue == "-1" || cellValue == "null" ? "" : cellValue;
}

const handleOrgCodeQuery = (data) => {
  queryParams.value.orgCode = data.orgCode;
};

const handleOrgCodeForm = (data) => {
  formPlot.value.orgCode = data.orgCode;
};

const handleOrgCodeEdit = (data) => {
  form.value.orgCode = data.orgCode;
};

/*** 导入参数 */
const uploadExcel = reactive({
  // 是否显示弹出层（导入）
  open: false,
  // 弹出层标题（导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: {
    "access-token": getToken(),
  },
  // 上传的地址
  url:
    window.VITE_APP_BASE_API +
    "/" + import.meta.env.VITE_APP_GATEWAYPATH_COSTANALYSIS + "/project/acBusiTypeInfo/templateImport",
});
/** 导入按钮操作 */
function handleImport() {
  uploadExcel.title = "经营类型管理";
  uploadExcel.open = true;
}
/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  uploadExcel.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  uploadExcel.open = false;
  uploadExcel.isUploading = false;
  proxy.$refs["uploadRef"].clearFiles();
  if (response.success == false) {
    if (response.code == 0) {
      dialogTableVisible.value = true;
      errDataList.value = response.data;
    } else {
      proxy.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          "</div>",
        "导入结果",
        {
          dangerouslyUseHTMLString: true,
        }
      );
    }
  } else {
    proxy.$message.success("导入成功！");
    getList();
  }
};

/** 查询经营类型管理列表 */
function getList() {
  loading.value = true;
  listAcBusiTypeInfo(queryParams.value).then((response) => {
    acBusiTypeInfoList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  // if (proxy.$refs["orgCodeFormRef"]) {
  //   proxy.$refs["orgCodeFormRef"].clear();
  // }
  reset();
  // resetQueryPlot();
}

// 表单重置
function reset() {
  form.value = {
    busiTypeId: null,
    statYear: null,
    orgCode: null,
    orgName: null,
    familyFarmName: null,
    familyFarmIccid: null,
    farmerId: null,
    farmerName: null,
    farmerIdNumber: null,
    busiType: "01",
    cropType: null,
    plantArea: null,
    manageType: null,
  };
  proxy.resetForm("acBusiTypeInfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  // queryParams.value.rows = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.$refs["orgCodeRef"].clear();
  proxy.resetForm("queryRef");
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.busiTypeId);
  farmerNames.value = selection.map((item) => item.farmerName);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作打开地块 */
function handleAdd() {
  // resetQueryPlot();
  // // reset();
  // openPlot.value = true
  // plotList.value = []

  reset();
  if (proxy.$refs["orgCodeEditRef"]) {
    proxy.$refs["orgCodeEditRef"].clear();
  }
  disSubmitForm.value = false;
  open.value = true;
}

/** 修改按钮操作 */
function handleUpdate(row, event) {
  reset();
  // 添加失去焦点事件
  let target = event.target;
  if (target.nodeName === "BUTTON" || target.nodeName === "SPAN") {
    target.parentNode.blur();
  }
  target.blur();
  if (proxy.$refs["orgCodeEditRef"]) {
    proxy.$refs["orgCodeEditRef"].clear();
  }
  const busiTypeId = row.busiTypeId || ids.value;
  getAcBusiTypeInfo(busiTypeId).then((response) => {
    form.value = response.data;
    disSubmitForm.value = false;
    open.value = true;
    // title.value = "修改经营类型管理";
  });
}

/** 提交按钮 */
function submitForm(event) {
  // 添加失去焦点事件
  let target = event.target;
  if (target.nodeName === "BUTTON" || target.nodeName === "SPAN") {
    target.parentNode.blur();
  }
  target.blur();
  proxy.$refs["acBusiTypeInfoRef"].validate((valid) => {
    if (valid) {
      disSubmitForm.value = true;
      form.value.busiType = "01";
      if (form.value.busiTypeId != null) {
        updateAcBusiTypeInfo(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          })
          .catch((res) => {
            disSubmitForm.value = false;
          });
      } else {
        addAcBusiTypeInfo(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            openPlot.value = false;
            open.value = false;
            getList();
          })
          .catch((res) => {
            disSubmitForm.value = false;
          });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const busiTypeIds = row.busiTypeId || ids.value;
  const farmerNameList = row.farmerName || farmerNames.value;
  // let msg = '';
  // if(Array.isArray(farmerNameList)){
  //   farmerNameList.forEach((t,i)=>{
  //     msg += '<p style="font-weight: 900">'+ t + '</p>';
  //   })
  // }else{
  //   msg = '<p style="font-weight: 900">'+ farmerNameList + '</p>';
  // }
  // ElMessageBox.confirm('是否确认删除农户姓名为' + msg + '的数据项？', "系统提示", {
  //   confirmButtonText: '确定',
  //   cancelButtonText: '取消',
  //   dangerouslyUseHTMLString: true,
  //   type: "warning",
  // })
  proxy.$modal
    .confirm('是否确认删除农户姓名为"' + farmerNameList + '"的数据项？')
    .then(function () {
      return Array.isArray(busiTypeIds)
        ? delAcBusiTypeInfos(busiTypeIds)
        : delAcBusiTypeInfo(busiTypeIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  postForExcel(
    "/"+import.meta.env.VITE_APP_GATEWAYPATH_COSTANALYSIS + "/project/acBusiTypeInfo/exportExcel",
    {
      ...queryParams.value,
    },
    "经营类型管理"
  );
}
/** 模板下载按钮操作 */
function handleDownLoad() {
  postForExcel(
    "/"+import.meta.env.VITE_APP_GATEWAYPATH_COSTANALYSIS + "/project/acBusiTypeInfo/templateDownload",
    queryParams.value,
    "经营类型管理模版"
  );
}

/** 查询数据弹窗中重置按钮 */
function resetQueryPlot() {
  reset();
  formPlot.value = {
    year: new Date().getFullYear().toString(),
    orgCode: null,
    plotName: null,
  };
  proxy.resetForm("formPlotRef");
  if (proxy.$refs["orgCodeFormRef"]) {
    proxy.$refs["orgCodeFormRef"].clear();
  }
  plotList.value = [];
  // formPlot.value.statYear = new Date().getFullYear()
}
/** 查询数据弹窗中搜索按钮 */
function handleQueryPlot() {
  proxy.$refs["formPlotRef"].validate((valid) => {
    if (valid) {
      queryPlot(formPlot.value).then((res) => {
        plotList.value = res.data;
      });
    }
  });
}
/** 查询数据弹窗中选择数据 */
function selectPlot(row) {
  reset();
  if (row.blStationNo) {
    form.value.orgCode = row.blStationNo;
  } else if (!row.blStationNo && row.blPrecinctNo) {
    form.value.orgCode = row.blPrecinctNo;
  } else if (!row.blStationNo && !row.blPrecinctNo && row.blFarmNo) {
    form.value.orgCode = row.blFarmNo;
  }
  form.value.statYear = row.year;
  // this.form.landName = row.plotName
  form.value.plotNo = row.plotNo;
  form.value.plantArea = row.contrArea;

  disSubmitForm.value = false;
  open.value = true;
}

getOptions();
getList();
</script>
<style lang="scss" scoped>
:deep(.el-input__inner) {
  text-align: left;
}

.busi-type-form {
  .el-select {
    width: 100% !important;
  }

  .el-input-number {
    width: 100% !important;
  }
}
</style>
