<template>
  <div class="home" id="indexHome">
    <title>成本效益分析系统</title>
<!--    <iframe-->
<!--        name="a"-->
<!--        id="a"-->
<!--        :height="height"-->
<!--        width="100%"-->
<!--        style="border: 0px!important;"-->
<!--        src="http://**********:8080/webroot/decision/view/form?viewlet=90_%E5%A4%A7%E5%B1%8F/%E6%88%90%E6%9C%AC%E6%95%88%E7%9B%8A%E5%88%86%E6%9E%90/%E6%88%90%E6%9C%AC%E6%95%88%E7%9B%8A%E5%88%86%E6%9E%90.frm&op=form_adaptive&org_code=860513&stat_year=2022"/>-->
  </div>
</template>

<script name="Index" setup>
import {authOrgs} from '@/api/bdh-agric-cost-analysis/user'
import {onMounted,ref} from 'vue'
const version = ref('3.8.2')
const height = ref(window.innerHeight - 88)


function goTarget(url) {
  window.open(url, '__blank')
}
function getAuthOrgs(){
  authOrgs().then((response) => {
    console.log(response,"response")
    const authData = response.data;
    const orgCodes = authData.orgCode;
    const statYear = authData.statYear;
    const iframe = document.createElement('iframe');

    let src = "http://**********:8080/webroot/decision/view/form?viewlet=90_%E5%A4%A7%E5%B1%8F/%E6%88%90%E6%9C%AC%E6%95%88%E7%9B%8A%E5%88%86%E6%9E%90/%E6%88%90%E6%9C%AC%E6%95%88%E7%9B%8A%E5%88%86%E6%9E%90.frm&op=form_adaptive";
    const org_code = orgCodes.join(',');
    const tree = orgCodes[0];
    src += "&org_code="+org_code+"&tree="+tree+"&stat_year="+statYear;
    iframe.src = src;
    iframe.height = height.value;
    iframe.width = '100%'
    document.getElementById('indexHome').appendChild(iframe);
  });
}
onMounted(()=>{

  getAuthOrgs();

})
</script>

<style lang="scss" scoped>
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }

  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }

  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}
</style>

