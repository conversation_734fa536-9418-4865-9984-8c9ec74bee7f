<template>
    <div class="app-container">
        <div ref="searchDom">
            <el-collapse-transition>
                <el-form :model="queryParams" ref="queryForm" class="queryClass form-line" :inline="true"
                    v-show="showSearch" label-width="80px">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="所属单位" prop="orgName">
                                <selectable-cascader v-model="queryParams.orgCode" :options="orgTreeList">
                                </selectable-cascader>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="年份" prop="statYear">
                                <el-select v-model="queryParams.statYear" clearable placeholder="请选择年份">
                                    <el-option v-for="item in statYearOptions" :key="item.code" :label="item.name"
                                        :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="种植作物" prop="raiseCrops">
                                <el-select v-model="queryParams.raiseCrops" filterable clearable
                                    @clear="queryParams.raiseCrops = null" placeholder="请选择种植作物">
                                    <el-option v-for="item in raiseCropsList" :key="item.raiseCropsCd"
                                        :label="item.raiseCropsNm" :value="item.raiseCropsCd" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="作业环节" prop="linkCode">
                                <el-select v-model="queryParams.linkCode" filterable clearable
                                    @clear="queryParams.linkCode = null" placeholder="请选择作业环节">
                                    <el-option v-for="item in linkCodeList" :key="item.linkCode" :label="item.linkName"
                                        :value="item.linkCode" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="集团必要" prop="isMust">
                                <el-select v-model="queryParams.isMust" filterable clearable
                                    @clear="queryParams.isMust = null" placeholder="请选择集团必要">
                                    <el-option label="是" value='1'></el-option>
                                    <el-option label="否" value='0'></el-option>
                                    <el-option label="全部" value="all"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="投入品" prop="inputConfig">
                                <el-select v-model="queryParams.inputConfig" filterable clearable
                                    @clear="queryParams.inputConfig = null" placeholder="请选择投入品">
                                    <el-option v-for="item in meansOptions" :key="item.code" :label="item.name"
                                        :value="item.code" />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="是否锁定" prop="isLocked">
                                <el-select v-model="queryParams.isLocked" filterable clearable
                                           @clear="queryParams.isLocked = null" placeholder="是否锁定">
                                    <el-option v-for="dict in pubIfOptions" :key="dict.code" :label="dict.name"
                                               :value="dict.code"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-collapse-transition>
        </div>

        <el-row :gutter="10" class="mb-4">
            <el-col :span="1.5">
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button type="primary" icon="Plus"  @click="handleAdd"
                    v-hasPermi="['dimAsLink:insert']">新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button icon="Edit"  :disabled="single|| levelFlag" @click="handleUpdate"
                    v-hasPermi="['dimAsLink:update']">修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button icon="Sort"  @click="handleFindSynchronousBranchLink"
                    v-hasPermi="['dimAsLink:findSynchronousBranchLink']">同步(分公司)
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button icon="Sort"  @click="handleFindSynchronousFarmLink"
                    v-hasPermi="['dimAsLink:findSynchronousFarmLink']">同步(农场)
                </el-button>
            </el-col>
            <el-button  icon="Delete" :disabled="multiple || levelFlag" @click="handleDelete"
                v-hasPermi="['dimAsLink:delete']">移除
            </el-button>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table border :data="dimAsLinkList" :height="tableConfig.height" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="所属单位" align="center" prop="orgName" />
            <el-table-column label="生产流程名称" align="center" prop="prodProcessName" />
            <el-table-column label="作业环节名称" align="center" prop="linkName" width="150">
                <template #default="scope">
                    <span> {{ scope.row.linkName }}</span>
                    <span v-if="scope.row.isMust == 1"
                          style="margin-left: 10px; border-radius: 4px;padding: 2px 6px 2px 4px;font-size: 12px;background: #e642421a; color: #e64242;font-weight: 400;
                        box-sizing: border-box;line-height: 16px;display: inline-block;min-width: 20px;text-align: center;">集</span>
                    <!-- 使用新组件，加锁解锁 -->
                    <LockStatusIcon v-if="scope.row.isLocked"
                                    :isLocked="scope.row.isLocked"
                                    :backgroundColor="true"/>
                </template>
            </el-table-column>
            <el-table-column label="作物名称" align="center" prop="raiseCrops" :formatter="dataFormat" />
            <el-table-column label="年份" align="center" prop="statYear" />
            <el-table-column label="计划开始时间" align="center" prop="planStartDate" />
            <el-table-column label="计划结束时间" align="center" prop="planEndDate" />
            <el-table-column label="农事标准" align="center" prop="standard" :show-overflow-tooltip="true" />
            <el-table-column label="集团必要" align="center">
                <template #default="scope">
                    <el-switch v-model="scope.row.isMust"
                        :disabled="!(ooorgCode.length ==2 && scope.row.orgCode.length == 2)"
                        @change='changeSwitch(scope.row)'>
                    </el-switch>
                    <!-- 新增图标部分,0-未锁定，1-已锁定 -->
                    <LockStatusIcon  v-if="scope.row.isMust" v-model="scope.row.isLocked"
                             :isLocked="scope.row.isLocked"
                             :disabled="!(ooorgCode.length ==2 && scope.row.orgCode.length == 2)"
                             @click="toggleLockStatus(scope.row)"
                             iconSize="20px"
                             :showPointerCursor="true"
                             :backgroundColor="false"
                             marginLeft="20px"
                    />
                </template>
            </el-table-column>


            <!-- <el-table-column label="实际开始时间" align="center" prop="actStartDate"/> -->
            <!-- <el-table-column label="实际结束时间" align="center" prop="actEndDate"/> -->
            <!-- <el-table-column label="作业环节状态" align="center" prop="linkType"/> -->
            <!-- <el-table-column label="生产流程编码" align="center" prop="prodProcessCode"/> -->
            <!-- <el-table-column label="组织机构编码" align="center" prop="orgCode"/> -->
            <!-- <el-table-column label="环节id" align="center" prop="linkId"/> -->
            <!-- <el-table-column label="作业环节编码" align="center" prop="linkCode"/> -->
            <!-- <el-table-column label="顺序" align="center" prop="linkOrder"/> -->
            <!-- <el-table-column label="创建人姓名" align="center" prop="createName"/> -->
            <!-- <el-table-column label="生效状态 1：生效，2：失效" align="center" prop="statusCd"/> -->
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" icon="Edit"
                        :disabled="scope.row.orgCode.length !== ooorgCode.length" @click="handleUpdate(scope.row)"
                        v-hasPermi="['dimAsLink:update']">修改
                    </el-button>
                    <el-button link type="primary" icon="Edit" v-hasPermi="['dimAsLink:eleCfgInfo']"
                        @click="handleFactor(scope.row)"
                        :disabled="scope.row.orgCode.length !== ooorgCode.length">记录因子管理
                    </el-button>
                    <!--          v-hasPermi="['dimAsLink:eleCfgInfo']"-->
                    <!-- <el-button
            link
            type="primary"
            icon="Delete"
            @click="handleDelete(scope.row)"

          >删除
          </el-button> -->
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total>0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.rows"
            @pagination="getList" />

        <!-- 添加或修改农事环节表对话框 -->
        <el-dialog :title="title" v-model="open" width="800px" append-to-body :before-close="cancel"
            :close-on-click-modal="false">
            <el-form ref="formRef" :model="form" :rules="rules" label-position="top" label-width="150px">
                <el-row :gutter="20" style="display: flex;flex-wrap: wrap;">
                    <el-col :span="8">
                        <el-form-item label="所属单位" prop="orgCode">
                            <selectable-cascader style="width:230px" v-model="form.orgCode" :options="orgTreeList"
                                disabled>
                            </selectable-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="年份" prop="statYear">
                            <el-select v-model="form.statYear" style="width:230px" :disabled="title.indexOf('修改')>-1"
                                clearable placeholder="请选择年份">
                                <el-option v-for="item in statYearOptions" :key="item.code" :label="item.name"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="种植作物" prop="raiseCrops">
                            <el-select v-model="form.raiseCrops" style="width:230px" v-if="title.indexOf('添加')>-1"
                                :disabled="title.indexOf('修改')>-1" clearable filterable placeholder="请选择种植作物">
                                <el-option v-for="item in raiseCropsListAdd" :key="item.raiseCropsCd"
                                    :label="item.raiseCropsNm" :value="item.raiseCropsCd" />
                            </el-select>
                            <el-select v-model="form.raiseCrops" style="width:230px" v-else
                                :disabled="title.indexOf('修改')>-1" clearable filterable placeholder="请选择种植作物">
                                <el-option v-for="item in raiseCropsList" :key="item.raiseCropsCd"
                                    :label="item.raiseCropsNm" :value="item.raiseCropsCd"
                                    :disabled="item.isUse == '0'" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="生产流程" prop="prodProcessCode">
                            <el-select v-model="form.prodProcessCode" clearable filterable :disabled="title.indexOf('修改')>-1"
                                style="width:100%" placeholder="请选择生产流程">
                                <el-option v-for="dict in prodProcessCodeOptions" :key="dict.code" :label="dict.name"
                                    :value="dict.code" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="作业环节名称" :prop="title.indexOf('修改')>-1?'linkName':'linkCode'">
                            <el-input v-model="form.linkName" v-if="title.indexOf('修改')>-1" style="width:230px"
                                disabled></el-input>
                            <el-select v-model="form.linkCode" v-else filterable clearable style="width:100%"
                                placeholder="请选择作业环节">
                                <el-option v-for="item in linkCodeOption" :key="item.linkCode" :label="item.linkName"
                                    :value="item.linkCode" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="计划开始时间" prop="planStartDate">
                            <el-date-picker clearable style="width:230px" v-model="form.planStartDate" type="date"
                                value-format="MMDD" format="MMDD" placeholder="请选择计划开始时间" @focus="timeFocus('Stat')"
                                :disabled-date="pickerOptionsStatQuery">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="计划结束时间" prop="planEndDate">
                            <el-date-picker clearable style="width:230px" v-model="form.planEndDate" type="date"
                                value-format="MMDD" format="MMDD" placeholder="请选择计划结束时间" @focus="timeFocus('End')"
                                :disabled-date="pickerOptionsEndQuery">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <!-- <el-col :span="8">
                    <el-form-item label="实际开始时间" prop="actStartDate">
                    <el-date-picker
                        clearable
                        style="width:230px"
                        v-model="form.actStartDate"
                        type="date"
                        value-format="MMDD"
                        format="MMDD"
                        placeholder="请选择实际开始时间"
                    >
                    </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item label="实际结束时间" prop="actEndDate">
                    <el-date-picker
                        clearable
                        style="width:230px"
                        v-model="form.actEndDate"
                        type="date"
                        value-format="MMDD"
                        format="MMDD"
                        placeholder="请选择实际结束时间"
                    >
                    </el-date-picker>
                    </el-form-item>
                </el-col> -->
                <el-row :gutter="20" style="display: flex;flex-wrap: wrap;">
                    <el-col :span="8">
                        <el-form-item label="是否播种" prop="isGrow">
                            <el-switch v-model="form.isGrow" active-color="#409EFF" inactive-color="#DCDFE6" :width="35"
                                active-value="1" inactive-value="0" @change="updateStatusSwi"
                                :disabled="form.orgCode!='86'&&getGroupLinkData&&getGroupLinkData.isGrow=='1'"
                                style="margin-right: 5px;">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="是否施肥" prop="isFertilize">
                            <el-switch v-model="form.isFertilize" active-color="#409EFF" inactive-color="#DCDFE6"
                                :width="35" active-value="1" inactive-value="0" @change="updateStatusSwi"
                                :disabled="form.orgCode!='86'&&getGroupLinkData&&getGroupLinkData.isFertilize=='1'"
                                style="margin-right: 5px;">
                            </el-switch>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="是否用药" prop="isPesticide">
                            <el-switch v-model="form.isPesticide" active-color="#409EFF" inactive-color="#DCDFE6"
                                :width="35" active-value="1" inactive-value="0" @change="updateStatusSwi('isPesticide')"
                                :disabled="form.orgCode!='86'&&getGroupLinkData&&getGroupLinkData.isPesticide=='1'"
                                style="margin-right: 5px;">
                            </el-switch>
                        </el-form-item>
                        <el-select v-if="form.isPesticide == '1'&&ooorgCode=='86'" v-model="form.pesticideTypeList"
                            filterable clearable multiple @change="$forceUpdate()" placeholder="请选择投入品">
                            <el-option v-for="item in pesticideTypeOptions" :key="item.code" :label="item.name"
                                :value="item.code" />
                        </el-select>
                    </el-col>
                </el-row>
                <el-form-item label="农事标准" prop="standard">
                    <el-input v-model.trim="form.standard" maxlength="200" type="textarea" :rows="2"
                        placeholder="请输入农事标准" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 记录因子对话框 -->
        <el-dialog title="管理记录因子" v-model="openFactor" width="800px" append-to-body :before-close="cancelFactor"
            :close-on-click-modal="false">
            <el-form ref="formRef" :model="tableData" label-position="top" label-width="150px">
                <el-row :gutter="20" style="display: flex;flex-wrap: wrap;">
                    <el-col :span="8">
                        <el-form-item label="种植作物" prop="raiseCrops">
                            <el-select v-model="tableData.raiseCrops" disabled placeholder="请选择种植作物">
                                <el-option v-for="item in raiseCropsList" :key="item.raiseCropsCd"
                                    :label="item.raiseCropsNm" :value="item.raiseCropsCd" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="生产流程" prop="prodProcessName">
                            <el-input v-model="tableData.prodProcessName" style="width:230px" disabled></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="作业环节" prop="linkCode">
                            <el-input v-model="tableData.linkName" style="width:230px" disabled></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-divider></el-divider>
            </el-form>
            <!-- 动态添加因子 -->
            <div v-if="tableData.orgCode == '86'">
                <div style="font-weight: 500;">记录因子 <el-button type="primary" icon="Plus"
                        @click="addElementOption" v-hasPermi="['dimAsLink:elementList']"
                        style="margin-left: 10px;">添加记录因子</el-button></div>
                <div style="display: flex;flex-wrap: wrap;">
                    <div v-for="(item,index) in formFactorDynamicState" :key="index"
                        style="margin: 10px 0;padding-left: 2%;width: 33%; overflow: hidden;">
                        <div>记录因子{{index+1}}</div>
                        <div style="display: flex;flex-wrap: wrap;">
                            <el-select v-model="item.elementId" @change="handleSelectItemChange(index,item.elementId)"
                                @focus="handleSelectItemFocus(index,item.elementId)" style="width: 170px;"
                                :placeholder="'请选择记录因子'+(index+1)">
                                <el-option v-for="item in elementOption" :key="item.elementId" :label="item.elementName"
                                    :value="item.elementId" />
                            </el-select>
                            <el-button link type="primary" style="margin-left: 10px;"
                                @click="handleDeleteItem(index,item)">删除</el-button>
                        </div>
                    </div>
                </div>
                <el-divider></el-divider>
            </div>
            <div style="display: flex;flex-wrap: wrap;">
                <template v-for="(formCont, contIndex) in formFactor">
                    <div style="margin: 10px 0;padding-left: 2%;width: 33%; overflow: hidden; "
                         :style="{'width': ((formCont.elementFillMethod === '3' && formCont.elementType === '2') ||
                            (formCont.elementFillMethod === '0' && formCont.elementType === '4')) ? '100%' : '33%'}">
                        <el-row>
                            <el-col :span="((formCont.elementFillMethod === '3' && formCont.elementType === '2') ||
                                (formCont.elementFillMethod === '0' && formCont.elementType === '4'))?8:24">
                                <el-checkbox style="width: 100%;" v-model="formCont.isChoice" true-value="1"
                                    false-value="0" @change="childChange(formCont)"
                                    :disabled="isCheckboxDisabled(formCont)">
                                    <span style="vertical-align:middle;">
                                        <el-tooltip :content="formCont.elementName" placement="top">
                                            <span style="display: inline-block;">{{formCont.elementName}}</span>
                                        </el-tooltip>
                                        <span v-if="formCont.isMustGrp == 1"
                                            style="margin-left: 10px; border-radius: 4px;padding: 2px 6px 2px 4px;font-size: 12px;background: #e642421a; color: #e64242;font-weight: 400;">
                                            集
                                        </span>
                                        <span v-if="formCont.isMustBranch == 1 && formCont.isMustGrp != 1"
                                            style="margin-left: 10px; border-radius: 4px;padding: 2px 6px 2px 4px;font-size: 12px;background: #e642421a; color: #e64242;font-weight: 400;">
                                            分
                                        </span>
                                        <span
                                            v-if="formCont.isMustFarm == 1 && formCont.isMustGrp != 1 && formCont.isMustBranch != 1"
                                            style="margin-left: 10px; border-radius: 4px;padding: 2px 6px 2px 4px;font-size: 12px;background: #e642421a; color: #e64242;font-weight: 400;">
                                            农
                                        </span>
                                    </span>

                                </el-checkbox>
                            </el-col>
                            <el-col :span="8" style="padding-left:1%; display: flex; align-items: center; "
                                v-if="formCont.elementFillMethod == '0'&&formCont.elementType == '4'">
                                限制
                            </el-col>
                            <el-col :span="8" style="padding-left:1%; display: flex; align-items: center; "
                                    v-if="formCont.elementFillMethod == '3'&&formCont.elementType == '2'">
                                解除投入品必填下拉项
                            </el-col>
                            <el-col :span="8" style="padding-left:1%; display: flex; align-items: center; "
                                    v-if="formCont.elementFillMethod == '3'&&formCont.elementType == '2'">
                                解除投入品必填项目
                            </el-col>
                        </el-row>
                        <el-row style="display: flex;margin-top: 10px;">
                            <el-col :span="((formCont.elementFillMethod === '3' && formCont.elementType === '2') ||
                                        (formCont.elementFillMethod === '0' && formCont.elementType === '4'))?5:18"
                                    style="min-width: 170px;">
                                <el-input :maxLength="formCont.elementLength" v-if="formCont.elementType == '0'"
                                    style="width:170px" v-model="formCont.elementDefValue"
                                    :placeholder="formCont.elementPromptInfo" clearable>
                                    <!-- <template slot="append" v-if="formCont.elementUnit">{{formCont.elementUnit}}</template> -->
                                </el-input>
                                <el-date-picker style="width: 170px;" v-if="formCont.elementType == '1'"
                                    :placeholder="formCont.elementPromptInfo" clearable value-format="YYYY-MM-DD"
                                    v-model="formCont.elementDefValue" type="date">
                                </el-date-picker>
                                <!-- 下拉选择器（类型2） -->
                                <el-select v-if="formCont.elementType == '2'" :placeholder="formCont.elementPromptInfo"
                                    clearable v-model="formCont.elementDefValue" style="width: 170px;">
                                    <el-option v-for="dict in  formCont.ListOptions" :key="dict.code" :label="dict.name"
                                        :value="dict.code" />
                                </el-select>
                                <el-input-number style="width: 170px;" :max=Number(num.repeat(formCont.elementLength))
                                    :precision="formCont.elementPrecision" :controls="false"
                                    v-model="formCont.elementDefValue" v-if="formCont.elementType == '4'"
                                    :placeholder="formCont.elementPromptInfo" clearable>
                                </el-input-number>
                            </el-col>
                            <el-col :span="((formCont.elementFillMethod === '3' && formCont.elementType === '2') ||
                                        (formCont.elementFillMethod === '0' && formCont.elementType === '4'))?2:6"
                                    :style="{'padding-left':((formCont.elementFillMethod === '3' && formCont.elementType === '2') ||
                                        (formCont.elementFillMethod === '0' && formCont.elementType === '4'))?'15px':''}"
                                    style="width:30px; display: flex; align-items: center;margin-left:10px;">
                                <div>{{ formCont.elementUnit }}</div>
                            </el-col>
                            <el-col :span="6" v-if="formCont.elementFillMethod == '0'&&formCont.elementType == '4'"
                                style="padding-left: 13px;">
                                <el-input-number :max=Number(num.repeat(formCont.elementLength))
                                    :disabled="ooorgCode!='86'" :precision="formCont.elementPrecision" :controls="false"
                                    v-model="formCont.minValue" placeholder="请输入最小值" clearable
                                    style="width: 170px;"></el-input-number>
                            </el-col>
                            <el-col :span="3" style="text-align: center;margin-top: 10px;margin-left: -10px;"
                                v-if="formCont.elementFillMethod == '0'&&formCont.elementType == '4'">
                                -
                            </el-col>
                            <el-col :span="6" v-if="formCont.elementFillMethod == '0'&&formCont.elementType == '4'">
                                <el-input-number :max=Number(num.repeat(formCont.elementLength))
                                    :disabled="ooorgCode!='86'" :precision="formCont.elementPrecision" :controls="false"
                                    v-model="formCont.maxValue" placeholder="请输入最大值" clearable
                                    style="width: 170px;margin-left: -5px;"></el-input-number>
                            </el-col>
                            <!--解除投入品必填下拉项-->
                            <el-col :span="6" v-if="formCont.elementFillMethod == '3' && formCont.elementType == '2'
                                    && !listOptionsLoading"
                                    style="padding-left: 13px;">
                                <el-select :placeholder="formCont.elementPromptInfo"
                                           clearable v-model="formCont.isInputRequired" style="width: 170px;">
                                    <el-option v-for="(dict,index) in formCont.ListOptions" :key="index" :label="dict.name"
                                               :value="dict.code"/>
                                </el-select>
                            </el-col>
                            <!--解除投入品必填项目-空位-->
                            <el-col :span="3" style="margin-left:-10px;"
                                    v-if="formCont.elementFillMethod == '3'&&formCont.elementType == '2'">
                            </el-col>
                            <!--解除投入品必填项目-->
                            <el-col :span="6" v-if="formCont.elementFillMethod == '3'&&formCont.elementType == '2'"
                                    style="width: 170px;margin-left: -5px;">
                                <el-select placeholder="请选择"
                                           clearable v-model="formCont.inputRequiredItemList" style="width: 170px;"
                                           multiple collapse-tags collapse-tags-tooltip :max-collapse-tags="3">
                                    <el-option v-for="dict in disRequiredInputItemsOptions"
                                               :key="dict.code" :label="dict.name" :value="dict.code">
                                    </el-option>
                                </el-select>
                            </el-col>
                        </el-row>
                    </div>
                </template>
            </div>
            <template #footer>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="submitFactorForm">确 定</el-button>
                    <el-button @click="cancelFactor">取 消</el-button>
                </div>
            </template>
        </el-dialog>


        <el-dialog title="同步环节" v-model="openLink" width="950px" append-to-body :before-close="cancelLink"
            :close-on-click-modal="false">
            <el-form :model="queryParamsLink" ref="queryParamsLink" class="queryClass form-line" :inline="true"
                label-width="110px">
                <el-row style="display: flex; flex-wrap: wrap">
                    <el-col :span="8">
                        <el-form-item label="年份" prop="statYear">
                            <el-select v-model="queryParamsLink.statYear" placeholder="请选择年份">
                                <el-option v-for="item in statYearOptions" :key="item.code" :label="item.name"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="所属单位" prop="orgCode">
                            <selectable-cascader v-model="queryParamsLink.orgCode" :options="orgTreeList" disabled>
                            </selectable-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="种植作物" prop="raiseCrops">
                            <el-select v-model="queryParamsLink.raiseCrops" filterable :clearable="false"
                                placeholder="请选择种植作物">
                                <el-option v-for="item in raiseCropsList" :key="item.raiseCropsCd"
                                    :label="item.raiseCropsNm" :value="item.raiseCropsCd" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="生产流程名称" prop="prodProcessCode">
                            <el-select v-model="queryParamsLink.prodProcessCode" clearable
                                @clear="queryParamsLink.prodProcessCode = null" placeholder="请选择生产流程名称">
                                <el-option v-for="item in prodProcessCodeOptions" :key="item.code" :label="item.name"
                                    :value="item.code" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item align="right" style="width: 100%;">
                            <el-button icon="Refresh"@click="resetQueryLink">重置</el-button>
                            <el-button type="primary" icon="Search" @click="handleQueryLink">搜索</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table border :data="dimAsLinkListLink" height="500" @selection-change="handleSelectionChangeLink">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="所属单位" align="center" prop="orgName" />
                <el-table-column label="生产流程名称" align="center" prop="prodProcessName" />
                <el-table-column label="作业环节名称" align="center" prop="linkName" />
                <el-table-column label="作物名称" align="center" prop="raiseCrops" :formatter="dataFormat" />
                <el-table-column label="年份" align="center" prop="statYear" />
                <el-table-column label="计划开始时间" align="center" prop="planStartDate" />
                <el-table-column label="计划结束时间" align="center" prop="planEndDate" />
                <el-table-column label="农事标准" align="center" prop="standard" :show-overflow-tooltip="true" />
            </el-table>
            <pagination v-show="totalLink>0" :total="totalLink" v-model:page="queryParamsLink.page"
                v-model:limit="queryParamsLink.rows" @pagination="changePage" />
            <template #footer>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="submitFormLink">确 定</el-button>
                    <el-button @click="cancelLink">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
    import SelectableCascader from "@/views/sysagriculturalsituation/components/SelectableCascader.vue";
    import LockStatusIcon from "@/views/sysagriculturalsituation/components/Icon/lockStatusIcon.vue"; // 引入新组件
    import '@/views/sysagriculturalsituation/assets/styles/index.scss' // global css
    import { getDicts, queryAllCropWhole, getOrg } from "@/api/sysagriculturalsituation/dict"
    import { getInitTreeCode, getInitTreeName, selectRaiseLabel, updateButtonStyle } from '@/api/sysagriculturalsituation/utils/cop'
    import {
        listDimAsLink,
        getDimAsLink,
        eleCfgInfo,
        delDimAsLinks,
        addDimAsLink,
        updateDimAsLink,
        findSynchronousBranchLink,
        findSynchronousFarmLink,
        insertSynchronousLink,
        logicDeleteById,
        queryListLinkCode,
        dimAsLinkIsMust,
        eleCfgInfoUpdate,
        elementList,
        getGroupLink,
        queryByProdAndRaise,
        dimAsLinkIsLocked,
    } from '@/api/sysagriculturalsituation/config/dimAsLink'
    import {
        queryRightfulVerf
    } from "@/api/sysagriculturalsituation/agriculturalManagement/farmingManagement/dimGrowPatterns";
    import {
        getRaiseCropsList,
        getRaiseCropsVarietyList,
        getLinkCode
    } from '@/api/sysagriculturalsituation/sow/dimAsGrowCfg'
    import commonPage from "../../mixins/commonPage";

    export default {
        name: '/config/dimAsLink/queryByPage',
        components: { SelectableCascader ,LockStatusIcon },
        data() {
            return {
                // 遮罩层
                loading: true,
                num: '9',
                // 选中数组
                ids: [],
                idsLink: [],
                names: [],
                // 非单个禁用
                single: true,
                // 非多个禁用
                multiple: true,
                // 显示搜索条件
                showSearch: true,
                // 总条数
                total: 0,
                totalLink: 0,
                // 农事环节表表格数据
                dimAsLinkList: [],
                dimAsLinkListLink: [],
                // 弹出层标题
                title: '',
                // 是否显示弹出层
                open: false,
                openLink: false,

                //记录因子管理
                // 是否显示弹出层
                formFactor: {},
                formFactorAll: {},
                formFactorDynamicState: [],
                openFactor: false,

                // 查询参数
                queryParams: {
                    page: 1,
                    rows: 10,
                    linkCode: null,
                    linkName: null,
                    linkOrder: null,
                    prodProcessCode: null,
                    prodProcessName: null,
                    orgCode: null,
                    orgName: null,
                    raiseCrops: null,
                    statYear: null,
                    planStartDate: null,
                    planEndDate: null,
                    linkType: null,
                    actStartDate: null,
                    actEndDate: null,
                    createName: null,
                    statusCd: null,
                    isMust: null,
                    inputConfig: null,
                    isLocked: null,
                },
                // 表单参数
                form: {},
                // 表单校验
                rules: {
                    orgCode: [{ required: true, message: '请选择所属单位', trigger: "change" }],
                    statYear: [{ required: true, message: '请选择年份', trigger: "change" }],
                    raiseCrops: [{ required: true, message: '请选择种植作物', trigger: "change" }],
                    prodProcessCode: [{ required: true, message: '请选择生产流程', trigger: "change" }],
                    linkCode: [{ required: true, message: '请选择作业环节名称', trigger: "change" }],
                    linkName: [{ required: false, message: '请选择作业环节名称', trigger: "blur" }],
                    planEndDate: [{ required: true, message: '请选择计划结束时间', trigger: "change" }],
                    planStartDate: [{ required: true, message: '请选择计划开始时间', trigger: "change" }],
                    standard: [{ required: true, message: '请输入农事标准', trigger: "blur" }],
                },
                orgTreeList: [],
                raiseCropsList: [],
                raiseCropsListAdd: [],
                statYearOptions: [],
                linkCodeList: [],
                prodProcessCodeOptions: [],
                tableConfig: {
                    height: window.innerHeight - 330
                },
                queryParamsLink: {
                    page: 1,
                    rows: 10,
                    orgCode: null,
                    statYear: null,
                    raiseCrops: null,
                    linkCode: null,
                },
                tableData: {},//表格数据
                flagIndex: '1',
                levelFlag: true,
                ooorgCode: '',
                timeSection: [],
                elementOption: [],
                elementOptionAll: [],
                getGroupLinkData: {},
                linkCodeOption: [],
                meansOptions: [
                    { code: '1', name: '施肥' },
                    { code: '2', name: '播种' },
                    { code: '3', name: '用药' },
                ],
                pesticideTypeOptions: [],
                searchHeight: 0,
                pubIfOptions: [],
                isInputRequired: null,
                inputRequiredItemList: [],
                listOptionsLoading: false, // 添加加载状态
                disRequiredInputItemsOptions: [
                    { code: '1', name: '是否播种' },
                    { code: '2', name: '是否施肥' },
                    { code: '3', name: '是否用药' },
                ],
            }
        },
        computed: {
            changeData() {
                const { orgCode, raiseCrops, statYear } = this.queryParams
                return {
                    orgCode,
                    raiseCrops,
                    statYear
                }
            },
            changeDataLink() {
                const { prodProcessCode, raiseCrops } = this.form
                return {
                    prodProcessCode,
                    raiseCrops
                }
            },
            //如果isMustGrp =1，集团以下不让点。isMustBranch =1，分公司以下不让点。isMustFarm农场一下不让点
            isCheckboxDisabled(formItem) {
                return (formItem) => {
                    const ooorgCodeLength = this.ooorgCode ? this.ooorgCode.length : 0;
                    if (formItem.isMustGrp == 1 && ooorgCodeLength !== 2) return true;
                    if (formItem.isMustBranch == 1 && ooorgCodeLength !== 4) return true;
                    if (formItem.isMustFarm == 1 && ooorgCodeLength !== 6) return true;
                    return false;
                };
            }
            // isCheckboxDisabled(formItem) {
            //     return function (formItem) {
            //         if (formItem.isMustGrp == 1 && this.ooorgCode.length != 2) {
            //             return true;
            //         }
            //         if (formItem.isMustBranch == 1 && this.ooorgCode.length != 4) {
            //             return true;
            //         }
            //         if (formItem.isMustFarm == 1 && this.ooorgCode.length != 6) {
            //             return true;
            //         }
            //         // 如果都不满足，返回false
            //         return false;
            //     }
            // },
        },
        watch: {
            //搜索隐藏调整表格大小
            showSearch: {
                handler() {
                    this.tableConfig.height = this.showSearch
                        ? window.innerHeight - this.searchHeight - 220
                        : window.innerHeight - 220;
                },
                immediate: true
            },
            changeData: {
                handler(newValue, oldValue) {
                    if (newValue.orgCode != null && newValue.raiseCrops != '' && newValue.raiseCrops != null && typeof (newValue.raiseCrops) == 'string' && newValue.statYear != null && typeof (newValue.statYear) == "string") {
                        if (newValue.orgCode != null && typeof (newValue.orgCode) == 'object') {
                            queryListLinkCode({ orgCode: newValue.orgCode[newValue.orgCode.length - 1], raiseCrops: newValue.raiseCrops, statYear: newValue.statYear }).then((response) => {
                                this.linkCodeList = response.data
                            })
                        } else if (newValue.orgCode != null && typeof (newValue.orgCode) == 'string') {
                            queryListLinkCode({ orgCode: newValue.orgCode, raiseCrops: newValue.raiseCrops, statYear: newValue.statYear }).then((response) => {
                                this.linkCodeList = response.data
                            })
                        }
                    }
                    if (oldValue.raiseCrops != null && newValue.raiseCrops != oldValue.raiseCrops) {
                        this.linkCodeList = []
                        this.queryParams.linkCode = null
                    }
                    if (oldValue.orgCode != null && typeof (newValue.orgCode) == 'object' && typeof (oldValue.orgCode) == 'object' && newValue.orgCode[newValue.orgCode.length - 1] != oldValue.orgCode[oldValue.orgCode.length - 1]) {
                        this.linkCodeList = []
                        this.queryParams.linkCode = null
                    }
                    if (oldValue.orgCode != null && typeof (newValue.orgCode) == 'object' && typeof (oldValue.orgCode) == 'string') {
                        this.linkCodeList = []
                        this.form.linkCode = null
                    }
                }
            },
            changeDataLink: {
                handler(newValue, oldValue) {
                    if (this.title.indexOf('添加') != -1) {
                        this.form.linkCode = null
                        this.linkCodeOption = []
                        if (newValue.prodProcessCode != null && newValue.prodProcessCode != '' && newValue.raiseCrops != null && newValue.raiseCrops != '') {
                            queryByProdAndRaise({ prodProcessCode: newValue.prodProcessCode, raiseCrops: newValue.raiseCrops }).then((response) => {
                                this.linkCodeOption = response.data
                            })
                        }
                    }
                }
            }
        },
        created() {
            let orgCode = getOrg().then((response) => {
                this.orgTreeList = response.data;
                this.queryParams.orgCode = getInitTreeCode(this.orgTreeList)
                this.ooorgCode = getInitTreeCode(this.orgTreeList)[0]
            });
            let yearCd = getDicts("plan_year_cd").then((response) => {
                this.statYearOptions = response.data
                this.queryParams.statYear = this.statYearOptions[0].code
            });
            let prodProcessCode = getDicts("prod_process_code").then((response) => {
                this.prodProcessCodeOptions = response.data
            });
            let pesticide_type = getDicts("pesticide_type").then((response) => {
                this.pesticideTypeOptions = response.data
            });
            let pubIf = getDicts('pub_if').then(response => {
                this.pubIfOptions = response.data
                this.pubIfOptions.unshift({ code: 'all', name: '全部' })
            })
            let raiseCrops = queryAllCropWhole({ useRange: '1' }).then((response) => {
                this.raiseCropsList = response.data
                this.raiseCropsListAdd = response.data.filter(item => {
                    return item.isUse != '0'
                })
            })
            Promise.all([raiseCrops, yearCd, orgCode]).then(() => {
                this.getList()
            })
        },
        mounted() {
            this.searchHeight = this.$refs.searchDom.clientHeight;
            this.tableConfig.height = this.showSearch
                ? window.innerHeight - this.searchHeight - 220
                : window.innerHeight - 220;
            updateButtonStyle()
        },
        methods: {
            toggleLockStatus(row) {
                const {isLocked, linkId} = row;
                // 构建参数对象
                const params = {
                    isLocked: isLocked == '1' ? '0' : '1', // 状态取反
                    linkId: linkId,
                };
                //关锁->开锁
                if (isLocked) {
                    dimAsLinkIsLocked(params).then(() => {
                        this.getList();
                        this.$message.success("操作成功");
                    }).catch((error) => {
                        this.$message.error("操作失败，请重试");
                    });
                } else {
                    // 开锁->关锁，弹出确认提示框
                    this.$confirm(
                            `<div>确认锁定该集团必要环节?锁定后</div>
                            <div>1.种植方案中，锁定环节显示、隐藏禁用。</div>
                            <div>2.农事记录及审核功能中，锁定环节退回、删除禁用。</div>
                            <div>3.所有已记录未结束的锁定环节会直接结束，所有未记录的锁定环节会自动未作业结束，所有新绑定种植方案有锁定环节会自动未作业结束。</div>`,
                            '系统提示',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: null,
                                dangerouslyUseHTMLString: true, // 允许使用 HTML 字符串
                                showClose: true,
                                customClass: 'set-del-model1'
                            }
                    ).then(() => {
                        return dimAsLinkIsLocked(params);
                    }).then(() => {
                        this.getList();
                        this.$message.success("操作成功");
                    }).catch((error) => {
                        if (error !== 'cancel') {
                            this.$message.error("操作失败，请重试");
                        }
                    });
                }
            },
            pickerOptionsStatQuery(time) {
                if (this.timeSection && this.timeSection[0]) {
                    if (this.form.planEndDate && this.timeSection && this.timeSection[0] && this.timeSection[0].planStartDate) {
                        let start = this.timeSection[0].planStartDate
                        return (time.getTime() < new Date(new Date().getFullYear() + '-' + start.substring(0, 2) + '-' + start.substring(2, 4)).getTime() - (24 * 60 * 60 * 1000)) || time.getTime() > new Date(new Date().getFullYear() + '-' + this.form.planEndDate.substring(0, 2) + '-' + this.form.planEndDate.substring(2, 4)).getTime()
                    } else if (this.timeSection && this.timeSection[0] && this.timeSection[0].planStartDate) {
                        let start = this.timeSection[0].planStartDate
                        let end = this.timeSection[0].planEndDate
                        return (time.getTime() < new Date(new Date().getFullYear() + '-' + start.substring(0, 2) + '-' + start.substring(2, 4)).getTime() - (24 * 60 * 60 * 1000)) || time.getTime() > new Date(new Date().getFullYear() + '-' + end.substring(0, 2) + '-' + end.substring(2, 4)).getTime()
                    }
                } else {
                    if (this.form.planEndDate) {
                        return (time.getTime() > new Date(new Date().getFullYear() + '-' + this.form.planEndDate.substring(0, 2) + '-' + this.form.planEndDate.substring(2, 4)).getTime())
                    }
                }
            },
            pickerOptionsEndQuery(time) {
                if (this.timeSection && this.timeSection[0]) {
                    if (this.form.planStartDate && this.timeSection && this.timeSection[0] && this.timeSection[0].planEndDate) {
                        let end = this.timeSection[0].planEndDate
                        return time.getTime() > new Date(new Date().getFullYear() + '-' + end.substring(0, 2) + '-' + end.substring(2, 4)).getTime() || (time.getTime() < new Date(new Date().getFullYear() + '-' + this.form.planStartDate.substring(0, 2) + '-' + this.form.planStartDate.substring(2, 4)).getTime() - (24 * 60 * 60 * 1000))
                    } else if (this.timeSection && this.timeSection[0] && this.timeSection[0].planEndDate) {
                        let start = this.timeSection[0].planStartDate
                        let end = this.timeSection[0].planEndDate
                        return time.getTime() > new Date(new Date().getFullYear() + '-' + end.substring(0, 2) + '-' + end.substring(2, 4)).getTime() || (time.getTime() < new Date(new Date().getFullYear() + '-' + start.substring(0, 2) + '-' + start.substring(2, 4)).getTime() - (24 * 60 * 60 * 1000))
                    }
                } else {
                    if (this.form.planStartDate) {
                        return time.getTime() < new Date(new Date().getFullYear() + '-' + this.form.planStartDate.substring(0, 2) + '-' + this.form.planStartDate.substring(2, 4)).getTime() - (24 * 60 * 60 * 1000)
                    }
                }
            },
            addElementOption() {
                this.formFactorDynamicState.push({
                    createBy: null,
                    createTime: null,
                    dictKey: null,
                    elementDefValue: null,
                    elementFillMethod: null,
                    elementId: null,
                    elementLength: null,
                    elementName: null,
                    elementPrecision: null,
                    elementPromptInfo: null,
                    elementType: null,
                    elementUnit: null,
                    externalDataId: null,
                    isChoice: null,
                    isMultiple: null,
                    orders: null,
                    params: null,
                    prodProcessCode: null,
                    remark: null,
                    statusCd: null,
                    updateBy: null,
                    updateTime: null
                })
            },
            // select 变化
            handleSelectItemChange(index, elementId) {
                let elementArr = this.elementOption.filter(item => {
                    return item.elementId == elementId
                })
                this.formFactorDynamicState[index] = Object.assign(this.formFactorDynamicState[index], elementArr[0]);
                (async () => {
                    const promises = elementArr.map(async it => {
                        if (it.elementFillMethod == '3') {
                            it.ListOptions = []
                            await getDicts(it.dictKey).then((response) => {
                                it.ListOptions = response.data
                            });
                        }
                        //外部数据
                        if (it.elementFillMethod == '4') {
                            it.ListOptions = []
                        }
                        if (it.elementType == '3') {
                            it.elementDefValue = null
                        }
                    });
                    await Promise.all(promises);
                    elementArr.forEach(it => {
                        it.linkCode = this.tableData.linkCode
                        it.linkId = this.tableData.linkId
                        it.linkName = this.tableData.linkName
                        it.orgCode = this.tableData.orgCode
                        it.orgName = this.tableData.orgName
                        it.maxValue = undefined
                        it.minValue = undefined
                        if (it.orgCode && it.orgCode.length == '2') {
                            it.isChoice = it.isMustGrp == 1 ? '1' : '0'
                        }
                        if (it.orgCode && it.orgCode.length == '4') {
                            it.isChoice = (it.isMustGrp == 1 || it.isMustBranch == 1) ? '1' : '0'
                        }
                        if (it.orgCode && it.orgCode.length == '6') {
                            it.isChoice = (it.isMustGrp == 1 || it.isMustBranch == 1 || it.isMustFarm == 1) ? '1' : '0'
                        }
                    })
                    this.formFactor = this.formFactor.concat(elementArr)
                    let values = []
                    this.formFactorAll.forEach(it => {
                        values.push(it.elementId)
                    })
                    this.formFactorDynamicState.forEach(it => {
                        values.push(it.elementId)
                    })
                    this.formFactor = this.formFactor.filter(it => {
                        return values.indexOf(it.elementId) !== -1
                    })
                })();
            },
            handleSelectItemFocus(index, elementId) {
                let values = []
                this.formFactor.forEach(it => {
                    values.push(it.elementId)
                })
                this.elementOption = this.elementOptionAll.filter(it => {
                    return values.indexOf(it.elementId) == -1 || it.elementId == elementId
                })
            },
            // 删除因子
            handleDeleteItem(index, row) {
                this.formFactorDynamicState.splice(index, 1)
                this.elementOption = this.elementOptionAll
                if (row.elementId != null) {
                    let iIndex = this.formFactor.findIndex(function (val) {
                        return val.elementId == row.elementId
                    })
                    this.formFactor.splice(iIndex, 1)
                }
            },
            timeFocus(type) {
                if (this.timeSection && this.timeSection[0]) {
                    let startFlag = this.isDateInRange(this.form.planStartDate, new Date().getFullYear() + '-' + this.timeSection[0].planStartDate.substring(0, 2) + '-' + this.timeSection[0].planStartDate.substring(2, 4), new Date().getFullYear() + '-' + this.timeSection[0].planStartDate.substring(0, 2) + '-' + this.timeSection[0].planStartDate.substring(2, 4))
                    let endFlag = this.isDateInRange(this.form.planEndDate, new Date().getFullYear() + '-' + this.timeSection[0].planEndDate.substring(0, 2) + '-' + this.timeSection[0].planEndDate.substring(2, 4), new Date().getFullYear() + '-' + this.timeSection[0].planEndDate.substring(0, 2) + '-' + this.timeSection[0].planEndDate.substring(2, 4))
                    if (type == 'Stat' && !startFlag && !this.form.planStartDate) {
                        this.form.planStartDate = null
                        this.form.planEndDate = null
                        return this.$notify.error({
                            title: "提示",
                            message: `请更正计划开始时间`,
                        });
                    }
                    if (type == 'End' && !endFlag && !this.form.planEndDate) {
                        this.form.planEndDate = null
                        return this.$notify.error({
                            title: "提示",
                            message: `请更正计划结束时间`,
                        });
                    }
                    this.$forceUpdate()
                }
            },
            isDateInRange(time, start, end) {
                var currentDate = new Date(time);
                var startDate = new Date(start);
                var endDate = new Date(end);
                // 如果当前日期在start和end之间，或者等于start或end，则返回true
                return currentDate >= startDate && currentDate <= endDate;
            },
            dataFormat(row, column) {
                return selectRaiseLabel(
                    this.raiseCropsList,
                    row[column.property] ? row[column.property].toString() : ""
                );
            },
            /** 查询农事环节表列表 */
            getList() {
                this.loading = true
                if (
                    this.queryParams.orgCode !== null &&
                    this.queryParams.orgCode &&
                    typeof this.queryParams.orgCode !== 'string'
                ) {
                    this.queryParams.orgCode =
                        this.queryParams.orgCode[this.queryParams.orgCode.length - 1]
                }
                let params = { ...this.queryParams }
                params.isMust == 'all' ? params.isMust = null : ''
                params.isLocked == 'all' ? params.isLocked = null : ''
                listDimAsLink(params).then(response => {
                    this.dimAsLinkList = response.data.records
                    if (this.dimAsLinkList.length > 0) {
                        this.dimAsLinkList.forEach(v => {
                            if (v.isMust == 1) {
                                v.isMust = true
                            } else {
                                v.isMust = false
                            }
                            v.isLocked = v.isLocked == 1;
                        })
                    }
                    this.total = response.data.total
                    this.loading = false
                })
            },
            // 取消按钮
            cancel() {
                this.open = false
                this.reset()
            },
            cancelLink() {
                this.openLink = false
            },
            changePage() {
                if (this.flagIndex == '1') {
                    this.getBranchList();
                } else {
                    this.getFarmList()
                }
            },
            // 表单重置
            reset() {
                this.form = {
                    linkId: null,
                    linkCode: null,
                    linkName: null,
                    linkOrder: null,
                    prodProcessCode: null,
                    prodProcessName: null,
                    orgCode: null,
                    orgName: null,
                    raiseCrops: null,
                    statYear: null,
                    planStartDate: null,
                    planEndDate: null,
                    linkType: null,
                    actStartDate: null,
                    actEndDate: null,
                    standard: null,
                    createBy: null,
                    createName: null,
                    createTime: null,
                    updateBy: null,
                    updateTime: null,
                    statusCd: null,
                    pesticideTypeList: []
                }
                this.resetForm('formRef')
            },
            /** 搜索按钮操作 */
            handleQuery() {
                this.queryParams.page = 1
                this.getList()
            },
            /** 重置按钮操作 */
            resetQuery() {
                this.resetForm('queryForm')
                this.handleQuery()
            },
            resetQueryLink() {
                this.queryParamsLink.orgCode = getInitTreeCode(this.orgTreeList)
                this.queryParamsLink.statYear = this.statYearOptions[0].code
                this.queryParamsLink.raiseCrops = this.raiseCropsList[0].raiseCropsCd
                this.queryParamsLink.linkCode = null
                this.handleQueryLink();
            },
            handleQueryLink() {
                this.queryParamsLink.page = 1;
                if (this.flagIndex == '1') {
                    this.getBranchList();
                } else {
                    this.getFarmList()
                }
            },
            // 多选框选中数据
            handleSelectionChange(selection) {
                this.ids = selection.map(item => item.linkId)
                this.names = selection.map(item => item.orgName)
                this.single = selection.length !== 1
                this.multiple = !selection.length
                selection.map(item => {
                    if (item.orgCode.length == this.ooorgCode.length) {
                        this.levelFlag = false
                    } else {
                        this.levelFlag = true
                    }
                })
            },
            handleSelectionChangeLink(selection) {
                this.idsLink = selection.map(item => item.linkId)
            },
            /** 新增按钮操作 */
            handleAdd() {
                this.reset()
                this.open = true
                this.form.orgCode = getInitTreeCode(this.orgTreeList)[0]
                this.form.orgName = getInitTreeName(this.orgTreeList)
                this.form.statYear = this.statYearOptions[0].code
                this.title = '添加农事环节表'
            },
            /** 修改按钮操作 */
            handleUpdate(row) {
                this.reset()
                const linkId = row.linkId || this.ids
                getDimAsLink(linkId).then(response => {
                    this.form = response.data
                    if (this.form.pesticideTypeList != null && this.form.pesticideTypeList != '') {
                        this.form.pesticideTypeList = this.form.pesticideTypeList.split(',')
                    }
                    let params = {
                        "linkCode": this.form.linkCode,
                        "raiseCrops": this.form.raiseCrops,
                        "statYear": this.form.statYear
                    }
                    getGroupLink(params).then(res => {
                        this.getGroupLinkData = res.data
                        this.open = true
                        this.title = '修改农事环节表'
                    })
                    let query = {
                        linkCodes: [this.form.linkCode],
                        raiseCrops: this.form.raiseCrops,
                        orgCode: row.orgCode
                    }
                    queryRightfulVerf(query).then(response => {
                        this.timeSection = response.data
                    })
                })
            },
            /** 记录因子按钮操作 */
            handleFactor(row) {
                this.tableData = row
                this.elementOption = []
                this.elementOptionAll = []
                this.formFactorDynamicState = []
                elementList({ "prodProcessCode": row.prodProcessCode }).then(response => {
                    this.elementOption = response.data
                    this.elementOptionAll = response.data
                })
                let params = {
                    orgCode: row.orgCode,
                    linkId: row.linkId,
                }
                eleCfgInfo(params).then(response => {
                    let datas = response.data
                    let values = []
                    datas.forEach(it => {
                        this.listOptionsLoading = true; // 开始加载
                        values.push(it.elementId)
                        it.maxValue == null ? it.maxValue = undefined : ''
                        it.minValue == null ? it.minValue = undefined : ''
                        if (it.elementFillMethod == '3') {
                            it.ListOptions = []
                            getDicts(it.dictKey).then((response) => {
                                it.ListOptions = response.data
                                this.$nextTick(() => {
                                    this.listOptionsLoading = false;
                                });
                            });
                        }
                        //外部数据
                        if (it.elementFillMethod == '4') {
                            it.ListOptions = []
                        }
                        if (it.elementType == '3') {
                            it.elementDefValue = null
                        }
                    })
                    // formItem.isMustGrp = 0
                    // formItem.isMustBranch = 0
                    // formItem.isMustFarm = 0
                    //如果这条数据是集团，判断  isMustGrp  =1   isChoice = 1 ,如果这条数据是分公司的，判断  isMustGrp，isMustBranch
                    datas.forEach(it => {
                        if (it.orgCode.length == '2') {
                            it.isChoice = it.isMustGrp == 1 ? '1' : '0'
                        }
                        if (it.orgCode.length == '4') {
                            it.isChoice = (it.isMustGrp == 1 || it.isMustBranch == 1) ? '1' : '0'
                        }
                        if (it.orgCode.length == '6') {
                            it.isChoice = (it.isMustGrp == 1 || it.isMustBranch == 1 || it.isMustFarm == 1) ? '1' : '0'
                        }
                    })
                    this.elementOption = this.elementOptionAll.filter(it => {
                        return values.indexOf(it.elementId) == -1
                    })
                    this.formFactor = datas
                    this.formFactorAll = datas
                    this.openFactor = true
                })
            },

            //记录因子提交
            submitFactorForm() {
                let flag = false
                let arr = []
                let params = JSON.parse(JSON.stringify(this.formFactor))
                if (params && params.length > 0) {
                    params.map(item => {
                        if (item.minValue == undefined) {
                            item.minValue = null
                        }
                        if (item.maxValue == undefined) {
                            item.maxValue = null
                        }
                        if (item.elementFillMethod == '0' && item.elementType == '4' && item.minValue != null && item.maxValue != null) {
                            if (parseFloat(item.minValue) > parseFloat(item.maxValue)) {
                                arr.push(item.elementName)
                                flag = true
                            }
                        }
                    })
                }
                if (flag) {
                    this.$message.error(arr.join('，') + ':最小值不能大于最大值')
                    return false
                }
                eleCfgInfoUpdate(params).then(response => {
                    this.$message.success('操作成功')
                    this.openFactor = false
                    this.getList()
                })
            },
            cancelFactor() {
                this.openFactor = false
            },
            childChange(formItem) {
                formItem.isMustGrp = 0
                formItem.isMustBranch = 0
                formItem.isMustFarm = 0
                //如果选中，组织机构等于2，isMustGrp =1 ,以此类推
                if (formItem.isChoice == 1) {
                    if (formItem.orgCode.length == 2) {
                        formItem.isMustGrp = 1
                    } else if (formItem.orgCode.length == 4) {
                        formItem.isMustBranch = 1
                    } else {
                        formItem.isMustFarm = 1
                    }
                }
                this.$forceUpdate()
            },
            /** 提交按钮 */
            submitForm() {
                let params = JSON.parse(JSON.stringify(this.form))
                if (
                    params.orgCode !== null &&
                    params.orgCode &&
                    typeof params.orgCode !== 'string'
                ) {
                    params.orgCode =
                        params.orgCode[params.orgCode.length - 1]
                }
                if (this.form.pesticideTypeList != null && this.form.pesticideTypeList != '') {
                    params.pesticideTypeList = this.form.pesticideTypeList.join(',')
                } else {
                    params.pesticideTypeList = null
                }
                this.$refs['formRef'].validate(valid => {
                    if (valid) {
                        if (params.linkId != null) {
                            updateDimAsLink(params).then(response => {
                                this.$message.success('修改成功')
                                this.open = false
                                this.getList()
                            })
                        } else {
                            params.prodProcessName = this.prodProcessCodeOptions.filter(item => {
                                return item.code == params.prodProcessCode
                            })[0].name
                            addDimAsLink(params).then(response => {
                                this.$message.success('新增成功')
                                this.open = false
                                this.getList()
                            })
                        }
                    }
                })
            },
            changeSwitch(datas) {
                let params = {
                    isMust: datas.isMust ? 1 : 0,
                    linkId: datas.linkId

                }
                dimAsLinkIsMust(params).then(response => {
                    this.$message.success('操作成功')
                    this.getList()
                })
            },
            submitFormLink() {
                if (this.idsLink.length == 0) {
                    this.$message.error('未选择所需同步环节信息！')
                    return false
                }
                let orgCode = getInitTreeCode(this.orgTreeList)
                let orgName = getInitTreeName(this.orgTreeList)
                if (
                    orgCode !== null &&
                    orgCode &&
                    typeof orgCode !== 'string'
                ) {
                    orgCode =
                        orgCode[orgCode.length - 1]
                }
                if (
                    orgName !== null &&
                    orgName &&
                    typeof orgName !== 'string'
                ) {
                    orgName =
                        orgName[orgName.length - 1]
                }
                let params = {
                    linkIds: this.idsLink,
                    // orgCode: '8605',
                    // orgName: '黑龙江北大荒农垦集团总公司北安分公司'
                    orgCode: orgCode,
                    orgName: orgName,
                }
                insertSynchronousLink(params).then(response => {
                    this.$message.success('同步成功')
                    this.openLink = false
                    // this.$store.dispatch('app/changeFullscreenLoading', false)
                    this.getList()
                })
            },
            handleFindSynchronousBranchLink() {
                // this.queryParamsLink.orgCode = '8605'
                this.queryParamsLink.statYear = this.statYearOptions[0].code
                this.queryParamsLink.raiseCrops = this.raiseCropsList[0].raiseCropsCd
                this.queryParamsLink.orgCode = this.ooorgCode
                this.flagIndex = '1'
                this.getBranchList()
            },
            getBranchList() {
                if (
                    this.queryParamsLink.orgCode !== null &&
                    this.queryParamsLink.orgCode &&
                    typeof this.queryParamsLink.orgCode !== 'string'
                ) {
                    this.queryParamsLink.orgCode =
                        this.queryParamsLink.orgCode[this.queryParamsLink.orgCode.length - 1]
                }
                findSynchronousBranchLink(this.queryParamsLink).then(response => {
                    this.dimAsLinkListLink = response.data.records
                    this.totalLink = response.data.total
                    this.openLink = true
                })
            },
            handleFindSynchronousFarmLink() {
                this.queryParamsLink.orgCode = this.ooorgCode
                // this.queryParamsLink.orgCode = '860503'
                this.queryParamsLink.statYear = this.statYearOptions[0].code
                this.queryParamsLink.raiseCrops = this.raiseCropsList[0].raiseCropsCd
                this.flagIndex = '2'
                this.getFarmList()
            },
            getFarmList() {
                if (
                    this.queryParamsLink.orgCode !== null &&
                    this.queryParamsLink.orgCode &&
                    typeof this.queryParamsLink.orgCode !== 'string'
                ) {
                    this.queryParamsLink.orgCode =
                        this.queryParamsLink.orgCode[this.queryParamsLink.orgCode.length - 1]
                }
                findSynchronousFarmLink(this.queryParamsLink).then(response => {
                    this.dimAsLinkListLink = response.data.records
                    this.totalLink = response.data.total
                    this.openLink = true
                })
            },
            /** 移除按钮操作 */
            handleDelete(row) {
                const _this = this;
                const linkIds = row.linkId || this.ids
                this.$confirm('是否确认移除所选数据项?', '警告', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                }).then(function () {
                    // return logicDeleteById(linkIds)
                    if (Array.isArray(linkIds)) {
                        delDimAsLinks(linkIds).then(response => {
                            _this.getList()
                            _this.$message.success(response.msg)
                        })
                    } else {
                        logicDeleteById(linkIds).then(response => {
                            _this.getList()
                            _this.$message.success('移除成功')
                        })
                    }
                })
            },
            updateStatusSwi(str) {
                if (str && str == 'isPesticide') {
                    this.form.inputConfig = []
                }
                var params = {
                    //orgCode:this.queryDatas.orgCode,
                    //statYear:this.queryDatas.statYear,
                    //linkCode:this.form.linkCode,
                    //raiseCrops:row.raiseCrops,
                    isEnable: this.form.isEnable === '1' ? '0' : '1'
                }
                if (this.form.usepesticideCfgId == null) {
                    // updateStatus(params).then(response => {

                    // }).catch(err => {
                    //   const isEnable = row.isEnable === '0' ? '1' : '0'
                    //   this.form.isEnable = isEnable
                    // })
                } else {
                    this.$confirm('是否将环节置为无效？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning'
                    }).then(() => {
                        // updateStatus(params).then(response => {

                        // }).catch(err => {
                        //   const isEnable = row.isEnable === '0' ? '1' : '0'
                        //   this.form.isEnable = isEnable
                        // })
                    }).catch(() => {
                        const isEnable = row.isEnable === '0' ? '1' : '0'
                        this.form.isEnable = isEnable
                    });
                }
            },
        }
    }


</script>
<style lang="scss">
    .set-del-model1 {
        padding: 0 0 0 0; /*上 右 下 左*/
    }

    .set-del-model1 .el-message-box__header {
        background-color: #f5f5f5; /* 灰色背景色 */
        padding: 10px 20px 10px 20px; /* 添加内边距 */
    }

    .set-del-model1 .el-message-box__title {
        font-size: 16px;
        font-weight: 700; /* 标题加粗 */
    }

    .set-del-model1 .el-message-box__content {
        padding: 0 20px 0 20px; /* 内容内边距 */
        line-height: 1.8; /* 行高，优化阅读体验 */
    }

    .set-del-model1 .el-message-box__headerbtn {
        padding: 5px 10px 0 0;
    }

    .set-del-model1 .el-message-box__btns {
        padding: 0 20px 10px 0;
    }
</style>