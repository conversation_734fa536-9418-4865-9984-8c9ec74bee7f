<template>
    <span :style="iconStyle">
        <!-- 通过 style 属性调整图片大小 -->
        <img :src="isLocked === true || isLocked === '1' ? lockIcon : unlockIcon"
             :alt="isLocked === true || isLocked === '1' ? 'Locked' : 'Unlocked'"
             :style="{ width: iconSize, height: iconSize }"/>
    </span>
</template>

<script>
import lockIcon from '@/views/sysagriculturalsituation/assets/icons/svg/lock.svg';
import unlockIcon from '@/views/sysagriculturalsituation/assets/icons/svg/unlock.svg';

export default {
    name: 'LockStatusIcon',
    props: {
        // 锁定状态，'1' 表示已锁定，'0' 表示未锁定
        isLocked: {
            type: [String, Boolean],
            default: false
        },
        iconSize: {
            type: String,
            default: '17px'
        },
        showPointerCursor: {
            type: Boolean,
            default: false
        },
        backgroundColor: {
            type: Boolean,
            default: false
        },
        marginLeft: {
            type: String,
            default: '10px'
        }

    },
    computed: {
        iconStyle() {
            const baseStyle = {
                color: this.isLocked === true || this.isLocked === '1' ? '#e64242' : 'green',
                verticalAlign: 'middle',
                padding: '1px 3px',
                boxSizing: 'border-box',
                lineHeight: '16px',
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                minWidth:'20px',
                textAlign: 'center',
                borderRadius: '4px',
            };
            if (this.showPointerCursor) {
                baseStyle.cursor = 'pointer';
            }
            if (this.backgroundColor) {
                baseStyle.background = '#e642421a';
            }
            if (this.marginLeft) {
                baseStyle.marginLeft = this.marginLeft;
            }
            return baseStyle;
        }
    },
    data() {
        return {
            lockIcon,
            unlockIcon
        };
    }
};
</script>