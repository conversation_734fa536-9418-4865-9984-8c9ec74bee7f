<template>
    <div class="fill app-container" style="position: relative;" v-loading.fullscreen.lock="fullloading">
        <el-collapse-transition>
            <el-form :model="queryParams" ref="queryForm" class="queryClass form-line" :inline="true"
                :rules="queryFormRules" label-width="120px">
                <el-row :gutter="20">
                    <el-col :span="6">
                        <el-form-item label="请选择所属单位" prop="orgCode">
                            <selectable-cascader v-model="queryParams.orgCode" :options="orgTreeList">
                            </selectable-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="统计年份" prop="statYear">
                            <el-date-picker :clearable="false" v-model="queryParams.statYear" type="year"
                                value-format="YYYY" placeholder="选择统计年份" format="YYYY" @blur="forceUpdate">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6">
                        <el-form-item label="数据状态" prop="dataStatus">
                            <el-select v-model="queryParams.dataStatus" placeholder="请选择数据状态"
                                @clear="queryParams.dataStatus = null" clearable>
                                <el-option v-for="item in dataStatusOptions" :key="item.code" :label="item.name"
                                    :value="item.code">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" v-if="fillTypeFlag === '1'">
                        <el-form-item label="填报开始时间" prop="taskBeginTime" v-if="fillTypeFlag === '1'">
                            <el-date-picker clearable v-model="queryParams.taskBeginTime" type="date"
                                value-format="YYYYMMDD" format="YYYY-MM-DD" :disabled-date="pickerOptionsStatQuery"
                                placeholder="请选择填报开始时间">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" v-if="fillTypeFlag === '1'">
                        <el-form-item label="填报截止时间" prop="taskEndTime" v-if="fillTypeFlag === '1'">
                            <el-date-picker clearable v-model="queryParams.taskEndTime" type="date"
                                value-format="YYYYMMDD" format="YYYY-MM-DD" :disabled-date="pickerOptionsEndQuery"
                                placeholder="请选择填报截止时间">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="6" :push="fillTypeFlag === '1' ? 12 : 0" align="right">
                        <el-form-item align="right">
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-collapse-transition>
        <div>
            <div style="margin-bottom: 10px;">
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5" v-if="fillTypeFlag === '2'">
                        <el-button type="primary" icon="Plus" :disabled="btnFlag" @click="addData"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:insert`]">新增</el-button>
                    </el-col>
                    <el-col :span="1.5" v-hasPermi="[`${this.paramsRouter.fileAuthName}:update`]">
                        <el-button icon="Edit" :disabled="single || dataFlag || dateFlag"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:update`]"
                            @click="handleUpdate">修改</el-button>
                    </el-col>
                    <el-col :span="1.5" v-if="fillTypeFlag === '1'">
                        <el-button icon="Top"
                            :disabled="this.showFill || this.queryParams.fileName == '' || this.queryParams.fileName == null"
                            @click="openFillIssue"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:createInfillTask`]">任务填报下发</el-button>
                    </el-col>
                    <!-- <el-col :span="1.5">
                        <el-button
                            icon="Top"
                            :disabled="single || dataStatusFlag"
                            @click="stationDataCommitBtn"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:stationDataCommit`]"
                        >作业站提交</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            icon="Bottom"
                            :disabled="single"
                            @click="stationDataBackBtn"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:stationDataBack`]"
                        >退回作业站</el-button>
                    </el-col> -->
                    <el-col :span="1.5" v-hasPermi="[`${this.paramsRouter.fileAuthName}:precinctDataCommit`]">
                        <el-button icon="Top" :disabled="single || dataStatusFlag || dateFlag"
                            @click="precinctDataCommitBtn"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:precinctDataCommit`]">管理区提交</el-button>
                    </el-col>
                    <el-col :span="1.5" v-hasPermi="[`${this.paramsRouter.fileAuthName}:precinctDataBack`]">
                        <el-button icon="Bottom" :disabled="single || dateFlag" @click="precinctDataBackBtn"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:precinctDataBack`]">退回管理区</el-button>
                    </el-col>
                    <el-col :span="1.5" v-hasPermi="[`${this.paramsRouter.fileAuthName}:farmDataCommit`]">
                        <el-button icon="Top" :disabled="single || dataStatusFlag || dateFlag"
                            @click="farmDataCommitBtn"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:farmDataCommit`]">农场提交</el-button>
                    </el-col>
                    <el-col :span="1.5" v-hasPermi="[`${this.paramsRouter.fileAuthName}:farmDataBack`]">
                        <el-button icon="Bottom" :disabled="single || dateFlag" @click="farmDataBackBtn"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:farmDataBack`]">退回农场</el-button>
                    </el-col>
                    <el-col :span="1.5" v-hasPermi="[`${this.paramsRouter.fileAuthName}:branchDataCommit`]">
                        <el-button icon="Top" :disabled="single || dataStatusFlag || dateFlag"
                            @click="branchDataCommitBtn"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:branchDataCommit`]">分公司提交</el-button>
                    </el-col>
                    <el-col :span="1.5" v-hasPermi="[`${this.paramsRouter.fileAuthName}:branchDataBack`]">
                        <el-button icon="Bottom" :disabled="single || dateFlag" @click="branchDataBackBtn"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:branchDataBack`]">退回分公司</el-button>
                    </el-col>
                    <el-col :span="1.5" v-if="downloadData && (downloadData.hasImage || downloadData.hasText)">
                        <el-button icon="Download" @click="openDialog('1')"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:exportOneLevelExcel`]">一级导出</el-button>
                    </el-col>
                    <el-col :span="1.5" v-else>
                        <exceldownload ref="exceldownload1" exceltext="一级导出" :flag="btnFlag" icon="Download"
                            :param="Object.assign(queryParams, { 'isOpenTransUnit': isOpenTransUnit, 'isShowLastYear': isShowLastYear })"
                            :url="exportUrl1" v-hasPermi="[`${this.paramsRouter.fileAuthName}:exportOneLevelExcel`]">
                        </exceldownload>
                    </el-col>
                    <el-col :span="1.5" v-if="downloadData && (downloadData.hasImage || downloadData.hasText)">
                        <el-button icon="Download" @click="openDialog('2')"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:exportExcel`]">二级导出</el-button>
                    </el-col>
                    <el-col :span="1.5" v-else>
                        <exceldownload ref="exceldownload2" exceltext="二级导出" :flag="btnFlag" icon="Download"
                            :param="Object.assign(queryParams, { 'isOpenTransUnit': isOpenTransUnit, 'isShowLastYear': isShowLastYear })"
                            :url="exportUrl" v-hasPermi="[`${this.paramsRouter.fileAuthName}:exportExcel`]">
                        </exceldownload>
                    </el-col>
                    <el-col :span="1.5" v-if="downloadData && (downloadData.hasImage || downloadData.hasText)">
                        <el-button icon="Download" @click="openDialog('3')"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:exportThreeLevelExcel`]">三级导出</el-button>
                    </el-col>
                    <el-col :span="1.5" v-else>
                        <exceldownload ref="exceldownload3" exceltext="三级导出" :flag="btnFlag" icon="Download"
                            :param="Object.assign(queryParams, { 'isOpenTransUnit': isOpenTransUnit, 'isShowLastYear': isShowLastYear })"
                            :url="exportUrl3" v-hasPermi="[`${this.paramsRouter.fileAuthName}:exportThreeLevelExcel`]">
                        </exceldownload>
                    </el-col>
                    <el-col :span="1.5" v-hasPermi="[`${this.paramsRouter.fileAuthName}:synchronizeData`]">
                        <el-button v-hasPermi="[`${this.paramsRouter.fileAuthName}:synchronizeData`]" icon="Upload"
                            :disabled="single || dataFlag || dateFlag" @click="handelSynchronizeData">同步数据</el-button>
                    </el-col>
                    <el-col :span="1.5" v-hasPermi="[`${this.paramsRouter.fileAuthName}:restoreAutoTotal`]">
                        <el-button icon="RefreshRight" @click="recoverBtn" :disabled="single || dateFlag || levelSelf"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:restoreAutoTotal`]">恢复自动汇总</el-button>
                    </el-col>
                    <el-col :span="1.5" v-hasPermi="[`${this.paramsRouter.fileAuthName}:preview`]">
                        <el-button icon="Download" :disabled="single" @click="previewBtn"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:preview`]">预览导出</el-button>
                    </el-col>
                    <el-col :span="1.5" v-hasPermi="[`${this.paramsRouter.fileAuthName}:queryAllFile`]">
                        <el-button icon="Download" @click="previewMutipleBtn"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:queryAllFile`]">多表导出</el-button>
                    </el-col>
                    <el-col :span="1.5" v-hasPermi="[`${this.paramsRouter.fileAuthName}:transUnit`]">
                        <el-button icon="Open" @click="handleToggleUnit"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:transUnit`]">单位转换
                        </el-button>
                    </el-col>
                    <el-col :span="1.5" v-hasPermi="[`${this.paramsRouter.fileAuthName}:delete`]">
                        <el-button icon="Delete" :disabled="single" @click="handleDelete"
                            v-hasPermi="[`${this.paramsRouter.fileAuthName}:delete`]">删除</el-button>
                    </el-col>
                    <el-col :span="1.5">
                        <el-button icon="Timer" @click="handleLastYear">与去年同期对比</el-button>
                        <!-- v-hasPermi="[`${this.paramsRouter.fileAuthName}:isShowLastYear`]" -->
                    </el-col>
                    <el-col :span="1.5">
                        <el-button v-if="this.flagShow && this.flagStatus1 != true" icon="Top"
                            @click="goBack">返回上级</el-button>
                    </el-col>
                </el-row>
            </div>
            <!-- <div style="float:right;margin-top: -40px;position: relative;margin-right: 120px;" v-if="fullOrgCode.length === 2">
                <div style="position:absolute;right: 0px;top:0px;width:335px;line-height: 26px;">
                    <span style="opacity: 0.6;font-size: 14px;" v-if="!single&&reportTask != null&&fullOrgCode.length === 2&&reportTask.isDaily == '1'">日报始末日期:{{reportTask.taskBeginTime}}-{{reportTask.taskEndTime}}</span>
                    <el-button
                        type="danger"
                        @click="handleConfirm"
                        :disabled="(todayDateStr>reportTask.taskEndTime)||!(!single&&reportTask != null&&fullOrgCode.length === 2&&reportTask.isDaily == '1')"
                        v-hasPermi="[`${this.paramsRouter.fileAuthName}:endTask`]"
                        style="position: absolute;right:0;top:0"
                    >提前结束日报</el-button>
                </div>
            </div> -->
            <div style="float:right;margin-top: -30px;"><el-checkbox v-model="showColumn"
                    @change="handleToggleCheckbox">固定列开关</el-checkbox></div>
            <div style="float:right;margin-top: -30px;margin-right: 120px;"
                v-if="fullOrgCode.length === 4 || fullOrgCode.length === 6"><el-checkbox
                    v-model="checkedTotal">汇总数据</el-checkbox></div>
            <!-- <div style="overflow-x: auto; overflow-y: auto; width:100%;padding-bottom: 10px;" :style="{ height:tableConfig.height + 'px'}"> -->
            <el-table class="reportTable" v-if="showTable" :data="columnListData" ref="multipleTable" border
                :height="tableConfig.height" :row-class-name="tableRowClassName" @header-dragend="handleHeaderDragend"
                @selection-change="handleSelectionChange" row-key="dataId" :tree-props="{ children: 'children' }"
                style="width: 100%">
                <el-table-column type="selection" width="55" align="center"
                    :fixed="showColumn ? 'left' : false"></el-table-column>
                <el-table-column label="统计年份" align="center" width="100" :fixed="showColumn ? 'left' : false"
                    prop="statYear"></el-table-column>
                <el-table-column label="填报开始时间" align="center" width="80" :fixed="showColumn ? 'left' : false"
                    prop="taskBeginTime"></el-table-column>
                <el-table-column label="填报结束时间" align="center" width="80" :fixed="showColumn ? 'left' : false"
                    prop="taskEndTime"></el-table-column>
                <el-table-column label="单位" align="center" v-if="!(tableLength == 3 && tableLastLength == 2)" width="80"
                    fixed="left" prop="orgName"></el-table-column>
                <el-table-column v-for="(itemFirst, indexFirst) in tableData" :label="itemFirst.headerElementName"
                    :key="itemFirst.headerElementId" align="center">
                    <el-table-column v-for="(itemSecond, indexSecond) in itemFirst.children"
                        :label="indexSecond == 0 || defUnitArr.length == 0 ? itemSecond.headerElementName : (isOpenTransUnit == '1' ? '单位:' + transUnitArr.join(',') : '单位:' + defUnitArr.join(','))"
                        :align="indexSecond == 1 ? 'right' : 'left'" :key="itemSecond.headerElementId">
                        <el-table-column v-for="(itemThird, indexThird) in itemSecond.children"
                            :min-width="!itemThird.children && itemThird.columnType == '4' ? '150px' : itemThird.headerElementName.length > 10 ? '220px' : itemThird.headerElementName.length > 6 ? '100px' : ''"
                            :prop="itemThird.columnList ? 'column' + itemThird.columnList : ''"
                            :label="itemThird.headerElementName + (itemThird.isUnitShow != '1' || !!itemThird.children || isOpenTransUnit == '1' && !itemThird.transUnit || isOpenTransUnit == '0' && !itemThird.defUnit || itemThird.isTrans == '0' || itemThird.isTrans == null ? '' : isOpenTransUnit == '1' ? '(' + itemThird.transUnit + ')' : '(' + itemThird.defUnit + ')')"
                            :key="itemThird.headerElementId" align="center">
                            <el-table-column v-for="(itemForth, indexForth) in itemThird.children"
                                :min-width="!itemForth.children && itemForth.columnType == '4' ? '150px' : itemForth.headerElementName.length > 10 ? '220px' : itemForth.headerElementName.length > 6 ? '100px' : ''"
                                :prop="itemForth.columnList ? 'column' + itemForth.columnList : ''"
                                :label="itemForth.headerElementName + (itemForth.isUnitShow != '1' || !!itemForth.children || isOpenTransUnit == '1' && !itemForth.transUnit || isOpenTransUnit == '0' && !itemForth.defUnit || itemForth.isTrans == '0' || itemForth.isTrans == null ? '' : isOpenTransUnit == '1' ? '(' + itemForth.transUnit + ')' : '(' + itemForth.defUnit + ')')"
                                :key="itemForth.headerElementId" align="center">
                                <el-table-column v-for="(itemFifth, indexFifth) in itemForth.children"
                                    :min-width="!itemFifth.children && itemFifth.columnType == '4' ? '150px' : itemFifth.headerElementName.length > 10 ? '220px' : itemFifth.headerElementName.length > 6 ? '100px' : ''"
                                    :prop="itemFifth.columnList ? 'column' + itemFifth.columnList : ''"
                                    :label="itemFifth.headerElementName + (itemFifth.isUnitShow != '1' || !!itemFifth.children || isOpenTransUnit == '1' && !itemFifth.transUnit || isOpenTransUnit == '0' && !itemFifth.defUnit || itemFifth.isTrans == '0' || itemFifth.isTrans == null ? '' : isOpenTransUnit == '1' ? '(' + itemFifth.transUnit + ')' : '(' + itemFifth.defUnit + ')')"
                                    :key="itemFifth.headerElementId" align="center">
                                    <el-table-column v-for="(itemSixth, indexSixth) in itemFifth.children"
                                        :min-width="!itemSixth.children && itemSixth.columnType == '4' ? '150px' : itemSixth.headerElementName.length > 10 ? '220px' : itemSixth.headerElementName.length > 6 ? '100px' : ''"
                                        :prop="itemSixth.columnList ? 'column' + itemSixth.columnList : ''"
                                        :label="itemSixth.headerElementName + (itemSixth.isUnitShow != '1' || !!itemSixth.children || isOpenTransUnit == '1' && !itemSixth.transUnit || isOpenTransUnit == '0' && !itemSixth.defUnit || itemSixth.isTrans == '0' || itemSixth.isTrans == null ? '' : isOpenTransUnit == '1' ? '(' + itemSixth.transUnit + ')' : '(' + itemSixth.defUnit + ')')"
                                        :key="itemSixth.headerElementId" align="center">
                                        <el-table-column v-for="(itemSeven, indexSixth) in itemSixth.children"
                                            :min-width="!itemSeven.children && itemSeven.columnType == '4' ? '150px' : itemSeven.headerElementName.length > 10 ? '220px' : itemSeven.headerElementName.length > 6 ? '100px' : ''"
                                            :prop="itemSeven.columnList ? 'column' + itemSeven.columnList : ''"
                                            :label="itemSeven.headerElementName + (itemSeven.isUnitShow != '1' || !!itemSeven.children || isOpenTransUnit == '1' && !itemSeven.transUnit || isOpenTransUnit == '0' && !itemSeven.defUnit || itemSeven.isTrans == '0' || itemSeven.isTrans == null ? '' : isOpenTransUnit == '1' ? '(' + itemSeven.transUnit + ')' : '(' + itemSeven.defUnit + ')')"
                                            :key="itemSeven.headerElementId" align="center">
                                            <template #default="{ row }" v-if="itemSeven.children == null">
                                                <el-tooltip effect="dark"
                                                    v-if="row['column' + itemSeven.columnList] && row['column' + itemSeven.columnList].length > 30 && itemSeven.columnType != '4'"
                                                    :content="row['column' + itemSeven.columnList]" placement="top">
                                                    <span>{{ row['column' + itemSeven.columnList].substring(0, 30) }}...</span>
                                                </el-tooltip>
                                                <div v-else>
                                                    <span
                                                        v-if="itemSeven.columnList == 'A'">{{ row['column' + itemSeven.columnList] }}</span>
                                                    <span v-if="itemSeven.columnList != 'A' && itemSeven.columnType != '4'"
                                                        :style="{ color: fullOrgCode.length == row.orgCode.length && row.childrenRptData && itemSeven.columnList != 'A' ? 'red' : '' }">{{ itemSeven.columnType
                                                            === '0' || itemSeven.columnType === '1' ?
                                                            formatNumber(scope.row['column' + itemSeven.columnList], (isOpenTransUnit == '1' && itemSeven.isTrans == '1' ?
                                                                itemSeven.transPrecision : itemSeven.columnPrecision), itemSeven.isTrans, itemSeven.transAlgor) : (isOpenTransUnit == '1' && itemSeven.isTrans == '1'
                                                                    ?
                                                                    Number(row['column' + itemSeven.columnList]) / (itemSeven.transAlgor.split('/')[1]):row['column'+itemSeven.columnList])}}</span>
                                                    <span
                                                        v-if="itemSeven.columnType == '4' && row['column' + itemSeven.columnList] != null && row['column' + itemSeven.columnList] != ''">
                                                        <el-image
                                                            v-for="(urlItem, urlIndex) in row['column' + itemSeven.columnList].split(',')"
                                                            :key="urlIndex"
                                                            style="width: 30px; height: 30px;margin-right:10px"
                                                            :src="urlItem" :preview-teleported="true"
                                                            :preview-src-list="row['column' + itemSeven.columnList].split(',')">
                                                        </el-image>
                                                    </span>
                                                    <span
                                                        v-if="fullOrgCode.length == row.orgCode.length && row.childrenRptData && row.childrenRptData['column' + itemSeven.columnList] && itemSeven.columnList != 'A' && checkedTotal && row['column' + itemSeven.columnList]">/</span>
                                                    <span
                                                        v-if="fullOrgCode.length == row.orgCode.length && row.childrenRptData && row.childrenRptData['column' + itemSeven.columnList] && itemSeven.columnList != 'A' && checkedTotal">{{ itemSeven.columnType
                                                            === '0' || itemSeven.columnType === '1' ?
                                                            formatNumber(row.childrenRptData['column' + itemSeven.columnList], (isOpenTransUnit == '1' && itemSeven.isTrans == '1' ?
                                                                itemSeven.transPrecision : itemSeven.columnPrecision), itemSeven.isTrans, itemSeven.transAlgor) :
                                                            (isOpenTransUnit == '1' && itemSeven.isTrans == '1' ? Number(row.childrenRptData['column' + itemSeven.columnList]) / (itemSeven.transAlgor.split('/')[1]) :row.childrenRptData['column'+itemSeven.columnList])}}
                                                    </span>
                                                </div>
                                            </template>
                                        </el-table-column>
                                        <template #default="{ row }" v-if="itemSixth.children == null">
                                            <el-tooltip effect="dark"
                                                v-if="row['column' + itemSixth.columnList] && row['column' + itemSixth.columnList].length > 30 && itemSixth.columnType != '4'"
                                                :content="row['column' + itemSixth.columnList]" placement="top">
                                                <span>{{ row['column' + itemSixth.columnList].substring(0, 30) }}...</span>
                                            </el-tooltip>
                                            <div v-else>
                                                <span
                                                    v-if="itemSixth.columnList == 'A'">{{ row['column' + itemSixth.columnList] }}</span>
                                                <span v-if="itemSixth.columnList != 'A' && itemSixth.columnType != '4'"
                                                    :style="{ color: fullOrgCode.length == row.orgCode.length && row.childrenRptData && itemSixth.columnList != 'A' ? 'red' : '' }">{{ itemSixth.columnType
                                                        === '0' || itemSixth.columnType === '1' ?
                                                        formatNumber(row['column' + itemSixth.columnList], (isOpenTransUnit == '1' && itemSixth.isTrans == '1' ?
                                                            itemSixth.transPrecision : itemSixth.columnPrecision), itemSixth.isTrans, itemSixth.transAlgor) : (isOpenTransUnit == '1' && itemSixth.isTrans == '1'
                                                                ?
                                                                Number(row['column' + itemSixth.columnList]) / (itemSixth.transAlgor.split('/')[1]):row['column'+itemSixth.columnList])}}</span>
                                                <span
                                                    v-if="itemSixth.columnType == '4' && row['column' + itemSixth.columnList] != null && row['column' + itemSixth.columnList] != ''">
                                                    <el-image
                                                        v-for="(urlItem, urlIndex) in row['column' + itemSixth.columnList].split(',')"
                                                        :key="urlIndex"
                                                        style="width: 30px; height: 30px;margin-right:10px"
                                                        :src="urlItem" :preview-teleported="true"
                                                        :preview-src-list="row['column' + itemSixth.columnList].split(',')">
                                                    </el-image>
                                                </span>
                                                <span
                                                    v-if="fullOrgCode.length == row.orgCode.length && row.childrenRptData && row.childrenRptData['column' + itemSixth.columnList] && itemSixth.columnList != 'columnA' && checkedTotal && row['column' + itemSixth.columnList]">/</span>
                                                <span
                                                    v-if="fullOrgCode.length == row.orgCode.length && row.childrenRptData && row.childrenRptData['column' + itemSixth.columnList] && itemSixth.columnList != 'columnA' && checkedTotal">{{ itemSixth.columnType
                                                        === '0' || itemSixth.columnType === '1' ?
                                                        formatNumber(row['column' + itemSixth.columnList], (isOpenTransUnit == '1' && itemSixth.isTrans == '1' ?
                                                            itemSixth.transPrecision : itemSixth.columnPrecision), itemSixth.isTrans, itemSixth.transAlgor) :
                                                        (isOpenTransUnit == '1' && itemSixth.isTrans == '1' ? Number(row.childrenRptData['column' + itemSixth.columnList]) / (itemSixth.transAlgor.split('/')[1]) : row.childrenRptData['column'+itemSixth.columnList])}}
                                                </span>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <template #default="{ row }" v-if="itemFifth.children == null">
                                        <el-tooltip effect="dark"
                                            v-if="row['column' + itemFifth.columnList] && row['column' + itemFifth.columnList].length > 30 && itemFifth.columnType != '4'"
                                            :content="row['column' + itemFifth.columnList]" placement="top">
                                            <span>{{ row['column' + itemFifth.columnList].substring(0, 30) }}...</span>
                                        </el-tooltip>
                                        <div v-else>
                                            <span
                                                v-if="itemFifth.columnList == 'A'">{{ row['column' + itemFifth.columnList] }}</span>
                                            <span v-if="itemFifth.columnList != 'A' && itemFifth.columnType != '4'"
                                                :style="{ color: fullOrgCode.length == row.orgCode.length && row.childrenRptData && itemFifth.columnList != 'A' ? 'red' : '' }">{{ itemFifth.columnType
                                                    === '0' || itemFifth.columnType === '1' ?
                                                    formatNumber(row['column' + itemFifth.columnList], (isOpenTransUnit == '1' && itemFifth.isTrans == '1' ?
                                                        itemFifth.transPrecision : itemFifth.columnPrecision), itemFifth.isTrans, itemFifth.transAlgor) : (isOpenTransUnit == '1' && itemFifth.isTrans == '1' ? Number(row['column' + itemFifth.columnList]) / (itemFifth.transAlgor.split('/')[1]):row['column'+itemFifth.columnList])}}</span>
                                            <span
                                                v-if="itemFifth.columnType == '4' && row['column' + itemFifth.columnList] != null && row['column' + itemFifth.columnList] != ''">
                                                <el-image
                                                    v-for="(urlItem, urlIndex) in row['column' + itemFifth.columnList].split(',')"
                                                    :key="urlIndex" style="width: 30px; height: 30px;margin-right:10px"
                                                    :src="urlItem" :preview-teleported="true"
                                                    :preview-src-list="row['column' + itemFifth.columnList].split(',')">
                                                </el-image>
                                            </span>
                                            <span
                                                v-if="fullOrgCode.length == row.orgCode.length && row.childrenRptData && row.childrenRptData['column' + itemFifth.columnList] && itemFifth.columnList != 'A' && checkedTotal && row['column' + itemFifth.columnList]">/</span>
                                            <span
                                                v-if="fullOrgCode.length == row.orgCode.length && row.childrenRptData && row.childrenRptData['column' + itemFifth.columnList] && itemFifth.columnList != 'A' && checkedTotal">{{ itemFifth.columnType
                                                    === '0' || itemFifth.columnType === '1' ?
                                                    formatNumber(row.childrenRptData['column' + itemFifth.columnList], (isOpenTransUnit == '1' && itemFifth.isTrans == '1' ?
                                                        itemFifth.transPrecision : itemFifth.columnPrecision), itemFifth.isTrans, itemFifth.transAlgor) :
                                                    (isOpenTransUnit == '1' && itemFifth.isTrans == '1' ? Number(row.childrenRptData['column' + itemFifth.columnList]) / (itemFifth.transAlgor.split('/')[1]) : row.childrenRptData['column'+itemFifth.columnList])}}
                                            </span>
                                        </div>
                                    </template>
                                </el-table-column>
                                <template #default="{ row }" v-if="itemForth.children == null">
                                    <el-tooltip effect="dark"
                                        v-if="row['column' + itemForth.columnList] && row['column' + itemForth.columnList].length > 30 && row['column' + itemForth.columnList] != '4'"
                                        :content="row['column' + itemForth.columnList]" placement="top">
                                        <span>{{ row['column' + itemForth.columnList].substring(0, 30) }}...</span>
                                    </el-tooltip>
                                    <div v-else>
                                        <span
                                            v-if="itemForth.columnList == 'A'">{{ row['column' + itemForth.columnList] }}</span>
                                        <span v-if="itemForth.columnList != 'A' && itemForth.columnType != '4'"
                                            :style="{ color: fullOrgCode.length == row.orgCode.length && row.childrenRptData && itemForth.columnList != 'A' ? 'red' : '' }">{{ itemForth.columnType
                                                === '0' || itemForth.columnType === '1' ?
                                                formatNumber(row['column' + itemForth.columnList], (isOpenTransUnit == '1' && itemForth.isTrans == '1' ?
                                                    itemForth.transPrecision : itemForth.columnPrecision), itemForth.isTrans, itemForth.transAlgor) : (isOpenTransUnit == '1' && itemForth.isTrans == '1' ? Number(row['column' + itemForth.columnList]) / (itemForth.transAlgor.split('/')[1]):row['column'+itemForth.columnList])}}</span>
                                        <span
                                            v-if="itemForth.columnType == '4' && row['column' + itemForth.columnList] != null && row['column' + itemForth.columnList] != ''">
                                            <el-image
                                                v-for="(urlItem, urlIndex) in row['column' + itemForth.columnList].split(',')"
                                                :key="urlIndex" style="width: 30px; height: 30px;margin-right:10px"
                                                :src="urlItem" :preview-teleported="true"
                                                :preview-src-list="row['column' + itemForth.columnList].split(',')">
                                            </el-image>
                                        </span>
                                        <span
                                            v-if="fullOrgCode.length == row.orgCode.length && row.childrenRptData && row.childrenRptData['column' + itemForth.columnList] && itemForth.columnList != 'A' && checkedTotal && row['column' + itemForth.columnList]">/</span>
                                        <span
                                            v-if="fullOrgCode.length == row.orgCode.length && row.childrenRptData && row.childrenRptData['column' + itemForth.columnList] && itemForth.columnList != 'A' && checkedTotal">{{ itemForth.columnType
                                                === '0' || itemForth.columnType === '1' ?
                                                formatNumber(row.childrenRptData['column' + itemForth.columnList], (isOpenTransUnit == '1' && itemForth.isTrans == '1' ?
                                                    itemForth.transPrecision : itemForth.columnPrecision), itemForth.isTrans, itemForth.transAlgor) :
                                                (isOpenTransUnit == '1' && itemForth.isTrans == '1' ? Number(row.childrenRptData['column' + itemForth.columnList]) / (itemForth.transAlgor.split('/')[1]) : row.childrenRptData['column'+itemForth.columnList])}}
                                        </span>
                                    </div>
                                </template>
                            </el-table-column>
                            <template #default="{ row }" v-if="itemThird.children == null">
                                <el-tooltip effect="dark"
                                    v-if="row['column' + itemThird.columnList] && row['column' + itemThird.columnList].length > 30 && itemThird.columnType != '4'"
                                    :content="row['column' + itemThird.columnList]" placement="top">
                                    <span>{{ row['column' + itemThird.columnList].substring(0, 30) }}...</span>
                                </el-tooltip>
                                <div v-else>
                                    <span v-if="itemThird.columnList == 'A'">{{ row['column' + itemThird.columnList] }}</span>
                                    <span v-if="itemThird.columnList != 'A' && itemThird.columnType != '4'"
                                        :style="{ color: fullOrgCode.length == row.orgCode.length && row.childrenRptData && itemThird.columnList != 'A' ? 'red' : '' }">{{ itemThird.columnType
                                            === '0' || itemThird.columnType === '1' ?
                                            formatNumber(row['column' + itemThird.columnList], (isOpenTransUnit == '1' && itemThird.isTrans == '1' ?
                                                itemThird.transPrecision : itemThird.columnPrecision), itemThird.isTrans, itemThird.transAlgor) : (isOpenTransUnit == '1' && itemThird.isTrans == '1' ? Number(row['column' + itemThird.columnList]) / (itemThird.transAlgor.split('/')[1]) :row['column'+itemThird.columnList])}}</span>
                                    <div
                                        v-if="itemThird.columnType == '4' && row['column' + itemThird.columnList] != null && row['column' + itemThird.columnList] != ''">
                                        <el-image
                                            v-for="(urlItem, urlIndex) in row['column' + itemThird.columnList].split(',')"
                                            :key="urlIndex" style="width: 30px; height: 30px;margin-right:10px"
                                            :src="urlItem" :preview-teleported="true"
                                            :preview-src-list="row['column' + itemThird.columnList].split(',')">
                                        </el-image>
                                    </div>
                                    <span
                                        v-if="fullOrgCode.length == row.orgCode.length && row.childrenRptData && row.childrenRptData['column' + itemThird.columnList] && itemThird.columnList != 'A' && checkedTotal && row['column' + itemThird.columnList]">/</span>
                                    <span
                                        v-if="fullOrgCode.length == row.orgCode.length && row.childrenRptData && row.childrenRptData['column' + itemThird.columnList] && itemThird.columnList != 'A' && checkedTotal">{{ itemThird.columnType
                                            === '0' || itemThird.columnType === '1' ?
                                            formatNumber(row.childrenRptData['column' + itemThird.columnList], (isOpenTransUnit == '1' && itemThird.isTrans == '1' ?
                                                itemThird.transPrecision : itemThird.columnPrecision), itemThird.isTrans, itemThird.transAlgor) :
                                            (isOpenTransUnit == '1' && itemThird.isTrans == '1' ? Number(row.childrenRptData['column' + itemThird.columnList]) / (itemThird.transAlgor.split('/')[1]) : row.childrenRptData['column'+itemThird.columnList])}}
                                    </span>
                                </div>
                            </template>
                        </el-table-column>
                        <!-- <template #default="{ row }" v-if="itemSecond.children ==null">
                            {{row['column'+itemSecond.columnList]}}
                        </template> -->
                    </el-table-column>
                    <!-- <template #default="{ row }" v-if="itemFirst.children ==null">
                        {{row['column'+itemFirst.columnList]}}
                    </template> -->
                </el-table-column>
                <el-table-column label="数据来源" align="center" width="80" :fixed="showColumn ? 'right' : false">
                    <template #default="scope">
                        <div v-if="scope.row.orgCode != '0'">{{ !!scope.row.childrenRptData ? '手动修改' :
                            scope.row.isAutoTotal === '0' ? '手动修改' : '自动汇总' }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="数据状态" align="center" width="100" fixed="right">
                    <template #default="scope">
                        <div v-if="scope.row.orgCode != '0'">
                            <div>
                                <div v-if="scope.row.orgCode.length > 2 && !beiAnFlag">{{ dataStatusFormat(scope.row) }}</div>
                                <span v-if="scope.row.orgCode.length < 10 && scope.row.childCount != '0'"
                                    :style="{ color: scope.row.childCommitCount == scope.row.childCount ? '#70B604' : 'red' }">({{ scope.row.childCommitCount }}/{{ scope.row.childCount }})</span>
                            </div>
                            <div v-if="isDaily && isDaily == '1'" style="white-space: nowrap;"
                                :style="{ color: handleDate(todayDateStr) >= handleDate(scope.row.taskEndTime) ? '#70B604' : 'red' }">
                                {{ handleDate(todayDateStr) >= handleDate(scope.row.taskEndTime) ? '现可以填报' : '暂不可填报' }}
                            </div>
                            <div v-if="isDaily && isDaily == '0'" style="white-space: nowrap;"
                                :style="{ color: (todayDateStr == scope.row.taskEndTime || handleDate(todayDateStr) + 1 >= handleDate(scope.row.taskEndTime)) ? '#70B604' : 'red' }">
                                {{ (todayDateStr == scope.row.taskEndTime || handleDate(todayDateStr) + 1 >=
                                    handleDate(scope.row.taskEndTime)) ? '现可以填报' : '暂不可填报' }}
                            </div>
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width="60" align="center" fixed="right"
                    v-if="!(flagStatus4 == true || (flagStatus1 == true && ooorgCode.length == 10) || (flagStatus3 && ooorgCode.length == 6) || (flagStatus2 && ooorgCode.length == 10) || (flagStatus2 && ooorgCode.length == 6) || (flagStatus3 && ooorgCode.length == 4))">
                    <template #default="scope">
                        <div style="padding:0 5px" v-if="scope.row.orgCode != '0'">
                            <el-button
                                v-if="!((flagStatus2 && scope.row.orgCode.length == 2) || (flagStatus2 && ooorgCode.length == 4 && scope.row.orgCode.length == 4) || (flagStatus3 && scope.row.orgCode.length == 4) || scope.row.orgCode.length == 12 || scope.row.childCount == 0 || scope.row.childCount == null)"
                                link icon="ZoomIn" type="primary" @click="handleNext(scope.row)"></el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <el-tooltip class="item" effect="dark" v-if="!!fileRemark && showTable" :content="fileRemark" placement="top"
            popper-class="large-text">
            <div
                style="position: absolute;width:calc(100% - 500px);min-width:300px;height:66px;margin-top: 10px;z-index:1000;padding:10px 10px;word-break: break-all;font-size: 14px;
                color: #606266;overflow: hidden;text-overflow: ellipsis;-webkit-line-clamp: 3;display: -webkit-box;-webkit-box-orient: vertical;">
                {{ fileRemark }}
            </div>
        </el-tooltip>

        <pagination v-show="total > 0 && flagStatus1" :total="total" v-model:page="queryParams.page"
            v-model:limit="queryParams.rows" @pagination="switchList" />
        <el-dialog :title="title" v-model="open" width="1000px" append-to-body :before-close="cancel"
            :close-on-click-modal="false">
            <el-form :model="form" ref="FormRef" :rules="rules" :hide-required-asterisk="false" :inline="true"
                label-width="180px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label-width="250px" label="所属单位" prop="orgCode">
                            <selectable-cascader style="width: 188px;" v-model="form.orgCode" :options="orgTreeList"
                                @label="getLabel" :disabled='disabledOrg'>
                            </selectable-cascader>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label-width="250px" label="统计年份" prop="statYear" v-if="paramsRouter.source == 'year'">
                            <el-date-picker style="width: 188px;" clearable v-model="form.statYear" type="year"
                                value-format="YYYY" placeholder="选择统计年份" format="YYYY" @blur="forceUpdate"
                                :disabled='disabledOrg'>
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item  label="统计时间" prop="statDate" v-else>
                            <el-date-picker style="width: 188px;" clearable v-model="form.statDate" type="date"
                                value-format="YYYY-MM-DD" format="YYYY-MM-DD" @change="changeTime" placeholder="请选择统计时间"
                                :disabled='disabledOrg'>
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                </el-row>
                <div v-for="(item, index) in form.column" :key="index">
                    <el-divider
                        v-if="item.vShowFlag && index != 0 && item.flag && item.title.parColumnName != null && item.title.parColumnName != ''">
                        <span v-if="item.title.parColumnNameThree">{{ item.title.parColumnNameThree }}-</span>
                        <span v-if="item.title.parColumnNameTwo">{{ item.title.parColumnNameTwo }}-</span>
                        <span v-if="item.title.parColumnName">{{ item.title.parColumnName }}</span>
                    </el-divider>
                    <el-row class="mapDiv">
                        <template v-for="(t, i) in item.list" :key="i">
                            <el-col :span="12" class="tooltip" v-if="t.columnList !== 'A' && t.vShowFlag">
                                <el-form-item label-width="250px" :label="t.columnName" :prop="'column.' + index + '.list.' + i + '.columnValue'"
                                    :error="JSON.stringify(errorMessage) == '{}' ? '' : errorMessage['column' + t.columnList]"
                                    :rules="t.columnType === '1' ? rules.number : t.columnType === '2' && t.isRequired == '1' ? rules.text : null"
                                    v-if="(t.columnType != '0' && t.columnList != 'A') || (t.columnType == '0' && t.columnList != null && t.columnList != '')">
                                    <!-- {{t.columnList}}--{{t.columnType}}-{{t.formulaContent}} -->
                                    <!-- 图片 -->
                                    <el-upload :ref="'upload' + index + '-' + i" :auto-upload="false"
                                        v-if="t.columnType === '4'" :action="action" :on-exceed="handleExceed"
                                        list-type="picture-card" :on-preview="handlePreview"
                                        :on-remove="(file, fileList) => handleRemove(file, fileList, index, i)"
                                        :before-upload="handleBeforeUpload"
                                        :on-change="file => handleChangeUpload(file, index, i)" accept=".jpg,.jpeg,.png"
                                        :file-list="form.column[index].list[i].fileupload" :limit="3"
                                        class="small-upload"
                                        :class="form.column[index].list[i].fileupload && form.column[index].list[i].fileupload.length === 3 ? 'hide_box' : ''">
                                        <el-icon>
                                            <Plus />
                                        </el-icon>
                                    </el-upload>
                                    <el-date-picker v-if="t.columnType === '3'"
                                        :disabled="t.isCrossTableColumn == '1' && isCrossRptSync == '0'"
                                        style="width: 188px;" clearable @change="timeChange" v-model="t.columnValue"
                                        type="date" value-format="YYYY-MM-DD" format="YYYY-MM-DD" @blur="forceUpdate"
                                        @focus="inputFocus" placeholder="请选择时间">
                                    </el-date-picker>
                                    <!-- 下拉 -->
                                    <el-select v-if="t.columnType == '2' && t.isDictionary === '1'"
                                        :disabled="t.isCrossTableColumn == '1' && isCrossRptSync == '0'"
                                        v-model="t.columnOptions" multiple style="width: 188px;"
                                        @change="updateFlag = true; $forceUpdate()" clearable
                                        :placeholder="'请选择_' + t.columnName">
                                        <el-option v-for="item in t.excelDictList.split(',')" :key="item" :label="item"
                                            :value="item" />
                                    </el-select>
                                    <el-input
                                        v-else-if="(selectRowData.isCanModify == '1' && t.isInsert1st === '1') || (t.columnType == '0' && t.columnList != null && t.columnList != '') || (t.isCrossTableColumn == '1' && isCrossRptSync == '0')"
                                        disabled v-model="t.columnValue" @input="forceUpdate"
                                        :placeholder="'请输入' + (t.parColumnName ? t.parColumnName : '') + '_' + t.columnName"
                                        style="width: 188px;" />
                                    <el-input v-else v-model="t.columnValue" @input="forceUpdate"
                                        :maxlength="t.columnLength || columnLength"
                                        :placeholder="'请输入' + (t.parColumnName ? t.parColumnName : '') + '_' + t.columnName"
                                        @change="inputChange(t, index, i)" @focus="inputFocus" style="width: 188px;" />
                                    <el-tooltip class="item"
                                        v-if="t.isCrossTableColumn == '1' && isCrossRptSync == '0' && t.crossTableColumnRemark != null"
                                        effect="dark" :content="t.crossTableColumnRemark" placement="top-start">
                                        <el-icon class="question">
                                            <QuestionFilled />
                                        </el-icon>
                                    </el-tooltip>
                                </el-form-item>
                            </el-col>
                        </template>
                    </el-row>
                </div>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="changeAlardy" v-if="updateFlag == false">确 定</el-button>
                    <el-button type="primary" @click="submitForm" v-if="updateFlag">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 任务填报下发 -->
        <el-dialog :title="titleFill" v-model="openFill" style="margin-top: 15vh !important;" width="500px"
            append-to-body :before-close="cancelFill" :close-on-click-modal="false">
            <el-form ref="formFillRef" :model="formFill" :rules="rulesFill" label-width="150px">
                <el-form-item label="年份" prop="statYear">
                    <el-select v-model="formFill.statYear" style="width:220px" placeholder="请选择年份">
                        <el-option v-for="item in statYearOptions" :key="item.code" :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <el-form-item label="所属单位" prop="orgCode">
                    <selectable-cascader v-model="formFill.orgCode" :options="orgTreeList" style="width:220px" disabled>
                    </selectable-cascader>
                </el-form-item>
                <el-form-item label="任务名称" prop="orgCode">
                    <el-input v-model="paramsRouter.distTaskName" style="width:220px" disabled></el-input>
                </el-form-item>
                <el-form-item label="日报" prop="isDaily">
                    <el-switch v-model="formFill.isDaily" active-color="#409EFF" inactive-color="#DCDFE6" :width="35"
                        active-value="1" inactive-value="0" style="margin-right: 5px;" @change="handelSwitch">
                    </el-switch>
                </el-form-item>
                <el-form-item label="填报开始时间" prop="taskBeginTime" v-if="formFill.isDaily == '1'">
                    <el-date-picker clearable v-model="formFill.taskBeginTime" type="date" value-format="YYYY-MM-DD"
                        format="YYYY-MM-DD" :disabled-date="pickerOptionsStat" placeholder="请选择填报开始时间"
                        style="width:220px">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="填报截止时间" prop="taskEndTime" v-if="formFill.isDaily == '1'">
                    <el-date-picker clearable v-model="formFill.taskEndTime" type="date" value-format="YYYY-MM-DD"
                        format="YYYY-MM-DD" :disabled-date="pickerOptionsEnd" placeholder="请选择填报截止时间"
                        style="width:220px">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="填报截止时间" prop="taskEndTime" v-if="formFill.isDaily == '0'">
                    <el-date-picker clearable v-model="formFill.taskEndTime" type="dates" value-format="YYYY-MM-DD"
                        format="YYYY-MM-DD" placeholder="请选择填报截止时间" :disabled-date="pickerOptions"
                        @change="handleMutiple" style="width:220px">
                    </el-date-picker>
                </el-form-item>
                <el-form-item label="填报范围" prop="taskScope">
                    <el-select v-model="formFill.taskScope" style="width:220px" @change="handleTaskScope"
                        placeholder="请选择填报范围">
                        <el-option v-for="item in taskScopeList" :key="item.code" :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <!-- 层级 -->
                <el-form-item label="指定层级" v-if="formFill.taskScope == '2'" prop="scopeLevel">
                    <el-select v-model="formFill.scopeLevel" style="width:220px" @change="$forceUpdate()"
                        placeholder="请选择指定层级">
                        <el-option v-for="item in scopeLevelList" :key="item.code" :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
                <!-- 指定单位 -->
                <el-form-item label="指定单位" v-if="formFill.taskScope == '3'" prop="scopeOrgCodeList">
                    <el-cascader v-model="formFill.scopeOrgCodeList" :options="orgTreeList" @change="$forceUpdate()"
                        :props="{
                            label: 'orgName',
                            value: 'orgCode',
                            expandTrigger: 'hover',
                            multiple: true,
                            checkStrictly: true
                        }" placeholder="请选择指定单位" style="width:220px" collapse-tags></el-cascader>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitFormFill">确 定</el-button>
                    <el-button @click="cancelFill">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 预览导出 -->
        <el-dialog title="预览报表" v-model="openPreview" width="1000px" append-to-body :before-close="cancelPreview"
            :close-on-click-modal="false">
            <el-form :model="previewForm" ref="previewFormRef" :inline="true" label-width="100px">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="所属单位" prop="orgCode">
                            <el-tooltip class="item" effect="dark" :content="previewForm.orgName" placement="top-start">
                                <selectable-cascader v-model="previewForm.orgCode" :options="orgTreeList" disabled>
                                </selectable-cascader>
                            </el-tooltip>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="实际填报时间" prop="taskEndTime">
                            <el-date-picker v-model="previewForm.taskEndTime" type="date" disabled
                                value-format="YYYYMMDD" format="YYYY-MM-DD">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item>
                            <div style="display: flex;justify-self: end;">
                                <exceldownload ref="exceldownload4" exceltext="导出" :flag="btnFlagPreview"
                                    icon="Download"
                                    :param="Object.assign(paramPreview, { 'isOpenTransUnit': isOpenTransUnit })"
                                    :url="exportPreviewUrl"
                                    v-hasPermi="[`${this.paramsRouter.fileAuthName}:previewExport`]">
                                </exceldownload>
                                <exceldownload ref="exceldownload5" exceltext="模板导出" :flag="btnFlagPreview"
                                    icon="Download"
                                    :param="Object.assign(paramTemplate, { 'isOpenTransUnit': isOpenTransUnit })"
                                    :url="exportByTemplateUrl"
                                    v-hasPermi="[`${this.paramsRouter.fileAuthName}:exportByTemplate`]"
                                    style="margin-left: 15px;"></exceldownload>
                            </div>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table border :data="previewTable" :span-method="objectSpanMethod" height="500" style="width: 100%">
                <el-table-column prop="项目0" v-if="lineFlag0" label="项目" align="center"></el-table-column>
                <el-table-column prop="项目1" v-if="lineFlag1" label="项目" align="center"></el-table-column>
                <el-table-column prop="项目2" v-if="lineFlag2" label="项目" align="center"></el-table-column>
                <el-table-column prop="项目3" v-if="lineFlag3" label="项目" align="center"></el-table-column>
                <el-table-column prop="项目4" v-if="lineFlag4" label="项目" align="center"></el-table-column>
                <el-table-column prop="rptColumnInfo.columnId" v-if="showColumnId" label="字段ID"
                    align="center"></el-table-column>
                <el-table-column prop="data" label="数据项" align="center">
                    <template #default="scope">
                        <div
                            v-if="scope.row.data != '' && scope.row.data != null && (scope.row.data.indexOf('http://') > -1 || scope.row.data.indexOf('https://') > -1)">
                            <el-image v-for="(urlItem, urlIndex) in scope.row.data.split(',')" :key="urlIndex"
                                style="width: 30px; height: 30px;margin-right:10px" :src="urlItem"
                                :preview-teleported="true" :preview-src-list="scope.row.data.split(',')">
                            </el-image>
                        </div>
                        <div v-else>{{ scope.row.data }}</div>
                    </template>
                </el-table-column>
            </el-table>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelPreview">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 多表导出 -->
        <el-dialog title="多表导出" v-model="openMutiplePreview" width="1000px" append-to-body
            :before-close="cancelMutiplePreview" :close-on-click-modal="false">
            <el-form :model="queryParams" ref="queryParamsRef" :inline="true" label-width="100px">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="所属单位" prop="orgCode">
                            <el-tooltip class="item" effect="dark" :content="queryParamsOrgName" placement="top-start">
                                <selectable-cascader v-model="queryParams.orgCode" :options="orgTreeList" disabled>
                                </selectable-cascader>
                            </el-tooltip>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="填报截止时间" prop="taskDateStr">
                            <el-date-picker v-model="queryMutipleParams.taskDateStr" type="date" value-format="YYYYMMDD"
                                format="YYYY-MM-DD">
                            </el-date-picker>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item>
                            <exceldownload ref="exceldownload5" exceltext="导出" :flag="btnMutipleFlag" icon="Download"
                                :param="Object.assign(queryMutipleParams, { 'isOpenTransUnit': isOpenTransUnit, 'isShowLastYear': isShowLastYear })"
                                :url="exportMutipleUrl" v-hasPermi="[`${this.paramsRouter.fileAuthName}:batchExport`]">
                            </exceldownload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <el-table border :data="previewMutipleTable" height="500" @selection-change="handleSelectionChangeInner"
                style="width: 100%">
                <el-table-column type="selection" label="序号" width="55" align="center" />
                <el-table-column prop="fileType" label="报表分类" align="center"></el-table-column>
                <el-table-column prop="expFileName" label="报表名称" align="center"></el-table-column>
            </el-table>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelMutiplePreview">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 预览图片 -->
        <el-dialog title="查看图片" v-model="imageDialogVisible" width="450px" append-to-body class="addDialog"
            :close-on-click-modal="false" center>
            <img width="100%" height="600px" :src="dialogImageUrl" alt="">
        </el-dialog>
        <!-- 下载弹窗 -->
        <el-dialog :title="downloadTitle" v-model="downloadVisible" width="450px" append-to-body
            :close-on-click-modal="false">
            <el-checkbox-group v-model="checkList">
                <el-checkbox label="报表" disabled></el-checkbox>
                <el-checkbox label="图片" v-if="downloadData && downloadData.hasImage"></el-checkbox>
                <el-checkbox label="文本" v-if="downloadData && downloadData.hasText"></el-checkbox>
            </el-checkbox-group>
            <div class="downTitle" v-if="checkList.indexOf('图片') > -1">请选择需要导出的图片的列名(至少选择 1 个)</div>
            <el-checkbox-group v-if="checkList.indexOf('图片') > -1" v-model="checkImgList" class="checkImgList">
                <el-checkbox v-if="downloadData.rptColumns.length > 0" v-for="dict in downloadData.rptColumns"
                    :key="dict.columnId" :label="dict.columnId">
                    {{ dict.columnName }}
                </el-checkbox>
            </el-checkbox-group>
            <div style="position: relative;width: 0;height: 0;overflow: hidden;">
                <exceldownload style="position: relative;z-index: -100;left:0;top:0" ref="exceldownload11"
                    exceltext="一级导出" :flag="btnFlag" icon="Download"
                    :param="Object.assign(queryParams, { 'isOpenTransUnit': isOpenTransUnit }, { 'exportImageColumnIds': checkImgList }, { 'checkList': checkList })"
                    :url="exportUrl1" v-hasPermi="[`${this.paramsRouter.fileAuthName}:exportOneLevelExcel`]">
                </exceldownload>
                <exceldownload style="position: relative;z-index: -100;left:0;top:0" ref="exceldownload22"
                    exceltext="二级导出" :flag="btnFlag" icon="Download"
                    :param="Object.assign(queryParams, { 'isOpenTransUnit': isOpenTransUnit }, { 'exportImageColumnIds': checkImgList }, { 'checkList': checkList })"
                    :url="exportUrl" v-hasPermi="[`${this.paramsRouter.fileAuthName}:exportExcel`]"></exceldownload>
                <exceldownload style="position: relative;z-index: -100;left:0;top:0" ref="exceldownload33"
                    exceltext="三级导出" :flag="btnFlag" icon="Download"
                    :param="Object.assign(queryParams, { 'isOpenTransUnit': isOpenTransUnit }, { 'exportImageColumnIds': checkImgList }, { 'checkList': checkList })"
                    :url="exportUrl3" v-hasPermi="[`${this.paramsRouter.fileAuthName}:exportThreeLevelExcel`]">
                </exceldownload>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="cancelDownload">取 消</el-button>
                    <el-button type="primary" @click="submitDownload">确 定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import SelectableCascader from "@/views/sysagriculturalsituation/components/SelectableCascader.vue";
import '@/views/sysagriculturalsituation/assets/styles/index.scss' // global css
import { getDicts } from "@/api/sysagriculturalsituation/dict"
import axios from 'axios'
import { queryAll, queryAllFile, queryByPageOneLevel, fillIn, getInfo, getInfoTask, restoreAutoTotal, endTask, preview, update, queryByPage, branchDataCommit, branchDataBack, farmDataCommit, farmDataBack, precinctDataBack, precinctDataCommit, createInfillTask, logicDeleteById, stationDataCommit, stationDataBack, synchronizeData, rptColumnLimitInfo, getFile, tips, checkColumnHasImage } from '@/api/sysagriculturalsituation/groupReport/fill';
import { getHeaderByFile, getColumnByFile, getHeaderByFileV1, getColumnByFileV1 } from '@/api/sysagriculturalsituation/groupReport/analyticalExcel';
import { getColumnByTask } from '@/api/sysagriculturalsituation/fillIn/rptDistributeTask'
import { getInitTreeCode, getInitTreeName, updateButtonStyle, selectDictLabel } from '@/api/sysagriculturalsituation/utils/cop';
import { getOrgByUser } from '@/api/sysagriculturalsituation/report/main2';
import exceldownload from "@/views/sysagriculturalsituation/components/exceldownloadGroup";
export default {
    components: {
        exceldownload, SelectableCascader
    },
    data() {
        const checkNumber = (rule, value, callback) => {
            let arr = rule.field.split('.')
            let indexFirst = arr[1]
            let indexSecond = arr[3]
            let str = this.form.column[indexFirst].list[indexSecond].columnValue
            let isBiggerThanLast = this.form.column[indexFirst].list[indexSecond].isBiggerThanLast
            if (str != null && str != '' && str != undefined) {
                let columnPrecision = this.form.column[indexFirst].list[indexSecond].columnPrecision === null ? 6 : parseInt(this.form.column[indexFirst].list[indexSecond].columnPrecision)
                var reg = ''
                if (columnPrecision === 0) {
                    reg = /^([1-9]\d*|[0]{1,1})$/      //整数
                } else {
                    reg = /^([0-9]{1}|^[1-9]{1}\d{1,15})(\.\d{1,12})?$/
                }
                let strValue = str.toString().split('.')
                let flag = true
                if (strValue[1]) {
                    flag = strValue[1].length <= columnPrecision
                }

                if (str) {
                    if (strValue[0].length > 8) {
                        callback(new Error('输入过长，请确认'))
                    } else if (!!this.levelData.limit && (this.selectRowData.isCanModify == '1' && isBiggerThanLast == '1')) { // 跨任务报表，数据继承时，任务开始当天，不做数据限制（大小）
                        let colName = this.form.column[indexFirst].list[indexSecond].columnList
                        let standard = this.levelData.limit['column' + colName]
                        let sNmmber = Number(standard)
                        if (reg.test(str) && flag && (str < sNmmber)) {
                            callback(new Error(`请确认数据,数值不能小于${sNmmber}`))
                        } else if (reg.test(str) && flag) {
                            callback()
                        } else {
                            let text = ['零', '一', '二', '三', '四', '五', '六', '七', '八']
                            if (columnPrecision === 0) {
                                callback(new Error(`请输入整数`))
                            } else {
                                callback(new Error(`请输入数字(可带${text[columnPrecision]}位小数)`))
                            }
                        }
                    } else {
                        if (reg.test(str) && flag) {
                            callback()
                        } else {
                            let text = ['零', '一', '二', '三', '四', '五', '六', '七', '八']
                            if (columnPrecision === 0) {
                                callback(new Error(`请输入整数`))
                            } else {
                                callback(new Error(`请输入数字(可带${text[columnPrecision]}位小数)`))
                            }
                        }
                    }
                } else {
                    callback('请输入数字')
                }
            } else {
                callback()
            }
        }
        const checkText = (rule, value, callback) => {
            let arr = rule.field.split('.')
            let indexFirst = arr[1]
            let indexSecond = arr[3]
            let str = this.form.column[indexFirst].list[indexSecond].columnValue
            if (str != null && str != '' && str != undefined) {
                callback()
            } else {
                callback(new Error('请输入'))
            }
        }
        return {
            columnLength: 200,
            checkAll: false,
            isIndeterminate: true,
            value: '',
            options: [],
            queryParams: {
                orgCode: '',
                fileName: '',
                statDate: null,
                page: 1,
                rows: 20,
                taskBeginTime: '',
                taskEndTime: '',
                dataStatus: null
            },
            queryFormRules: {
                fileName: [{ required: true, message: '请选择上传文件', trigger: "change" }]
            },
            // 总条数
            total: 0,
            form: {},
            orgTreeList: [],
            table: [],
            form: {
                column: []
            },
            title: '',
            open: false,
            rules: {
                number: [{
                    validator: checkNumber,
                    trigger: "blur"
                }, {
                    required: false,
                    message: "请输入",
                    trigger: "blur",
                }],
                text: [{
                    validator: checkText,
                }, {
                    required: true,
                    message: "请输入",
                    trigger: ["blur", "change"]
                }],
            },
            btnFlag: true,
            btnFlagPreview: false,
            columnListData: [],
            columnListDataCopy: [],
            selectIndex: '',
            single: true,
            levelFlag: true,
            titleFill: "任务填报下发",
            openFill: false,
            isDaily: null,
            todayDateStr: null,
            formFill: {
                statYear: null,
                orgCode: null,
                taskBeginTime: null,
                taskEndTime: null,
                isDaily: '0',
                taskScope: '1',
                scopeLevel: null,
                scopeOrgCodeList: []
            },
            taskScopeList: [
                {
                    code: '1',
                    name: '默认'
                },
                {
                    code: '2',
                    name: '指定层级'
                },
                {
                    code: '3',
                    name: '指定单位'
                }
            ],
            scopeLevelList: [
                {
                    code: '1',
                    name: '集团'
                },
                {
                    code: '2',
                    name: '分公司'
                },
                {
                    code: '3',
                    name: '农场'
                },
                {
                    code: '4',
                    name: '管理区'
                }
            ],
            rulesFill: {
                orgCode: [{ required: true, message: '请选择所属单位', trigger: "change" }],
                isDaily: [{ required: true, message: '请选择是否为日报', trigger: "change" }],
                taskBeginTime: [{ required: true, message: '请选择填报开始时间', trigger: "change" }],
                taskEndTime: [{ required: true, message: '请选择填报截止时间', trigger: "change" }],
                taskScope: [{ required: true, message: '请选择填报范围', trigger: "change" }],
                scopeLevel: [{ required: true, message: '请选择指定层级', trigger: "change" }],
                scopeOrgCodeList: [{ required: true, message: '请选择指定单位', trigger: "change" }],
            },
            dataStatusFlag: false,
            selectBox: [],
            dataStatusOptions: [],
            disabledOrg: false,
            headerLine: 0, // 文件头行数
            ooorgCode: '',
            rowData: {},
            flagShow: false,
            flagStatus1: true, // 层级1
            flagStatus2: false, // 层级2
            flagStatus3: false, // 层级3
            flagStatus4: false, // 层级4:作业站
            showFill: true, // 任务填报下发的禁用
            dataFlag: true, // 任务填报下发的禁用
            exportUrl1: import.meta.env.VITE_APP_BASE_API + "/bdh-group-report-api/fillIn/rptDataInfo/exportOneLevelExcel",
            exportUrl: import.meta.env.VITE_APP_BASE_API + "/bdh-group-report-api/fillIn/rptDataInfo/exportExcel",
            exportUrl3: import.meta.env.VITE_APP_BASE_API + "/bdh-group-report-api/fillIn/rptDataInfo/exportThreeLevelExcel",
            exportPreviewUrl: import.meta.env.VITE_APP_BASE_API + "/bdh-group-report-api/fillIn/rptDataInfo/previewExport",
            exportMutipleUrl: import.meta.env.VITE_APP_BASE_API + "/bdh-group-report-api/fillIn/rptDataInfo/batchExport",
            exportByTemplateUrl: import.meta.env.VITE_APP_BASE_API + "/bdh-group-report-api/fillIn/rptDataInfo/exportByTemplate",
            fillOrgLevelList: [],
            fillTypeFlag: null,
            tableConfig: {
                height: window.innerHeight - 370,
            },
            formulaContentList: [],
            getColumnByFileList: [],
            fullloading: false,
            selectRowData: {},
            objColumn: {},
            dateFlag: true,
            sameLevelFlag: true,
            levelData: {},
            openPreview: false,
            previewTable: [],
            lineFlag0: false,
            lineFlag1: false,
            lineFlag2: false,
            lineFlag3: false,
            lineFlag4: false,
            showColumnId: false,
            maxTerm: '0',
            rowSpanArr: [],
            mergeObj: {}, // 用来记录需要合并行的下标
            tableProps: [], // 表格中的列名
            previewForm: {},
            openMutiplePreview: false,
            previewMutipleTable: [],
            queryParamsOrgName: null,
            paramPreview: {},
            paramTemplate: {},
            btnMutipleFlag: true,
            queryMutipleParams: {
                taskDateStr: null
            },
            ids: [],
            updateFlag: false, // 表单是否发生改动
            checkedTotal: false,
            reportTask: {},
            fullOrgCode: '',
            levelSelf: true,
            errorMessage: {},
            taskDateStr: null,
            action: "#",
            uploadFileUrl: import.meta.env.VITE_APP_BASE_API + "/" + import.meta.env.VITE_APP_GATEWAYPATH_AGRICULTURALSITUATION + "/file/uploadBybase64",
            imageDialogVisible: false,
            imgfullscreenLoading: false,
            dialogImageUrl: '',
            fileSize: 10,
            isUploading1: false,
            tableData: [],
            showTable: false,
            showColumn: true,
            isOpenTransUnit: '0', // 是否开启单位转换
            defUnitArr: [],
            transUnitArr: [],
            fileRemark: null,
            isCrossRptSync: '1', // 是否开启跨报表同步 0是 1 否
            downloadVisible: false,
            downloadTitle: null,
            downloadData: {},
            checkList: ['报表'],
            checkImgList: [],
            beiAnFlag: false,
            tableLastLength: -1,
            tableLength: 0,
            isShowLastYear: '0', // 是否开启 与去年同期对比
            switchType: '',
            statYearOptions: []
        }
    },
    created() {
        this.queryMutipleParams.taskDateStr = this.getDate();
        this.paramsRouter = this.$route.query
        this.isDaily = this.$route.query.isDaily
        this.todayDateStr = this.$route.query.todayDateStr
        this.queryParams.fileName = this.paramsRouter.distTaskName
        this.queryParams.fileAuthName = this.paramsRouter.fileAuthName
        this.fillTypeFlag = this.paramsRouter.fillType// 下发填报
        this.paramsRouter.source = 'year'
        this.queryParams.statYear = this.$route.query.statYear || new Date().getFullYear().toString()
        this.title = this.paramsRouter.distTaskName
        let orgCode = getOrgByUser().then((response) => {
            this.orgTreeList = response.data;
            this.queryParams.orgCode = getInitTreeCode(this.orgTreeList)
            this.queryParamsOrgName = getInitTreeName(this.orgTreeList)
            this.formFill.orgCode = getInitTreeCode(this.orgTreeList)
            // this.ooorgCode = this.queryParams.orgCode[this.queryParams.orgCode.length - 1]
            this.ooorgCode = getInitTreeCode(this.orgTreeList)[0]
            this.fullOrgCode = getInitTreeCode(this.orgTreeList)[0]
        });
        let time = new Date()
        // this.queryParams.statDate = this.formatDateTime(time)
        let dataStatus = getDicts("data_status").then(response => {
            this.dataStatusOptions = response.data;
        });
        this.showFill = true
        let yearCd = getDicts("year_cd").then((response) => {
            this.statYearOptions = response.data
        });
        Promise.all([orgCode, dataStatus, yearCd]).then(() => {
            this.handleQuery()
            let orgCode = this.queryParams.orgCode
            if (
                this.queryParams.orgCode !== null &&
                this.queryParams.orgCode &&
                typeof this.queryParams.orgCode !== 'string'
            ) {
                orgCode = this.queryParams.orgCode[this.queryParams.orgCode.length - 1]
            }
            getFile({ "fileName": this.queryParams.fileName, "orgCode": orgCode }).then(response => {
                this.isCrossRptSync = response.data.isCrossRptSync || '1'
                if (!!response.data && !!response.data.fileRemark) {
                    this.fileRemark = '备注：' + response.data.fileRemark
                } else {
                    this.fileRemark = null
                }
                this.beiAnFlag = response.data.orgCode == '8605'
            })
            checkColumnHasImage({ "fileName": this.queryParams.fileName, "orgCode": orgCode }).then(response => {
                this.downloadData = response.data
            })
        })
        this.queryParams.taskBeginTime = this.paramsRouter.taskBeginTime
        this.queryParams.taskEndTime = this.paramsRouter.taskEndTime
        // if(this.fillTypeFlag === '1'&&this.paramsRouter.source != 'year'){
        //     this.queryParams.taskBeginTime = this.formatDateTime(time)
        //     this.queryParams.taskEndTime = this.formatDateTime(time)
        // } else {
        //     this.queryParams.taskBeginTime = null
        //     this.queryParams.taskEndTime = null
        // }
    },
    mounted() {
        window.onresize = () => {
            return (() => {
                this.$nextTick(() => {
                    this.tableConfig.height = window.innerHeight - 370
                })
            })();
        };
        this.$nextTick(() => {
            this.tableConfig.height = window.innerHeight - 370
        })
        updateButtonStyle()
    },
    watch: {
        "previewTable": function (newVal, oldVal) {
            if (newVal.length > 0) {
                this.getSpanArr(this.previewTable);
            }
        }
    },
    methods: {
        pickerOptionsStat(time) {
            if (this.formFill.taskEndTime) {
                return (time.getTime() > new Date(this.formFill.taskEndTime).getTime()) || time.getTime() < (Date.now() - (24 * 60 * 60 * 1000)) || (time.getTime() < new Date(this.formFill.taskEndTime).getTime() - 21 * (24 * 60 * 60 * 1000))
            } else {
                return time.getTime() < (Date.now() - (24 * 60 * 60 * 1000))
            }
        },
        pickerOptionsEnd(time) {
            if (this.formFill.taskBeginTime) {
                return time.getTime() < new Date(this.formFill.taskBeginTime).getTime() - (24 * 60 * 60 * 1000) || time.getTime() > new Date(this.formFill.taskBeginTime).getTime() + 20 * (24 * 60 * 60 * 1000)
            } else {
                return time.getTime() < (Date.now() - (24 * 60 * 60 * 1000))
            }
        },
        pickerOptions(time) {
            const now = new Date().getTime();
            const oneDay = 86400000; // 一天的毫秒数
            // 如果没有 taskEndTime 数据
            if (!this.formFill.taskEndTime?.length) {
                return time.getTime() < now - oneDay;
            }
            const timeMs = time.getTime();
            let isDisabled = timeMs < now - oneDay; // 默认禁用昨天之前的日期

            for (const item of this.formFill.taskEndTime) {
                const itemTime = new Date(item).getTime();

                // 条件1: 在目标日期前两天到前一天之间 (禁用区间)
                const condition1 = timeMs > itemTime - 2 * oneDay && timeMs < itemTime - oneDay;

                // 条件2: 在目标日期当天到后一天之间 (禁用区间)
                const condition2 = timeMs > itemTime && timeMs < itemTime + oneDay;

                // 如果满足任一条件则禁用
                if (condition1 || condition2) {
                    isDisabled = true;
                    break; // 找到匹配项后可提前退出
                }
            }
            return isDisabled;
        },
        pickerOptionsStatQuery(time) {
            if (this.queryParams.taskEndTime) {
                let str = this.queryParams.taskEndTime
                let timeStr = str.substring(0, 4) + '-' + str.substring(4, 6) + '-' + str.substring(6, 8)
                return (time.getTime() > new Date(timeStr).getTime())
            }
        },
        pickerOptionsEndQuery(time) {
            if (this.queryParams.taskBeginTime) {
                let str = this.queryParams.taskBeginTime
                let timeStr = str.substring(0, 4) + '-' + str.substring(4, 6) + '-' + str.substring(6, 8)
                return time.getTime() < new Date(timeStr).getTime() - (24 * 60 * 60 * 1000)
            }
        },
        handleLastYear() {
            this.isShowLastYear = this.isShowLastYear == '1' ? '0' : '1'
            this.$message.success((this.isShowLastYear == '1' ? "已经开启" : "已经关闭") + "与去年同期对比")
            this.switchList(this.switchType)
            // this.handleQuery()
        },
        openDialog(type) {
            if (type == '1') {
                this.downloadTitle = '请选择要一级导出的内容'
            } else if (type == '2') {
                this.downloadTitle = '请选择要二级导出的内容'
            } else if (type == '3') {
                this.downloadTitle = '请选择要三级导出的内容'
            }
            this.downloadVisible = true
            if (this.downloadData && this.downloadData.hasText) {
                this.checkList = ['报表', '文本']
            } else {
                this.checkList = ['报表']
            }
            this.checkImgList = []
        },
        submitDownload() {
            if (this.checkList.length == 0) {
                this.$message.error("请选择要导出的内容")
                return
            }
            if (this.checkList.indexOf('图片') > -1 && this.checkImgList.length == 0) {
                this.$message.error("请至少选择 1 个要导出的图片的列名")
                return
            }
            this.$nextTick(() => {
                if (this.downloadTitle == '请选择要一级导出的内容') {
                    this.$refs.exceldownload11.execDownload()
                } else if (this.downloadTitle == '请选择要二级导出的内容') {
                    this.$refs.exceldownload22.execDownload()
                } else if (this.downloadTitle == '请选择要三级导出的内容') {
                    this.$refs.exceldownload33.execDownload()
                }
            })

            this.downloadVisible = false
        },
        cancelDownload() {
            this.downloadVisible = false
        },
        handleToggleCheckbox() {
            let _this = this
            _this.showTable = false
            setTimeout(() => {
                _this.showTable = true
            })
        },
        handleToggleUnit() {
            this.isOpenTransUnit = this.isOpenTransUnit === '0' ? '1' : '0'
            this.$message.success("单位转换成功")
        },
        handleMutiple(value) {
            if (value && value.length > 15) {
                this.$message.error("累计多选日期不大于15个")
                this.formFill.taskEndTime = value.slice(0, 15)
            }
        },
        handlePreview(file) {
            this.imageDialogVisible = true
            this.dialogImageUrl = file.url
        },
        handleExceed() {
            this.$message.warning(`当前限制选择 3 个文件`);
        },
        // 上传前校检格式和大小
        handleBeforeUpload(file) {
            // 校检文件类型
            var fileType = ['png', 'jpg', 'jpeg']
            if (fileType) {
                let fileExtension = "";
                if (file.name.lastIndexOf(".") > -1) {
                    fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
                }
                const isTypeOk = fileType.some((type) => {
                    if (file.type.indexOf(type) > -1) return true;
                    if (fileExtension && fileExtension.indexOf(type) > -1) return true;
                    return false;
                });
                if (!isTypeOk) {
                    this.$message.error(`文件格式不正确, 请上传${fileType.join("/")}格式文件!`);
                    return false;
                }
            }
            // 校检文件大小
            if (this.fileSize) {
                const isLt = file.size / 1024 / 1024 < this.fileSize;
                if (!isLt) {
                    this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);
                    return false;
                }
            }
            return true;
        },
        //改变图片上传
        handleChangeUpload(file, index, i) {
            this.updateFlag = true
            // 校检文件类型
            var fileType = ['png', 'jpg', 'jpeg']
            if (fileType) {
                let fileExtension = "";
                if (file.name.lastIndexOf(".") > -1) {
                    fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
                }
                const isTypeOk = fileType.some((type) => {
                    if (fileExtension && fileExtension.indexOf(type) > -1) return true;
                    return false;
                });
                if (!isTypeOk) {
                    this.$message.error(`文件格式不正确, 请上传${fileType.join("/")}格式文件!`);
                    this.$refs['upload' + index + '-' + i].uploadFiles[0].length = this.$refs['upload' + index + '-' + i][0].uploadFiles.length - 1
                    return false;
                }
            }
            // 校检文件大小
            if (this.fileSize) {
                const isLt = file.size / 1024 / 1024 < this.fileSize;
                if (!isLt) {
                    this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);
                    this.$refs['upload' + index + '-' + i][0].uploadFiles.length = this.$refs['upload' + index + '-' + i][0].uploadFiles.length - 1
                    return false;
                }
            }
            if (this.isUploading1) return;
            this.imgfullscreenLoading = true
            let imgName = file.name;
            let _this = this;
            this.isUploading1 = true;
            this.getBase64(file.raw).then((res) => {
                let formData = new FormData();
                formData.append("fileStr", res);
                axios({
                    method: "post",
                    headers: { "content-type": "application/x-www-form-urlencoded" },
                    url: _this.uploadFileUrl,
                    data: formData,
                }).then((res) => {
                    if (res.status == 200) {
                        this.isUploading1 = false;
                        this.imgfullscreenLoading = false
                        this.$nextTick(() => {
                            _this.form.column[index].list[i].fileupload.push({
                                name: imgName,
                                url: res.data.data,
                                percentage: file.percentage,
                                raw: file.raw,
                                size: file.size,
                                status: file.status,
                                uid: file.uid,
                            });
                            _this.$forceUpdate()
                        });
                    } else {
                        this.$message.error("上传失败");
                    }
                });
            });
        },
        getBase64(file) {
            return new Promise(function (resolve, reject) {
                let reader = new FileReader();
                let imgResult = "";
                reader.readAsDataURL(file);
                reader.onload = function () {
                    imgResult = reader.result;
                };
                reader.onerror = function (error) {
                    reject(error);
                };
                reader.onloadend = function () {
                    resolve(imgResult);
                };
            });
        },
        handleRemove(file, fileList, index, i) {
            this.form.column[index].list[i].fileupload = fileList
            this.updateFlag = true
            this.$forceUpdate()
        },
        getDate() {
            var now = new Date();
            var year = now.getFullYear(); //得到年份
            var month = now.getMonth(); //得到月份
            var date = now.getDate(); //得到日期
            var hour = " 00:00:00"; //默认时分秒 如果传给后台的格式为年月日时分秒，就需要加这个，如若不需要，此行可忽略
            month = month + 1;
            month = month.toString().padStart(2, "0");
            date = date.toString().padStart(2, "0");
            var defaultDate = `${year}${month}${date}`;//
            return defaultDate;
        },
        handleEndStr() {
            this.queryMutipleParams.taskDateStr = this.taskDateStr
        },
        formatNumber(val, fix = 2, isTrans, dividend) {
            fix = fix == null ? 2 : fix
            var absoluteValue = Math.abs(val);
            let symbol = val < 0 ? "-" : "";
            if (this.isOpenTransUnit === '1' && isTrans === '1') {
                let dividends = Number(dividend.split('/')[1])
                absoluteValue = Number(absoluteValue) / dividends
            }
            absoluteValue = Number(absoluteValue).toFixed(fix); // 保留小数2位
            absoluteValue = "" + absoluteValue; // 转换成字符串
            var int = absoluteValue.slice(0, fix * -1 - 1);  // 拿到整数
            var ext = absoluteValue.slice(fix * -1 - 1);    // 获取到小数
            //每个三位价格逗号
            int = int.split("").reverse().join("") // 翻转整数
            var temp = ""; // 临时变量
            for (var i = 0; i < int.length; i++) {
                temp += int[i];
                if ((i + 1) % 3 == 0 && i != int.length - 1) {
                    temp += ",";    // 每隔三个数字拼接一个逗号
                }
            }
            temp = temp.split("").reverse().join("");   // 加完逗号之后翻转
            temp = symbol + temp + ext;  // 整数小数拼接
            return temp;    // 返回
        },
        handleConfirm() {
            let _this = this
            this.$confirm('是否提前结束该日报?', '提前结束提醒', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                endTask({ sheetId: _this.paramsRouter.sheetId, orgCode: _this.ooorgCode, distTaskId: _this.paramsRouter.distTaskId }).then((response) => {
                    _this.$message({
                        type: 'success',
                        message: '日报提前结束成功!'
                    });
                    _this.flagStatus1 = true
                    _this.flagStatus2 = false
                    _this.flagStatus3 = false
                    _this.flagStatus4 = false
                    _this.handleQuery()
                })
            }).catch(() => {

            });
        },
        previewMutipleBtn() {
            queryAllFile({ orgCode: this.fullOrgCode }).then((response) => {
                this.previewMutipleTable = response.data;
                this.openMutiplePreview = true
                this.queryMutipleParams.taskDateStr = this.getDate();
            });
        },
        // 多选框选中数据
        handleSelectionChangeInner(selection) {
            this.ids = selection.map(item => item.fileId)
            this.btnMutipleFlag = !selection.length
            this.queryMutipleParams.fileIdList = this.ids
            this.queryMutipleParams.orgCode = this.queryParams.orgCode
        },
        recoverBtn() {
            let _this = this
            _this.$confirm('是否确认？确认后，将清空本级手动修改数据，换成下级的汇总数据，不可恢复', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const dataId = _this.selectBox.dataId
                restoreAutoTotal({ dataId: dataId }).then(response => {
                    if (response.success == true) {
                        _this.$message.success("恢复自动汇总成功");
                        _this.switchList();
                        _this.switchType = ''
                        _this.single = true
                        _this.levelFlag = true
                        _this.selectBox = []
                    } else {
                        _this.$message.error(response.msg);
                    }
                });
            }).catch(() => { });
        },
        previewBtn() {
            this.lineFlag1 = false
            this.lineFlag2 = false
            this.lineFlag3 = false
            this.lineFlag4 = false
            this.tableProps = []
            this.previewForm.orgCode = this.selectBox.orgCode
            this.previewForm.orgName = this.selectBox.orgName
            // 日报任务中
            if (this.paramsRouter.isDaily == '1') {
                if ((Number(this.todayDateStr) >= Number(this.reportTask.taskBeginTime)) && (Number(this.todayDateStr) <= Number(this.reportTask.taskEndTime))) {
                    this.previewForm.taskEndTime = this.todayDateStr
                } else if (Number(this.todayDateStr) > Number(this.reportTask.taskEndTime)) { // 任务完成
                    this.previewForm.taskEndTime = this.selectBox.taskEndTime
                } else if (Number(this.todayDateStr) < Number(this.reportTask.taskBeginTime)) {
                    this.previewForm.taskEndTime = null
                }
            } else { // 非日报
                if (Number(this.todayDateStr) >= Number(this.paramsRouter.taskBeginTime) && Number(this.todayDateStr) <= Number(this.paramsRouter.taskEndTime)) {
                    this.previewForm.taskEndTime = this.todayDateStr
                } else if (Number(this.todayDateStr) > Number(this.paramsRouter.taskEndTime)) { // 任务完成
                    this.previewForm.taskEndTime = this.selectBox.taskEndTime
                } else if (Number(this.todayDateStr) < Number(this.paramsRouter.taskBeginTime)) {
                    this.previewForm.taskEndTime = null
                }
            }
            this.paramPreview = { 'dataId': this.selectBox.dataId }
            this.paramTemplate = {
                'dataId': this.selectBox.dataId,
                "orgCode": this.selectBox.orgCode,
                "fileName": this.queryParams.fileName,
                "statDate": this.selectBox.statDate,
                "page": 1,
                "rows": 20,
                "dataStatus": this.selectBox.dataStatus,
                "fileAuthName": this.paramsRouter.fileAuthName,
                "statYear": this.selectBox.statYear,
                "exportImageColumnIds": [],
            }
            preview(this.selectBox.dataId, { "isOpenTransUnit": this.isOpenTransUnit }).then(res => {
                this.previewTable = res.data
                if (this.previewTable && this.previewTable.length > 0) {
                    let obj = this.previewTable[0]
                    if (obj.hasOwnProperty('项目0')) {
                        this.lineFlag0 = true
                        this.tableProps.push('项目0')
                        this.maxTerm = '0'
                    }
                    if (obj.hasOwnProperty('项目1')) {
                        this.lineFlag1 = true
                        this.tableProps.push('项目1')
                        this.maxTerm = '1'
                    }
                    if (obj.hasOwnProperty('项目2')) {
                        this.lineFlag2 = true
                        this.tableProps.push('项目2')
                        this.maxTerm = '2'
                    }
                    if (obj.hasOwnProperty('项目3')) {
                        this.lineFlag3 = true
                        this.tableProps.push('项目3')
                        this.maxTerm = '3'
                    }
                    if (obj.hasOwnProperty('项目4')) {
                        this.lineFlag4 = true
                        this.tableProps.push('项目4')
                        this.maxTerm = '4'
                    }
                    this.tableProps.push('字段ID')
                }
                this.openPreview = true
            })
        },
        // getSpanArr方法
        getSpanArr(data) {
            this.tableProps.forEach((key, index1) => {
                let count = 0; // 用来记录需要合并行的起始位置
                this.mergeObj[key] = []; // 记录每一列的合并信息
                data.forEach((item, index) => {
                    // index == 0表示数据为第一行，直接 push 一个 1
                    if (index === 0) {
                        this.mergeObj[key].push(1);
                    } else {
                        /*判断当前行是否与上一行其值相等
                        如果相等 在 count 记录的位置其值 +1
                        表示当前行需要合并 并push 一个 0 作为占位
                        */
                        if (this.maxTerm === '1') {
                            if (key === '项目1' || key === '项目2' || key === '项目3' || key === '项目4') {
                                /*判断当前行是否与上一行其值相等,如果相等 在 count 记录的位置其值 +1表示当前行需要合并 并push 一个 0 作为占位*/
                                if (item['rptColumnInfo']['columnId'] === data[index - 1]['rptColumnInfo']['columnId'] && item[key] === data[index - 1][key]) {
                                    this.mergeObj[key][count] += 1;
                                    this.mergeObj[key].push(0);
                                } else {
                                    // 如果当前行和上一行其值不相等
                                    count = index; // 记录当前位置
                                    this.mergeObj[key].push(1); // 重新push 一个 1
                                }
                            } else {
                                if (item[key] === data[index - 1][key]) {
                                    this.mergeObj[key][count] += 1;
                                    this.mergeObj[key].push(0);
                                } else {
                                    // 如果当前行和上一行其值不相等
                                    count = index; // 记录当前位置
                                    this.mergeObj[key].push(1); // 重新push 一个 1
                                }
                            }
                        }

                        if (this.maxTerm === '2') {
                            if (key === '项目2' || key === '项目3' || key === '项目4') {
                                /*判断当前行是否与上一行其值相等,如果相等 在 count 记录的位置其值 +1表示当前行需要合并 并push 一个 0 作为占位*/
                                if (item['rptColumnInfo']['columnId'] === data[index - 1]['rptColumnInfo']['columnId'] && item[key] === data[index - 1][key]) {
                                    this.mergeObj[key][count] += 1;
                                    this.mergeObj[key].push(0);
                                } else {
                                    // 如果当前行和上一行其值不相等
                                    count = index; // 记录当前位置
                                    this.mergeObj[key].push(1); // 重新push 一个 1
                                }
                            } else {
                                if (item[key] === data[index - 1][key]) {
                                    this.mergeObj[key][count] += 1;
                                    this.mergeObj[key].push(0);
                                } else {
                                    // 如果当前行和上一行其值不相等
                                    count = index; // 记录当前位置
                                    this.mergeObj[key].push(1); // 重新push 一个 1
                                }
                            }
                        }

                        if (this.maxTerm === '3') {
                            if (key === '项目3' || key === '项目4') {
                                /*判断当前行是否与上一行其值相等,如果相等 在 count 记录的位置其值 +1表示当前行需要合并 并push 一个 0 作为占位*/
                                if (item['rptColumnInfo']['columnId'] === data[index - 1]['rptColumnInfo']['columnId'] && item[key] === data[index - 1][key]) {
                                    this.mergeObj[key][count] += 1;
                                    this.mergeObj[key].push(0);
                                } else {
                                    // 如果当前行和上一行其值不相等
                                    count = index; // 记录当前位置
                                    this.mergeObj[key].push(1); // 重新push 一个 1
                                }
                            } else {
                                if (item[key] === data[index - 1][key]) {
                                    this.mergeObj[key][count] += 1;
                                    this.mergeObj[key].push(0);
                                } else {
                                    // 如果当前行和上一行其值不相等
                                    count = index; // 记录当前位置
                                    this.mergeObj[key].push(1); // 重新push 一个 1
                                }
                            }
                        }

                        if (this.maxTerm === '4') {
                            if (key === '项目4') {
                                /*判断当前行是否与上一行其值相等,如果相等 在 count 记录的位置其值 +1表示当前行需要合并 并push 一个 0 作为占位*/
                                if (item['rptColumnInfo']['columnId'] === data[index - 1]['rptColumnInfo']['columnId'] && item[key] === data[index - 1][key]) {
                                    this.mergeObj[key][count] += 1;
                                    this.mergeObj[key].push(0);
                                } else {
                                    // 如果当前行和上一行其值不相等
                                    count = index; // 记录当前位置
                                    this.mergeObj[key].push(1); // 重新push 一个 1
                                }
                            } else {
                                if (item[key] === data[index - 1][key]) {
                                    this.mergeObj[key][count] += 1;
                                    this.mergeObj[key].push(0);
                                } else {
                                    // 如果当前行和上一行其值不相等
                                    count = index; // 记录当前位置
                                    this.mergeObj[key].push(1); // 重新push 一个 1
                                }
                            }
                        }

                    }
                })
            })
        },
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            // 判断列的属性
            if (this.tableProps.indexOf(column.property) !== -1) {
                // 判断其值是不是为0
                if (this.mergeObj[column.property][rowIndex]) {
                    return [this.mergeObj[column.property][rowIndex], 1]
                } else {
                    // 如果为0则为需要合并的行
                    return [0, 0];
                }
            }
        },
        handelSwitch() {
            this.formFill.taskBeginTime = null
            this.formFill.taskEndTime = null
        },
        inputFocus() {
            this.updateFlag = true
        },
        inputChange(obj) {
            // 模拟row
            this.levelData.data['column' + obj.columnList] = obj.columnValue
            let data = this.levelData.data
            let arrr = this.objColumn[obj.columnList]
            let columnType = ''
            if (arrr == undefined) {
                return false
            } else {
                arrr.map(item => {
                    let columnPrecision = item.columnPrecision === null ? 2 : item.columnPrecision // 保留小数位 默认2
                    const formulaFunction = new Function('data', `return ${item.formula}`);
                    this.levelData.data[item.formulaTitle] = formulaFunction(this.levelData.data);
                    if (isFinite(this.levelData.data[item.formulaTitle])) {
                        // 保留小数
                        let num = this.levelData.data[item.formulaTitle]
                        this.levelData.data[item.formulaTitle] = Number(num.toFixed(columnPrecision))
                    } else {
                        this.levelData.data[item.formulaTitle] = null
                    }
                    this.form.column[item.columnWrapperX].list[item.columnWrapperY].columnValue = this.levelData.data[item.formulaTitle]
                    columnType = this.form.column[item.columnWrapperX].list[item.columnWrapperY].columnType
                    this.inputChange({ columnList: item.formulaTitle.split('column')[1], columnValue: this.levelData.data[item.formulaTitle] })
                })
            }
        },
        getListInitial() {
            this.single = true
            this.levelFlag = true
            this.selectBox = []
            let orgCode = this.queryParams.orgCode
            if (
                this.queryParams.orgCode !== null &&
                this.queryParams.orgCode &&
                typeof this.queryParams.orgCode !== 'string'
            ) {
                orgCode =
                    this.queryParams.orgCode[this.queryParams.orgCode.length - 1]
            }
            let query = {
                "sheetId": this.sheetId,
                "orgCode": orgCode,
                "statDate": this.paramsRouter.source == 'year' ? null : this.queryParams.statDate,
                "statYear": this.paramsRouter.source == 'year' ? this.queryParams.statYear : null,
                "taskBeginTime": this.queryParams.taskBeginTime == null ? null : this.queryParams.taskBeginTime.split("-").join(''),
                "taskEndTime": this.queryParams.taskEndTime == null ? null : this.queryParams.taskEndTime.split("-").join(''),
                "page": this.queryParams.page,
                "rows": this.queryParams.rows,
                "dataStatus": this.queryParams.dataStatus,
                "isShowLastYear": this.isShowLastYear
            }
            queryByPageOneLevel(query).then(res => {
                this.columnListData = res.data.records
                this.$nextTick(() => {
                    this.$refs.multipleTable.doLayout();
                })
                this.total = res.data.total
                this.columnListData.map((item, index) => {
                    item.children = []
                    item.crossTableSameDate != null ? item.children.push(Object.assign(item.crossTableSameDate, { dataId: item.dataId + '11' + index, orgCode: '0' })) : ''
                    item.crossTableSameDateDiff != null ? item.children.push(Object.assign(item.crossTableSameDateDiff, { dataId: item.dataId + '12' + index, orgCode: '0' })) : ''
                    item.crossTableLastDate != null ? item.children.push(Object.assign(item.crossTableLastDate, { dataId: item.dataId + '13' + index, orgCode: '0' })) : ''
                    item.crossTableLastDateDiff != null ? item.children.push(Object.assign(item.crossTableLastDateDiff, { dataId: item.dataId + '14' + index, orgCode: '0' })) : ''
                    item.sameTableLastDate != null ? item.children.push(Object.assign(item.sameTableLastDate, { dataId: item.dataId + '15' + index, orgCode: '0' })) : ''
                    item.sameTableLastDateDiff != null ? item.children.push(Object.assign(item.sameTableLastDateDiff, { dataId: item.dataId + '16' + index, orgCode: '0' })) : ''
                    item.sameTableSameDate != null ? item.children.push(Object.assign(item.sameTableSameDate, { dataId: item.dataId + '17' + index, orgCode: '0' })) : ''
                    item.sameTableSameDateDiff != null ? item.children.push(Object.assign(item.sameTableSameDateDiff, { dataId: item.dataId + '18' + index, orgCode: '0' })) : ''
                    item.columnA = item.orgName
                })
            })
        },
        // 查看农场级别数据
        handleNext(row) {
            this.flagShow = true
            if (this.flagStatus1) {
                this.rowData = row
                this.queryParams.page = 1
                this.flagStatus1 = false
                this.flagStatus2 = true
                this.flagStatus3 = false
                this.flagStatus4 = false
                this.switchList()
                this.switchType = ''
            } else if (this.flagStatus2) { // 农场查看管理区
                this.rowData = row
                this.queryParams.page = 1
                this.flagStatus1 = false
                this.flagStatus2 = false
                this.flagStatus3 = true
                this.flagStatus4 = false
                this.switchList()
                this.switchType = ''
            } else if (this.flagStatus3) {
                this.rowData = row
                this.queryParams.page = 1
                this.flagStatus1 = false
                this.flagStatus2 = false
                this.flagStatus3 = false
                this.flagStatus4 = true
                this.switchList()
                this.switchType = ''
            }
        },
        // 返回上级列表
        goBack() {
            this.single = true
            this.levelFlag = true
            this.selectBox = []
            if (this.flagStatus2) { // 二级返回一级
                this.flagShow = false
                this.queryParams.page = 1
                this.getListInitial()
                this.flagStatus1 = true
                this.flagStatus2 = false
                this.flagStatus3 = false
                this.flagStatus4 = false
            } else if (this.flagStatus3) { // 三级返回二级 农场-分公司/管理区-农场
                this.flagShow = true
                this.queryParams.page = 1
                this.flagStatus1 = false
                this.flagStatus2 = true
                this.flagStatus3 = false
                this.flagStatus4 = false
                this.switchList("threeBackTwo")
                this.switchType = 'threeBackTwo'
            } else if (this.flagStatus4) { // 管理区返回农场
                this.flagShow = true
                this.queryParams.page = 1
                this.flagStatus1 = false
                this.flagStatus2 = false
                this.flagStatus3 = true
                this.flagStatus4 = false
                this.switchList("back")
                this.switchType = 'back'
            }
        },
        switchList(val) {
            this.$refs.multipleTable.clearSelection();
            this.single = true
            this.levelFlag = true
            this.selectBox = []
            if (this.flagStatus1) {
                this.getListInitial()
            } else if (this.flagStatus2) {
                let query = {
                    "sheetId": this.sheetId,
                    "orgCode": this.ooorgCode,
                    "statDate": null,
                    "taskBeginTime": this.rowData.taskBeginTime,
                    "taskEndTime": this.rowData.taskEndTime,
                    "page": this.queryParams.page,
                    "rows": this.queryParams.rows,
                    "dataStatus": this.queryParams.dataStatus,
                    "isShowLastYear": this.isShowLastYear
                }
                queryByPage(query).then(res => {
                    this.columnListData = res.data
                    this.$nextTick(() => {
                        this.$refs.multipleTable.doLayout();
                    })
                    this.columnListData.map((item, index) => {
                        item.children = []
                        item.crossTableSameDate != null ? item.children.push(Object.assign(item.crossTableSameDate, { dataId: item.dataId + '21' + index, orgCode: '0' })) : ''
                        item.crossTableSameDateDiff != null ? item.children.push(Object.assign(item.crossTableSameDateDiff, { dataId: item.dataId + '22' + index, orgCode: '0' })) : ''
                        item.crossTableLastDate != null ? item.children.push(Object.assign(item.crossTableLastDate, { dataId: item.dataId + '23' + index, orgCode: '0' })) : ''
                        item.crossTableLastDateDiff != null ? item.children.push(Object.assign(item.crossTableLastDateDiff, { dataId: item.dataId + '24' + index, orgCode: '0' })) : ''
                        item.sameTableLastDate != null ? item.children.push(Object.assign(item.sameTableLastDate, { dataId: item.dataId + '25' + index, orgCode: '0' })) : ''
                        item.sameTableLastDateDiff != null ? item.children.push(Object.assign(item.sameTableLastDateDiff, { dataId: item.dataId + '26' + index, orgCode: '0' })) : ''
                        item.sameTableSameDate != null ? item.children.push(Object.assign(item.sameTableSameDate, { dataId: item.dataId + '27' + index, orgCode: '0' })) : ''
                        item.sameTableSameDateDiff != null ? item.children.push(Object.assign(item.sameTableSameDateDiff, { dataId: item.dataId + '28' + index, orgCode: '0' })) : ''
                        item.columnA = item.orgName
                    })
                })
            } else if (this.flagStatus3) {
                let code = this.rowData.orgCode
                if (val == 'threeBackTwo') { // 农场-分公司/管理区-农场
                    code = this.ooorgCode.length == 2 ? this.rowData.orgCode.substring(0, 4) : this.rowData.orgCode.substring(0, 6)
                } else if (val == 'back') {
                    code = this.rowData.orgCode.substring(0, 4)
                }
                let query = {
                    "sheetId": this.sheetId,
                    "orgCode": code,
                    "statDate": null,
                    "taskBeginTime": this.rowData.taskBeginTime,
                    "taskEndTime": this.rowData.taskEndTime,
                    "page": this.queryParams.page,
                    "rows": this.queryParams.rows,
                    "dataStatus": this.queryParams.dataStatus,
                    "isShowLastYear": this.isShowLastYear
                }
                queryByPage(query).then(res => {
                    this.columnListData = res.data
                    this.$nextTick(() => {
                        this.$refs.multipleTable.doLayout();
                    })
                    this.columnListData.map((item, index) => {
                        item.children = []
                        item.crossTableSameDate != null ? item.children.push(Object.assign(item.crossTableSameDate, { dataId: item.dataId + '31' + index, orgCode: '0' })) : ''
                        item.crossTableSameDateDiff != null ? item.children.push(Object.assign(item.crossTableSameDateDiff, { dataId: item.dataId + '32' + index, orgCode: '0' })) : ''
                        item.crossTableLastDate != null ? item.children.push(Object.assign(item.crossTableLastDate, { dataId: item.dataId + '33' + index, orgCode: '0' })) : ''
                        item.crossTableLastDateDiff != null ? item.children.push(Object.assign(item.crossTableLastDateDiff, { dataId: item.dataId + '34' + index, orgCode: '0' })) : ''
                        item.sameTableLastDate != null ? item.children.push(Object.assign(item.sameTableLastDate, { dataId: item.dataId + '35' + index, orgCode: '0' })) : ''
                        item.sameTableLastDateDiff != null ? item.children.push(Object.assign(item.sameTableLastDateDiff, { dataId: item.dataId + '36' + index, orgCode: '0' })) : ''
                        item.sameTableSameDate != null ? item.children.push(Object.assign(item.sameTableSameDate, { dataId: item.dataId + '37' + index, orgCode: '0' })) : ''
                        item.sameTableSameDateDiff != null ? item.children.push(Object.assign(item.sameTableSameDateDiff, { dataId: item.dataId + '38' + index, orgCode: '0' })) : ''
                        item.columnA = item.orgName
                    })
                })
            } else {
                let code = this.rowData.orgCode
                if (val == 'back') {
                    code = code.substring(0, 6)
                }
                let query = {
                    "sheetId": this.sheetId,
                    "orgCode": code,
                    "statDate": this.paramsRouter.source == 'year' ? null : val == 'back' ? this.queryParams.statDate : this.rowData.statDate,
                    "statYear": this.paramsRouter.source == 'year' ? val == 'back' ? this.queryParams.statYear : this.rowData.statYear : null,
                    "taskBeginTime": this.rowData.taskBeginTime,
                    "taskEndTime": this.rowData.taskEndTime,
                    "page": this.queryParams.page,
                    "rows": this.queryParams.rows,
                    "dataStatus": this.queryParams.dataStatus,
                    "isShowLastYear": this.isShowLastYear
                }
                queryByPage(query).then(res => {
                    this.columnListData = res.data
                    this.$nextTick(() => {
                        this.$refs.multipleTable.doLayout();
                    })
                    this.columnListData.map((item, index) => {
                        item.children = []
                        item.crossTableSameDate != null ? item.children.push(Object.assign(item.crossTableSameDate, { dataId: item.dataId + '41' + index, orgCode: '0' })) : ''
                        item.crossTableSameDateDiff != null ? item.children.push(Object.assign(item.crossTableSameDateDiff, { dataId: item.dataId + '42' + index, orgCode: '0' })) : ''
                        item.crossTableLastDate != null ? item.children.push(Object.assign(item.crossTableLastDate, { dataId: item.dataId + '43' + index, orgCode: '0' })) : ''
                        item.crossTableLastDateDiff != null ? item.children.push(Object.assign(item.crossTableLastDateDiff, { dataId: item.dataId + '44' + index, orgCode: '0' })) : ''
                        item.sameTableLastDate != null ? item.children.push(Object.assign(item.sameTableLastDate, { dataId: item.dataId + '45' + index, orgCode: '0' })) : ''
                        item.sameTableLastDateDiff != null ? item.children.push(Object.assign(item.sameTableLastDateDiff, { dataId: item.dataId + '46' + index, orgCode: '0' })) : ''
                        item.sameTableSameDate != null ? item.children.push(Object.assign(item.sameTableSameDate, { dataId: item.dataId + '47' + index, orgCode: '0' })) : ''
                        item.sameTableSameDateDiff != null ? item.children.push(Object.assign(item.sameTableSameDateDiff, { dataId: item.dataId + '48' + index, orgCode: '0' })) : ''
                        item.columnA = item.orgName
                    })
                })
            }
        },
        openFillIssue() {
            this.openFill = true
            this.formFill.orgCode = getInitTreeCode(this.orgTreeList)
            this.formFill.isDaily = '0'
            this.formFill.taskScope = '1'
            this.formFill.statYear = this.statYearOptions[0].code
        },
        cancelFill() {
            this.openFill = false
            this.formFill = {
                statYear: null,
                taskBeginTime: null,
                taskEndTime: null,
                orgCode: null,
                isDaily: '0',
                taskScope: '1',
                scopeLevel: null,
                scopeOrgCodeList: []
            };
            this.resetForm("formFillRef");
        },
        changeAlardy() {
            let _this = this
            this.$confirm('您要保存的数据为上次提交的数据，本次未做修改，是否继续保存', "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(function () {
                _this.submitForm()
                _this.updateFlag = false
            })
        },
        handleTaskScope(val) {
            this.formFill.scopeLevel = null
            this.formFill.scopeOrgCodeList = []
            this.$forceUpdate()
        },
        submitFormFill() {
            this.$refs["formFillRef"].validate(valid => {
                if (valid) {
                    let taskEndTime = ''
                    if (this.formFill.isDaily == '0') {
                        let arr = []
                        this.formFill.taskEndTime.map(item => {
                            arr.push(item.split("-").join(''))
                        })
                        taskEndTime = arr.join(',')
                    } else {
                        taskEndTime = this.formFill.taskEndTime.split("-").join('')
                    }
                    let uniqueArray = []
                    if (this.formFill.taskScope == '3') {
                        let twoDimensionalArray = [...this.formFill.scopeOrgCodeList]
                        // 使用flat()将二维数组展平为一维数组
                        let flattenedArray = twoDimensionalArray.flat();
                        // 使用Set去重
                        uniqueArray = [...new Set(flattenedArray)];
                    } else {
                        uniqueArray = null
                    }
                    let params = {
                        statYear: this.formFill.statYear,
                        orgCode: typeof (this.formFill.orgCode) == "object" ? this.formFill.orgCode[this.formFill.orgCode.length - 1] : this.formFill.orgCode,
                        taskBeginTime: this.formFill.isDaily == '1' ? this.formFill.taskBeginTime.split("-").join('') : null,
                        taskEndTime: taskEndTime,
                        fileName: this.queryParams.fileName,
                        fillLevel: '000004',
                        sheetId: this.sheetId,
                        isDaily: this.formFill.isDaily,
                        taskScope: this.formFill.taskScope,
                        scopeLevel: this.formFill.scopeLevel,
                        scopeOrgCodeList: uniqueArray
                    }
                    createInfillTask(params).then(response => {
                        this.$message.success("任务填报下发成功");
                        this.cancelFill()
                        // this.queryParams.orgCode = getInitTreeCode(this.orgTreeList)
                        this.flagStatus1 = true
                        this.flagStatus2 = false
                        this.flagStatus3 = false
                        this.flagStatus4 = false
                        this.getListInitial();
                        this.single = true
                        this.levelFlag = true
                        this.selectBox = []
                    }).catch((err) => {
                        this.formFill.taskBeginTime = null
                        this.formFill.taskEndTime = null
                    });
                }
            });
        },
        dataStatusFormat(row, column) {
            return selectDictLabel(this.dataStatusOptions, row.dataStatus != null && row.dataStatus != undefined ? row.dataStatus.toString() : "");
        },
        getLabel(e) {
            this.form.orgName = e
        },
        changeTime(val) {
        },
        handleCheckAllChange(val) {
            this.isIndeterminate = false
        },
        handleDate(str) {
            let timeStr = str.substring(0, 4) + '-' + str.substring(4, 6) + '-' + str.substring(6, 8)
            let date = new Date(timeStr).getTime() / 86400000
            return date
        },
        handleSelectionChange(selection) {
            this.dataFlag = true
            this.dateFlag = true
            this.sameLevelFlag = true
            this.levelFlag = true
            this.single = selection.length !== 1
            this.levelSelf = true
            if (!this.single) {
                this.levelSelf = !(selection[0].orgCode == this.fullOrgCode)
                this.dataFlag = selection[0].dataStatus == '02'
                this.selectBox = selection[0]
                this.sameLevelFlag = selection[0].orgCode !== getInitTreeCode(this.orgTreeList)[0]
                // 有任务开始的第一天允许修改，且每级均可修改，其余时间不允许修改
                if (this.isDaily === '0') { // 非日报提前两天可填报
                    this.dateFlag = !(selection[0].taskEndTime == this.todayDateStr ||
                        this.handleDate(selection[0].taskEndTime) <= this.handleDate(this.todayDateStr) + 1)
                } else {
                    this.dateFlag = selection[0].taskEndTime > this.todayDateStr
                }
                this.levelFlag = selection[0].orgCode.length < 3 // 分公司 农场及管理区可修改

                // 勾选再出结束日报
                if (selection[0].distTaskId) {
                    getInfoTask(selection[0].distTaskId).then(response => {
                        this.reportTask = response.data
                    })
                }
            }
            this.$forceUpdate()
        },
        // 作业站提交
        stationDataCommitBtn() {
            const dataId = this.selectBox.dataId
            stationDataCommit(dataId).then(response => {
                if (response.success == true) {
                    this.$message.success("提交成功");
                    this.switchList();
                    this.switchType = ''
                    this.single = true
                    this.levelFlag = true
                    this.selectBox = []
                } else {
                    this.$message.error(response.msg);
                }
            });
        },
        // 作业站退回
        stationDataBackBtn() {
            const dataId = this.selectBox.dataId
            stationDataBack(dataId).then(response => {
                if (response.success == true) {
                    this.$message.success("退回成功");
                    this.switchList();
                    this.switchType = ''
                    this.single = true
                    this.levelFlag = true
                    this.selectBox = []
                } else {
                    this.$message.error(response.msg);
                }
            });
        },
        // 管理区提交
        precinctDataCommitBtn() {
            const isModify = this.selectBox.isModify
            if (isModify == null) {
                let _this = this
                this.$confirm('您要提交的数据为上次提交的数据，本次未做修改，是否继续提交', "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                }).then(function () {
                    const dataId = _this.selectBox.dataId
                    precinctDataCommit(dataId).then(response => {
                        if (response.success == true) {
                            _this.$message.success("提交成功");
                            _this.switchList();
                            _this.switchType = ''
                            _this.single = true
                            _this.levelFlag = true
                            _this.selectBox = []
                        } else {
                            _this.$message.error(response.msg);
                        }
                    });
                })
            } else {
                const dataId = this.selectBox.dataId
                precinctDataCommit(dataId).then(response => {
                    if (response.success == true) {
                        this.$message.success("提交成功");
                        this.switchList();
                        this.switchType = ''
                        this.single = true
                        this.levelFlag = true
                        this.selectBox = []
                    } else {
                        this.$message.error(response.msg);
                    }
                });
            }
        },
        // 管理区退回
        precinctDataBackBtn() {
            const dataId = this.selectBox.dataId
            precinctDataBack(dataId).then(response => {
                if (response.success == true) {
                    this.$message.success("退回成功");
                    this.switchList();
                    this.switchType = ''
                    this.single = true
                    this.levelFlag = true
                    this.selectBox = []
                } else {
                    this.$message.error(response.msg);
                }
            });
        },
        // 农场提交
        farmDataCommitBtn() {
            const dataId = this.selectBox.dataId
            farmDataCommit(dataId).then(response => {
                if (response.success == true) {
                    this.$message.success("提交成功");
                    this.switchList();
                    this.switchType = ''
                    this.single = true
                    this.levelFlag = true
                    this.selectBox = []
                } else {
                    this.$message.error(response.msg);
                }
            });
        },
        // 农场退回
        farmDataBackBtn() {
            const dataId = this.selectBox.dataId
            farmDataBack(dataId).then(response => {
                if (response.success == true) {
                    this.$message.success("退回成功");
                    this.switchList();
                    this.switchType = ''
                    this.single = true
                    this.levelFlag = true
                    this.selectBox = []
                } else {
                    this.$message.error(response.msg);
                }
            });
        },
        // 分公司提交
        branchDataCommitBtn() {
            const dataId = this.selectBox.dataId
            branchDataCommit(dataId).then(response => {
                if (response.success == true) {
                    this.$message.success("提交成功");
                    this.switchList('threeBackTwo');
                    this.switchType = 'threeBackTwo'
                    this.single = true
                    this.levelFlag = true
                    this.selectBox = []
                } else {
                    this.$message.error(response.msg);
                }
            });
        },
        // 分公司退回
        branchDataBackBtn() {
            const dataId = this.selectBox.dataId
            branchDataBack(dataId).then(response => {
                if (response.success == true) {
                    this.$message.success("退回成功");
                    this.switchList('threeBackTwo');
                    this.switchType = 'threeBackTwo'
                    this.single = true
                    this.levelFlag = true
                    this.selectBox = []
                } else {
                    this.$message.error(response.msg);
                }
            });
        },
        // 同步数据
        handelSynchronizeData() {
            this.fullloading = true
            const dataId = this.selectBox.dataId
            synchronizeData(dataId).then(response => {
                if (response.success == true) {
                    this.$message.success("同步成功");
                    this.switchList();
                    this.switchType = 'threeBackTwo'
                    this.single = true
                    this.levelFlag = true
                    this.selectBox = []
                    this.fullloading = false
                } else {
                    this.$message.error(response.msg);
                }
            });
        },
        handleUpdate() {
            this.title = '修改' + this.queryParams.fileName
            getInfo(this.selectBox.dataId).then(res => {
                this.levelData = res.data
                let time = new Date()
                let params = { "taskId": this.paramsRouter.distTaskId, "fileName": this.paramsRouter.distTaskName, "orgCode": this.selectBox.orgCode }
                getColumnByTask(params).then((response) => {
                    let arrNum = response.data.map(item => item.columnList)
                    this.levelData = res.data
                    let time = new Date()
                    this.form.column.map(item => {
                        let leg = 0
                        item.list.map(t => {
                            if (t.columnList == "A") {
                                t.columnValue = this.levelData.data.orgName
                            }
                            if (arrNum.indexOf(t.columnList) > -1) {
                                t.vShowFlag = true
                                leg++
                            } else {
                                t.vShowFlag = false
                            }
                            let label = 'column' + t.columnList
                            // if(t.columnList!="A"&&t.columnType !== '4'&&t.columnType !== '2') {
                            //     t.columnValue = Number(this.selectBox[label]) == Number(this.levelData.data[label]) || this.levelData.data[label] == null? this.selectBox[label]:this.levelData.data[label]
                            // } else {
                            //     t.columnValue = this.selectBox[label]
                            // }
                            t.columnValue = this.levelData.data[label] || this.selectBox[label]
                            this.form.orgCode = this.selectBox.orgCode
                            if (t.columnType == '4') {
                                t.fileupload = []
                                if (t.columnValue != null && t.columnValue != '') {
                                    let arr = t.columnValue.split(',')
                                    arr.map(urlItem => {
                                        t.fileupload.push({
                                            name: urlItem,
                                            url: urlItem,
                                        })
                                    })
                                }
                            }
                            if (t.columnType == '2' && t.isDictionary === '1') { // 下拉多选
                                t.columnOptions = t.columnValue ? t.columnValue.split(',') : []
                            }
                            if (this.selectBox.statDate == null || this.selectBox.statDate == '') {
                                if (this.paramsRouter.source == 'year') {
                                    this.form.statYear = this.selectBox.statYear || new Date().getFullYear().toString()
                                } else {
                                    this.form.statDate = this.formatDateTime(time)
                                }
                            } else {
                                if (this.paramsRouter.source == 'year') {
                                    this.form.statYear = this.selectBox.statYear
                                } else {
                                    this.form.statDate = this.selectBox.statDate
                                }
                            }
                        })
                        if (leg > 0) {
                            item.vShowFlag = true
                        } else {
                            item.vShowFlag = false
                        }
                    })
                    this.open = true
                    this.disabledOrg = true
                })
            })
            this.selectRowData = { ...this.selectBox }
            this.single = true
            // this.selectBox = []
            this.$forceUpdate()
        },
        handleQuery() {
            // this.$refs["queryForm"].validate(valid => {
            // if (valid) {
            this.showTable = false
            this.single = true
            this.levelFlag = true
            this.selectBox = []
            this.queryParams.page = 1
            if (
                this.queryParams.orgCode !== null &&
                this.queryParams.orgCode &&
                typeof this.queryParams.orgCode !== 'string'
            ) {
                this.ooorgCode = this.queryParams.orgCode[this.queryParams.orgCode.length - 1]
                this.queryParams.orgCode = this.queryParams.orgCode[this.queryParams.orgCode.length - 1]
            }
            let params = {
                fileName: this.queryParams.fileName,
                orgCode: this.queryParams.orgCode
            }
            let getColumn = getColumnByFileV1(params).then(response => {
                let defUnitArr = []
                let transUnitArr = []
                response.data.map(x => {
                    if (x.defUnit != null) {
                        defUnitArr.push(x.defUnit)
                    }
                    if (x.transUnit != null) {
                        transUnitArr.push(x.transUnit)
                    }
                })
                this.defUnitArr = Array.from(new Set(defUnitArr))
                this.transUnitArr = Array.from(new Set(transUnitArr))
                this.getColumnByFileList = response.data
                let arr = []
                let arrA = []
                this.form.column = []
                let sheetId = ''
                response.data.map(item => {
                    arr.push({
                        parColumnName: item.parColumnName,
                        parColumnNameTwo: item.parColumnNameTwo,
                        parColumnNameThree: item.parColumnNameThree,
                    })
                })
                let resultarr = []
                arr = this.deleteRepeat(arr)
                // 寻找父类，重构数据
                for (var i in arr) {
                    arrA = []
                    for (var j in response.data) {
                        if (arr[i].parColumnName == response.data[j].parColumnName &&
                            arr[i].parColumnNameTwo == response.data[j].parColumnNameTwo &&
                            arr[i].parColumnNameThree == response.data[j].parColumnNameThree) {
                            sheetId = response.data[j].sheetId
                            if (response.data[j].columnValue == null && response.data[j].columnType != '3' && response.data[j].isRequired != '1') {
                                // this.$set( response.data[j], 'columnValue', '0')
                                response.data[j]['columnValue'] = '0'
                            } else if (response.data[j].columnValue == null && response.data[j].columnType === '3') {
                                // this.$set( response.data[j], 'columnValue', null)
                                response.data[j]['columnValue'] = null
                            }
                            arrA.push(response.data[j])
                        }
                    }
                    let flagArr = arrA.filter(item => {
                        // isTotal不是1的，允许填写
                        return (item.columnType == '0' || item.columnType == '03') && item.formulaContent === null
                    })
                    let flagStatus = true
                    if (flagArr.length === arrA.length) {
                        flagStatus = false
                    }
                    this.form.column[i] = {
                        title: arr[i],
                        flag: flagStatus,
                        list: arrA
                    }
                }
                let arrayList = [...response.data]
                this.formulaContentList = []
                let _this = this
                function getFormulaContentList() {
                    _this.form.column.map((tFirst, iFirst) => {
                        tFirst.list.map((tSecond, iSecond) => {
                            arrayList.map(itemArr => {
                                if (tSecond.columnList == itemArr.columnList) {
                                    itemArr.columnWrapperX = iFirst
                                    itemArr.columnWrapperY = iSecond
                                    if (itemArr.formulaContent != null && itemArr.formulaContent != '' && itemArr.columnType == '0' && itemArr.isSumProduct != '1') {
                                        _this.formulaContentList.push({
                                            columnList: itemArr.columnList, formulaContent: transformFormula(itemArr.formulaContent), involve: itemArr.formulaDesc, columnPrecision: itemArr.columnPrecision,
                                            columnWrapperX: iFirst, columnWrapperY: iSecond
                                        })
                                        resultarr = resultarr.concat(itemArr.formulaDesc.split(","))
                                    }
                                }
                            })
                        })

                    })

                }
                function transformFormula(formula, prefix = "data.column") {
                    // 匹配变量名（连续的字母）
                    const variableRegex = /([A-Za-z]+)/g;

                    // 替换变量名为 Number(data.columnXX)
                    return formula.replace(variableRegex, (match) => {
                        return `Number(${prefix}${match})`;
                    });
                }
                async function fun2() {
                    await getFormulaContentList()
                    let set = new Set(resultarr);
                    let newArr = Array.from(set);
                    let objColumn = {}
                    newArr.map(newArrITem => {
                        objColumn[newArrITem] = []
                        _this.formulaContentList.map(itemList => {
                            if (itemList.involve.split(',').indexOf(newArrITem) > -1) {
                                objColumn[newArrITem].push({
                                    formulaTitle: `column${itemList.columnList}`,
                                    formula: itemList.formulaContent,
                                    columnWrapperX: itemList.columnWrapperX,
                                    columnWrapperY: itemList.columnWrapperY,
                                    columnPrecision: itemList.columnPrecision
                                })
                            }
                        })

                    })
                    _this.objColumn = objColumn
                }
                fun2()
                this.btnFlag = false
                this.$forceUpdate()
            })
            Promise.all([getColumn]).then(() => {
                getHeaderByFileV1(params).then(response => {
                    console.log(66,response);
                    this.table = response.data
                    // 用el-table 重构表头数据
                    let tableCopy = { ...response.data }
                    let tableLength = Object.keys(tableCopy).length
                    this.tableLength = tableLength

                    let tableData = []
                    for (let ii = tableLength - 1; ii > -1; ii--) {
                        if (ii == tableLength - 1) { // 最低一层回填倒数第二层
                            console.log(77777);
                            this.tableLastLength = tableCopy[ii].length
                            let start = 0
                            if (this.tableLastLength == 3 && tableLength == 3) {
                                tableCopy[ii - 1].map((tableItem, tableIndex) => {
                                    if (tableItem.occupyLineNum <= 1) { // 倒数第二层 高度小于等于一 意味着有包含项
                                        if (tableIndex == 0) {
                                            tableItem.children = tableCopy[ii].slice(0, 2)
                                        } else if (tableIndex == 1) {
                                            tableItem.children = tableCopy[ii].slice(2, 3)
                                        }
                                    }
                                })
                            } else {
                                tableCopy[ii].map((itemLast, itemIndex) => {
                                    itemLast.itemLevel = ii
                                })
                                tableCopy[ii - 1].map((tableItem) => {
                                    if (tableItem.occupyLineNum <= 1) { // 倒数第二层 高度小于等于一 意味着有包含项
                                        tableItem.children = tableCopy[ii].slice(start, tableItem.occupyListNum + start)
                                        start = tableItem.occupyListNum + start
                                    }
                                })
                            }
                        }
                        else if (ii == 2) { // 第三层回填第二层
                            console.log(888888);
                            tableCopy[ii].map((itemLast, itemIndex) => {
                                itemLast.itemLevel = ii
                            })
                            let length = tableCopy[ii].length
                            let startHalf = Math.ceil(length / 2)
                            let endHalf = length - startHalf
                            if (tableCopy[ii - 1].length == 1) {
                                tableCopy[ii - 1][0].children = tableCopy[ii]
                            } else if (tableCopy[ii - 1].length == 2) {
                                tableCopy[ii - 1][0].children = tableCopy[ii].slice(0, startHalf)
                                tableCopy[ii - 1][1].children = tableCopy[ii].slice(startHalf, length)
                            }
                        }
                        else if (ii != 0) { // 非最高层
                            console.log(9999);
                            console.log(111, tableCopy[ii]);
                            tableCopy[ii].map((itemLast, itemIndex) => {
                                itemLast.itemLevel = ii
                            })
                            let mArr = tableCopy[ii - 1]
                            let nArr = tableCopy[ii]
                            let start = 0 // 起始
                            let flag = 0
                            for (let jj = 0; jj < mArr.length; jj++) {
                                if (mArr[jj].occupyLineNum <= 1) {
                                    let step = 0 // 步长
                                    // 需要计算出加几个 = mArr[jj].occupyListNum
                                    let accumulation = 0 // 累加
                                    for (let kk = flag; kk < nArr.length; kk++) {
                                        accumulation += Number(nArr[kk].occupyListNum)
                                        flag += 1
                                        step += 1
                                        if (mArr[jj].occupyListNum == accumulation) {
                                            mArr[jj].children = nArr.slice(start, start + step)
                                            start += step
                                            break
                                        }
                                    }
                                }
                            }
                            tableData = mArr

                        }
                    }
                    let tableDataCopy = [...tableData]
                    let stepLength = 0
                    // 遍历树结构 没有子节点就是最后一级 按照顺序添加columnList
                    let _this = this
                    function recursive(node) {
                        if (node.children) {
                            for (let child of node.children) {
                                recursive(child)
                            }
                        } else {
                            node.columnList = _this.getColumnByFileList[stepLength].columnList
                            node.columnType = _this.getColumnByFileList[stepLength].columnType
                            node.defUnit = _this.getColumnByFileList[stepLength].defUnit
                            node.transUnit = _this.getColumnByFileList[stepLength].transUnit
                            node.transAlgor = _this.getColumnByFileList[stepLength].transAlgor
                            node.isUnitShow = _this.getColumnByFileList[stepLength].isUnitShow
                            node.isTrans = _this.getColumnByFileList[stepLength].isTrans
                            node.columnPrecision = _this.getColumnByFileList[stepLength].columnPrecision
                            node.transPrecision = _this.getColumnByFileList[stepLength].transPrecision
                            if (stepLength < _this.getColumnByFileList.length - 1) {
                                stepLength += 1
                            }
                        }
                    }
                    recursive(tableDataCopy[0])
                    // 使用示例（直接修改原始数据）
                    this.removeUnitNodes(tableDataCopy);
                    this.tableData = tableDataCopy
                    this.showTable = true
                    if (JSON.stringify(response.data) != "{}") {
                        this.headerLine = Object.keys(this.table).length
                        this.sheetId = this.table['0'][0].sheetId
                        if (
                            this.queryParams.orgCode !== null &&
                            this.queryParams.orgCode &&
                            typeof this.queryParams.orgCode !== 'string'
                        ) {
                            this.queryParams.orgCode =
                                this.queryParams.orgCode[this.queryParams.orgCode.length - 1]
                        }
                        let query = {
                            "sheetId": this.sheetId,
                            "orgCode": this.queryParams.orgCode,
                            "statDate": this.paramsRouter.source == 'year' ? null : this.queryParams.statDate,
                            "statYear": this.paramsRouter.source == 'year' ? this.queryParams.statYear : null,
                            "taskBeginTime": this.queryParams.taskBeginTime == null ? null : this.queryParams.taskBeginTime.split("-").join(''),
                            "taskEndTime": this.queryParams.taskEndTime == null ? null : this.queryParams.taskEndTime.split("-").join(''),
                            "page": this.queryParams.page,
                            "rows": this.queryParams.rows,
                            "dataStatus": this.queryParams.dataStatus,
                            "isShowLastYear": this.isShowLastYear
                        }
                        queryByPageOneLevel(query).then(res => {
                            if (JSON.stringify(response.data) != "{}") {
                                this.columnListData = res.data.records
                                this.$nextTick(() => {
                                    this.$refs.multipleTable.doLayout();
                                })
                                this.total = res.data.total
                                this.columnListData.map((item, index) => {
                                    item.children = []
                                    item.crossTableSameDate != null ? item.children.push(Object.assign(item.crossTableSameDate, { dataId: item.dataId + '51' + index, orgCode: '0' })) : ''
                                    item.crossTableSameDateDiff != null ? item.children.push(Object.assign(item.crossTableSameDateDiff, { dataId: item.dataId + '52' + index, orgCode: '0' })) : ''
                                    item.crossTableLastDate != null ? item.children.push(Object.assign(item.crossTableLastDate, { dataId: item.dataId + '53' + index, orgCode: '0' })) : ''
                                    item.crossTableLastDateDiff != null ? item.children.push(Object.assign(item.crossTableLastDateDiff, { dataId: item.dataId + '54' + index, orgCode: '0' })) : ''
                                    item.sameTableLastDate != null ? item.children.push(Object.assign(item.sameTableLastDate, { dataId: item.dataId + '55' + index, orgCode: '0' })) : ''
                                    item.sameTableLastDateDiff != null ? item.children.push(Object.assign(item.sameTableLastDateDiff, { dataId: item.dataId + '56' + index, orgCode: '0' })) : ''
                                    item.sameTableSameDate != null ? item.children.push(Object.assign(item.sameTableSameDate, { dataId: item.dataId + '57' + index, orgCode: '0' })) : ''
                                    item.sameTableSameDateDiff != null ? item.children.push(Object.assign(item.sameTableSameDateDiff, { dataId: item.dataId + '58' + index, orgCode: '0' })) : ''
                                    item.columnA = item.orgName
                                })
                            }
                        })
                    }
                })
            })
            this.flagStatus1 = true
            this.flagStatus2 = false
            this.flagStatus3 = false
            this.flagStatus4 = false
            this.showFill = false
            // }
            // })
        },
        removeUnitNodes(nodes) {
            for (let i = nodes.length - 1; i >= 0; i--) {
                const node = nodes[i];
                // 先递归处理子节点
                if (node.children && node.children.length > 0) {
                    this.removeUnitNodes(node.children);
                }
                // 删除当前节点（从后向前遍历避免索引错位）
               if (this.tableLength == 3 && this.tableLastLength == 2) {
                 if (node.headerElementName.stream == "单位") {
                   nodes.splice(i, 1);
                 }
               } else {
                 if (node.headerElementName == "单位") {
                   nodes.splice(i, 1);
                 }
               }
            }
        },
        selectChange(val) {
            this.options.map(item => {
                if (val == item.fileName) {
                    this.queryParams.fileName = item.fileName
                    this.queryParams.orgCodefile = item.orgCode
                    this.title = item.fileName
                }
            })
            this.handleQuery()
        },
        resetQuery() {
            // let time = new Date()
            // this.queryParams.statDate = this.formatDateTime(time)
            // this.form.statDate = this.formatDateTime(time)
            // this.$set(this.form, "statDate", this.formatDateTime(time))
            // this.queryParams.orgCode = getInitTreeCode(this.orgTreeList)
            // this.queryParams.fileName = null
            this.queryParams.page = 1
            this.queryParams.rows = 20
            this.queryParams.taskBeginTime = null
            this.queryParams.taskEndTime = null
            this.queryParams.statDate = null
            if (this.paramsRouter.source == 'year') {
                this.queryParams.statYear = new Date().getFullYear().toString()
            } else {
                this.queryParams.statYear = null
            }
            this.queryParams.dataStatus = null
            this.btnFlag = true
            this.single = true
            this.levelFlag = true
            this.selectBox = []
            // this.switchList()
            this.queryParams.orgCode = getInitTreeCode(this.orgTreeList)
            this.handleQuery()
        },
        unique(arr) {
            var set = new Set(arr);
            //方法1：
            return Array.from(set);
            //方法2：把set打散放进去
            var newArr = [...set];
            return newArr;
        },
        deleteRepeat(arr) {
            arr = arr.map(item => {
                return JSON.stringify(item);
            });
            return Array.from(new Set(arr)).map(item => {
                return JSON.parse(item);
            });
        },
        sortArray(array) {
            array.sort((a, b) => {
                if (a.length !== b.length) {
                    return a.length - b.length
                } else {
                    return a.localeCompare(b);
                }
            })
            return array
        },
        addData() {
            this.open = true
            this.form.orgCode = getInitTreeCode(this.orgTreeList)
            if (this.paramsRouter.source == 'year') {
                this.form.statYear = new Date().getFullYear().toString()
            } else {
                let time = new Date()
                this.form.statDate = this.formatDateTime(time)
                // this.$set(this.form, "statDate", this.formatDateTime(time))
                this.form.statDate = this.formatDateTime(time)
            }
            // this.$set(this.form, "statDate", this.formatDateTime(time))
            this.form.statDate = this.formatDateTime(time)
            this.form.column.map(item => {
                item.list.map(t => {
                    if (t.columnType != '3') {
                        t.columnValue = '0'
                    } else if (t.columnType === '3') {
                        t.columnValue = null
                    }
                    if (t.columnType == '4') {
                        t.fileupload = []
                    }
                    this.selectRowData['column' + t.columnList] = t.columnValue
                })
            })
        },
        cancel() {
            this.open = false
            this.title = this.queryParams.fileName
            this.disabledOrg = false
            this.switchList()
            this.switchType = ''
            this.single = true
            this.levelFlag = true
            this.selectBox = []
            this.$refs.FormRef.clearValidate()
            this.updateFlag = false
        },
        cancelPreview() {
            this.openPreview = false
        },
        cancelMutiplePreview() {
            this.openMutiplePreview = false
            this.previewMutipleTable = []
        },
        formatDateTime(date) {
            var y = date.getFullYear();
            var m = date.getMonth() + 1;
            m = m < 10 ? ('0' + m) : m;
            var d = date.getDate();
            d = d < 10 ? ('0' + d) : d;
            return y + '-' + m + '-' + d;
        },
        submitForm() {
            this.errorMessage = {}
            this.$refs["FormRef"].validate(valid => {
                if (valid) {
                    let arrB = []
                    let sheetId = ''
                    this.form.column.map(item => {
                        item.list.map(itemB => {
                            if (itemB.columnType == '4') {
                                itemB.columnValue = ''
                                if (itemB.fileupload && itemB.fileupload.length > 0) {
                                    let urlArr = []
                                    itemB.fileupload.map(urlItem => {
                                        urlArr.push(urlItem.url)
                                    })
                                    itemB.columnValue = urlArr.join(',')
                                }
                            }
                            if (itemB.columnType == '2' && itemB.isDictionary === '1') {
                                itemB.columnValue = Array.isArray(itemB.columnOptions) && itemB.columnOptions.length > 0 ? itemB.columnOptions.join(',') : ''
                            }
                            // if(itemB.columnList == 'A') {
                            //     itemB.columnValue = this.form.orgName
                            // }
                            arrB.push(itemB)
                            sheetId = itemB.sheetId
                        })
                    })
                    if (
                        this.form.orgCode !== null &&
                        this.form.orgCode &&
                        typeof this.form.orgCode !== 'string'
                    ) {
                        this.form.orgCode =
                            this.form.orgCode[this.form.orgCode.length - 1]
                    }
                    let params = {}
                    if (this.paramsRouter.source == 'year') {
                        params = {
                            "rptColumnInfoList": arrB,
                            "orgCode": this.form.orgCode,
                            "sheetId": sheetId,
                            "statYear": this.form.statYear
                        }
                    } else {
                        params = {
                            "rptColumnInfoList": arrB,
                            "orgCode": this.form.orgCode,
                            "sheetId": sheetId,
                            "statDate": this.form.statDate
                        }
                    }
                    if (this.title.slice(0, 2) == '修改') {
                        let errorNameL = []
                        let errorNameN = []
                        let errorNameK = []
                        this.form.column.map(item => {
                            item.list.map(t => {
                                let label = 'column' + t.columnList
                                this.selectRowData[label] = t.columnValue
                                let reg = /^([0-9]{1}|^[1-9]{1}\d{1,15})(\.\d{1,6})?$/
                                if (t.columnType == '1') { // 字段是数值类型
                                    let value = t.columnValue
                                    if (value === null || value === '' || typeof (value) == 'undefined') {
                                        errorNameK.push(t.columnName)
                                    } else if (reg.test(value) && value.toString().split('.')[0].length > 10) {
                                        errorNameL.push(t.columnName)
                                    } else if (reg.test(value)) {
                                    } else {
                                        errorNameN.push(t.columnName)
                                    }
                                }
                            })
                        })
                        if (errorNameL.length > 0) {
                            this.$message.error(errorNameL.join(',') + "数字过大，请确认")
                            return false
                        } else if (errorNameN.length > 0) {
                            this.$message.error(errorNameN.join(',') + "请输入数字(可带六位小数)")
                            return false
                        } else if (errorNameK.length > 0) {
                            this.$message.error(errorNameK.join(',') + "不可为空")
                            return false
                        }
                        if (this.paramsRouter.source == 'year') {
                            this.selectRowData.statYear = this.form.statYear
                        } else {
                            this.selectRowData.statDate = this.form.statDate
                        }
                        // 二次确认
                        tips(this.selectRowData).then(tip => {
                            if (tip.success == true) {
                                if (tip.data.tips) { // true 二次确认
                                    let _this = this
                                    _this.$confirm(tip.data.message, "提示", {
                                        confirmButtonText: "确定",
                                        cancelButtonText: "取消",
                                        type: "warning"
                                    }).then(function () {
                                        rptColumnLimitInfo(_this.selectRowData).then(response => {
                                            if (response.success == true) {
                                                if (response.data.checkSuccess == 1) {
                                                    update(_this.selectRowData).then(response => {
                                                        if (response.success == true) {
                                                            _this.$message.success('修改成功')
                                                            _this.open = false
                                                            _this.switchList()
                                                            _this.switchType = ''
                                                            _this.single = true
                                                            _this.levelFlag = true
                                                            _this.selectBox = []
                                                            _this.$refs.FormRef.clearValidate()
                                                            _this.updateFlag = false
                                                        } else {
                                                            _this.$message.error(response.msg);
                                                        }
                                                    })
                                                } else {
                                                    _this.errorMessage = response.data.data
                                                    let obj = Object.values(_this.errorMessage)
                                                    _this.$message.error(obj[0]);
                                                }
                                            }
                                        })
                                    }).catch(() => {
                                        // _this.open = false
                                        // _this.switchList()
                                        // _this.single = true
                                        // _this.levelFlag = true
                                        // _this.selectBox = []
                                        // _this.$refs.FormRef.clearValidate()
                                        // _this.updateFlag = false
                                    });
                                } else {
                                    rptColumnLimitInfo(this.selectRowData).then(response => {
                                        if (response.success == true) {
                                            if (response.data.checkSuccess == 1) {
                                                update(this.selectRowData).then(response => {
                                                    if (response.success == true) {
                                                        this.$message.success('修改成功')
                                                        this.open = false
                                                        this.switchList()
                                                        this.switchType = ''
                                                        this.single = true
                                                        this.levelFlag = true
                                                        this.selectBox = []
                                                        this.$refs.FormRef.clearValidate()
                                                        this.updateFlag = false
                                                    } else {
                                                        this.$message.error(response.msg);
                                                    }
                                                })
                                            } else {
                                                this.errorMessage = response.data.data
                                                let obj = Object.values(this.errorMessage)
                                                this.$message.error(obj[0]);
                                            }
                                        }
                                    })
                                }
                            }
                        })
                    } else {
                        fillIn(params).then(response => {
                            if (response.success == true) {
                                this.$message.success('填报成功')
                                this.open = false
                                this.switchList()
                                this.switchType = ''
                                this.single = true
                                this.levelFlag = true
                                this.selectBox = []
                                this.$refs.FormRef.clearValidate()
                            } else {
                                this.$message.error(response.msg);
                            }
                        })
                    }
                }
            })
        },
        forceUpdate(val) {
            this.$forceUpdate()
        },
        timeChange(val) {
            this.updateFlag = true
            this.$forceUpdate()
        },
        handleDelete(row) {
            const dataId = this.selectBox.dataId
            const dataCode = this.selectBox.orgCode
            const dataName = this.selectBox.orgName
            // if(dataCode.length != 4 && this.fillTypeFlag === '1') {
            //     this.$message.error("不能选择非分公司数据")
            //     return false
            // }
            let _this = this

            this.$confirm('是否确认删除所属单位为"' + dataName + '"的数据项?', "警告", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(function () {
                logicDeleteById(dataId).then(response => {
                    if (response.success == true) {
                        _this.getListInitial();
                        _this.$message.success("删除成功");
                    }
                })
            }).then(() => {
            })
        },
        tableRowClassName({ row, rowIndex }) {
            let rowClass = ""
            if (row.dataStatus == "03" && rowIndex == 0) {
                rowClass = "fixed-row-danger"
            } else if (row.dataStatus == "03") {
                rowClass = "danger";
            } else if (rowIndex == 0) {
                rowClass = "fixed-row";
            }
            return rowClass;
        },
        handleHeaderDragend() {
            this.$nextTick(() => {
                this.$refs.multipleTable.doLayout();
            })
        }
    }
}
</script>

<style scoped lang="scss">
.tooltip .question.el-icon {
    position: absolute;
    right: -25px;
    top: 12px
}

table {
    border-collapse: collapse;
    width: 100%;
}

td {
    border: 1px solid #ACBED1;
    text-align: center;
    min-width: 55px;
}

.addWidth {
    width: 150px !important;
}

.headLine td {
    background-color: #f5f7fa;
    /* color:#515a6e; */
    font-size: 13px;
}

.rowLine td {
    /* color:#606266; */
    font-size: 14px;
}
</style>
<style>
/* 隐藏样式  >>> 根据需要添加(如果用的脚手架样式不能覆盖) */
.hide_box .el-upload--picture-card {
    display: none !important;
}

.small-upload .el-upload-list__item {
    width: 80px;
    height: 80px;
}

.small-upload .el-upload {
    width: 80px;
    height: 80px;
}

.mapDiv .el-col .el-form-item .el-form-item__content>div:nth-child(2) {
    display: none;
}

.mapDiv .el-col .el-form-item .el-form-item__content>div.el-form-item__error {
    display: block !important;
}

.reportTable .el-table__header .el-table__cell {
    padding: 3px 0 !important;
}

/* 这里添加固定行的样式设置，不定位也可以根据需求修改行样式 */
.el-table .fixed-row {
    /* 粘性定位 */
    position: sticky;
    position: -webkit-sticky;
    top: 0;
    z-index: 3;
}

.el-table .fixed-row-danger {
    /* 粘性定位 */
    position: sticky;
    position: -webkit-sticky;
    top: 0;
    z-index: 3;
    color: #F56C6C;
}

.large-text.el-tooltip__popper {
    font-size: 18px !important;
}

.downTitle {
    margin: 10px 0;
}

.downTitle::before {
    display: inline-block;
    margin-right: 4px;
    font-family: SimSun, sans-serif;
    font-size: 14px;
    line-height: 1;
    color: #ff4d4f;
    content: '*';
}

.checkImgList {
    height: 150px;
    overflow: auto;
}

.checkImgList .el-checkbox {
    display: block !important;
}
</style>
