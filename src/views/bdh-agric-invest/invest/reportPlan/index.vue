<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form :model="queryParams" ref="queryRef" v-show="showSearch" label-width="80px" class="form-line">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年份" prop="year">
                  <el-select v-model="queryParams.year" clearable placeholder="请选择年份">
                      <el-option
                          v-for="dict in yearOption"
                          :key="dict.code"
                          :label="dict.name"
                          :value="dict.code"
                      />
                  </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所在单位" prop="orgCode">
                <org-select ref="queryOrgRef" @handleOrgCode="handleOrgCode" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业名称" prop="aiCompanyId">
                <el-select v-model="queryParams.aiCompanyId" clearable placeholder="请选择企业简称">
                  <el-option
                      v-for="dict in companyNameOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="作业季节" prop="fertilizeSeason">
                <el-select
                    v-model="queryParams.fertilizeSeason"
                    clearable
                    placeholder="请选择作业季节">
                  <el-option
                      v-for="dict in fertilizes"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="审核状态" prop="auditStatus">
                <el-select v-model="queryParams.auditStatus" clearable placeholder="全部">
                  <el-option
                      v-for="dict in auditStatusOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="18" align="right">
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>


    <!-- 多种操作 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['reportPlan:reportTypeSubList']"
        >生成采购计划
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['reportPlan:logicDeleteByIds']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="Search"
          @click="handleReportAll"
          v-hasPermi="['reportPlan:reported']"
          >投入品订购计划
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            icon="Check"
            :disabled="single"
            @click="handleApprove"
            v-hasPermi="['reportPlan:approve']"
        >审核
        </el-button>
      </el-col>
<!--      <el-col :span="1.5">
        <el-button
            icon="Delete"
            @click="handleDelete"
            v-hasPermi="['reportPlan:logicDeleteByIdsSpec']"
        >仅删除计划【运维专用】
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            icon="Delete"
            @click="handleDeleteSpec"
            v-hasPermi="['reportPlan:logicDeleteByIdsSpec']"
        >删除计划及上报数据【运维专用】
        </el-button>
      </el-col>-->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 分页查询表单 -->
    <el-table border :height="tableHeight"  :data="reportInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="ID" align="center" width="55" prop="reportPlanId"/>
      <el-table-column label="年份" align="center" prop="year" width="100" />
      <el-table-column label="作业季节" align="center" prop="fertilizeSeason" :formatter="fertilizeSeasonFmt"/>
      <el-table-column label="所在单位" align="center" prop="orgName"/>
      <el-table-column label="企业名称" align="center" prop="companyAbbr"/>
      <el-table-column label="本次订购数量" align="center" prop="currentNum"/>
      <el-table-column label="上报时间" align="center" prop="reportTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.reportTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="审核状态" prop="auditStatus" :formatter="auditStatuFormat"/>
      <el-table-column label="当前审核人" align="center" :show-overflow-tooltip="true"  :formatter="fmtAuditName" />
      <el-table-column label="当前流程节点" align="center" :formatter="fmtAuditNode" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-link type="primary" icon="View" @click="handleView(scope.row)">查看</el-link>
          <el-link type="danger" icon="Delete" v-hasPermi="['reportPlan:logicDeleteByIds']" v-if="scope.row.auditStatus==4" @click="handleDelete(scope.row)">删除</el-link>
          <el-link type="success" icon="User" @click="viewFlow(scope.row)">查看流程</el-link>
        </template>
      </el-table-column>
    </el-table>

    <!--   分页查询底部栏     -->
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.rows"
      @pagination="getList"
    />

    <!-- 添加或修改投入品采购上报对话框 -->
    <el-dialog :title="title" v-model="open"  :width="1200"  :close-on-click-modal="false"  append-to-body>
      <report-plan-detail v-if="flag" ref="detail" @cancelFn="cancel" @submitFormFn="submitFormDebounce"></report-plan-detail>
      <!-- <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFormDebounce">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template> -->
    </el-dialog>

    <el-dialog :title="title" v-model="openInfoView" :width="1200"  :close-on-click-modal="false" append-to-body>
      <report-plan-view :reportPlanId="form.reportPlanId" v-if="openInfoFlag"></report-plan-view>
    </el-dialog>

    <el-dialog :title="title" v-model="openInfoAllView" :width="1200"  :close-on-click-modal="false" append-to-body>
      <report-plan-all v-if="openInfoAllFlag"></report-plan-all>
    </el-dialog>

    <!--审核-->
    <el-dialog title="审核" v-model="openApproveView"  append-to-body :close-on-click-modal="false">
      <el-form ref="approveForm" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="是否通过" prop="agree">
          <el-radio v-model="form.agree" :label="true">同意</el-radio>
          <el-radio v-model="form.agree" :label="false">不同意</el-radio>
        </el-form-item>
        <el-form-item label="审核意见" prop="opinion">
           <el-input v-model="form.opinion" type="textarea"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitApproveDebounce">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看流程对话框 -->
    <!-- 查看流程对话框 -->
    <el-dialog
        v-model="processVisible"
        title="查看流程"
        width="800px"
        :close-on-click-modal="false"
    >
      <FlowDetail v-model="form.instId" v-if="flowDetailTag" ></FlowDetail>
      <template #footer>
                <span class="dialog-footer">
                    <el-button @click="processVisible = false">关闭</el-button>
                </span>
      </template>
    </el-dialog>


  </div>
</template>

<script setup name="/invest/prodReportPlan/queryByPage">

import reportPlanDetail from './reportPlanDetail'
import reportPlanView from './reportPlanView'
import reportPlanAll from './reportPlanAll'
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import {listReportPlan,
  report2planProdList,
  getOne,
  getReportPlan,
  addReportPlan,
  updateReportPlan,
  delReportPlan,
  delReportPlans,
  delReportPlansSpec,
  reportedPlanList,
  approve} from "@/api/bdh-agric-invest/invest/reportPlan";

import {allCompanyInfo} from '@/api/bdh-agric-invest/invest/companyInfo'
import FlowDetail from "@/views/bdh-agric-invest/components/FlowDetail/index.vue";
import {getDicts, queryAllOrgTree} from '@/api/bdh-agric-invest/system/dict/data'
import {debounce} from "lodash";
import {ref} from "vue";
const {proxy} = getCurrentInstance();

const fertilizes = ref([]);
const reportInfoList = ref([]);
const open = ref(false);
const openInfoView = ref(false);
const openInfoFlag = ref(true);
const openInfoAllView = ref(false);
const openInfoAllFlag = ref(true);
const openApproveView = ref(false);
const openApproveFlag = ref(true);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const flowDetailTag=ref(false);
const total = ref(0);
const title = ref("");
const yearOption = ref([]);
const companyInfoOption = ref([]);
const companyNameOption = ref([]);
const companyNameValue = ref("");
const treeOptions = ref([]);
const auditStatusOption = ref([]);
const processVisible = ref(false);
const detail=ref(null)
const instId=ref(null)
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const selectProps = ref({
  value: "orgCode",
  label: "orgName",
  checkStrictly : true
});
const data = reactive({
  flag:true,
  form: {},
  year: null,
  queryParams: {
    rows: 10,
    page: 1,
    year: null,
    orgName: null,
    orgCode: null,
    aiCompanyId: null,
    companyAbbr: null,
    auditStatus: null,
  },
  rules:{
    agree: [
      {required: true, message: "请选择是否同意", trigger: "change"}
    ],
    opinion:[{message:"请输入审批意见",
      validator:function(rule, value, callback){
        if(!value&&!form.value.agree){
          callback("请输入审核意见！")
        }else{
          callback()
        }
      },blur},{max:100,message: "最多100个字符",trigger:"blur"}]
  }
});
const queryOrgRef = ref()
const defaultOrgCode = ref('');

const {queryParams, form,year,flag,rules} = toRefs(data);

onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});

function fmtAuditName(row,column){
  if(row.flowMsg&&JSON.parse(row.flowMsg).auditNames){
    return JSON.parse(row.flowMsg).auditNames
  }
  return "无";
}

function fmtAuditNode(row,column){
  if(row.flowMsg&&JSON.parse(row.flowMsg).node){
    return JSON.parse(row.flowMsg).node
  }
  return "无";
}


function viewFlow(row){
  form.value=row
  flowDetailTag.value = false
  nextTick(() => {
    flowDetailTag.value=true;
  });
  processVisible.value=true
}

//表格审核状态栏字典转换
function auditStatuFormat(row, column) {
  let auditStatus = null
  if (auditStatusOption.value.length > 0) {
    auditStatusOption.value.forEach((v, i) => {
      if (v.code == row.auditStatus) {
        auditStatus = v.name
      }
    })
  }
  return auditStatus;
}

const handleOrgCode = ({orgCode,orgName})=>{
  queryParams.value.orgCode=orgCode
}
// 获取字典
const getDict = () => {
  //查询年份
  getDicts('year_cd').then(response => {
    yearOption.value = response.data
    queryParams.value.year = yearOption.value[0].code
        queryAllOrgTree("3").then((res) => {
            var orgList = res.data;
            if (orgList && orgList.length && orgList.length > 0) {
                defaultOrgCode.value = orgList[0].orgCode;
                queryParams.value.orgCode = defaultOrgCode.value;
            }
            getList()
        })
  });

  //企业简称
  allCompanyInfo({}).then(response => {
    companyInfoOption.value = response.data
    //console.info(companyInfoOption.value);
    response.data.forEach(each => {
      companyNameOption.value.push({
        code: each.aiCompanyId,
        name: each.companyAbbr
      })
      //console.log(each)
    })
  });

  //审核状态
  getDicts('agric_invest_audit_status').then(response => {
    auditStatusOption.value = response.data
  });

  getDicts("fertilize_season").then((response) => {
    fertilizes.value = response.data
  });
}


function fertilizeSeasonFmt(row){
  for(let i=0;i<fertilizes.value.length;i++){
    if(fertilizes.value[i].code==row.fertilizeSeason){
      return fertilizes.value[i].name
    }
  }
}


/** 查询投入品采购上报列表 */
function getList() {
  if(year.value){
    queryParams.value.year=year.value.getFullYear()
  }

  listReportPlan(queryParams.value).then(response => {
    reportInfoList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    updateBy: null,
    updateTime: null,
    reportPlanId: null,
    year: null,
    orgCode: null,
    orgName: null,
    aiCompanyId: null,
    reportTime: null,
    auditStatus: null,
    auditPerson: null,
    remark: null,
    createBy: null,
    createTime: null,
    statusCd: null,
    detail:null,
    agree:true,
    opinion:null
  };
  proxy.resetForm("reportInfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  // queryParams.value.rows = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  year.value=null
  proxy.$refs['queryOrgRef'].clear()
  queryParams.value.year = yearOption.value[0].code;
  queryParams.value.orgCode = defaultOrgCode.value;
  queryOrgRef.value.setValue(defaultOrgCode.value, "");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.reportPlanId);
  single.value = selection.length != 1;

  if(selection.length != 1){
    single.value=true;
  }else{
    //auditStatus==0
    let canAudit=[]
    canAudit=selection.filter(item=>item.auditStatus==0)

    single.value=!(canAudit.length==1)
    if(!single.value){
      instId.value=selection.map(item => item.instId)[0]
    }
  }
  //审核不通过的才可以删除
  let canRemove=[];

  if(selection&&selection.length){
    //如果选择了，就判断一下选择的数据里 是否有不允许删除的变量
    canRemove=selection.filter(item=>item.auditStatus==4)
    multiple.value = !(canRemove&&canRemove.length==selection.length)
  }else{
    multiple.value = true
  }
}

function handleApprove(){
  if(ids.value){
    form.value.reportPlanId=ids.value[0]
    form.value.instId=instId;
    form.value.agree=true
    form.value.opinion=null
    openApproveView.value=true;
  }
}

const submitApproveDebounce = debounce(submitApproveForm,500);
function submitApproveForm(){
  proxy.$refs["approveForm"].validate(valid => {
    if (valid) {
      let apprveForm={reportPlanId:form.value.reportPlanId,
        agree:form.value.agree,instId:form.value.instId,
        opinion:form.value.opinion}
      approve(apprveForm).then(response=>{
        if(response.success){
          openApproveView.value=false;
          proxy.$modal.msgSuccess("成功!");
          getList();
        }
      })
    }
  })
}


/** 新增按钮操作 */
function handleAdd() {
  reset();
  flag.value=false;
  nextTick(() => {
    flag.value=true;
  });
  open.value = true;
  title.value = "添加投入品采购上报计划";
}

/** 修改按钮操作 */
function handleView(row) {
  reset();
  const reportPlanId = row.reportPlanId || ids.value
  getReportPlan(reportPlanId).then(response => {
    form.value = response.data;
    openInfoFlag.value=false;
    nextTick(() => {
      openInfoFlag.value=true;
    });
    title.value = "查看投入品采购上报计划";
    openInfoView.value=true;
  });
}


function handleReportAll(){
  openInfoAllFlag.value=false;
  nextTick(() => {
    openInfoAllFlag.value=true;
  });
  title.value = "查看投入品采购上报计划";
  openInfoAllView.value=true;
}

/** 提交按钮 */
const submitFormDebounce = debounce(submitForm,500);
function submitForm() {

  detail.value=proxy.$refs.detail.validForm();
  if(!detail.value){
    return
  }

  if(!detail.value){
    proxy.$modal.msgError("表单为空!");
    return
  }

  if(!detail.value.year){
    proxy.$modal.msgError("请输入年份!");
    return
  }

  if(!detail.value.aiCompanyId){
    proxy.$modal.msgError("请选择企业!");
    return
  }

  if(!detail.value.fertilizeSeason){
    proxy.$modal.msgError("请选择作业季节!");
    return
  }

  if(!detail.value.details||detail.value.details.length==0){
    proxy.$modal.msgError("上报详情为空，请仔细检查数据!");
    return
  }

  let hasCurrentNum=false;
  let list=[]
  detail.value.details.forEach(function(item){
    if(item.currentNum!=null && item.currentNum!=0){
      hasCurrentNum=true
      list.push(item);
    }
  });

  if(!hasCurrentNum){
    proxy.$modal.msgError("请至少选择一个商品，输入本次订购量!");
    return
  }

  //currentNum > 0 才生成上报
  let iform={}
  iform.year=detail.value.year;
  iform.aiCompanyId=detail.value.aiCompanyId
  iform.fertilizeSeason=detail.value.fertilizeSeason
  iform.details=list;
  iform.report2planIds=detail.value.report2planIds
  addReportPlan(iform).then(response=>{
    if(response.success){
      proxy.$modal.msgSuccess("成功!");
      open.value = false;
      getList();
    }else{
      proxy.$modal.msgError(response.msg)
    }
  })
}

/** 删除按钮操作 */
function handleDelete(row) {
  var prodReportIds = null;
  if(row&&row.reportPlanId){
    prodReportIds=[row.reportPlanId]
  }else{
    prodReportIds=ids.value
  }
  console.info('-----',prodReportIds);
  if(prodReportIds==null||prodReportIds.length==0){
    proxy.$modal.msgError("请选择待删除的计划");
    return
  }
  proxy.$modal.confirm('是否确认删除？').then(function () {
    return delReportPlans(prodReportIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 删除计划，及对应的上报数据 */
function handleDeleteSpec(){
  var prodReportIds=ids.value
  console.info('-----',prodReportIds);
  if(prodReportIds==null||prodReportIds.length==0){
    proxy.$modal.msgError("请选择待删除的计划");
    return
  }
  proxy.$modal.confirm('您确认删除本条计划，以及计划对应的所有管理区上报数据吗？').then(function () {
    return delReportPlansSpec(prodReportIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('invest/reportInfo/export', {
    ...queryParams.value
  }, `reportInfo_${new Date().getTime()}.xlsx`)
}

getDict();
</script>
<style scoped>
.el-link {
  margin-right: 8px;
}
.el-link .el-icon--right.el-icon {
  vertical-align: text-bottom;
}
</style>
