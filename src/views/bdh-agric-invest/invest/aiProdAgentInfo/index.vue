<!--
@name: 投入品上报
@description:
@author: manchunyu
@time: 2022-12-02
-->
<template>
  <div class="app-container">
    <!-- 筛选条件及搜索栏 -->
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form
          v-show="showSearch"
          ref="queryRef"
          :inline="true"
          :model="_this.queryParams"
          label-width="80px"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年份" prop="year">
                <el-select
                  v-model="_this.queryParams.year"
                  placeholder="请选择年份"
                  @change="handleQuery"
                >
                  <el-option
                    v-for="dict in _this.yearOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所在单位" prop="orgCode">
                <org-select
                  ref="queryOrgRef"
                  :defaultOrgCode="true"
                  @handleOrgCode="handleOrgCodeQuery"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="农户姓名" prop="farmerName">
                <el-input
                  v-model="_this.queryParams.farmerName"
                  placeholder="请输入农户姓名"
                  maxlength="100"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="身份证号" prop="idIccid">
                <el-input
                  v-model="_this.queryParams.idIccid"
                  placeholder="请输入农户身份证号"
                  maxlength="100"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业名称" prop="aiCompanyId">
                <el-select
                  v-model="_this.queryParams.aiCompanyId"
                  placeholder="请选择企业名称"
                  @change="chgCompany"
                >
                  <el-option
                    v-for="dict in _this.companyAbbrOption"
                    :key="dict.aiCompanyId"
                    :label="dict.companyAbbr"
                    :value="dict.aiCompanyId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="作业季节" prop="season">
                <el-select
                  v-model="_this.queryParams.season"
                  placeholder="请选择作业季节"
                  @change="handleQuery"
                >
                  <el-option
                    v-for="(val, key) in _this.fertilizes"
                    :key="key"
                    :label="val"
                    :value="key"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="商品分类" prop="aiTypeId">
                <ProduceTypeDynamic
                  :companyId="_this.queryParams.aiCompanyId"
                  @handleTypeId="handleTypeId"
                  ref="produceTypeRef"
                ></ProduceTypeDynamic>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="商品名称" prop="aiTypeSubName">
                <el-input
                  v-model="_this.queryParams.aiTypeSubName"
                  placeholder="请输入商品名称"
                  maxlength="100"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="确认状态" prop="confirmStatus">
                <el-select
                  v-model="_this.queryParams.confirmStatus"
                  placeholder="全部"
                  @change="handleQuery"
                  clearable
                >
                  <el-option
                    v-for="(val, key) in _this.confirmStatus"
                    :key="key"
                    :label="val"
                    :value="key"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="审批状态" prop="auditStatus">
                <el-select
                  v-model="_this.queryParams.auditStatus"
                  clearable
                  placeholder="全部"
                >
                  <el-option
                    v-for="(val, key) in _this.auditStatusOption"
                    :key="key"
                    :label="val"
                    :value="key"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6" :push="6" align="right">
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['aiprodagentinfo#batch']"
          >新增
        </el-button>
        <el-button
          icon="Edit"
          :disabled="_this.selection.length != 1"
          v-hasPermi="['aiprodagentinfo#modify']"
          @click="handleEdit(false)"
          >修改
        </el-button>
        <el-button
          icon="Delete"
          :disabled="_this.selection.length == 0"
          @click="handleDelete"
          v-hasPermi="['aiprodagentinfo#logicDeleteByIds']"
          >删除
        </el-button>
        <el-button
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['aiprodagentinfo#exportXls']"
          >导入</el-button
        >
        <el-button
          @click="handleExport"
          v-hasPermi="['aiprodagentinfo#export']"
          >导出
        </el-button>
        <el-button
          icon="Check"
          @click="handleApprove(false)"
          :disabled="_this.selection.length != 1"
          v-hasPermi="['aiprodagentinfo#approve']"
          >审核
        </el-button>
<!--        <el-button
          type="success"
          plain
          icon="Check"
          @click="handleAudit(false)"
          :disabled="_this.selection.length != 1"
          v-hasPermi="['aiprodagentinfo#audit']"
          >启动审核
        </el-button>-->
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <!-- 查询表格 -->
    <div v-text="_this.title" class="title"></div>
    <el-table
      border
      :height="tableHeight"

      :data="_this.consigneeInfoList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <ColumnItem :columns="_this.columns" />
      <el-table-column
        label="操作"
        align="center"
        width="150"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            type="primary"
            link
            icon="Edit"
            @click="handleEdit(scope.row)"
            v-hasPermi="['aiprodagentinfo#modify']"
            >编辑
          </el-button>
          <!--发起流程-->
          <el-button
              v-if="scope.row.auditStatus === -1&&scope.row.confirmStatus===1"
              type="primary"
              link
              icon="CircleCheck"
              @click="handleStartFlow(scope.row)"
              v-hasPermi="['aiprodagentinfo#audit']"
          >发起流程
          </el-button>
          <el-link type="success" icon="User" @click="viewFlow(scope.row)" v-if="scope.row.instId"
            >查看流程</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页查询底部选页栏 -->
    <pagination
      v-show="_this.total > 0"
      v-model:limit="_this.queryParams.rows"
      v-model:page="_this.queryParams.page"
      :total="_this.total"
      @pagination="getList"
    />
    <!-- 查看流程对话框 -->
    <el-dialog v-model="_this.processVisible" title="查看流程" width="800px" :close-on-click-modal="false">
      <FlowDetail
        v-model="_this.flow.instId"
        v-if="_this.processVisible"
      ></FlowDetail>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="_this.processVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 添加或修改公示配置管理表对话框 -->
    <add
      :dialogTitle="_this.dialogTitle"
      @success="getList"
      v-model:dialogOpen="_this.dialogOpen"
      v-if="_this.dialogOpen"
    />
    <edit
      :dialogTitle="_this.dialogEditTitle"
      v-model:dialogOpen="_this.dialogEditOpen"
      :prodAgentId="_this.prodAgentId"
      @success="getList"
      v-if="_this.dialogEditOpen"
    />
    <!-- 用户导入对话框 -->
    <el-dialog
      :title="upload.title"
      v-model="upload.open"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="prodImportFormRef" :model="importForm"  :rules="rulesImport" label-width="auto">
        <el-row  :gutter="10">
          <el-col :span="6">
            <el-form-item label="年份" prop="year">
              <el-select
                  v-model="importForm.year"
                  clearable
                  placeholder="请选择年份">
                <el-option
                    v-for="dict in _this.yearOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="作业季节" prop="fertilizeSeason">
              <el-select
                  v-model="importForm.fertilizeSeason"
                  clearable
                  placeholder="请选择作业季节">
                <el-option
                    v-for="(val, key) in _this.fertilizes"
                    :key="key"
                    :label="val"
                    :value="key"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="所在单位" prop="orgCode">
              <org-select  v-if="orgFlag" ref="importOrgRef" :defaultOrgCode="true"
                           :checkStrictly="false"
                           @handleOrgCode="handleImportOrgCode"/>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="企业简称" prop="aiCompanyId" label-width="auto">
              <el-select v-model="importForm.aiCompanyId"
                         clearable placeholder="请选择企业简称">
                <el-option
                    v-for="dict in _this.companyAbbrOption"
                    :key="dict.aiCompanyId"
                    :label="dict.companyAbbr"
                    :value="dict.aiCompanyId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text"><em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">

            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!--审核-->
    <el-dialog title="审核" v-model="_this.openApproveView" append-to-body :close-on-click-modal="false">
      <el-form
        ref="approveForm"
        :model="_this.form"
        :rules="_this.rulesApprove"
        label-width="100px"
      >
        <el-form-item label="是否通过" prop="agree">
          <el-radio v-model="_this.form.agree" :label="true">同意</el-radio>
          <el-radio v-model="_this.form.agree" :label="false">不同意</el-radio>
        </el-form-item>
        <el-form-item label="审核意见" prop="option">
          <el-input v-model="_this.form.option" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitApproveForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script name="/agent/aiprodagentinfo/queryPage" setup>
import { getToken } from "@/utils/auth";
import {
  queryPage,
  config,
  logicDeleteByIds,
  approve,
  audit,
} from "@/api/bdh-agric-invest/invest/aiProdAgentInfo";
import { allCompanyInfo } from "@/api/bdh-agric-invest/invest/companyInfo";
import { getDicts } from "@/api/bdh-agric-invest/system/dict/data";
import { postForExcel } from "@/api/bdh-agric-invest/invest/excel";
import {reactive, ref, toRefs} from "vue";
import ColumnItem from "@/views/bdh-agric-invest/components/columnItem";
import add from "./add";
import edit from "./edit";
import FlowDetail from "@/views/bdh-agric-invest/components/FlowDetail/index.vue";
const { proxy } = getCurrentInstance();
//所在单位
const queryOrgRef = ref();
//商品分类
const produceTypeRef = ref();
//审核表单
const approveForm = ref();
const orgFlag = ref(false)
const importOrgRef= ref();
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom

const data=reactive({
  //导入form
  importForm:{
    year: null,
    orgCode: null,
    fertilizeSeason:null,
    aiCompanyId: null,
  },

  //导入rules
  rulesImport: {
    year: [
      {required: true, message: "请输入年份", trigger: "change"}
    ],
    fertilizeSeason: [
      {required: true, message: "请选择作业季节", trigger: "change"}
    ],
    orgCode: [
      {required: true, message: "请输入收货单位", trigger: "change"}
    ],
    aiCompanyId: [
      {required: true, message: "请选择企业", trigger: "change"}
    ],
  },
})
const showSearch = ref(true);
const {importForm,rulesImport} = toRefs(data);
//页面数据
const _this = reactive({

  processVisible: false,
  // 确认状态
  confirmStatus: [],
  //表格数据
  consigneeInfoList: [],
  //加载状态
  loading: true,
  //年份
  yearOption: [],
  //季节数据
  fertilizes: [],
  //企业数据
  companyAbbrOption: [],
  //动态列头
  columns: [
    {
      label: "工单ID",
      prop: "prodAgentId",
    },
    {
      label: "年份",
      prop: "year",
    },
    {
      label: "所在单位",
      prop: "orgFullName",
    },
    {
      label: "农户姓名",
      prop: "farmerName",
    },
    {
      label: "身份证",
      prop: "idIccid",
    },
    {
      label: "企业简称",
      prop: "companyName",
    },
    {
      label: "作业季节",
      prop: "seasonLabel",
    },
    {
      label: "投入品分类",
      prop: "aiTypeName",
    },
    {
      label: "商品名称",
      prop: "aiTypeSubName",
    },
    {
      label: "订购数量",
      prop: "subscribeNum",
    },
    {
      label: "审核状态",
      prop: "auditStatusLabel",
    },
    {
      label: "确认状态",
      prop: "confirmStatusLabel",
    },
  ],
  //审核状态
  auditStatusOption: [],
  total: 0,
  queryParams: {
    year: "", //年份
    orgCode: "", //组织机构编码
    farmerName: "", //农户姓名
    idIccid: "", //身份证号
    aiCompanyId: null, //公司ID
    season: null, //作业季节Code
    aiTypeId: null, //商品类型id数组
    aiTypeSubName: "", //商品名称
    auditStatus: null, //审核状态: -1:未启动;0:审批中;1:审批通过;3:已撤销;4:审批拒绝
    confirmStatus: null, //确认状态 0：未确认 1：已确认
    page: 1, //请求页码
    rows: 10, //每页条数
  },
  //表格title
  title: "",
  dialogTitle: "农户订购信息",
  dialogOpen: false,
  dialogEditTitle: "农户订购信息",
  dialogEditOpen: false,
  flow: {},
  selection: [],
  form: {
    agree: true,
    option: "",
  },
  openApproveView: false,
  rulesApprove: {
    agree: [{ required: true, message: "请选择是否同意", trigger: "change" }],
    option: [
      { required: true, message: "请输入审核意见", trigger: "change" },
      { max: 100, message: "最多100个字符", trigger: "blur" },
    ],
  },
});
/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { "access-token": getToken() },
  // 上传的地址
  url: window.VITE_APP_BASE_API + "/agent/aiprodagentinfo/importXls",
});

onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});

const handleImportOrgCode = (orgInfo) => {
  if(!orgInfo.orgCode) importOrgRef.value.clear()
  importForm.value.orgCode=orgInfo.orgCode
}

/** 启动启动审核 */
function handleAudit() {
  var id = _this.selection[0].prodAgentId;
  proxy.$modal
    .confirm("确认发起流程么？")
    .then(function () {
      return audit(id);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("发起流程成功");
    })
    .catch(() => {});
}

function handleStartFlow(data){
  proxy.$modal.confirm('确认发起流程么？').then(function () {
    return audit(data.prodAgentId);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("发起流程成功");
  }).catch(() => {
  });
}

//审核按钮
function handleApprove(row) {
	_this.form= {
    agree: true,
    option: "",
  }
  _this.openApproveView = true;
}
//审核提交
function submitApproveForm() {
  approveForm.value.validate((valid) => {
    if (valid) {
      var id = _this.selection[0].instId;
      var obj = Object.assign({}, _this.form);
      obj.instId = id;
      approve(obj).then((response) => {
        if (response.success) {
          _this.openApproveView = false;
          proxy.$modal.msgSuccess("成功!");
          getList();
        }
      });
    }
  });
}
/** 删除按钮操作 */
function handleDelete() {
  var ids = [];
  _this.selection.forEach((element) => {
    ids.push(element.prodAgentId);
  });
  proxy.$modal
    .confirm("是否确认删除该信息？")
    .then(function () {
      return logicDeleteByIds(ids);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

// 多选框选中数据
function handleSelectionChange(selection) {
  _this.selection = selection;
}
//查看流程按钮
function viewFlow(row) {
  _this.flow = row;
  _this.processVisible = true;
}
//编辑按钮
function handleEdit(row) {
  if (row) {
    if(row.confirmStatus==1){
      proxy.$modal.msgError("已确认的订单不允许修改");
      return
    }
    if(row.auditStatus!=-1){
      proxy.$modal.msgError("发起审核的订单不允许修改");
      return
    }
    _this.prodAgentId = row.prodAgentId;
  } else {
    if(_this.selection[0].confirmStatus==1){
      proxy.$modal.msgError("已确认的订单不允许修改");
      return
    }
    if(_this.selection[0].auditStatus!=-1){
      proxy.$modal.msgError("发起审核的订单不允许修改");
      return
    }
    _this.prodAgentId = _this.selection[0].prodAgentId;
  }
  _this.dialogEditOpen = true;
}
//新增
function handleAdd() {
  _this.dialogOpen = true;
}
function chgCompany() {
  _this.queryParams.aiTypeId = null;
  handleQuery();
}
function handleTypeId(type) {
  _this.queryParams.aiTypeId = type.aiTypeId;
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "导入";
  upload.open = true;
  orgFlag.value = false;
  nextTick(() => {
    orgFlag.value = true;
  });
}
/** 下载模板操作 */
function importTemplate() {

  proxy.$refs["prodImportFormRef"].validate(valid => {
    console.info(importForm.value);
    if(valid){
      postForExcel('/agent/aiprodagentinfo/exportTmpl', importForm.value,'农户上报导入模板')
    }
  })

  /*proxy.download(
    window.VITE_APP_BASE_API + "/agent/aiprodagentinfo/exportTmpl",
    {},
    `投入品农户上报导入模板.xlsx`
  );*/
}
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].clearFiles();
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
      response.msg +
      "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};
/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}
/** 获取字典 */
const getDict = () => {
  //作业季节 确认状态 审批状态
  config().then((response) => {
    _this.fertilizes = response.data.fertilizeSeason; //作业季节
    _this.confirmStatus = response.data.confirmStatus; //确认状态
    _this.auditStatusOption = response.data.auditStatus; //审批状态
  });
  allCompanyInfo({}).then((response) => {
    _this.companyAbbrOption = response.data;
    //查询年份
    getDicts("year_cd").then((response) => {
      _this.yearOption = response.data.sort((a, b) => {
        return Number(b.code) - Number(a.code);
      });
      initSearch();
      getList();
    });
  });
};
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryOrgRef.value.clear();
  produceTypeRef.value.clear();
  initSearch();
  handleQuery();
}
//默认值赋值
function initSearch() {
  if (_this.fertilizes) {
    for (let key in _this.fertilizes) {
      _this.queryParams.season = key;
      break;
    }
  } else {
    _this.queryParams.season = null;
  }
  if (_this.companyAbbrOption.length > 0) {
    _this.queryParams.aiCompanyId = _this.companyAbbrOption[0].aiCompanyId;
  } else {
    _this.queryParams.aiCompanyId = null;
  }
  if (_this.yearOption.length > 0) {
    _this.queryParams.year = _this.yearOption[0].code;
  } else {
    _this.queryParams.year = null;
  }
  _this.queryParams.orgCode = null;
}
/** 搜索按钮操作 */
function handleQuery() {
	_this.queryParams.page = 1;
  getList();
}
//所在单位更改
const handleOrgCodeQuery = (orgInfo) => {
  if (!orgInfo.orgCode) queryOrgRef.value.clear();
  _this.queryParams.orgCode = orgInfo.orgCode;
};

/** 查询收货人列表 */
function getList() {
  _this.loading = true;
  var obj = Object.assign({}, _this.queryParams);
  if (obj.aiTypeId) {
    obj.aiTypeIds = [obj.aiTypeId];
  } else {
    obj.aiTypeIds = [];
  }
  delete obj.aiTypeId;
  queryPage(obj)
    .then((response) => {
      var data = response.data;
      _this.consigneeInfoList = data.records;
      console.info('_this.consigneeInfoList->',_this.consigneeInfoList);
      _this.loading = false;
      _this.total = data.total;
    })
    .catch((err) => {
      _this.loading = false;
      _this.consigneeInfoList = [];
    });
}

/** 导出按钮操作 */
function handleExport() {
  postForExcel(
    "/agent/aiprodagentinfo/exportXls",
    _this.queryParams,
    "投入品上报"
  );
}
getDict();
</script>
<style scoped>
.el-link {
  margin-right: 8px;
}

.el-link .el-icon--right.el-icon {
  vertical-align: text-bottom;
}
.mb8 {
  min-height: 30px;
}
.title {
  font-size: 20px;
  text-align: center;
  margin-bottom: 4px;
  font-weight: 600;
}
</style>
