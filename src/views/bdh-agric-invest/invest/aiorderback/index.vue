<!--
@name: 农户退货审核
@description:
@author: <PERSON><PERSON><PERSON><PERSON>
@time: 2023-09-07
-->
<template>
  <div class="app-container">
    <!-- 筛选条件及搜索栏 -->
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form
          v-show="showSearch"
          ref="queryRef"
          :model="_this.queryParams"
          label-width="80px"
          class="form-line"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="农户归属" prop="orgCode">
                <org-select
                    ref="queryOrgRef"
                    placeholderText="请选择农户归属"
                    @handleOrgCode="handleOrgCodeQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="审批状态" prop="returnStatus">
                <el-select
                    v-model="_this.queryParams.returnStatus"
                    placeholder="审批状态"
                    clearable
                >
                  <el-option
                      v-for="dict in _this.returnStatus"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="农户姓名" prop="farmerName">
                <el-input
                  v-model="_this.queryParams.farmerName"
                  placeholder="请输入农户姓名"
                  maxlength="100"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="订单编号" prop="orderNo">
                <el-input
                    v-model="_this.queryParams.orderNo"
                    placeholder="请输入订单编号"
                    maxlength="100"
                    clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="订购时间" style="font-weight: bold" prop="orderNo">
                <el-date-picker
                    v-model="dataRange"
                    type="daterange"
                    value-format="YYYY-MM-DD"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="退货时间" style="font-weight: bold" prop="orderNo">
                <el-date-picker
                    v-model="returnDataRange"
                    type="daterange"
                    value-format="YYYY-MM-DD"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" align="right">
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          @click="handleExport"
          icon="Download"
          v-hasPermi="['orderReturn:export']"
          >导出
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table border :height="tableHeight"  :data="_this.consigneeInfoList">
      <el-table-column label="农户归属" align="center"  prop="orgName"/>
      <el-table-column label="审批状态" align="center" :formatter="returnStatusFmt"   prop="returnStatus"/>
      <el-table-column label="农户姓名" align="center"  prop="farmerName"/>
      <el-table-column label="订单时间" align="center"  prop="orderTime"/>
      <el-table-column label="退货时间" align="center"  prop="createTime"/>
      <el-table-column label="订单编号" align="center"  prop="orderNo">
        <template #default="scope">
          <el-link type="primary"
              @click="orderView(scope.row)">{{scope.row.orderNo}}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="退货单号" align="center" prop="returnOrderNo">
        <template #default="scope">
          <el-link type="primary"
              @click="returnOrderView(scope.row)">{{scope.row.returnOrderNo}}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-link type="primary" v-if="scope.row.returnStatus=='01'"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['orderReturn:approve']"
          >审核
          </el-link>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页查询底部选页栏 -->
    <pagination
      v-show="_this.total > 0"
      v-model:limit="_this.queryParams.rows"
      v-model:page="_this.queryParams.page"
      :total="_this.total"
      @pagination="getList"
    />

    <!--orderInfo-->
    <el-dialog :title="orderInfoTitle" v-model="orderInfoOpen" width="900px" :close-on-click-modal="false" append-to-body>
      <el-form ref="orderRef" :model="form" :rules="rules" label-width="100px" label-position="top" >
        <el-row :gutter="20" style="display: flex;flex-wrap: wrap">
          <el-col :span="8">
            <el-form-item label="农户姓名" prop="statYear">
              <el-input v-model="form.farmerName"  placeholder="身份证号" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="农户归属" prop="orgName">
              <el-input v-model="form.orgName" :title="form.orgName" placeholder="农户归属" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系方式" prop="orgName">
              <el-input v-model="form.farmerTelephone" placeholder="联系方式" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号" prop="farmerIccid">
              <el-input v-model="form.idIccid" placeholder="身份证号" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单编号" prop="orderNo">
              <el-input v-model="form.orderNo" placeholder="下单编号" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单状态" prop="orderStatus" style="font-weight: bold">
              <el-tag>{{orderStatusOpinionFmt(form.orderStatus)}}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="下单时间" prop="orderTime">
              <el-input v-model="form.orderTime" placeholder="下单时间" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="订单金额" prop="salesAmountTotal">
              <el-input v-model="form.salesAmountTotal" placeholder="订单金额" disabled />
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item prop="orderDetails">
              <template #label>
                <span style="font-weight:bold">订单商品</span>
              </template>
              <el-table border :data="form.orderDetails">
                <el-table-column label="商品名称" align="center" prop="aiTypeSubName"/>
                <el-table-column label="商品数量" align="center" prop="subscribeNum"/>
                <el-table-column label="商品单价（元）" align="center" prop="salePrice"/>
              </el-table>
            </el-form-item>
          </el-col>

          <el-col v-if="orderType=='2'||orderType=='3'" :span="8">
            <el-form-item label="退货单号" prop="returnOrderNo">
              <el-input v-model="form.returnOrderNo" placeholder="退货单号" disabled />
            </el-form-item>
          </el-col>
          <el-col v-if="orderType=='2'||orderType=='3'" :span="8">
            <el-form-item label="退货申请时间" prop="createTime">
              <el-input v-model="form.createTime" placeholder="退货申请时间" disabled />
            </el-form-item>
          </el-col>
          <el-col v-if="orderType=='2'||orderType=='3'" :span="8">
            <el-form-item label="退货金额" prop="returnOrderNo">
              <el-input v-model="form.returnPrice" placeholder="退货金额" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="orderType=='2'||orderType=='3'">
            <el-form-item label="退货原因" prop="returnReason">
              <el-input
                  v-model="form.returnReason"
                  disabled
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  maxlength="200"
                  type="textarea"
                  placeholder="请输入审核意见"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="orderType=='2'||orderType=='3'" :span="24">
            <el-form-item  prop="details">
              <template #label>
                <span style="font-weight:bold">退货商品</span>
              </template>
              <el-table border :data="form.details">
                <el-table-column label="商品名称" align="center" prop="aiTypeSubName"/>
                <el-table-column label="订购量" align="center" prop="subscribeNum"/>
                <el-table-column label="已退货量" align="center" prop="alreadyReturnNum">
                  <template #default="scope">
                    {{scope.row.alreadyReturnNum?scope.row.alreadyReturnNum:0}}
                  </template>
                </el-table-column>
                <el-table-column label="本次退货量" align="center" prop="returnNum"/>
                <el-table-column label="商品单价（元）" align="center" prop="salePrice"/>
              </el-table>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="orderType=='3'">
            <el-form-item label="是否通过" prop="agree">
              <el-radio-group v-model="form.agree">
                <el-radio :label="false">不同意</el-radio>
                <el-radio :label="true">同意</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>

          <el-col :span="24" v-if="orderType=='3'">
            <el-form-item label="审核意见" prop="opinion">
              <el-input
                  v-model="form.opinion"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  maxlength="200"
                  type="textarea"
                  placeholder="请输入审核意见"
              />
            </el-form-item>
          </el-col>
          <el-col v-if="form.returnStatus!='01' && orderType=='2'" :span="24">
            <el-form-item label="审核信息" prop="flowMsg">
              <el-timeline>
                <el-timeline-item
                    v-for="(activity, index) in JSON.parse(form.flowMsg)"
                    :key="index"
                    :color="nodeColor(activity.state)"
                    :timestamp="activity.state==='2'?activity.time:null">
                  {{activity.node }}{{activity.opinion?' 意见:'+activity.opinion:''}}
                </el-timeline-item>
              </el-timeline>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="orderType=='3'" type="primary" @click="submit">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>
<script name="/invest/orderback/queryByPage" setup>
import {
  page,getInfo,approve,returnInfo
} from "@/api/bdh-agric-invest/invest/orderReturn";
import { getDicts } from "@/api/bdh-agric-invest/system/dict/data";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import { postForExcel } from "@/api/bdh-agric-invest/invest/excel";
import {reactive, ref, toRefs} from "vue";
const { proxy } = getCurrentInstance();
//所在单位
const queryOrgRef = ref();
//商品分类
const produceTypeRef = ref();
//审核表单
const approveForm = ref();
const orgFlag = ref(false)
const importOrgRef= ref();
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const dataRange = ref([]);
const returnDataRange = ref([]);
const orderStatus = ref([]);
const orderInfoTitle=ref("");
const orderInfoOpen=ref(false);
const orderType=ref("1") //1 订单 2 退货单 3 审核
const data=reactive({
  //导入form
  form:{
    orgName:null,
    prodOrderId:null,
    returnStatus:null,
    orderStatus:null,
    flowMsg:null,
    returnReason:null,
    salesAmountTotal:null,
    returnPrice:null,
    orderReturnId:null,
    statYear:null,
    orgCode:null,
    orderDetails: null,
    details:[],
    farmerName:null,
    orderNo:null,
    returnOrderNo:null,
    idIccid:null,
    farmerOrgCode:null,
    farmerOrgName:null,
    farmerTelephone:null,
    agree:null,
    opinion:null
  },
  rules:{
    agree: [{ required: true, message: '请选择是否审核通过', trigger: "change" }],
    opinion: [{ required: true, message: '请输入审核意见', trigger: "change" }],
  }
})
const orderStatusOpinion = ref([]) //订单状态
const showSearch = ref(true);
const {form,rules} = toRefs(data);
//页面数据
const _this = reactive({

  processVisible: false,
  // 确认状态
  confirmStatus: [],
  //表格数据
  consigneeInfoList: [],
  //加载状态
  loading: true,
  //年份
  yearOption: [],
  //季节数据
  fertilizes: [],
  //企业数据
  companyAbbrOption: [],
  //动态列头
  columns: [

  ],
  total: 0,
  returnStatus: [],
  queryParams: {
    orderNo:"",
    orgCode: "", //组织机构编码
    farmerName: "", //农户姓名
    page: 1, //请求页码
    rows: 10, //每页条数
    orderBeginDate:null,
    orderEndDate:null,
    returnBeginDate:null,
    returnEndDate:null,
    returnStatus: null,
    returnStatusList: []
  },
  //表格title
  title: "",
});

function nodeColor(state){
  if(state==='2'){
    return '#141515';
  }
  if(state==='1'){
    return '#0bbd87'
  }
  return '';
}

function orderStatusOpinionFmt(orderStatus){
  for(let i=0;i<orderStatusOpinion.value.length;i++){
    if(orderStatusOpinion.value[i].code==orderStatus){
      return orderStatusOpinion.value[i].name
    }
  }
}
onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});

/** 获取字典 */
const getDict = () => {
  //订单状态
  getDicts("invest_order_status").then((response) => {
    orderStatusOpinion.value = response.data;
  });

  //查询审核状态
  getDicts("return_status").then((response) => {
    console.info('returnStatus',response);
    _this.returnStatus = response.data;
    if(_this.returnStatus.length>0){
      _this.queryParams.returnStatus=_this.returnStatus[0].code
    }
    getList();
  });

};

//弹出审核页面  审核页面
function handleUpdate (row){
  clearForm();
  returnInfo(row.orderReturnId).then(response=>{
    if(response.success){
      orderType.value="3";
      form.value=response.data;
      orderInfoOpen.value=true;
      orderInfoTitle.value="审核";
    }
  })
}

function submit(){
  proxy.$refs["orderRef"].validate(valid => {
    if (valid) {
      approve({"orderReturnId":form.value.orderReturnId,"agree":form.value.agree,"opinion":form.value.opinion}).then(response => {
        proxy.$modal.msgSuccess("提交成功");
        orderInfoOpen.value=false;
        getList();
      });
    }
  });
}

function cancel(){
  orderInfoOpen.value=false;
}

//orderView 查看订单详情
function orderView(row){
  clearForm();
  getInfo(row.prodOrderId).then(response=>{
    if(response.success){
      orderType.value="1";
      form.value=response.data;
      orderInfoOpen.value=true;
      orderInfoTitle.value="订单详情";
    }
  })
}
//returnOrderView 查看退货单详情
function returnOrderView(row){
  console.info(row);
  clearForm();
  returnInfo(row.orderReturnId).then(response=>{
    if(response.success){
      orderType.value="2";
      form.value=response.data;
      orderInfoOpen.value=true;
      orderInfoTitle.value="退货详情";
    }
  })
}

function clearForm(){
  form.value={
    orgName:null,
    prodOrderId:null,
    returnStatus:null,
    orderStatus:null,
    flowMsg:null,
    returnReason:null,
    salesAmountTotal:null,
    returnPrice:null,
    orderReturnId:null,
    statYear:null,
    orgCode:null,
    details:[],
    farmerName:null,
    orderNo:null,
    returnOrderNo:null,
    idIccid:null,
    farmerOrgCode:null,
    farmerOrgName:null,
    farmerTelephone:null
  }
}

function returnStatusFmt(row){
  for(let i=0;i<_this.returnStatus.length;i++){
    if(_this.returnStatus[i].code==row.returnStatus){
      return _this.returnStatus[i].name
    }
  }
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryOrgRef.value.clear();
  _this.queryParams.orderBeginDate=null;
  _this.queryParams.orderEndDate=null;
  _this.queryParams.returnBeginDate=null;
  _this.queryParams.returnEndDate=null;
  dataRange.value=[]
  returnDataRange.value=[]
  handleQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
	_this.queryParams.page = 1;
  getList();
}
//所在单位更改
const handleOrgCodeQuery = (orgInfo) => {
  if (!orgInfo.orgCode) queryOrgRef.value.clear();
  _this.queryParams.orgCode = orgInfo.orgCode;
};

/** 查询收货人列表 */
function getList() {
  console.info('_this.queryParams:',_this.queryParams);
  //_this.loading.value = true;
  if (dataRange.value && dataRange.value.length && dataRange.value.length == 2) {
    _this.queryParams.orderBeginDate = dataRange.value[0];
    _this.queryParams.orderEndDate = dataRange.value[1];
  }else{
    _this.queryParams.orderBeginDate = null;
    _this.queryParams.orderEndDate = null;
  }
  //

  if (dataRange.value&& returnDataRange.value.length && returnDataRange.value.length == 2) {
    _this.queryParams.returnBeginDate = returnDataRange.value[0];
    _this.queryParams.returnEndDate = returnDataRange.value[1];
  }else{
    _this.queryParams.returnBeginDate = null;
    _this.queryParams.returnEndDate = null;
  }

  page(_this.queryParams).then((response) => {
    _this.consigneeInfoList = response.data.records;
    _this.total = response.data.total;
  });
}

/** 导出按钮操作 */
function handleExport() {
  postForExcel(
    "/bdh-agric-invest-api/invest/orderback/export",
    _this.queryParams,
    "投入品农户退货"
  );
}
getDict();
</script>
<style scoped>
.el-link {
  margin-right: 8px;
}

.el-link .el-icon--right.el-icon {
  vertical-align: text-bottom;
}
.mb8 {
  min-height: 30px;
}
.title {
  font-size: 20px;
  text-align: center;
  margin-bottom: 4px;
  font-weight: 600;
}
</style>
