<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px" class="form-line">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年度" prop="year">
                  <el-select v-model="queryParams.year" clearable placeholder="请选择年度">
                      <el-option
                          v-for="dict in yearOption"
                          :key="dict.code"
                          :label="dict.name"
                          :value="dict.code"
                      />
                  </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="公示标题" prop="bulletinTitle">
                <el-input style="width:214px"
                    v-model="queryParams.bulletinTitle"
                    maxlength="50"
                    show-word-limit
                    placeholder="请输入公示标题"
                    clearable
                    @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="公示日期" prop="queryDateRange">
                <el-date-picker style="width:270px"
                    v-model="queryDateRange"
                    type="daterange"
                    value-format="YYYY-MM-DD"
                    range-separator="~"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="公示对象" prop="bulletinObject">
                <org-select @handleOrgCode="handleOrgCodeQuery" ref="orgRef" :level="1"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="发布状态" prop="isPublish">
                <el-select v-model="queryParams.isPublish" placeholder="请选择公示发布状态">
                  <el-option
                      v-for="item in isPublish"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.disabled"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="18" align="right">
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['bulletinConfInfo:insert']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            icon="Edit"
            :disabled="single"
            @click="handleUpdate(null,'update')"
            v-hasPermi="['bulletinConfInfo:update']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['bulletinConfInfo:logicDeleteByIds']"
        >删除
        </el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--            icon="Download"-->
<!--            @click="handleExport"-->
<!--            v-hasPermi="['bulletinConfInfo:export']"-->
<!--        >导出-->
<!--        </el-button>-->
<!--      </el-col>-->
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border :height="tableHeight"  :data="bulletinConfInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="年度" align="center" width="100" prop="year" >
      </el-table-column>
      <el-table-column label="公示标题" align="center" prop="bulletinTitle" >
      </el-table-column>
      <el-table-column label="公示对象" align="center" prop="orgNameList">
      </el-table-column>
      <el-table-column label="公示时间" align="center" width="220" >
        <template #default="scope">
          {{geBulletinDateRange(scope.row)}}
        </template>
      </el-table-column>
      <el-table-column label="发布状态" align="center" prop="isPublish">
        <template #default="scope">
          <el-switch
              v-model="scope.row.isPublish"
              active-value="1"
              inactive-value="0"
              @click="handleDisplayChg(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200px" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
              type="primary"
              link
              size="small"
              @click="handleUpdate(scope.row,'view')"
              v-hasPermi="['bulletinConfInfo:update']"
          >查看
          </el-button>
          <el-button
              type="primary"
              link
              size="small"
              @click="handleUpdate(scope.row,'update')"
              v-hasPermi="['bulletinConfInfo:update']"
          >修改
          </el-button>
          <el-button
              type="primary"
              link
              size="small"
              @click="handleDelete(scope.row)"
              v-hasPermi="['bulletinConfInfo:logicDeleteById']"
          >删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.rows"
        @pagination="getList"
    />

    <!-- 添加或修改公示配置管理表对话框 -->
    <el-dialog :title="title"  v-model="open" width="500px" append-to-body :close-on-click-modal="false">
      <div class="form-wrapper bulletinConfInfo">
        <el-form ref="bulletinConfInfoRef" :disabled="updateType=='view'" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="年度" prop="year">
                <el-select v-model="form.year" style="width:100%" clearable placeholder="请选择年度">
                    <el-option
                        v-for="dict in yearOption"
                        :key="dict.code"
                        :label="dict.name"
                        :value="dict.code"
                    />
                </el-select>
            </el-form-item>
          <el-form-item label="公示标题" prop="bulletinTitle">
            <el-input v-model="form.bulletinTitle" maxlength="50" show-word-limit placeholder="请输入公示标题"/>
          </el-form-item>
          <el-form-item label="公示日期" prop="formDateRange" class="item">
            <el-date-picker
                v-model="form.formDateRange"
                type="daterange"
                value-format="YYYY-MM-DD"
                range-separator="~"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
            />
          </el-form-item>
          <el-form-item label="公示对象" prop="orgCode" class="item">
            <org-select @handleOrgCode="handleOrgCodeForm" :disabled="true" :key="keyNum" :level="3" :checkStrictly="false" :default-org-code="form.orgCode"/>
          </el-form-item>
          <el-form-item label="是否发布" prop="isPublish">
            <el-switch
                v-model="form.isPublish"
                active-value="1"
                inactive-value="0"
            />
          </el-form-item>
          <el-form-item label="公示附件" prop="annexUrlArray">
            <fileUpload urlApi="bdh-agric-invest-api" :limit="1" :show="updateType=='update'" v-model:fileType="fileType" v-model="form.annexUrlArray"
                        @fileUploadChange="fileUploadAnnexUrl"
            ></fileUpload>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary"  v-if="updateType=='update'"  @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="/invest/bulletinConfInfo/queryByPage">
import {
  listBulletinConfInfo,
  getBulletinConfInfo,
  delBulletinConfInfos,
  addBulletinConfInfo,
  updateBulletinConfInfo
} from "@/api/bdh-agric-invest/invest/bulletinConfInfo";
import {reactive, ref, toRefs} from "vue";
import {getDicts} from "@/api/bdh-agric-invest/system/dict/data";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import {postForExcel} from "@/api/bdh-agric-invest/invest/excel.js";

const {proxy} = getCurrentInstance();

const bulletinConfInfoList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const yearOption = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const keyNum = ref(0);
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom

const data = reactive({
  form: {formDateRange: []},
  queryParams: {
    rows: 10,
    page: 1,
    year: null,
    bulletinTitle: null,
    beginTime: null,
    endTime: null,
    bulletinObject: null,
    orgCode: null,
    orgName: null,
    isPublish: null,
    annexUrl: null,
    annexUrlArray: [],
    statusCd: null,
    bulletin: null,
  },
  rules: {
    bulletinTitle: [{required: true, message: "请输入公示标题", trigger: "blur"},
      {length: 30, message: "最多30个字符", trigger: "blur"}],
    year: [{required: true, message: "请选择年度", trigger: "blur"}],
    beginTime: [{required: true, message: "请选择公示开始时间", trigger: "blur"}],
    endTime: [{required: true, message: "请选择公示结束时间", trigger: "blur"}],
    orgCode: [{required: true, message: "请选择公示对象", trigger: "change"}],
    formDateRange: [{required: true, message: "请选择公示日期", trigger: "blur"}],
  }
});

const fileType = ['pdf'];
const queryDateRange = ref([]);
// const formDateRange = ref([]);
//发布状态 已发布1 未发布0

const isPublish = [
  {
    value: 0,
    label: '未发布',
  },
  {
    value: 1,
    label: '已发布',
  }];

const bulletinObject = ref([]);
const updateType=ref('update');
const {queryParams, form, rules} = toRefs(data);

onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 280
        : window.innerHeight - 280;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 280
        : window.innerHeight - 280;
});

/** 查询公示配置管理表列表 */
function getList() {
  loading.value = true;
  queryParams.value.beginTime = queryDateRange.value[0];
  queryParams.value.endTime = queryDateRange.value[1];
  listBulletinConfInfo(queryParams.value).then(response => {
    bulletinConfInfoList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}


function handleDisplayChg(row){
  let text = row.isPublish == 1 ? "发布" : "取消发布";
  proxy.$modal.confirm('确认要"' + text + '"该公告吗?').then(function () {
    let param={bulletinConfId:row.bulletinConfId,isPublish:row.isPublish};
    return updateBulletinConfInfo(param);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(function () {
    row.isPublish = row.isPublish === "0" ? "1" : "0";
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    year: null,
    bulletinTitle: null,
    beginTime: null,
    endTime: null,
    bulletinObject: null,
    orgCode: null,
    orgName: null,
    isPublish: null,
    annexUrl: null,
    annexUrlArray: [],
    remark: null,
    createBy: null,
    updateBy: null,
    updateTime: null,
    bulletinConfId: null,
    createTime: null,
    statusCd: null,
    bulletin: '1',
    formDateRange: []
  };
  proxy.resetForm("bulletinConfInfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  // queryParams.value.rows = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.orgCode=null;
  proxy.$refs['orgRef'].clear();
  queryDateRange.value = [];
  queryParams.value.year = yearOption.value[0].code
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.bulletinConfId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  updateType.value='update'
  keyNum.value++;
  reset();
  open.value = true;
  title.value = "添加公示配置";
}
function handleOrgCodeQuery(orgInfo){
  queryParams.value.orgCode = [orgInfo.orgCode];
}
function handleOrgCodeForm(orgInfo){
  console.log('orgInfo1111：',orgInfo);
  form.value.orgCode = orgInfo.orgCode;
  form.value.orgName = orgInfo.orgName;
}
/** 修改按钮操作 */
function handleUpdate(row,type) {
  updateType.value=type
  keyNum.value++;
  reset();
  var  bulletinConfId=null
  if(row){
    bulletinConfId=row.bulletinConfId
  }else{
    bulletinConfId=ids.value[0]
  }
  getBulletinConfInfo(bulletinConfId).then(response => {
    form.value = response.data;
    let begin = new Date(form.value.beginTime).toISOString().split('T')[0];
    let end = new Date(form.value.endTime).toISOString().split('T')[0];
    let arr = [begin,end];
    form.value.formDateRange = arr;
    form.value.orgCode = response.data.orgCodeList;
    form.value.orgName = response.data.orgNameList;
    open.value = true;
    if(type=='update'){
      title.value = "修改公示配置";
    }else if(type='view'){
      title.value = "查看公示配置";
    }

  });
}

function isArrayFn(value){
  if (typeof Array.isArray === "function") {
    return Array.isArray(value);
  }else{
    return Object.prototype.toString.call(value) === "[object Array]";
  }
}

/** 提交按钮 */
function submitForm() {


  proxy.$refs["bulletinConfInfoRef"].validate(valid => {
    if (valid) {

      let param = form.value;
      delete param.orgCodeList;
      delete param.orgNameList;
      delete param.bulletinOrgCode;

      param.beginTime = param.formDateRange[0];
      param.endTime = param.formDateRange[1];
      param.bulletin = '1';
      console.info('1111111111');
      if(param.orgCode&&!isArrayFn(param.orgCode)){
        param.orgCode = [param.orgCode]
      }
      if(param.orgName&&!isArrayFn(param.orgName)){
        param.orgName = [param.orgName];
      }

      if (form.value.bulletinConfId != null) {
        updateBulletinConfInfo(param).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addBulletinConfInfo(param).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });

}

/** 删除按钮操作 */
function handleDelete(row) {
  const bulletinTitle = row.bulletinTitle;
  proxy.$modal.confirm('是否确认删除？').then(function () {
    var delIds=[];
    if(row&&row.bulletinConfId){
      delIds=[row.bulletinConfId]
    }else{
      delIds=ids.value
    }
    console.info('bulletinConfIds',delIds);
    return delBulletinConfInfos(delIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  postForExcel(
      "/bdh-agric-invest-api/bulletin/invest/bulletinConfInfo/export",
      queryParams.value,
      "公示配置管理"
  );
  // proxy.download('bdh-agric-invest-api/invest/bulletinConfInfo/export', {
  //   ...queryParams.value
  // }, `bulletinConfInfo_${new Date().getTime()}.xlsx`)
}


function geBulletinDateRange(obj){
  const beginTime = new Date(obj.beginTime);
  const endTime = new Date(obj.endTime);
  return  beginTime.toISOString().split('T')[0]
          + '至' +
          endTime.toISOString().split('T')[0];
}

function getBulletinObjectFromDict(){
  getDicts('aiInvest:bulletinObject').then(response=>{
    bulletinObject.value = response.data;
  })
}

/** 获取字典 */
const getDict = () => {
    //查询年份
    getDicts('year_cd').then(response => {
        yearOption.value = response.data.sort((a, b) => {
            return Number(b.code) - Number(a.code)
        })
        queryParams.value.year = yearOption.value[0].code
        getList();
    });
}

let fileUploadAnnexUrl = (value) => {
  if (Array.isArray(value) && value.length > 0) {
    // const urls = value.map(item => item.url || item).join(',');
    const urls = value.map(item => item.url || item);
    form.value.annexUrlArray = urls;
    proxy.$refs.bulletinConfInfoRef.clearValidate('annexUrlArray');
  } else {
    form.value.companyLicenseAddr = '';
  }
}

getDict();
getBulletinObjectFromDict();
</script>

<style scoped lang="scss">
@import "@/views/bdh-agric-invest/assets/styles/index.scss";
.upload-file{
  width: 100%;
}
.bulletinConfInfo{
  :deep(.el-form-item--default){
    margin-bottom: 18px !important;
  }
}
</style>
