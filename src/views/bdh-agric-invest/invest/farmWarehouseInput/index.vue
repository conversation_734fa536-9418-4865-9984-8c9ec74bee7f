<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form
          class="form-line"
          :model="queryParams"
          ref="queryForm"
          v-show="showSearch"
          label-width="80px"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年份" prop="year">
                <el-select v-model="queryParams.year" clearable placeholder="请选择年份">
                  <el-option
                    v-for="dict in yearOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所在单位" prop="orgCode">
                <org-select
                  ref="queryOrgRef"
                  @handleOrgCode="handleOrgCode"
                  :defaultOrgCode="queryParams.orgCode"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业简称" prop="aiCompanyId">
                <el-select
                  v-model="queryParams.aiCompanyId"
                  clearable
                  placeholder="请选择企业简称"
                >
                  <el-option
                    v-for="dict in companyAbbrOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="作业季节" prop="fertilizeSeason">
                <el-select
                  v-model="queryParams.fertilizeSeason"
                  placeholder="请选择作业季节"
                  clearable
                >
                  <el-option
                    v-for="dict in fertilizeSeasonOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="入库单号" prop="inoutOrderNo">
                <el-input
                  v-model="queryParams.inoutOrderNo"
                  placeholder="请输入入库单号"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="商品类型" prop="outboundProdType">
                <el-select
                  v-model="queryParams.outboundProdType"
                  placeholder="请选择商品类型"
                  clearable
                >
                  <el-option
                    v-for="dict in outboundProdTypeOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="入库状态" prop="outboundStatus">
                <el-select
                  v-model="queryParams.outboundStatus"
                  clearable
                  placeholder="请选择入库状态"
                >
                  <el-option
                    v-for="dict in outboundStatusOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6" align="right">
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['farmWarehouseInput:insert']"
          >入库加工商品</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          plain
          icon="Delete"
          :disabled="deleteBatch"
          @click="handleDelete"
          v-hasPermi="['farmWarehouseInput:logicDeleteByIds']"
          >删除</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['farmWarehouseInput:importExcel']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          plain
          @click="importTemplate"
          v-hasPermi="['farmWarehouseInput:exportTemplate']"
          >下载模板</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['farmWarehouseInput:exportExcel']"
          >导出</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          plain
          icon="CircleCheck"
          @click="handleConfirm"
          :disabled="confirmBatch"
          v-hasPermi="['farmWarehouseInput:confirmWarehouseIn']"
          >确认入库</el-button
        >
      </el-col>

      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      :data="farmWarehouseInputList"
      border
      :height="tableHeight"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="年份" align="center" prop="year" />
      <el-table-column label="企业简称" align="center" prop="companyAbbr" />
      <el-table-column
        label="作业季节"
        align="center"
        prop="fertilizeSeason"
        :formatter="fertilizeSeasonFormatter"
      />
      <el-table-column label="入库单号" align="center" prop="inoutOrderNo" />
      <el-table-column label="出库库房" align="center" prop="outboundTargetName">
        <template #default="scope">
          <div style="color: dodgerblue">{{ scope.row.outboundTargetName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="出库时间" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="入库商品类型"
        align="center"
        prop="outboundProdType"
        :formatter="outboundProdTypeFormatter"
      />
      <el-table-column label="入库总量" align="center" prop="operateStock">
        <template #default="scope">
          <a style="color: dodgerblue" @click="clickDetail(scope.row)">{{
            scope.row.operateStock
          }}</a>
        </template>
      </el-table-column>
      <el-table-column label="入库时间" align="center" prop="operateTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.operateTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="入库人" align="center" prop="operatePerson" />
      <el-table-column
        label="入库状态"
        align="center"
        prop="outboundStatus"
        :formatter="outboundStatusFormatter"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200px"
      >
        <template #default="scope">
          <el-button
            type="primary"
            icon="Edit"
            link
            v-if="scope.row.outboundProdType == 2 && scope.row.outboundStatus == 0"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['farmWarehouseInput:update']"
            >修改</el-button
          >
          <el-button
            type="danger"
            icon="Delete"
            link
            v-if="scope.row.outboundProdType == 2 && scope.row.outboundStatus == 0"
            @click="handleDelete(scope.row)"
            v-hasPermi="['farmWarehouseInput:logicDeleteById']"
            >删除</el-button
          >
          <el-button
            type="primary"
            icon="View"
            link
            @click="clickDetail(scope.row)"
            v-hasPermi="['farmWarehouseInput:info']"
            >查看详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.rows"
      @pagination="getList"
    />

    <!-- 入库加工商品对话框 -->
    <el-dialog
      :title="title"
      v-model="openProcessed"
      width="764px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="processedFormRef"
        :model="processedForm"
        :rules="processedRules"
        label-width="80px"
        label-position="top"
        :disabled="viewDetailOnly"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="年份" prop="year">
              <el-select v-model="processedForm.year" clearable placeholder="请选择年份">
                <el-option
                  v-for="dict in yearOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在单位" prop="orgCode">
              <org-select
                v-if="orgFlag"
                @handleOrgCode="handleOrgCodeForm"
                :defaultOrgCode="processedForm.orgCode"
                :orgCode="processedForm.orgCode"
                ref="processedFormOrgRef"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="企业简称" prop="aiCompanyId">
              <el-select
                v-model="processedForm.aiCompanyId"
                clearable
                placeholder="请选择企业简称"
              >
                <el-option
                  v-for="dict in companyAbbrOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="作业季节" prop="fertilizeSeason">
              <el-select
                v-model="processedForm.fertilizeSeason"
                clearable
                placeholder="请选择作业季节"
              >
                <el-option
                  v-for="dict in fertilizeSeasonOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入库库房" prop="warehouseId">
              <el-select
                v-model="processedForm.warehouseId"
                clearable
                placeholder="请选择入库库房"
              >
                <el-option
                  v-for="dict in farmWarehouseOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入库商品类型" prop="outboundProdType">
              <el-select
                v-model="processedForm.outboundProdType"
                clearable
                disabled
                placeholder="请选择入库商品类型"
              >
                <el-option
                  v-for="dict in outboundProdTypeOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="primary"
            icon="Plus"
            @click="handleAddProduct"
            :disabled="viewOnly"
            v-if="!viewOnly"
            >选择加工商品</el-button
          >
        </el-col></el-row
      >

      <el-table border :data="addProduceList" ref="addProduceTableRef">
        <el-table-column align="center" label="投入品分类" prop="aiTypeName" />
        <el-table-column align="center" label="商品名称" prop="aiTypeSubName" />
        <el-table-column align="center" label="规格" prop="aiTypeSubSpecs" />
        <el-table-column align="center" label="本次入库量" prop="operateStock">
          <template #default="scope">
            <!--            <el-input-->
            <!--              v-model="scope.row.operateStock"-->
            <!--              placeholder="请输入"-->
            <!--              @blur="() => validateTotalRatio(scope.row)"-->
            <!--              :disabled="viewOnly"-->
            <!--            >-->
            <!--            </el-input>-->
            <el-input
              v-model="scope.row.operateStock"
              placeholder="请输入"
              :disabled="viewOnly"
            >
            </el-input>
          </template>
        </el-table-column>
        <el-table-column align="center" label="商品重量" prop="aiTypeSubWeight" />
        <el-table-column align="center" label="计量单位" prop="aiTypeSubUnit" />
        <el-table-column
          align="center"
          class-name="small-padding fixed-width"
          label="操作"
          width="150px"
        >
          <template
            #default="scope"
            v-if="!viewOnly || processedForm.outboundProdType == 2"
          >
            <el-button
              v-if="viewOnly && processedForm.outboundProdType == 2"
              type="primary"
              icon="View"
              link
              size="small"
              @click="openDetailDialog(scope.row)"
              >查看</el-button
            >
            <el-button
              v-if="!viewOnly"
              icon="Delete"
              link
              size="small"
              type="danger"
              @click="handleDeleteProduct(scope.row)"
              :disabled="viewOnly"
              >删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="submitForm"
            :disabled="!dataIsValid"
            v-if="!viewOnly"
            >确 定</el-button
          >
          <el-button @click="cancel" v-if="!viewOnly">取 消</el-button>
          <el-button @click="cancel" v-if="viewOnly">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 加工商品查看详情-->
    <el-dialog
      title="加工商品详情"
      v-model="showFarmDialog"
      width="900px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-table border :data="processList" style="margin-top: 10px">
        <el-table-column align="center" type="index" width="55" />
        <el-table-column align="center" label="商品名称" prop="aiTypeSubName" />
        <el-table-column align="center" label="规格" prop="aiTypeComSpecs" />
        <el-table-column align="center" label="比例" prop="aiTypeSubWeight">
          <template #default="scope"> {{ scope.row?.aiTypeComPercent ?? 0 }}% </template>
        </el-table-column>
        <el-table-column align="center" label="本次出库量" prop="outWarehouseStockNow" />
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showFarmDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 需要入库的加工商品-->
    <el-dialog
      title="选择加工商品"
      v-model="openProduct"
      width="900px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="productRef"
        :model="productForm"
        :rules="productRules"
        label-width="80px"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="企业简称" prop="aiCompanyId">
              <el-select
                v-model="productForm.aiCompanyId"
                disabled
                placeholder="请选择企业简称"
              >
                <el-option
                  v-for="dict in companyAbbrOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="加工商品分类" prop="aiTypeId">
              <el-cascader
                ref="parAiTypeRef"
                v-model="productForm.aiTypeId"
                :props="produceTypeProps"
                :options="sortOption"
                :show-all-levels="false"
                @change="handleParAiTypeChangeProduct"
                placeholder="请选择加工商品分类"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="商品名称" prop="aiTypeSubName">
              <el-input
                v-model="productForm.aiTypeSubName"
                placeholder="请输入商品名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" style="text-align: right">
            <el-button icon="Refresh" @click="resetProductQuery">重置</el-button>
            <el-button icon="Search" type="primary" @click="handleProductQuery"
              >搜索</el-button
            >
          </el-col>
        </el-row>
      </el-form>

      <el-table
        border
        :data="productList"
        ref="produceTableRef"
        @selection-change="handleSelectionChoosen"
        style="margin-top: 10px"
      >
        <el-table-column
          align="center"
          type="selection"
          width="55"
          :selectable="isSelect"
        />
        <el-table-column align="center" label="商品编号" prop="aiTypeSubCode" />
        <el-table-column align="center" label="分类名称" prop="aiTypeName" />
        <el-table-column align="center" label="商品名称" prop="aiTypeSubName" />
        <el-table-column align="center" label="规格" prop="aiTypeSubSpecs" />
        <el-table-column align="center" label="重量" prop="aiTypeSubWeight">
          <template #default="scope">
            {{ scope.row.aiTypeSubWeight }}{{ scope.row.productWeightUnit }}
          </template>
        </el-table-column>

        <el-table-column align="center" label="计量单位" prop="productWeightUnit">
          <template #default="scope">
            {{ scope.row.productWeightUnit }}
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="productTotal > 0"
        :total="productTotal"
        v-model:page="productForm.page"
        v-model:limit="productForm.rows"
        @pagination="handleProductQuery"
      />
      
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitProduct">确 定</el-button>
          <el-button @click="cancelProduct">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 入库统共商品对话框-->
    <el-dialog
      title="入库信息"
      v-model="openUnified"
      width="764px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="unifiedFormRef"
        :model="unifiedForm"
        :rules="unifiedRules"
        label-width="80px"
        label-position="top"
        :disabled="viewOnly"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="年份" prop="year">
              <el-select v-model="unifiedForm.year" clearable placeholder="请选择年份">
                <el-option
                  v-for="dict in yearOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在单位" prop="orgCode">
              <org-select
                v-if="orgFlag"
                @handleOrgCode="handleOrgCodeForm"
                :defaultOrgCode="unifiedForm.orgCode"
                :orgCode="unifiedForm.orgCode"
                ref="unifiedFormOrgRef"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="企业简称" prop="companyAbbr">
              <el-select
                v-model="unifiedForm.companyAbbr"
                clearable
                placeholder="请选择企业简称"
              >
                <el-option
                  v-for="dict in companyAbbrOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="作业季节" prop="fertilizeSeason">
              <el-select
                v-model="unifiedForm.fertilizeSeason"
                clearable
                placeholder="请选择作业季节"
              >
                <el-option
                  v-for="dict in fertilizeSeasonOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入库单号" prop="inoutOrderNo">
              <el-input
                v-model="unifiedForm.inoutOrderNo"
                placeholder="请输入入库单号"
                maxlength="30"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库库房" prop="outboundTargetName">
              <el-input
                v-model="unifiedForm.outboundTargetName"
                placeholder="请输入出库库房"
                maxlength="30"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库商品类型" prop="outboundProdType">
              <el-select
                v-model="unifiedForm.outboundProdType"
                clearable
                placeholder="请选择出库商品类型"
              >
                <el-option
                  v-for="dict in outboundProdTypeOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库日期" prop="operateTime">
              <el-date-picker
                v-model="unifiedForm.operateTime"
                placeholder="请输入出库日期"
                maxlength="30"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库对象" prop="outboundTargetTypeName">
              <el-input
                v-model="unifiedForm.outboundTargetTypeName"
                placeholder="请输入出库对象"
                maxlength="30"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入库库房" prop="warehouseName">
              <el-input
                v-model="unifiedForm.warehouseName"
                placeholder="请输入入库库房"
                maxlength="30"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入库人" prop="operatePerson">
              <el-input
                v-model="unifiedForm.operatePerson"
                placeholder="请输入入库人"
                maxlength="30"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="入库日期" prop="createTime">
              <el-date-picker
                v-model="unifiedForm.createTime"
                placeholder="请输入入库日期"
                maxlength="30"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-table border :data="unifiedList" ref="unifiedTableRef">
        <el-table-column align="center" label="投入品分类" prop="aiTypeName" />
        <el-table-column align="center" label="商品名称" prop="aiTypeSubName" />
        <el-table-column align="center" label="规格" prop="aiTypeSubSpecs" />
        <el-table-column align="center" label="本次入库量" prop="operateStock" />
        <el-table-column align="center" label="商品重量" prop="aiTypeSubWeight" />
        <el-table-column align="center" label="计量单位" prop="aiTypeSubUnit" />
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog
      :title="upload.title"
      v-model="upload.open"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text"><em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip"></div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <!--            <el-link-->
            <!--              type="primary"-->
            <!--              :underline="false"-->
            <!--              style="font-size: 12px; vertical-align: baseline"-->
            <!--              @click="importTemplate"-->
            <!--              >下载模板</el-link-->
            <!--            >-->
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="farmWarehouseInput" setup>
import { ref } from "vue";
import { postForExcel } from "@/api/bdh-agric-invest/invest/excel";
import { getToken } from "@/utils/auth";
import { getDicts, queryAllOrgTree } from "@/api/bdh-agric-invest/system/dict/data";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import {
  confirmWarehouseIn,
  getFarmWarehouseAll,
  getProduceProcessTypeTreeByCompanyIdInput,
  infoProcess,
  insertProcess,
  listFarmWarehouseInput,
  listQueryByPageProcess,
  logicDeleteByIds,
  logicDeleteByWarehouseRecordId,
  queryAllCompanyInfoInput,
  updateProcess,
  getProcessDetailList,
} from "@/api/bdh-agric-invest/invest/farmWarehouseInput";
const { proxy } = getCurrentInstance();

const farmWarehouseInputList = ref([]); //主列表
const open = ref(false);
const openProcessed = ref(false); //加工商品弹出页
const openProduct = ref(false); //加工商品中选择商品弹出页
const openUnified = ref(false); //统供商品弹出页
const showSearch = ref(true);
// const loading = ref(false);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const showFarmDialog = ref(false); //加工商品弹出详情页
const processList = ref([]); //主列表

const queryOrgRef = ref();
const processedFormOrgRef = ref();
const unifiedFormOrgRef = ref();
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const defaultOrgCode = ref(""); //默认初始所在单位
const orgFlag = ref(false); //渲染所在单位ref
const viewOnly = ref(false); //表单只读
const viewDetailOnly = ref(false); //表单只读

//控制批量删除按钮
const hasTypeAndStatusOne = ref(true);
const hasStatusOne = ref(true);
const deleteBatch = ref(true); //控制批量删除按钮
const confirmBatch = ref(true); //控制批量删除按钮

//字典们
const yearOption = ref([]); //年份字典
const fertilizeSeasonOption = ref([]); //作业季节字典
const companyAbbrOption = ref([]); //企业简称列表
const farmWarehouseOption = ref([]); //农场列表
const sortOption = ref([]); //加工商品分类字典
const outboundStatusOption = ref([
  {
    code: 0,
    name: "待确认",
  },
  {
    code: 1,
    name: "已确认",
  },
]); //出库状态字典
const outboundProdTypeOption = ref([
  {
    code: 1,
    name: "统供商品",
  },
  {
    code: 2,
    name: "加工商品",
  },
]); //出库商品类型字典

const productList = ref([]); //可选择的加工商品列表
const productTotal = ref(0); //加工商品总数
const selectedProducts = ref([]); //已选择的加工商品列表
const addProduceList = ref([]); //加工商品列表

const dataIsValid = ref(true); //入库量-校验

const unifiedList = ref([]); //统供商品列表
const groupIdArr = ref([]);

const produceTypeProps = {
  //商品分类
  value: "aiTypeId",
  label: "aiTypeName",
  level: "aiTypeLevel",
  checkStrictly: true,
};

const data = reactive({
  processedForm: {},
  processedRules: {
    orgCode: [{ required: true, message: "请选择所在单位", trigger: "change" }],
    year: [{ required: true, message: "请选择年份", trigger: "change" }],
    aiCompanyId: [{ required: true, message: "请选择企业简称", trigger: "change" }],
    fertilizeSeason: [{ required: true, message: "请选择作业季节", trigger: "change" }],
    warehouseId: [{ required: true, message: "请选择入库库房", trigger: "change" }],
  },
  productForm: {
    page: 1,
    rows: 10
  },
  productRules: {},
  unifiedForm: {},
  unifiedRules: {},
  form: {},
  queryParams: {
    rows: 10,
    page: 1,
    orgCode: "",
    year: "",
    aiCompanyId: "",
    fertilizeSeason: "",
    inoutOrderNo: "",
    outboundProdType: null,
    outboundStatus: null,
  },
  queryParamsRules: {},
  rules: {},
});
const {
  queryParams,
  queryParamsRules,
  form,
  rules,
  processedRules,
  processedForm,
  productForm,
  productRules,
  unifiedForm,
  unifiedRules,
} = toRefs(data);

/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { "access-token": getToken() },
  // 上传的地址
  url:
    window.VITE_APP_BASE_API +
    "/bdh-agric-invest-api/invest/farmWarehouseInput/importExcel",
});

onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 220
    : window.innerHeight - 220;
});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 220
    : window.innerHeight - 220;
});

/** 查询农场投入品库房列表 */
function getList() {
  listFarmWarehouseInput(queryParams.value).then((response) => {
    farmWarehouseInputList.value = response.data.records;
    total.value = response.data.total;
  });
}
// getList();

// 取消按钮
function cancel() {
  openProcessed.value = false;
  openUnified.value = false;
  reset();
}

// 表单重置
function reset() {
  processedForm.value = {
    year: "",
    orgCode: "",
    aiCompanyId: "",
    fertilizeSeason: "",
    inoutOrderNo: "",
    warehouseId: "",
    outboundProdType: "",
  };
  proxy.resetForm("processedFormRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  queryOrgRef.value.clear();
  queryParams.value.orgCode = defaultOrgCode.value;
  queryOrgRef.value.setValue(defaultOrgCode.value, "");
  queryParams.value.year = yearOption.value[0].code;
  handleQuery();
}

//控制批量删除按钮
function hasItemWithTypeAndStatusOne(selection) {
  for (const item of selection) {
    if (item.outboundProdType !== 2 || item.outboundStatus !== 0) {
      return true; // 如果找到任何不满足条件的item，返回true
    }
  }
  return false; // 没有找到满足条件的item，返回false
}
//控制确认入库按钮
function hasItemWithStatusOne(selection) {
  for (const item of selection) {
    if (item.outboundStatus !== 0) {
      return true; // 如果找到任何不满足条件的item，返回true
    }
  }
  return false; // 没有找到满足条件的item，返回false
}
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  groupIdArr.value = selection.flatMap((item) => item.groupIdArr);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
  hasTypeAndStatusOne.value = hasItemWithTypeAndStatusOne(selection); // 更新新的响应式值
  hasStatusOne.value = hasItemWithStatusOne(selection); // 更新新的响应式值
  deleteBatch.value = !(!multiple.value && !hasTypeAndStatusOne.value);
  confirmBatch.value = !(!multiple.value && !hasStatusOne.value);
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  addProduceList.value = [];
  if (proxy.$refs["processedFormOrgRef"]) {
    proxy.$refs["processedFormOrgRef"].clear();
  }
  orgFlag.value = false;
  nextTick(() => {
    orgFlag.value = true;
  });
  viewOnly.value = false;
  viewDetailOnly.value = false;
  processedForm.value.outboundProdType = 2;
  openProcessed.value = true;
  title.value = "新增入库加工商品信息";
}

//选择加工商品弹出页
function handleAddProduct() {
  if (!processedForm.value.orgCode) {
    proxy.$modal.msgError("请选择所在单位");
    return;
  }
  if (!processedForm.value.aiCompanyId) {
    proxy.$modal.msgError("请选择企业简称");
    return;
  }
  productForm.value.aiTypeId = "";
  productForm.value.aiTypeSubName = "";
  productForm.value.page = 1;
  productForm.value.rows = 10;
  productForm.value.aiCompanyId = processedForm.value.aiCompanyId;
  productForm.value.orgCode = processedForm.value.orgCode;
  listQueryByPageProcess(productForm.value).then((response) => {
    productList.value = response.data.records;
    productTotal.value = response.data.total;
    getAiTypeSort();
    openProduct.value = true;
    proxy.$refs.produceTableRef.clearSelection();
  });
}

function handleProductQuery() {
  productForm.value.aiCompanyId = processedForm.value.aiCompanyId;
  productForm.value.orgCode = processedForm.value.orgCode;
  listQueryByPageProcess(productForm.value).then((response) => {
    productList.value = response.data.records;
    productTotal.value = response.data.total;
  });
}

function resetProductQuery() {
  productForm.value.aiTypeId = "";
  productForm.value.aiTypeSubName = "";
  productForm.value.page = 1;
  handleProductQuery();
}
//选择加工商品弹出页多选
function handleSelectionChoosen(selection) {
  selectedProducts.value = selection.map((item) => ({
    aiTypeId: item.aiTypeId,
    aiTypeSubId: item.aiTypeSubId,
    aiTypeName: item.aiTypeName,
    aiTypeSubName: item.aiTypeSubName,
    aiTypeSubSpecs: item.aiTypeSubSpecs,
    aiTypeSubWeight: item.aiTypeSubWeight,
    aiTypeSubUnit: item.productWeightUnit,
  }));
  console.log(selectedProducts.value, "selectedProducts");
}
//增加商品弹出页中的列表数据是否可以选择
function isSelect(row) {
  if (addProduceList.value.some((item) => item.aiTypeSubId === row.aiTypeSubId)) {
    return false;
  } else {
    return true;
  }
}
//将增加商品里的列表选中商品放到新增中
function submitProduct() {
  if (!addProduceList.value) {
    addProduceList.value = [];
  }
  selectedProducts.value.forEach((item) => {
    if (!addProduceList.value.some((i) => i.aiTypeSubId === item.aiTypeSubId)) {
      addProduceList.value.push(item);
    }
  });
  openProduct.value = false;
}
function cancelProduct() {
  openProduct.value = false;
}
//删除新增中的商品
function handleDeleteProduct(row) {
  const index = addProduceList.value.findIndex(
    (item) => item.aiTypeSubId === row.aiTypeSubId
  );
  if (index !== -1) {
    addProduceList.value.splice(index, 1);
  }
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  if (proxy.$refs["processedFormOrgRef"]) {
    proxy.$refs["processedFormOrgRef"].clear();
  }
  orgFlag.value = false;
  nextTick(() => {
    orgFlag.value = true;
  });
  viewOnly.value = false;
  viewDetailOnly.value = true;
  // const warehouseRecordId = row.warehouseRecordId;
  const inoutOrderNo = row.inoutOrderNo;
  const year = row.year;
  const aiCompanyId = row.aiCompanyId;
  infoProcess({ inoutOrderNo: inoutOrderNo, year: year, aiCompanyId: aiCompanyId }).then(
    (response) => {
      processedForm.value = response.data;
      addProduceList.value = response.data.farmWarehouseRecordProcsssPOList;
      openProcessed.value = true;
      title.value = "修改入库加工商品信息";
    }
  );
}

//入库量-校验
const validateTotalRatio = (row) => {
  const ratioVal = row.operateStock;
  if (isNaN(ratioVal)) {
    // dataIsValid.value = false;
    proxy.$modal.msgError("请输入数字");
    return false;
  } else if (ratioVal <= 0 || ratioVal > ********* || !/^\d+$/.test(ratioVal)) {
    // dataIsValid.value = false;
    proxy.$modal.msgError("请输入9位以内且大于0的整数");
    return false;
  } else {
    // dataIsValid.value = true;
    return true;
  }
};

/** 提交按钮 */
function submitForm() {
  proxy.$refs["processedFormRef"].validate((valid) => {
    if (valid) {
      // 非空校验
      if (addProduceList.value.length === 0) {
        proxy.$modal.msgError("请添加至少一条商品信息");
        return;
      }
      // 校验每一行的operateStock
      const hasInvalidRow = addProduceList.value.some((row) => !validateTotalRatio(row));
      if (hasInvalidRow) {
        return;
      }
      const emptyInput = addProduceList.value.some((row) => !row.operateStock);
      if (emptyInput) {
        proxy.$modal.msgError("请输入本次入库量");
        return;
      }
      if (processedForm.value.warehouseRecordId != null) {
        processedForm.value.farmWarehouseRecordProcsssPOList = addProduceList.value;
        updateProcess(processedForm.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          openProcessed.value = false;
          getList();
        });
      } else {
        // 从 companyAbbrOption 中查找选中的 aiCompanyId 对应的 companyAbbr
        const selectedCompany = companyAbbrOption.value.find(
          (item) => item.code == processedForm.value.aiCompanyId
        );
        // 如果找到了对应的公司简称，则将其添加到 form 中
        if (selectedCompany) {
          processedForm.value.companyAbbr = selectedCompany.name;
        }
        //查找对应的库房house
        const selectedWarehouse = farmWarehouseOption.value.find(
          (item) => item.code == processedForm.value.warehouseId
        );
        // 如果找到了对应的公司简称，则将其添加到 form 中
        if (selectedWarehouse) {
          processedForm.value.warehouseName = selectedWarehouse.name;
        }
        processedForm.value.farmWarehouseRecordProcsssPOList = addProduceList.value;
        insertProcess(processedForm.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          openProcessed.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const groupIdArrIds = row.groupIdArr || groupIdArr.value;
  proxy
    .$confirm("是否确认删除此数据?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(function () {
      return Array.isArray(groupIdArrIds)
        ? logicDeleteByIds(groupIdArrIds)
        : logicDeleteByWarehouseRecordId(groupIdArrIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    });
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "导入";
  upload.open = true;
}
/** 下载模板操作 */
function importTemplate() {
  postForExcel(
    "/bdh-agric-invest-api/invest/farmWarehouseInput/exportTemplate",
    queryParams.value,
    "投入品入库导入模板"
  );
}
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].clearFiles();
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
      response.msg +
      "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};
/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}

// 导出操作按钮
function handleExport() {
  postForExcel(
    "/bdh-agric-invest-api/invest/farmWarehouseInput/exportExcel",
    queryParams.value,
    "农场投入品入库管理"
  );
}

//确认入库
function handleConfirm() {
  const warehouseRecordIdList = groupIdArr.value;
  confirmWarehouseIn({
    outboundStatus: 1,
    warehouseRecordIdList: warehouseRecordIdList,
  }).then((response) => {
    proxy.$modal.msgSuccess("确认成功");
    getList();
  });
}

//点击库存总量
function clickDetail(row) {
  orgFlag.value = false;
  nextTick(() => {
    orgFlag.value = true;
  });
  if (row.outboundProdType == 2) {
    //加工商品
    const inoutOrderNo = row.inoutOrderNo;
    const year = row.year;
    const aiCompanyId = row.aiCompanyId;
    infoProcess({
      inoutOrderNo: inoutOrderNo,
      year: year,
      aiCompanyId: aiCompanyId,
    }).then((response) => {
      processedForm.value = response.data;
      addProduceList.value = response.data.farmWarehouseRecordProcsssPOList;
      title.value = "入库信息";
      viewOnly.value = true;
      viewDetailOnly.value = true;
      openProcessed.value = true;
    });
  } else {
    //统供商品
    const inoutOrderNo = row.inoutOrderNo;
    const year = row.year;
    const aiCompanyId = row.aiCompanyId;
    infoProcess({
      inoutOrderNo: inoutOrderNo,
      year: year,
      aiCompanyId: aiCompanyId,
    }).then((response) => {
      unifiedForm.value = response.data;
      unifiedList.value = response.data.farmWarehouseRecordProcsssPOList;
      viewOnly.value = true;
      viewDetailOnly.value = true;
      openUnified.value = true;
    });
  }
}

const handleOrgCode = (orgCode) => {
  queryParams.value.orgCode = orgCode.orgCode;
};
const handleOrgCodeForm = (orgCodeInfo) => {
  processedForm.value.orgCode = orgCodeInfo.orgCode;
  processedForm.value.orgName = orgCodeInfo.orgName;
};

const getDict = () => {
  getDicts("year_cd").then((response) => {
    yearOption.value = response.data.sort((a, b) => {
      return Number(b.code) - Number(a.code);
    });
    queryParams.value.year = yearOption.value[0].code;
  });
  getDicts("fertilize_season").then((res) => {
    fertilizeSeasonOption.value = res.data;
    // queryParams.value.fertilizeSeason = fertilizeSeasonOption.value[0].code
  });
  queryAllOrgTree("1").then((res) => {
    var orgList = res.data;
    if (orgList && orgList.length && orgList.length > 0) {
      defaultOrgCode.value = orgList[0].orgCode;
      queryParams.value.orgCode = defaultOrgCode.value;
      getList();
    }
  });
  queryAllCompanyInfoInput({}).then((response) => {
    response.data.forEach((each) => {
      companyAbbrOption.value.push({
        code: each.aiCompanyId,
        name: each.companyAbbr,
        wholeName: each.companyName,
      });
    });
    // queryParams.value.aiCompanyId = companyAbbrOption.value[0].code;
  });
  getFarmWarehouseAll({ orgCode: processedForm.value.orgCode }).then((response) => {
    response.data.forEach((each) => {
      farmWarehouseOption.value.push({
        code: each.warehouseId,
        name: each.warehouseName,
      });
    });
  });
};

function fertilizeSeasonFormatter(row) {
  var name = "";
  fertilizeSeasonOption.value.forEach((item) => {
    if (item.code == row.fertilizeSeason) {
      name = item.name;
    }
  });
  return name;
}
function outboundProdTypeFormatter(row) {
  var name = "";
  outboundProdTypeOption.value.forEach((item) => {
    if (item.code == row.outboundProdType) {
      name = item.name;
    }
  });
  return name;
}
function outboundStatusFormatter(row) {
  var name = "";
  outboundStatusOption.value.forEach((item) => {
    if (item.code == row.outboundStatus) {
      name = item.name;
    }
  });
  return name;
}

//获取加工商品分类
const getAiTypeSort = () => {
  getProduceProcessTypeTreeByCompanyIdInput(productForm.value.aiCompanyId).then(
    (response) => {
      sortOption.value = response.data;
    }
  );
};
function handleParAiTypeChangeProduct() {
  const checkedNode = proxy.$refs["parAiTypeRef"].getCheckedNodes();
  productForm.value.aiTypeId = checkedNode[0].data.aiTypeId;
}

//查看详情
function openDetailDialog(row) {
  getProcessDetailList({ warehouseRecordId: row.warehouseRecordId }).then((response) => {
    processList.value = response.data;
    showFarmDialog.value = true;
  });
}
getDict();
</script>
<style scoped></style>
