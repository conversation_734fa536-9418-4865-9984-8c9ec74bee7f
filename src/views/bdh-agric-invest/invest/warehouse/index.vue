<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form :model="queryParams" ref="queryForm" class="form-line" v-show="showSearch" label-width="80px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="所属单位" prop="orgCode">
                <org-select ref="queryOrgRef" :defaultOrgCode="queryParams.orgCode" @handleOrgCode="handleOrgCodeQuery" clearable/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="仓库名称" prop="warehouseName">
                <el-input v-model="queryParams.warehouseName" placeholder="请输入仓库名称" clearable @keyup.enter="handleQuery" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="负责人" prop="director">
                <el-input v-model="queryParams.director" placeholder="请输入负责人" clearable @keyup.enter="handleQuery" />
              </el-form-item>
            </el-col>
            <el-col :span="6" align="right">
              <el-button icon="Refresh"  @click="resetQuery">重置</el-button>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>
          <!-- <el-form-item label="仓库类型" prop="warehouseType">
            <el-select v-model="queryParams.warehouseType" placeholder="请选择仓库类型">
              <el-option
                v-for="dict in warehouseTypeOptions"
                :key="dict.code"
                :label="dict.name"
                :value="dict.code"
              ></el-option>
            </el-select>
          </el-form-item> -->
        </el-form>
      </el-collapse-transition>
    </div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="Plus" v-hasPermi="['warehouse:insert']" @click="handleAdd">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="Edit" :disabled="single" v-hasPermi="['warehouse:update']" @click="handleUpdate">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="Delete" :disabled="multiple" v-hasPermi="['warehouse:logicDeleteById']" @click="handleDelete">删除
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border :data="asMpWarehouseList" :height="tableHeight" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="所属单位" align="center" prop="orgName" />
      <el-table-column label="仓库名称" align="center" prop="warehouseName" />
      <el-table-column label="面积(m²)" align="center" prop="warehouseArea" />
      <el-table-column label="负责人" align="center" prop="director" />
      <el-table-column label="联系方式" align="center" prop="contactNo" />
      <el-table-column label="仓库位置" align="center" prop="address" :show-overflow-tooltip="true" />
      <el-table-column label="仓库类型" align="center" prop="warehouseType" :formatter="warehouseTypeFormatter" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button v-hasPermi="['warehouse:update']" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)">修改
          </el-button>
          <el-button v-hasPermi="['warehouse:logicDeleteByIds']" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.rows"
      @pagination="getList" />

    <!-- 添加或修改生资仓库信息对话框 -->
    <el-dialog :title="title" v-model="open" width="764px" append-to-body :close-on-click-modal="false"
      :before-close="cancel">
      <el-form ref="formRef" :model="form" :rules="rules" label-position="top" v-if="open">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="仓库名称" prop="warehouseName">
              <el-input v-model="form.warehouseName" placeholder="请输入仓库名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属单位" prop="orgCode">
              <org-select ref="shipOrgRef" :defaultOrgCode="dorgCode" @handleOrgCode="handleOrgCodeQueryDetail" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="面积" prop="warehouseArea">
              <el-input v-model="form.warehouseArea" placeholder="请输入面积">
                <template #suffix>(m²)</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="负责人" prop="director">
              <el-input v-model="form.director" maxlength="20" placeholder="请输入负责人" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="联系方式" prop="contactNo">
              <el-input v-model="form.contactNo" placeholder="请输入联系方式" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="仓库位置" prop="address">
              <el-input v-model="form.address" maxlength="200" placeholder="请输入仓库位置" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="仓库类型" prop="warehouseType">
              <el-select v-model="form.warehouseType" placeholder="请选择仓库类型">
                <el-option v-for="dict in warehouseTypeOptions" :key="dict.code" :label="dict.name"
                  :value="dict.code"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="经度" prop="longitude">
              <el-input v-model="form.longitude" placeholder="请输入经度" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纬度" prop="latitude">
              <el-input v-model="form.latitude" placeholder="请输入纬度" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="/invest/warehouse/queryByPage">
import {
  listWarehouse,
  getWarehouse,
  delWarehouse,
  delWarehouses,
  addWarehouse,
  updateWarehouse
} from "@/api/bdh-agric-invest/invest/warehouse";
import { queryAllOrgTree} from "@/api/bdh-agric-invest/system/dict/data";
import { getDicts } from "@/api/bdh-agric-invest/system/dict/data";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import { ref, reactive, toRefs, onMounted, watch, nextTick, getCurrentInstance, onBeforeUnmount } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const { proxy } = getCurrentInstance();

const validateNum = (rule, value, callback) => {
  var reg = /^([0-9]{1}|^[1-9]{1}\d{1,15})(\.\d{1,4})?$/
  if (value === null || value === '' || typeof (value) == 'undefined') {
    callback(new Error('请输入数字'))
  } else if (reg.test(value) && value.toString().split('.')[0].length > 10) {
    callback(new Error('数字过大，请确认'))
  } else if (reg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入数字(可带四位小数)'))
  }
};

const validatePhone = (rule, value, callback) => {
  let regPone = null
  let mobile = /^1(3|4|5|6|7|8|9)\d{9}$/ //手机正则
  let tel = /^(0[0-9]{2,3}\-)([2-9][0-9]{4,7})+(\-[0-9]{1,4})?$/ //座机正则
  if (value === null || value === '' || typeof (value) == 'undefined' || mobile.test(value) || tel.test(value)) {
    callback()
  } else {
    callback(new Error('请输入正确的联系电话'))
  }
};

const validateLongitude = (rule, value, callback) => {
  //var reg = /^[EW]?((\d|[1-9]\d|1[0-7]\d)[s\-,;°度](\d|[0-5]\d)[s\-,;′＇'’分](\d|[0-5]\d)(\.\d{1,2})?[s\-,;"＂″”秒]?$)/
  var reg = /^[-+]?(((0|1?[0-7]?[0-9]?)|([1-9]?[0-9]?))(([.][0-9]{1,8})?)|180(([.][0]{1,8})?))$/
  if (value === null || value === '' || typeof (value) == 'undefined' || reg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入正确的经度'))
  }
};

const validateLatitude = (rule, value, callback) => {
  //var reg = /^[EW]?((\d|[1-8]\d)[s\-,;°度](\d|[0-5]\d)[s\-,;′＇'’分](\d|[0-5]\d)(\.\d{1,2})?[s\-,;"＂″”秒]?$)/
  var reg = /^[-+]?([1-8]?\d(\.\d{1,8})?|90(\.0{1,8})?)$/
  if (value === null || value === '' || typeof (value) == 'undefined' || reg.test(value)) {
    callback()
  } else {
    callback(new Error('请输入正确的纬度'))
  }
};


const searchDom = ref(null);
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const queryOrgRef = ref(null);
const shipOrgRef = ref(null);
const formRef = ref(null);

const data = reactive({
  // 遮罩层
  loading: true,
  // 选中数组
  ids: [],
  names: [],
  // 非单个禁用
  single: true,
  // 非多个禁用
  multiple: true,
  // 显示搜索条件
  showSearch: true,
  // 总条数
  total: 0,
  // 生资仓库信息表格数据
  asMpWarehouseList: [],
  // 弹出层标题
  title: '',
  // 是否显示弹出层
  open: false,
  // 查询参数
  queryParams: {
    page: 1,
    rows: 10,
    warehouseName: null,
    orgCode: null,
    orgName: null,
    warehouseArea: null,
    director: null,
    contactNo: null,
    warehouseType: "",
    longitude: null,
    latitude: null,
    dataStatus: null,
    statusCd: null
  },
  // 表单参数
  form: {},
  // 表单校验
  rules: {
    orgCode: [{ required: true, message: '请选择所属单位', trigger: "change" }],
    warehouseName: [{ required: true, message: '请填写生资仓库名称', trigger: "blur" }],
    director: [{ required: true, message: '请填写负责人', trigger: "blur" }],
    warehouseArea: [{ required: true, validator: validateNum, trigger: "blur" }],
    contactNo: [{ required: false, validator: validatePhone, trigger: 'blur' }],
    longitude: [{ required: false, validator: validateLongitude, trigger: 'blur' }],
    latitude: [{ required: false, validator: validateLatitude, trigger: 'blur' }],
  },
  orgTreeList: [],
  warehouseTypeOptions: [],
  dorgCode: true
});
const {
  loading,
  ids,
  names,
  single,
  multiple,
  showSearch,
  total,
  asMpWarehouseList,
  title,
  open,
  queryParams,
  form,
  rules,
  orgTreeList,
  warehouseTypeOptions,
  dorgCode
} = toRefs(data);

const defaultOrgCode = ref('');
/** 组织机构下拉 */
function handleOrgCodeQuery(orgInfo) {
  if (!orgInfo.orgCode) queryOrgRef.value.clear();
  queryParams.value.orgCode = orgInfo.orgCode;
  queryParams.value.orgName = orgInfo.orgName;
}

queryAllOrgTree("3").then((res) => {
  var orgList = res.data;
  if (orgList && orgList.length && orgList.length > 0) {
    defaultOrgCode.value = orgList[0].orgCode;
    queryParams.value.orgCode = defaultOrgCode.value;
  }
  handleQuery()
})

/** 新增修改对话框 —— 组织机构下拉 */
function handleOrgCodeQueryDetail(orgInfo) {
  if (!orgInfo.orgCode) shipOrgRef.value.clear();
  form.value.orgCode = orgInfo.orgCode;
  form.value.orgName = orgInfo.orgName;
}

/** 查询生资仓库信息列表 */
function getList() {
  loading.value = true;
  if (
    queryParams.value.orgCode !== null &&
    queryParams.value.orgCode &&
    typeof queryParams.value.orgCode !== 'string'
  ) {
    queryParams.value.orgCode =
      queryParams.value.orgCode[queryParams.value.orgCode.length - 1];
  }
  listWarehouse(queryParams.value).then(response => {
    asMpWarehouseList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    warehouseId: null,
    warehouseName: null,
    orgCode: null,
    orgName: null,
    warehouseArea: null,
    director: null,
    contactNo: null,
    warehouseType: null,
    longitude: null,
    latitude: null,
    remark: null,
    dataStatus: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    statusCd: null
  };
  if (formRef.value) {
    formRef.value.resetFields();
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  if (Array.isArray(queryParams.value.orgCode)) {
    queryParams.value.orgCode =
      queryParams.value.orgCode[queryParams.value.orgCode.length - 1];
  }
  queryParams.value.page = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  queryOrgRef.value.clear()
  queryParams.value.orgCode = defaultOrgCode.value;
  queryOrgRef.value.setValue(defaultOrgCode.value, "");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.warehouseId);
  names.value = selection.map(item => item.warehouseName);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = '添加生资仓库';
  dorgCode.value = true;
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const warehouseId = row.warehouseId || ids.value;
  getWarehouse(warehouseId).then(response => {
    form.value = response.data;
    open.value = true;
    dorgCode.value = form.value.orgCode;
    title.value = '修改生资仓库';
  });
}

/** 提交按钮 */
function submitForm() {
  formRef.value.validate(valid => {
    if (valid) {
      if (Array.isArray(form.value.orgCode)) {
        form.value.orgCode =
          form.value.orgCode[form.value.orgCode.length - 1];
      }
      if (form.value.warehouseId != null) {
        updateWarehouse(form.value).then(response => {
          ElMessage.success("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addWarehouse(form.value).then(response => {
          ElMessage.success("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const warehouseIds = row.warehouseId || ids.value;
  const warehouseNames = row.warehouseName || names.value;
  ElMessageBox.confirm('是否确认删除生资仓库信息仓库名称为"' + warehouseNames + '"的数据项?', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(function () {
    return Array.isArray(warehouseIds) ? delWarehouses(warehouseIds) : delWarehouse(warehouseIds);
  }).then(() => {
    getList();
    ElMessage.success("删除成功");
  }).catch(() => {}); // Catch to prevent uncaught promise rejection
}

function warehouseTypeFormatter(row, column) {
  var name = '';
  warehouseTypeOptions.value.forEach(item => {
    if (item.code == row.warehouseType) {
      name = item.name;
    }
  });
  return name;
}


onMounted(() => {
  getDicts('warehouse_type').then((response) => {
    warehouseTypeOptions.value = response.data;
  });
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
      ? window.innerHeight - searchHeight.value - 220
      : window.innerHeight - 220;
});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
      ? window.innerHeight - searchHeight.value - 220
      : window.innerHeight - 220;
});




</script>

<style scoped>
.el-link {
  margin-right: 8px;
}

.el-link .el-icon--right.el-icon {
  vertical-align: text-bottom;
}
.mb8 {
  min-height: 30px;
}
.title {
  font-size: 20px;
  text-align: center;
  margin-bottom: 4px;
  font-weight: 600;
}
.unit {
  font-size: 10px;
  text-align: right;
  margin-bottom: 4px;
  font-weight: 600;
}
/* Adjust input width if necessary, similar to shipmentQuantity */
/* :deep(.el-input__wrapper) {
  width: 190px !important;
} */
</style>