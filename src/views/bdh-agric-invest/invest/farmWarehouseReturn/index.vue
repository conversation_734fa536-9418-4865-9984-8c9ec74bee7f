<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form
          :model="queryParams"
          ref="queryRef"
          class="form-line"
          v-show="showSearch"
          label-width="80px"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年份" prop="year">
                <el-select
                  v-model="queryParams.year"
                  clearable
                  placeholder="请选择年份"
                >
                  <el-option
                    v-for="dict in yearOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item

                label="所在单位"
                prop="orgCode"
              >
                <org-select
                  ref="queryOrgRef"
                  @handleOrgCode="handleOrgCode"
                  :level="1"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="企业名称"
                prop="aiCompanyId"
              >
                <el-select
                  v-model="queryParams.aiCompanyId"
                  clearable
                  placeholder="请选择企业名称"
                >
                  <el-option
                    v-for="dict in companyNameOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="作业季节" prop="fertilizeSeason">
                <el-select
                  v-model="queryParams.fertilizeSeason"
                  placeholder="请选择作业季节"
                  clearable
                >
                  <el-option
                    v-for="dict in fertilizeSeasonOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="出库对象" prop="returnByType">
                <el-select
                  v-model="queryParams.returnByType"
                  placeholder="请选择出库对象"
                  clearable
                >
                  <el-option
                    v-for="dict in returnByTypeOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="queryParams.returnByType == '2'">
              <el-form-item label="农户姓名" prop="returnName">
                <el-input
                  v-model="queryParams.returnName"
                  placeholder="请输入农户姓名"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="queryParams.returnByType == '2'">
              <el-form-item label="身份证号" prop="returnBy">
                <el-input
                  v-model="queryParams.returnBy"
                  placeholder="请输入身份证号"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="queryParams.returnByType == '1'">
              <el-form-item label="出库到库房" prop="returnBy">
                <el-select
                  v-model="queryParams.returnBy"
                  placeholder="请选择出库库房"
                  clearable
                >
                  <el-option
                    v-for="dict in farmWarehouseOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="商品类型" prop="outboundProdType">
                <el-select
                  v-model="queryParams.outboundProdType"
                  placeholder="请选择商品类型"
                  clearable
                >
                  <el-option
                    v-for="dict in outboundProdTypeOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="商品名称"
                prop="aiTypeSubName"
              >
                <el-input
                  v-model="queryParams.aiTypeSubName"
                  placeholder="请输入商品名称"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                label="审批状态"
                prop="auditStatus"
              >
                <el-select
                  v-model="queryParams.auditStatus"
                  clearable
                  placeholder="全部"
                >
                  <el-option
                    v-for="dict in auditStatusOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="Search" @click="handleQuery"
        >搜索</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['farmWarehouseReturn:insert']"
          >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="Delete"
          :disabled="deleteBatch"
          @click="handleDelete"
          v-hasPermi="['farmWarehouseReturn:logicDeleteByIds']"
          >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          v-hasPermi="['prodreturn:exportExcel']"
          icon="Download"
          @click="handleExport"
          >导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            v-hasPermi="['farmWarehouseReturn:confirmWarehouseReturn']"
          icon="CircleCheck"
          @click="handleApprove"
          :disabled="multiple"
          >确认退货
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      border
      :height="tableHeight"
      :data="prodreturnList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="退货编码" align="center" prop="returnCode" />
      <el-table-column label="年份" align="center" prop="year" />
      <el-table-column
        label="作业季节"
        align="center"
        prop="fertilizeSeason"
        :formatter="fertilizeSeasonFormatter"
      />
      <el-table-column label="所在单位" align="center" prop="orgName" />
      <el-table-column label="出库房" align="center" prop="warehouseId" :formatter="farmWarehouseOptionFmt"/>
      <el-table-column label="出库对象" align="center" prop="returnByType" :formatter="returnByTypeOptionFmt" />
      <el-table-column label="出库对象名称" align="center" prop="returnName">
        <template #default="scope">
          <div style="color: dodgerblue">{{ scope.row.returnName }}({{scope.row.returnBy}})</div>
        </template>
      </el-table-column>
      <el-table-column label="出库商品类型" align="center" prop="aiTypeName" />
      <el-table-column label="企业简称" align="center" prop="aiCompanyId" :formatter="companyNameOptionFmt" />
      <el-table-column label="投入品名称" align="center" prop="aiTypeSubName" />
      <el-table-column label="规格" align="center" prop="aiTypeSubSpecs" />
      <el-table-column label="退货数量" align="center" prop="returnNum">
        <template #default="scope">
          <div style="color: red">{{ scope.row.returnNum }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="退货原因"
        align="center"
        prop="returnReason"
        :formatter="returnReasonFmt"
      />
      <el-table-column label="上报日期" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="审核状态"
        align="center"
        prop="auditStatus"
        :formatter="auditStatusFmt"
      />
      <el-table-column
        label="当前审核人"
        align="center"
        prop="auditPerson"
        :show-overflow-tooltip="true"
        :formatter="fmtAuditName"
      />
      <el-table-column
        label="操作"
        align="center"
        width="150px"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-link
            type="primary"
            icon="Edit"
            v-if="scope.row.auditStatus === 0"
            @click="handleUpdate(scope.row, 'update')"
            v-hasPermi="['farmWarehouseReturn:update']"
            >修改
          </el-link>
          <el-link
            type="primary"
            icon="View"
            @click="handleUpdate(scope.row, 'view')"
            v-hasPermi="['farmWarehouseReturn:info']"
            >查看详情
          </el-link>
          <el-link
            type="error"
            icon="Delete"
            v-if="scope.row.auditStatus === 0"
            @click="handleDelete(scope.row)"
            v-hasPermi="['farmWarehouseReturn:logicDeleteById']"
            >删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.rows"
      @pagination="getList"
    />

    <!-- 添加或修改投入品退货管理对话框 -->
    <el-dialog
      :title="title"
      :close-on-click-modal="false"
      v-model="open"
      width="800px"
      append-to-body
    >
      <el-form
        ref="prodreturnRef"
        :disabled="type == 'view'"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="年份" prop="year">
              <el-select v-model="form.year" clearable placeholder="请选择年份">
                <el-option
                  v-for="dict in yearOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在单位" prop="orgCode">
              <org-select
                @handleOrgCode="handleOrgCode"
                v-if="rstorg"
                :defaultOrgCode="form.orgCode"
                :orgCode="form.orgCode"
                ref="formOrgRef"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="企业名称" prop="aiCompanyId">
              <el-select
                v-model="form.aiCompanyId"
                clearable
                placeholder="请选择企业名称"
              >
                <el-option
                  v-for="dict in companyNameOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="作业季节" prop="fertilizeSeason">
              <el-select
                v-model="form.fertilizeSeason"
                clearable
                placeholder="请选择作业季节"
              >
                <el-option
                  v-for="dict in fertilizeSeasonOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库单号" prop="inoutOrderNo">
              <el-input
                v-model="form.inoutOrderNo"
                readonly
                class="input-with-select"
                placeholder="请选择出库单"
              >
                <template #append>
                  <el-button type="danger" @click="handleShipInfo"
                    >选择</el-button
                  >
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="库房名称" prop="warehouseName">
              <el-input
                v-model="form.warehouseName"
                disabled
                placeholder="请输入库房名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库对象" prop="returnByType">
              <el-select
                  v-model="form.returnByType"
                  placeholder="请选择出库对象"
                  clearable
                  disabled
              >
                <el-option
                    v-for="dict in returnByTypeOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="农户姓名" prop="returnName">
              <el-input
                v-model="form.returnName"
                disabled
                placeholder="请输入农户姓名"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证" prop="returnBy">
              <el-input
                v-model="form.returnBy"
                disabled
                placeholder="请输入身份证"
              />
            </el-form-item> </el-col
          ><el-col :span="8">
            <el-form-item label="地号名称" prop="plotName">
              <el-input
                v-model="form.plotName"
                disabled
                placeholder="请输入地号名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库商品类型" prop="aiTypeName">
              <el-input
                v-model="form.aiTypeName"
                disabled
                placeholder="请输入出库商品类型"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库商品名称" prop="aiTypeSubName">
              <el-input
                v-model="form.aiTypeSubName"
                disabled
                placeholder="请输入出库商品名称"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="规格" prop="aiTypeSubSpecs">
              <el-input
                v-model="form.aiTypeSubSpecs"
                disabled
                placeholder="请输入规格"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="退货原因" prop="returnReason">
              <el-select
                v-model="form.returnReason"
                clearable
                placeholder="请选择退货原因"
              >
                <el-option
                  v-for="dict in returnReasonDict"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="退货数量" prop="returnNum">
              <el-input-number v-model="form.returnNum" :min="1" :max="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="问题描述" prop="quesDesc">
              <el-input
                v-model="form.quesDesc"
                type="textarea"
                placeholder="请输入问题描述"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="商品图片" prop="attachments">
              <imageUpload
                :limit="3"
                v-model="form.attachments"
                :viewImageOnly="viewImageOnly"
              ></imageUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            v-show="type == 'update'"
            @click="submitFormDebounce"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      title="发货单信息"
      v-model="showShipInfo"
      :width="1200"
      :close-on-click-modal="false"
      append-to-body
    >
      <ship-info
        v-if="shipInfoFlag"
        @shipInfo="ckShipInfo"
        v-model="queryShipForm"
      ></ship-info>
    </el-dialog>
  </div>
</template>

<script setup name="/invest/farmWarehouseReturn/queryByPage">
import { getDicts, queryAllOrgTree } from "@/api/bdh-agric-invest/system/dict/data";
import { postForExcel } from "@/api/bdh-agric-invest/invest/excel";
import ShipInfo from "./shipInfo";
import { nextTick, ref } from "vue";
import { debounce } from "lodash";
import {
  getFarmWarehouseAll,
  queryAllCompanyInfoInput,
} from "@/api/bdh-agric-invest/invest/farmWarehouseInput";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import {
  confirmWarehouseReturnList,
  infoFarmWarehouseReturn,
  insertfarmWarehouseReturn,
  listfarmWarehouseReturn,
  logicDeleteByIdsFarmWarehouseReturn,
  updatefarmWarehouseReturn,
} from "@/api/bdh-agric-invest/invest/farmWarehouseReturn";
const rstorg = ref(true); //org回显
const { proxy } = getCurrentInstance();
const prodreturnList = ref([]); //主页列表
const returnReasonDict = ref([]); //表单中退货原因字典
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const showShipInfo = ref(false);
const instId = ref(null);
const viewImageOnly = ref(false);//上传附件只读
//各种字典
const yearOption = ref([]);//年份字典
const companyNameOption = ref([]);//企业简称字典
const fertilizeSeasonOption = ref([]); //作业季节字典
const farmWarehouseOption = ref([]); //农场库房列表
const auditStatusOption = ref([
  {
    code: 0,
    name: "未审核",
  },
  {
    code: 1,
    name: "已审核",
  },
]);//审核状态字典
const outboundProdTypeOption = ref([
  {
    code: 1,
    name: "统供商品",
  },
  {
    code: 2,
    name: "加工商品",
  },
]); //出库商品类型字典
const returnByTypeOption = ref([
  {
    code: 1,
    name: "农场",
  },
  {
    code: 2,
    name: "农户",
  },
]); //退货人类型字典

//发货单明细
const shipInfoFlag = ref(true);
const shipDetailInfo = ref({}); //发货单明细弹窗
//控制批量删除按钮
const hasTypeAndStatusOne = ref(true);
const deleteBatch = ref(true); //控制批量删除按钮

const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const type = ref("update");

const data = reactive({
  form: {},
  queryShipForm: {
    year: new Date(),
    orgCode: null,
    aiCompanyId: null,
    fertilizeSeason: null,
  },
  year: new Date(),
  //分页查询
  queryParams: {
    rows: 10,
    page: 1,
    aiCompanyId: null,
    fertilizeSeason: null,
    year: null,
    orgCode: null,
    auditStatus: null,
    returnByType: null,
    returnName: null,
    returnBy: null,
    outboundProdType: null,
    aiTypeSubName: null,
  },
  rules: {
    year: [{ required: true, message: "请选择年份", trigger: "change" }],
    orgCode: [{ required: true, message: "请选择所在单位", trigger: "change" }],
    aiCompanyId: [{ required: true, message: "请选择企业", trigger: "change" }],
    inoutOrderNo: [
      { required: true, message: "请选择出库单号", trigger: "change" },
    ],
    returnReason: [
      { required: true, message: "请选择退货原因", trigger: "change" },
    ],
    fertilizeSeason: [
      { required: true, message: "请选择作业季节", trigger: "change" },
    ],
    returnNum: [
      { required: true, message: "请输入退货数量", trigger: "change" },
    ],
    quesDesc: [
      { required: true, message: "请输入问题描述", trigger: "change" },
      { max: 100, message: "最多100个字符", trigger: "change" },
    ],
    attachments: [
      { required: true, message: "请上传商品图片", trigger: "change" },
    ],
  },
});
const queryOrgRef = ref();
const defaultOrgCode = ref("");
const defaultOrgName = ref("");
const { queryParams, form, rules, year, queryShipForm } = toRefs(data);

onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 220
    : window.innerHeight - 220;
});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 220
    : window.innerHeight - 220;
});

function ckShipInfo(detail) {
  shipDetailInfo.value = detail;
  form.value.warehouseName = shipDetailInfo.value.warehouseName;//库房名称
  form.value.returnByType = shipDetailInfo.value.outboundTargetType;//出库对象
  form.value.returnName = shipDetailInfo.value.outboundTargetName;//农户姓名
  form.value.returnBy = shipDetailInfo.value.outboundTargetCode;//身份证号码
  form.value.plotName = shipDetailInfo.value.plotName;//地块名称
  form.value.aiTypeName = shipDetailInfo.value.aiTypeName;//商品类型
  form.value.aiTypeSubName = shipDetailInfo.value.aiTypeSubName;//商品名称
  form.value.aiTypeSubId = shipDetailInfo.value.aiTypeSubId;//商品名称id
  form.value.aiTypeSubSpecs = shipDetailInfo.value.aiTypeSubSpecs;//商品规格
  form.value.inoutOrderNo = shipDetailInfo.value.inoutOrderNo;//出库单号
  showShipInfo.value = false;
}



/** 查询投入品退货管理列表 */
function getList() {
  loading.value = true;
  listfarmWarehouseReturn(queryParams.value).then((response) => {
    prodreturnList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

const handleOrgCode = ({ orgCode, orgName }) => {
  queryParams.value.orgCode = orgCode;
  form.value.orgCode = orgCode;
  form.value.orgName = orgName;
};

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    warehouseName: null,
    warehouseId: null,
    returnByType: null,
    returnName: null,
    returnBy: null,
    plotName: null,
    returnReason: null,
    returnNum: null,
    quesDesc: null,
    attachments: null,
    orgCode: null,
    orgName: null,
    year:null,
    aiCompanyId:null,
    fertilizeSeason:null,
    aiTypeName:null,
    aiTypeId:null,
    aiTypeSubName:null
  };
  proxy.resetForm("prodreturnRef");
}
const getDict = () => {
  //查询年份
  getDicts("year_cd").then((response) => {
    yearOption.value = response.data.sort((a, b) => {
      return Number(b.code) - Number(a.code);
    });
    queryParams.value.year = yearOption.value[0].code;
    queryAllOrgTree("1").then((res) => {
      var orgList = res.data;
      if (orgList && orgList.length && orgList.length > 0) {
        defaultOrgCode.value = orgList[0].orgCode;
        defaultOrgName.value = orgList[0].orgName;
        queryParams.value.orgCode = defaultOrgCode.value;
      }
      getList();
    });
  });
  //企业简称
  queryAllCompanyInfoInput({}).then((response) => {
    response.data.forEach((each) => {
      companyNameOption.value.push({
        code: each.aiCompanyId,
        name: each.companyAbbr,
        wholeName: each.companyName,
      });
    });
  });
  //退货原因
  getDicts("return_reason_dict").then((response) => {
    returnReasonDict.value = response.data;
  });
  //作业季节
  getDicts("fertilize_season").then((response) => {
    fertilizeSeasonOption.value = response.data;
  });
  //库房列表
  getFarmWarehouseAll({ orgCode: queryParams.value.orgCode }).then(
    (response) => {
      response.data.forEach((each) => {
        farmWarehouseOption.value.push({
          code: each.warehouseId,
          name: each.warehouseName,
        });
      });
    }
  );
};

//当前审核人
function fmtAuditName(row, column) {
  if (row.flowMsg && JSON.parse(row.flowMsg).auditNames) {
    return JSON.parse(row.flowMsg).auditNames;
  }
  return "无";
}

//退货原因format
function returnReasonFmt(row, value) {
  let reasonMsg = "";
  returnReasonDict.value.forEach(function (item) {
    if (row.returnReason == item.code) {
      reasonMsg = item.name;
    }
  });
  return reasonMsg;
}
//企业简称format
function companyNameOptionFmt(row, value) {
  let companyName = "";
  companyNameOption.value.forEach(function (item) {
    if (row.aiCompanyId == item.code) {
      companyName = item.name;
    }
  });
  return companyName;
}
//出库对象类型format
function returnByTypeOptionFmt(row, value) {
  let returnByType = "";
  returnByTypeOption.value.forEach(function (item) {
    if (row.returnByType == item.code) {
      returnByType = item.name;
    }
  });
  return returnByType;
}

//库房名称format
function farmWarehouseOptionFmt(row, value) {
  let farmWarehouse = "";
  farmWarehouseOption.value.forEach(function (item) {
    if (row.warehouseId == item.code) {
      farmWarehouse = item.name;
    }
  });
  return farmWarehouse;
}

//审核状态format
function auditStatusFmt(row, value) {
  let auditStatusName = "";
  auditStatusOption.value.forEach(function (item) {
    if (row.auditStatus == item.code) {
      auditStatusName = item.name;
    }
  });
  return auditStatusName;
}
//作业季节format
function fertilizeSeasonFormatter(row) {
  for (let i = 0; i < fertilizeSeasonOption.value.length; i++) {
    if (fertilizeSeasonOption.value[i].code == row.fertilizeSeason) {
      return fertilizeSeasonOption.value[i].name;
    }
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  // queryParams.value.rows = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryOrgRef.value.clear();
  queryParams.value.year = yearOption.value[0].code;
  queryParams.value.orgCode = defaultOrgCode.value;
  queryOrgRef.value.setValue(defaultOrgCode.value, "");
  handleQuery();
}

//选择发运单
function handleShipInfo() {
  if (!form.value.year) {
    proxy.$modal.msgError("请选择年份");
    return;
  }
  if (!form.value.orgCode) {
    proxy.$modal.msgError("请选择所在单位");
    return;
  }
  if (!form.value.aiCompanyId) {
    proxy.$modal.msgError("请选择企业名称");
    return;
  }
  if (!form.value.fertilizeSeason) {
    proxy.$modal.msgError("请选择作业季节");
    return;
  }

  queryShipForm.value = {
    year: form.value.year,
    orgCode: form.value.orgCode,
    aiCompanyId: form.value.aiCompanyId,
    fertilizeSeason: form.value.fertilizeSeason,
  };
  //refresh shipInfo 刷新发货单选择模块
  shipInfoFlag.value = false;
  nextTick(() => {
    shipInfoFlag.value = true;
  });
  showShipInfo.value = true;
}

// // 多选框选中数据
// function handleSelectionChange(selection) {
//   ids.value = selection.map((item) => item.farmReturnId);
//   single.value = selection.length != 1;
//   if (selection.length != 1) {
//     single.value = true;
//   } else {
//     //auditStatus==0
//     let canAudit = [];
//     canAudit = selection.filter((item) => item.auditStatus == 0);
//     single.value = !(canAudit.length == 1);
//     if (!single.value) {
//       instId.value = selection.map((item) => item.instId)[0];
//     }
//   }
//   //审核不通过的才可以删除
//   let canRemove = [];
//   if (selection && selection.length) {
//     //如果选择了，就判断一下选择的数据里 是否有不允许删除的变量
//     canRemove = selection.filter((item) => item.auditStatus == 1);
//     multiple.value = !(canRemove && canRemove.length == selection.length);
//   } else {
//     multiple.value = true;
//   }
// }
//控制批量删除按钮
function hasItemWithTypeAndStatusOne(selection) {
  for (const item of selection) {
    if (item.auditStatus !== 0) {
      return true; // 如果找到任何不满足条件的item，返回true
    }
  }
  return false; // 没有找到满足条件的item，返回false
}
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.farmReturnId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
  hasTypeAndStatusOne.value = hasItemWithTypeAndStatusOne(selection); // 更新新的响应式值
  deleteBatch.value = !(!multiple.value && !hasTypeAndStatusOne.value);
}
//确认退货
function handleApprove() {
  if (ids.value) {
    confirmWarehouseReturnList({ idList: ids.value }).then((response) => {
      if (response.success) {
        proxy.$modal.msgSuccess("确认成功");
        getList();
      }
    });
  }
}

/** 新增按钮操作 */
function handleAdd() {
  type.value = "update";
  reset();
  if (proxy.$refs["formOrgRef"]) {
    proxy.$refs["formOrgRef"].clear();
  }
  rstorg.value = false;
  nextTick(() => {
    rstorg.value = true;
  });
  form.value.aiCompanyId = null;
  form.value.orgCode = defaultOrgCode.value;
  form.value.orgName = defaultOrgName.value;
  form.value.year = new Date().getFullYear();
  form.value.returnNum = 1;
  open.value = true;
  title.value = "新增农场出库退货";
  viewImageOnly.value = false;
}

/** 修改按钮操作 */
function handleUpdate(row, addtype) {
  type.value = addtype;
  reset();
  const farmReturnId = row.farmReturnId;
  infoFarmWarehouseReturn(farmReturnId).then((response) => {
    form.value = response.data;
    year.value = new Date(form.value.year);
    open.value = true;
    if (addtype == "view") {
      title.value = "查看农场出库退货";
      viewImageOnly.value = true;
    } else {
      title.value = "修改农场出库退货";
      viewImageOnly.value = false;
    }
    rstorg.value = false;
    nextTick(() => {
      rstorg.value = true;
    });
  });
}

/** 提交按钮 */
const submitFormDebounce = debounce(submitForm, 500);
function submitForm() {
  proxy.$refs["prodreturnRef"].validate((valid) => {
    if (valid) {
      var subform = {
        aiCompanyId: form.value.aiCompanyId,
        returnByType: form.value.returnByType,
        returnName: form.value.returnName,
        returnBy: form.value.returnBy,
        plotName: form.value.plotName,
        returnReason: form.value.returnReason,
        returnNum: form.value.returnNum,
        quesDesc: form.value.quesDesc,
        attachments:form.value.attachments,
        orgCode: form.value.orgCode,
        orgName: form.value.orgName,
        year: form.value.year,
        fertilizeSeason: form.value.fertilizeSeason,
        aiTypeSubId:form.value.aiTypeSubId,
        aiTypeSubName:form.value.aiTypeSubName,
        inoutOrderNo:form.value.inoutOrderNo,
        farmReturnId:form.value.farmReturnId
      };
      if (form.value.farmReturnId != null) {
        updatefarmWarehouseReturn(subform).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          year.value = null;
          form.value.aiCompanyId = null;
          proxy.$refs["formOrgRef"].clear();
          getList();
        });
      } else {
        insertfarmWarehouseReturn(subform).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          year.value = null;
          form.value.aiCompanyId = null;
          proxy.$refs["formOrgRef"].clear();
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  var farmReturnIds = [];
  if (row.farmReturnId) {
    farmReturnIds = [row.farmReturnId];
  } else {
    farmReturnIds = ids.value;
  }
  proxy.$modal
    .confirm("是否确认删除此数据？")
    .then(function () {
      return logicDeleteByIdsFarmWarehouseReturn(farmReturnIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  postForExcel(
    "/invest/farmWarehouseReturn/exportExcel",
    queryParams.value,
    "农场出库退货"
  );
}

getDict();
</script>
<style scoped>
.el-descriptions {
  margin-top: 20px;
}
.cell-item {
  display: flex;
  align-items: center;
}
.margin-top {
  margin-top: 20px;
}
.input-with-select {
  background-color: var(--el-fill-color-blank);
}
</style>
