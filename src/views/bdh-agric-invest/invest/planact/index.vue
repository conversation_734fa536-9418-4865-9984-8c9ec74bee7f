<!--
@name: 收货人管理
@description:
@author: manchunyu
@time: 2022-12-02
-->
<template>
  <div class="app-container">
    <!-- 筛选条件及搜索栏 -->
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form
          v-show="showSearch"
          ref="queryRef"
          :model="_this.queryParams"
          label-width="80px"
          class="form-line"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年份" prop="year">
                <el-select
                  v-model="_this.queryParams.year"
                  placeholder="请选择年份"
                  @change="handleQuery"
                >
                  <el-option
                    v-for="dict in _this.yearOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业名称" prop="aiCompanyId">
                <el-select
                  v-model="_this.queryParams.aiCompanyId"
                  placeholder="请选择企业名称"
                  @change="chgCompany"
                >
                  <el-option
                    v-for="dict in _this.companyAbbrOption"
                    :key="dict.aiCompanyId"
                    :label="dict.companyAbbr"
                    :value="dict.aiCompanyId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="作业季节" prop="fertilizeSeason">
                <el-select
                  v-model="_this.queryParams.fertilizeSeason"
                  placeholder="请选择作业季节"
                  @change="handleQuery"
                  clearable
                >
                  <el-option
                    v-for="dict in _this.fertilizes"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所在单位" prop="orgCode">
                <org-select
                  ref="queryOrgRef"
                  :defaultOrgCode="true"
                  :level="3"
                  @handleOrgCode="handleOrgCodeQuery"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="生资分类" prop="aiTypeId">
                <ProduceTypeDynamic
                  :companyId="_this.queryParams.aiCompanyId"
                  @handleTypeId="handleTypeId"
                  ref="produceTypeRef"
                ></ProduceTypeDynamic>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="商品名称" prop="aiTypeSubName">
                <el-input
                  v-model="_this.queryParams.aiTypeSubName"
                  placeholder="请输入商品名称"
                  maxlength="100"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <b>
                <el-form-item label="截止日期" prop="endDate">
                  <el-date-picker
                    type="date"
                    value-format="YYYY-MM-DD"
                    v-model="_this.queryParams.endDate"
                    placeholder="截止日期"
                    style="width: 450px;"
                  />
                </el-form-item>
              </b>
            </el-col>
            <el-col :span="6" align="right">
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          icon="Download"
          @click="handleExport"
          v-hasPermi="['aiProdFlowInfo#postForExcel']"
          >导出
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <!-- 查询表格 -->
    <div v-text="_this.title" class="title"></div>
    <div v-text="_this.unit" class="unit"></div>
    <el-table border :height="tableHeight"  :data="_this.consigneeInfoList">
      <ColumnItem :columns="_this.columns" />
    </el-table>
  </div>
</template>


<script name="/report/planact/rpt" setup>
import { rpt } from "@/api/bdh-agric-invest/invest/planact";
import { allCompanyInfo } from "@/api/bdh-agric-invest/invest/companyInfo";
import { getDicts } from "@/api/bdh-agric-invest/system/dict/data";
import { postForExcel } from "@/api/bdh-agric-invest/invest/excel";
import { ref } from "vue";
import ColumnItem from "@/views/bdh-agric-invest/components/columnItem";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import ProduceTypeDynamic from "@/views/bdh-agric-invest/components/ProduceTypeDynamic";



const { proxy } = getCurrentInstance();
//所在单位
const queryOrgRef = ref();
//生资分类
const produceTypeRef = ref();
const showSearch = ref(true);
//页面数据
const _this = reactive({
  //表格数据
  consigneeInfoList: [],
  //加载状态
  loading: true,
  //年份
  yearOption: [],
  //季节数据
  fertilizes: [],
  //企业数据
  companyAbbrOption: [],
  //动态列头
  columns: [],
  queryParams: {
    aiCompanyId: null, //企业
    aiTypeName: "",
    aiTypeSubName: "", //商品名称
    aiTypeId: null, //商品id
    arriveType: 0,
    authedOrgNos: [],
    fertilizeSeason: null, //季节  1 春季  3 秋季
    beginDate: "",
    buyType: 0,
    companyAbbr: "",
    endDate: "", //截至日期
    leftNum: 0,
    orgCode: "", //组织机构
    orgName: "",
    planNum: 0,
    sendNum: 0,
    subCodeLen: 0,
    year: null, //年份
  },
  //表格title
  title: "",
  unit: "",//单位
});
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom

onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 240
        : window.innerHeight - 240;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 240
        : window.innerHeight - 240;
});

function chgCompany() {
  _this.queryParams.aiTypeId = null;
  handleQuery();
}
function handleTypeId(type) {
  _this.queryParams.aiTypeId = type.aiTypeId;
}

/** 获取字典 */
const getDict = () => {
  allCompanyInfo({}).then((response) => {
    _this.companyAbbrOption = response.data;
    //查询季节
    getDicts("fertilize_season").then((response) => {
      _this.fertilizes = response.data;
      //查询年份
      getDicts("year_cd").then((response) => {
        _this.yearOption = response.data.sort((a, b) => {
          return Number(b.code) - Number(a.code);
        });
        initSearch();
        getList();
      });
    });
  });
};
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryOrgRef.value.clear();
  produceTypeRef.value.clear();
  initSearch();
  handleQuery();
}
//默认值赋值
function initSearch() {
  if (_this.fertilizes.length > 0) {
    _this.queryParams.fertilizeSeason = _this.fertilizes[0].code;
  } else {
    _this.queryParams.fertilizeSeason = null;
  }
  if (_this.companyAbbrOption.length > 0) {
    _this.queryParams.aiCompanyId = _this.companyAbbrOption[0].aiCompanyId;
  } else {
    _this.queryParams.aiCompanyId = null;
  }
  if (_this.yearOption.length > 0) {
    _this.queryParams.year = _this.yearOption[0].code;
  } else {
    _this.queryParams.year = null;
  }
  _this.queryParams.orgCode = null;
  _this.queryParams.orgName = null;
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
//所在单位更改
const handleOrgCodeQuery = (orgInfo) => {
  if (!orgInfo.orgCode) queryOrgRef.value.clear();
  _this.queryParams.orgCode = orgInfo.orgCode;
  _this.queryParams.orgName = orgInfo.orgName;
};

/** 查询收货人列表 */
function getList() {
  _this.loading = true;
  var obj = Object.assign({}, _this.queryParams);
  rpt(obj)
    .then((response) => {
      var data = response.data;
      _this.columns = data.columnList;
      _this.consigneeInfoList = data.dataList;
      _this.title = data.title;
      _this.unit = "单位："+data.unit
      _this.loading = false;
    })
    .catch((err) => {
      _this.loading = false;
      _this.columns = [];
      _this.consigneeInfoList = [];
      _this.title = "";
    });
}

/** 导出按钮操作 */
function handleExport() {
  postForExcel(
    "/bdh-agric-invest-api/report/planact/export",
    _this.queryParams,
    "投入品计划执行表"
  );
}
getDict();
</script>
<style scoped>
.el-link {
  margin-right: 8px;
}

.el-link .el-icon--right.el-icon {
  vertical-align: text-bottom;
}
.mb8 {
  min-height: 30px;
}
.title {
  font-size: 20px;
  text-align: center;
  margin-bottom: 4px;
  font-weight: 600;
}
.unit {
  font-size: 10px;
  text-align: right;
  margin-bottom: 2px;
  font-weight: 600;
}
:deep(.el-input__wrapper) {
  width: 190px !important;
}
</style>
