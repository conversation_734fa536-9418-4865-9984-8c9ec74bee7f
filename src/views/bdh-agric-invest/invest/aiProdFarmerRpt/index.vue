<!--
@name: 线上订购情况明细表
@description:
@author: y<PERSON><PERSON><PERSON>
@time: 2023-06-29
-->
<template>
  <div class="app-container">
    <!-- 筛选条件及搜索栏 -->
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form
          v-show="showSearch"
          ref="queryRef"
          :model="_this.queryParams"
          label-width="80px"
          class="form-line"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年份" prop="year">
                <el-select
                  v-model="_this.queryParams.statYear"
                  placeholder="请选择年份"
                >
                  <el-option
                    v-for="dict in _this.yearOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所在单位" prop="orgCode">
                <org-select
                    ref="queryOrgRef"
                    :defaultOrgCode="true"
                    @handleOrgCode="handleOrgCodeQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="作业季节" prop="fertilizeSeason">
                <el-select clearable
                    v-model="_this.queryParams.fertilizeSeason"
                    placeholder="请选择作业季节"
                >
                  <el-option
                      v-for="dict in _this.fertilizes"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="订单状态" prop="orderStatus">
                <el-select clearable
                    v-model="_this.queryParams.orderStatus"
                    placeholder="订单状态"
                >
                  <el-option
                      v-for="dict in orderStatus"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业名称" prop="aiCompanyId">
                <el-select clearable
                    v-model="_this.queryParams.aiCompanyId"
                    placeholder="请选择企业名称"
                    @change="chgCompany"
                >
                  <el-option
                      v-for="dict in _this.companyAbbrOption"
                      :key="dict.aiCompanyId"
                      :label="dict.companyAbbr"
                      :value="dict.aiCompanyId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="商品分类" prop="aiTypeId">
                <ProduceTypeDynamic
                    :companyId="_this.queryParams.aiCompanyId"
                    @handleTypeId="handleTypeId"
                    ref="produceTypeRef"
                ></ProduceTypeDynamic>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="商品名称" prop="aiTypeSubName">
                <el-input clearable
                    v-model="_this.queryParams.aiTypeSubName"
                    placeholder="请输入商品名称"
                    maxlength="100"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="农户姓名" prop="farmerName">
                <el-input clearable
                  v-model="_this.queryParams.farmerName"
                  placeholder="请输入农户姓名"
                  maxlength="100"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="身份证号" prop="idIccid">
                <el-input clearable
                  v-model="_this.queryParams.idIccid"
                  placeholder="请输入农户身份证号"
                  maxlength="100"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="订购时间" style="font-weight: bold" prop="orderNo">
                <el-date-picker clearable
                    v-model="dataRange"
                    type="daterange"
                    value-format="YYYY-MM-DD"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" align="right">
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          @click="handleExport"
          icon="Download"
          v-hasPermi="['aiProdFarmerRpt:export']"
          >导出
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table border :height="tableHeight"  :data="_this.consigneeInfoList">
      <el-table-column label="农户姓名" align="center"  prop="farmerName"/>
      <el-table-column label="身份证" align="center"  prop="idIccid"/>
      <el-table-column label="年份" align="center"  prop="statYear"/>
      <el-table-column label="企业名称" align="center"  prop="companyAbbr"/>
      <el-table-column label="作业季节" align="center"  prop="fertilizeSeason" :formatter="fmtSeason"/>
      <el-table-column label="所在单位" align="center"  prop="orgName"/>
      <el-table-column label="商品名称" align="center"  prop="aiTypeSubName"/>
      <el-table-column label="订购数量" align="center"  prop="subscribeNum"/>
      <el-table-column label="单价" align="center"  prop="salePrice"/>
      <el-table-column label="总金额" align="center"  prop="salesAmountTotal"/>
    </el-table>
    <!-- 分页查询底部选页栏 -->
    <pagination
      v-show="_this.total > 0"
      v-model:limit="_this.queryParams.rows"
      v-model:page="_this.queryParams.page"
      :total="_this.total"
      @pagination="getList"
    />

  </div>
</template>
<script name="/report/farmerOrder/rpt" setup>
import { getToken } from "@/utils/auth";
import {
  page
} from "@/api/bdh-agric-invest/report/aiProdOrderFarmerRpt";
import { allCompanyInfo } from "@/api/bdh-agric-invest/invest/companyInfo";
import { getDicts } from "@/api/bdh-agric-invest/system/dict/data";
import { postForExcel } from "@/api/bdh-agric-invest/invest/excel";
import {reactive, ref, toRefs} from "vue";
import ColumnItem from "@/views/bdh-agric-invest/components/columnItem";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import ProduceTypeDynamic from "@/views/bdh-agric-invest/components/ProduceTypeDynamic";

const { proxy } = getCurrentInstance();
//所在单位
const queryOrgRef = ref();
//商品分类
const produceTypeRef = ref();
//审核表单
const approveForm = ref();
const orgFlag = ref(false)
const importOrgRef= ref();
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const dataRange = ref([]);
const orderStatus = ref([]);
const data=reactive({
  //导入form
  importForm:{
    year: null,
    orgCode: null,
    fertilizeSeason:null,
    aiCompanyId: null,
  },
})
const showSearch = ref(true);
const {importForm,rulesImport} = toRefs(data);
//页面数据
const _this = reactive({

  processVisible: false,
  // 确认状态
  confirmStatus: [],
  //表格数据
  consigneeInfoList: [],
  //加载状态
  loading: true,
  //年份
  yearOption: [],
  //季节数据
  fertilizes: [],
  //企业数据
  companyAbbrOption: [],
  //动态列头
  columns: [
    {
      label: "年份",
      prop: "year",
    },
    {
      label: "企业名称",
      prop: "companyAbbr",
    },
    {
      label: "所在单位",
      prop: "orgName",
    },
    {
      label: "农户姓名",
      prop: "farmerName",
    },
    {
      label: "身份证",
      prop: "idIccid",
    },
    {
      label: "订单编号",
      prop: "orderNo",
    },
    {
      label: "投入品分类",
      prop: "aiTypeName",
    },
    {
      label: "商品名称",
      prop: "aiTypeSubName",
    },
    {
      label: "订购数量",
      prop: "subscribeNum",
    },
    {
      label: "订购时间",
      prop: "createTime",
    },
  ],
  total: 0,
  queryParams: {
    orderNo:"",
    statYear: "", //年份
    orgCode: "", //组织机构编码
    farmerName: "", //农户姓名
    idIccid: "", //身份证号
    aiCompanyId: null, //公司ID
    fertilizeSeason: null, //作业季节Code
    aiTypeId: null, //商品类型id数组
    aiTypeSubName: "", //商品名称
    orderStatus: null, //订单状态
    page: 1, //请求页码
    rows: 10, //每页条数
    beginTime:null,
    endTime:null
  },
  //表格title
  title: "",
});

onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});

function chgCompany() {
  _this.queryParams.aiTypeId = null;
  handleQuery();
}
function handleTypeId(type) {
  _this.queryParams.aiTypeId = type.aiTypeId;
}

function fmtSeason(row){
  if(row.fertilizeSeason&&row.fertilizeSeason!=''){
    return _this.fertilizes.filter(i=>i.code==row.fertilizeSeason)[0].name
  }
}

/** 获取字典 */
const getDict = () => {
  getDicts("fertilize_season").then((response) => {
    _this.fertilizes = response.data;
  });
  getDicts("invest_order_status").then((response) => {
    console.info('orderStatus',response);
    orderStatus.value = response.data;
  });
  allCompanyInfo({}).then((response) => {
    _this.companyAbbrOption = response.data;
  });
  //查询年份
  getDicts("year_cd").then((response) => {
    _this.yearOption = response.data;
    console.info('_this.yearOption:',_this.yearOption);
    if(_this.yearOption.length>0){
      _this.queryParams.statYear=_this.yearOption[0].code
    }
    getList();
  });

};

function orderStatusFmt(row){

  for(let i=0;i<orderStatus.value.length;i++){
    if(orderStatus.value[i].code==row.orderStatus){
      return orderStatus.value[i].name
    }
  }

}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryOrgRef.value.clear();
  produceTypeRef.value.clear();
  _this.queryParams.beginTime=null;
  _this.queryParams.endTime=null;
  dataRange.value=[]
  handleQuery();
}

/** 搜索按钮操作 */
function handleQuery() {
	_this.queryParams.page = 1;
  getList();
}
//所在单位更改
const handleOrgCodeQuery = (orgInfo) => {
  if (!orgInfo.orgCode) queryOrgRef.value.clear();
  _this.queryParams.orgCode = orgInfo.orgCode;
};

/** 查询收货人列表 */
function getList() {
  console.info('_this.queryParams:',_this.queryParams);
  //_this.loading.value = true;
  if (dataRange.value && dataRange.value.length == 2) {
    _this.queryParams.beginTime = dataRange.value[0];
    _this.queryParams.endTime = dataRange.value[1];
  }else{
    _this.queryParams.beginTime = null;
    _this.queryParams.endTime = null;
  }
  page(_this.queryParams).then((response) => {
    console.info('response:',response);
    _this.consigneeInfoList = response.data.records;
    _this.total = response.data.total;
    //_this.loading.value = false;
  });
}

/** 导出按钮操作 */
function handleExport() {
  postForExcel(
    "/bdh-agric-invest-api/report/farmerOrder/export",
    _this.queryParams,
    "线上订购明细表"
  );
}
getDict();
</script>
<style scoped>
.el-link {
  margin-right: 8px;
}

.el-link .el-icon--right.el-icon {
  vertical-align: text-bottom;
}
.mb8 {
  min-height: 30px;
}
.title {
  font-size: 20px;
  text-align: center;
  margin-bottom: 4px;
  font-weight: 600;
}
</style>
