<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form
          class="form-line"
          :model="queryParams"
          ref="queryForm"
          v-show="showSearch"
          label-width="100px"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="汇总时间" prop="dateRange">
                <el-date-picker
                  v-model="dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                  @change="handleDateChange"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所在单位" prop="orgCode">
                <org-select
                  ref="queryOrgRef"
                  @handleOrgCode="handleOrgCode"
                  :defaultOrgCode="queryParams.orgCode"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="发货单位" prop="warehouseOrgCode">
                <org-select
                  ref="warehouseOrgRef"
                  @handleOrgCode="handleWarehouseOrgCode"
                  :defaultOrgCode="queryParams.warehouseOrgCode"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="审核状态" prop="auditStatus">
                <el-select
                  v-model="queryParams.auditStatus"
                  clearable
                  placeholder="请选择审核状态"
                >
                  <el-option
                    v-for="dict in auditStatusOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['farmWarehouseSummary:insert']"
          >新建出库汇总表</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Check"
          @click="handleApprove"
          v-hasPermi="['farmWarehouseSummary:approve']"
          :disabled="single || !getSelectedRow() || getSelectedRow()?.auditStatus !== 0"
          >审核</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button 
          plain 
          icon="Download" 
          @click="handleExport"
          v-hasPermi="['farmWarehouseSummary:printSummaryList']"
          :disabled="multiple"
          >下载出库汇总表及其明细</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button plain icon="Download" @click="handleExportExcel" v-hasPermi="['farmWarehouseSummary:exportFarmWarehouseSummaryExcel']">导出</el-button>
      </el-col>

      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      :data="farmWarehouseSummaryList"
      border
      :height="tableHeight"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="所在单位" align="center" prop="orgName" />
      <el-table-column label="汇总日期" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column label="制表人" align="center" prop="creatorName" />
      <el-table-column label="供货单位" align="center" prop="warehouseOrgName" />
      <el-table-column
        label="出库商品类型"
        align="center"
        prop="outboundTargetType"
        :formatter="outboundProdTypeFormatter"
      />
      <el-table-column label="总出库量" align="center" prop="operateStock" />
      <el-table-column label="出库商品重量" align="center" prop="aiTypeSubWeight" />
      <el-table-column label="商品单位" align="center" prop="aiTypeSubUnit" />
      <el-table-column label="总金额（元）" align="center" prop="amounts" />
      <el-table-column
        label="审核状态"
        align="center"
        prop="auditStatus"
        :formatter="auditStatusFormatter"
      />
      <el-table-column label="当前执行人" align="center" prop="auditNames" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="300"
      >
        <template #default="scope">
          <el-button
            type="primary"
            icon="View"
            link
            @click="handleView(scope.row)"
            v-hasPermi="['farmWarehouseSummary:getSummaryList']"
            >查看出库汇总表</el-button
          >
          <el-button
            type="success"
            icon="Upload"
            link
            @click="handleRowSubmitAudit(scope.row)"
            v-show="scope.row.auditStatus === -1"
            v-hasPermi="['farmWarehouseSummary:subAudit']"
            >发起流程</el-button
          >
          <el-button
            v-show="scope.row.auditStatus === -1"
            type="danger"
            icon="Delete"
            link
            @click="handleDelete(scope.row)"
            v-hasPermi="['farmWarehouseSummary:delete']"
            >删除</el-button
          >
          <el-button
            v-show="scope.row.auditStatus !== -1"
            type="success"
            icon="User"
            link
            @click="viewFlow(scope.row)"
            >查看流程</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.rows"
      @pagination="getList"
    />

    <!-- 汇总表对话框 -->
    <summary-table
      v-model:visible="open"
      :summaryRow="currentSummaryRow"
      @organization-click="handleOrganizationClick"
    />

    <!-- 收货单位详情对话框 -->
    <organization-detail-dialog
      v-model:visible="openOrganizationDetail"
      :rowData="organizationDetailParams"
    />

    <!-- 审核对话框 -->
    <el-dialog
      title="审核出库汇总表"
      v-model="openApprove"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form :model="approveForm" label-width="80px">
        <el-form-item label="审批意见" prop="opinion">
          <el-input
            v-model="approveForm.opinion"
            type="textarea"
            :rows="3"
            placeholder="请输入审批意见"
            maxlength="200"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="openApprove = false">取 消</el-button>
          <el-button type="success" @click="submitApprove(true)">同 意</el-button>
          <el-button type="danger" @click="submitApprove(false)">拒 绝</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增出库汇总表对话框 -->
    <el-dialog
      title="新增出库汇总表"
      v-model="openCreate"
      width="80%"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="createFormRef"
        :model="createForm"
        :rules="createRules"
        label-width="80px"
        class="mb8"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="年份" prop="year">
              <el-select v-model="createForm.year" clearable placeholder="请选择年份">
                <el-option
                  v-for="dict in yearOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在单位" prop="orgCode">
              <org-select
                ref="createFormOrgRef"
                @handleOrgCode="handleCreateFormOrgCode"
                :defaultOrgCode="createForm.orgCode"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库对象" prop="outboundTargetType">
              <el-select
                v-model="createForm.outboundTargetType"
                clearable
                placeholder="请选择出库对象"
              >
                <el-option
                  v-for="dict in outboundTargetTypeOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-table
        :data="farmWarehouseOutputList"
        border
        height="400px"
        @selection-change="handleOutputSelectionChange"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="年份" align="center" prop="year" />
        <el-table-column
          label="作业季节"
          align="center"
          prop="fertilizeSeason"
          :formatter="fertilizeSeasonFormatter"
        />
        <el-table-column label="企业简称" align="center" prop="companyAbbr" />
        <el-table-column label="单号" align="center" prop="inoutOrderNo" />
        <el-table-column label="出库库房" align="center" prop="warehouseName" />
        <el-table-column label="出库时间" align="center" prop="createTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="出库对象"
          align="center"
          prop="outboundTargetType"
          :formatter="outboundTargetTypeFormatter"
        />
        <el-table-column label="出库对象名称" align="center" prop="outboundTargetName" />
        <el-table-column
          label="出库商品类型"
          align="center"
          prop="outboundProdType"
          :formatter="outboundProdTypeFormatter"
        />
        <el-table-column label="出库总量" align="center" prop="operateStock" />
      </el-table>

      <pagination
        v-show="outputTotal > 0"
        :total="outputTotal"
        v-model:page="outputQueryParams.page"
        v-model:limit="outputQueryParams.rows"
        @pagination="handleOutputPagination"
      />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelCreate">取 消</el-button>
          <el-button type="primary" @click="submitCreateForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看流程对话框 -->
    <enhanced-flow-detail 
      v-model:visible="processVisible"
      :rowData="currentFlowRow"
      @closed="handleFlowClosed"
    />
  </div>
</template>

<script name="/invest/farmWarehouseSummary/queryByPage" setup>
import { ref, reactive, toRefs, computed, onMounted, watch, nextTick } from "vue";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import SummaryTable from "./SummaryTable.vue";
import OrganizationDetailDialog from "./OrganizationDetailDialog.vue";
import EnhancedFlowDetail from "./EnhancedFlowDetail.vue";
import { postForExcel, postForZip } from "@/api/bdh-agric-invest/invest/excel";
import { getToken } from "@/utils/auth";
import { getDicts, queryAllOrgTree } from "@/api/bdh-agric-invest/system/dict/data";
import {
  queryFarmWarehouseSummaryByPage,
  insertFarmWarehouseSummary,
  logicDeleteById,
  getSummaryList,
  startFlow,
  approve,
} from "@/api/bdh-agric-invest/invest/farmWarehouseSummary";
import { listfarmWarehouseOutput } from "@/api/bdh-agric-invest/invest/farmWarehouseOutput";

const { proxy } = getCurrentInstance();

const farmWarehouseSummaryList = ref([]); // 主列表
const open = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);

const queryOrgRef = ref();
const warehouseOrgRef = ref();
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const defaultOrgCode = ref(""); // 默认初始所在单位
const orgListDetail = ref([]); // org列表

// 当前汇总表信息
const currentSummaryRow = ref({});

// 收货单位详情对话框
const openOrganizationDetail = ref(false);
const organizationDetailParams = ref({});

// 审核对话框
const openApprove = ref(false);
const approveForm = ref({
  opinion: "",
});

// 流程对话框
const processVisible = ref(false);
const currentFlowRow = ref({});

// 审核状态选项
const auditStatusOption = ref([
  {
    code: -1,
    name: "待提交",
  },
  {
    code: 0,
    name: "审批中",
  },
  {
    code: 1,
    name: "审批通过",
  },
  {
    code: 3,
    name: "已撤销",
  },
  {
    code: 4,
    name: "审批拒绝",
  },
]);

// 出库商品类型选项
const outboundTargetTypeOption = ref([]);

// 表单详情数据
const outboundTargetTypeText = computed(() => {
  let text = "";
  outboundTargetTypeOption.value.forEach((item) => {
    if (item.code == form.value.outboundTargetType) {
      text = item.name;
    }
  });
  return text;
});

const auditStatusText = computed(() => {
  let text = "";
  auditStatusOption.value.forEach((item) => {
    if (item.code == form.value.auditStatus) {
      text = item.name;
    }
  });
  return text;
});

// 新增出库汇总表相关变量
const openCreate = ref(false);
const createFormOrgRef = ref();
const yearOption = ref([]);
const selectedOutputList = ref([]);
const farmWarehouseOutputList = ref([]);
const fertilizeSeasonOption = ref([]);
const outboundProdTypeOption = ref([
  { code: 1, name: "统供商品" },
  { code: 2, name: "加工商品" },
]);

// 新增对话框中出库列表的分页变量
const outputTotal = ref(0);
const outputQueryParams = ref({
  rows: 10,
  page: 1,
});

const data = reactive({
  form: {},
  queryParams: {
    rows: 10,
    page: 1,
    orgCode: "",
    warehouseOrgCode: "",
    startTime: "",
    endTime: "",
    auditStatus: null,
  },
  createForm: {
    year: "",
    orgCode: "",
    outboundTargetType: "",
  },
  createRules: {
    year: [{ required: true, message: "请选择年份", trigger: "change" }],
    orgCode: [{ required: true, message: "请选择所在单位", trigger: "change" }],
    outboundTargetType: [
      { required: true, message: "请选择出库对象", trigger: "change" },
    ],
  },
});

const { queryParams, form, createForm, createRules } = toRefs(data);

onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 220
    : window.innerHeight - 220;
  getDict();
});

watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 220
    : window.innerHeight - 220;
});

// 处理日期选择变化
const handleDateChange = (val) => {
  if (val) {
    queryParams.value.startTime = val[0];
    queryParams.value.endTime = val[1];
  } else {
    queryParams.value.startTime = "";
    queryParams.value.endTime = "";
  }
};

// 处理所在单位选择
const handleOrgCode = (orgCodeInfo) => {
  queryParams.value.orgCode = orgCodeInfo.orgCode;
};

// 处理发货单位选择
const handleWarehouseOrgCode = (orgCodeInfo) => {
  queryParams.value.warehouseOrgCode = orgCodeInfo.orgCode;
};

// 查询出库汇总列表
const getList = () => {
  queryFarmWarehouseSummaryByPage(queryParams.value).then((response) => {
    for (let item of response.data.records) {
      item.auditNames = item.flowMsg ? JSON.parse(item.flowMsg).auditNames : "";
    }
    farmWarehouseSummaryList.value = response.data.records;
    total.value = response.data.total;
  });
};

// 多选框选中数据
const handleSelectionChange = (selection) => {
  ids.value = selection.map((item) => item.warehouseSummaryId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
};

// 搜索按钮操作
const handleQuery = () => {
  queryParams.value.page = 1;
  getList();
};

// 重置按钮操作
const resetQuery = () => {
  dateRange.value = [];
  proxy.resetForm("queryForm");
  queryOrgRef.value.clear();
  warehouseOrgRef.value.clear();
  handleQuery();
};

// 处理新建表单所在单位选择
const handleCreateFormOrgCode = (orgCodeInfo) => {
  createForm.value.orgCode = orgCodeInfo.orgCode;
};

// 新增按钮操作
const handleAdd = () => {
  const currentYear = new Date().getFullYear().toString();

  createForm.value = {
    year: currentYear,
    orgCode:createForm.value.orgCode,
    outboundTargetType:
      outboundTargetTypeOption.value.length > 0
        ? outboundTargetTypeOption.value[0].code
        : "",
  };

  // 如果当前年份不在选项中，默认使用第一个选项
  if (
    yearOption.value.length > 0 &&
    !yearOption.value.some((item) => item.code === currentYear)
  ) {
    createForm.value.year = yearOption.value[0].code;
  }

  selectedOutputList.value = [];
  // 重置分页参数
  outputQueryParams.value.page = 1;
  outputQueryParams.value.rows = 10;
  outputTotal.value = 0;

  openCreate.value = true;
  getOutputList();
};

// 获取出库列表数据
const getOutputList = () => {
  listfarmWarehouseOutput({
    rows: outputQueryParams.value.rows,
    page: outputQueryParams.value.page,
    orgCode: createForm.value.orgCode,
    year: createForm.value.year,
    outboundTargetType: createForm.value.outboundTargetType,
  }).then((response) => {
    farmWarehouseOutputList.value = response.data.records || [];
    outputTotal.value = response.data.total || 0;
  });
};

// 监听表单变化，更新出库列表
watch(
  () => [
    createForm.value.year,
    createForm.value.orgCode,
    createForm.value.outboundTargetType,
  ],
  () => {
    if (
      openCreate.value &&
      createForm.value.year &&
      createForm.value.orgCode &&
      createForm.value.outboundTargetType
    ) {
      outputQueryParams.value.page = 1; // 重置分页
      getOutputList();
    }
  }
);

// 出库列表分页处理
const handleOutputPagination = () => {
  getOutputList();
};

// 出库表格选择变化
const handleOutputSelectionChange = (selection) => {
  selectedOutputList.value = selection;
};

// 取消新增
const cancelCreate = () => {
  openCreate.value = false;
  proxy.resetForm("createFormRef");
};

// 提交新建表单
const submitCreateForm = () => {
  proxy.$refs["createFormRef"].validate((valid) => {
    if (valid) {
      if (selectedOutputList.value.length === 0) {
        proxy.$modal.msgError("请选择至少一条出库记录");
        return;
      }

      const farmWarehouseSummaryDetailList = selectedOutputList.value.map((item) => ({
        inoutOrderNo: item.inoutOrderNo,
      }));

      // 根据orgCode找到对应的orgName
      let warehouseOrgName = "";
      const orgItem = orgListDetail.value.find(
        (org) => org.orgCode === createForm.value.orgCode
      );
      if (orgItem) {
        warehouseOrgName = orgItem.orgName;
      }

      const data = {
        year: createForm.value.year,
        warehouseOrgCode: createForm.value.orgCode,
        warehouseOrgName: warehouseOrgName,
        outboundTargetType: createForm.value.outboundTargetType,
        farmWarehouseSummaryDetailList: farmWarehouseSummaryDetailList,
      };

      insertFarmWarehouseSummary(data).then((response) => {
        proxy.$modal.msgSuccess("新增出库汇总表成功");
        openCreate.value = false;
        getList();
      });
    }
  });
};

// 查看出库汇总表按钮操作
const handleView = (row) => {
  currentSummaryRow.value = row;
  open.value = true;
};

// 处理收货单位点击事件
const handleOrganizationClick = (data) => {
  // 根据指定的数据结构传递参数
  organizationDetailParams.value = {
    warehouseSummaryId: currentSummaryRow.value.warehouseSummaryId,
    aiTypeSubId: data.aiTypeSubId || null,
    aiTypeSubName: data.aiTypeSubName || "",
    aiTypeSubSpecs: data.aiTypeSubSpecs || "",
    aiTypeSubWeight: data.aiTypeSubWeight || null,
    aiTypeSubUnit: data.aiTypeSubUnit || "",
    aiGuidingPrice: data.aiGuidingPrice || null,
    organizationNo: data.organizationNo || "",
    organizationName: data.organizationName || "",
    processCostUnit: data.processCostUnit || null,
  };
  openOrganizationDetail.value = true;
};

// 获取选中的行
const getSelectedRow = () => {
  if (ids.value.length === 1) {
    return farmWarehouseSummaryList.value.find(
      (item) => item.warehouseSummaryId === ids.value[0]
    );
  }
  return null;
};

// 行级提交审核
const handleRowSubmitAudit = (row) => {
  proxy
    .$confirm("是否确认提交审核?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(() => {
      const params = {
        warehouseSummaryId: row.warehouseSummaryId,
      };

      startFlow(params).then((response) => {
        proxy.$modal.msgSuccess("提交审核成功");
        getList();
      });
    });
};

// 打开审核对话框
const handleApprove = () => {
  const selectedRow = getSelectedRow();
  if (selectedRow && selectedRow.auditStatus === 0) {
    approveForm.value.opinion = "";
    openApprove.value = true;
  } else {
    proxy.$modal.msgError("请选择一条审核状态为'审批中'的记录进行审核");
  }
};

// 提交审核意见
const submitApprove = (agree) => {
  const selectedRow = getSelectedRow();
  if (!selectedRow) {
    proxy.$modal.msgError("请选择一条记录");
    return;
  }

  const params = {
    warehouseSummaryId: selectedRow.warehouseSummaryId,
    agree: agree,
    instId: selectedRow.instId || "",
    flowMsg: selectedRow.flowMsg || "",
    opinion: approveForm.value.opinion,
  };

  approve(params).then((response) => {
    proxy.$modal.msgSuccess(agree ? "审核通过成功" : "审核拒绝成功");
    openApprove.value = false;
    getList();
  });
};

// 删除按钮操作
const handleDelete = (row) => {
  const warehouseSummaryIds = row.warehouseSummaryId || ids.value;
  proxy
    .$confirm("是否确认删除此数据?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(() => {
      return logicDeleteById(warehouseSummaryIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    });
};

// 导出按钮操作
const handleExport = () => {
  if (ids.value.length === 0) {
    proxy.$modal.msgError("请选择至少一条记录进行导出");
    return;
  }

  postForZip(
    "/bdh-agric-invest-api/invest/farmWarehouseSummary/printSummaryList",
    ids.value,
    "农场投入品出库汇总"
  );
};

const handleExportExcel = () => {
  postForExcel(
    "/bdh-agric-invest-api/invest/farmWarehouseSummary/exportFarmWarehouseSummaryExcel",
    queryParams.value,
    "农场投入品出库汇总记录"
  );
};

// 出库商品类型格式化
const outboundTargetTypeFormatter = (row) => {
  let name = "";
  outboundTargetTypeOption.value.forEach((item) => {
    if (item.code == row.outboundTargetType) {
      name = item.name;
    }
  });
  return name;
};

// 审核状态格式化
const auditStatusFormatter = (row) => {
  let name = "";
  auditStatusOption.value.forEach((item) => {
    if (item.code == row.auditStatus) {
      name = item.name;
    }
  });
  return name;
};

// 出库商品类型格式化
const outboundProdTypeFormatter = (row) => {
  let name = "";
  outboundProdTypeOption.value.forEach((item) => {
    if (item.code == row.outboundProdType) {
      name = item.name;
    }
  });
  return name;
};

// 作业季节格式化
const fertilizeSeasonFormatter = (row) => {
  let name = "";
  fertilizeSeasonOption.value.forEach((item) => {
    if (item.code == row.fertilizeSeason) {
      name = item.name;
    }
  });
  return name;
};

// 查看流程
const viewFlow = (row) => {
  currentFlowRow.value = row;
  processVisible.value = true;
};

// 流程对话框关闭回调
const handleFlowClosed = () => {
  currentFlowRow.value = {};
};

// 获取字典数据

const getDict = () => {
  getDicts("outbound_target_type").then((res) => {
    outboundTargetTypeOption.value = res.data.slice(0, 2);
  });

  getDicts("fertilize_season").then((res) => {
    fertilizeSeasonOption.value = res.data;
  });

  getDicts("year_cd").then((response) => {
    yearOption.value = response.data.sort((a, b) => {
      return Number(b.code) - Number(a.code);
    });
  });

  queryAllOrgTree("1").then((res) => {
    const orgList = res.data;
    orgListDetail.value = orgList;
    getList();
  });
};
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}

.summary-row {
  margin-top: 0;
  font-weight: bold;
}

.summary-row :deep(.el-table__row) {
  background-color: #f5f7fa;
}
</style>
