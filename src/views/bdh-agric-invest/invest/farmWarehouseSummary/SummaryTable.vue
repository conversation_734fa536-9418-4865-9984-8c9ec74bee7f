<template>
  <!-- 汇总表对话框 -->
  <el-dialog
    :title="dialogTitle"
    v-model="dialogVisible"
    width="1200px"
    append-to-body
    :close-on-click-modal="false"
    @closed="handleDialogClosed"
  >
    <div>
      <el-table :data="summaryDetailList" border :height="height">
        <el-table-column
          label="序号"
          type="index"
          width="60"
          align="center"
          :index="(index) => (summaryQueryParams.page - 1) * summaryQueryParams.rows + index + 1"
          v-if="showIndex"
        />
        <template v-for="column in summaryColumns" :key="column.prop">
          <!-- 特殊处理收货单位列，使其可点击 -->
          <el-table-column
            v-if="column.prop === 'organizationName'"
            :label="column.label"
            :prop="column.prop"
            :width="column.width || '180'"
            :min-width="column.minWidth"
            :align="column.align || 'center'"
          >
            <template #default="scope">
              <!-- 当是"规模家庭农场"且当前行的type不等于1时，显示为普通文本 -->
              <span
                v-if="column.label == '规模家庭农场' && scope.row.type != 1"
                :style="{
                  color: '#333',
                  fontWeight: 'bold',
                }"
              >
                {{ scope.row[column.prop] }}
              </span>
              <!-- 其他情况显示为可点击按钮 -->
              <el-button
                v-else
                type="text"
                @click="handleOrganizationClick(scope.row, column.label)"
                :style="{
                  color: column.label == '规模家庭农场' ? 'dodgerblue' : '#333',
                  fontWeight: 'bold',
                }"
              >
                {{ scope.row[column.prop] }}
              </el-button>
            </template>
          </el-table-column>

          <!-- 普通列 -->
          <el-table-column
            v-else
            :label="column.label"
            :prop="column.prop"
            :width="column.width"
            :min-width="column.minWidth"
            :align="column.align || 'center'"
            :formatter="column.formatter"
          >
            <template #default="scope" v-if="column.slot">
              <slot :name="column.slot" :row="scope.row"></slot>
            </template>
          </el-table-column>
        </template>
      </el-table>
    </div>

    <!-- 分页组件 -->
    <pagination
      v-show="summaryDetailTotal > 0"
      :total="summaryDetailTotal"
      v-model:page="summaryQueryParams.page"
      v-model:limit="summaryQueryParams.rows"
      @pagination="handleSummaryPagination"
    />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleSummaryExportExcel" type="primary" icon="Download"
          >导出</el-button
        >
        <el-button @click="handleDialogClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { defineProps, defineEmits, ref, computed, watch } from "vue";
import { getSummaryList } from "@/api/bdh-agric-invest/invest/farmWarehouseSummary";
import { postForExcel } from "@/api/bdh-agric-invest/invest/excel";

const props = defineProps({
  // 对话框显示状态
  visible: {
    type: Boolean,
    default: false,
  },
  // 当前汇总表信息
  summaryRow: {
    type: Object,
    default: () => ({}),
  },
  // 表格高度
  height: {
    type: [String, Number],
    default: "auto",
  },
  // 是否显示序号列
  showIndex: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(["update:visible", "organization-click", "closed"]);

// 对话框状态
const dialogVisible = ref(false);

// 汇总表详情数据
const summaryDetailList = ref([]);
const summaryDetailTotal = ref(0);
const summaryColumns = ref([]);

// 分页参数
const summaryQueryParams = ref({
  rows: 10,
  page: 1,
});

// 当前汇总表ID
const currentSummaryId = ref("");

// 对话框标题
const dialogTitle = computed(() => {
  if (props.summaryRow.outboundTargetType == 1) {
    return "农业科技服务中心投入品出库汇总表";
  } else if (props.summaryRow.outboundTargetType == 2) {
    return "管理区投入品出库汇总表";
  }
  return "投入品出库汇总表";
});

// 监听visible变化
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val;
    if (val) {
      openSummaryDialog(props.summaryRow);
    }
  }
);

// 监听dialogVisible变化
watch(dialogVisible, (val) => {
  if (val !== props.visible) {
    emit("update:visible", val);
  }
});

// 打开汇总表对话框
const openSummaryDialog = (row) => {
  currentSummaryId.value = row.warehouseSummaryId;

  // 根据outboundTargetType动态设置列配置
  const firstColumnLabel = row.outboundTargetType == 1 ? "规模家庭农场":"收货单位" ;

  summaryColumns.value = [
    { label: firstColumnLabel, prop: "organizationName", width: "280" },
    { label: "商品名称", prop: "aiTypeSubName", width: "150" },
    { label: "原材料名称", prop: "aiTypeComName", width: "150" },
    { label: "商品规格", prop: "aiTypeSubSpecs", width: "120" },
    { label: "商品单位", prop: "aiTypeSubUnit" },
    { label: "商品数量", prop: "operateStock" },
    { label: "单价", prop: "aiGuidingPrice" },
    { label: "金额", prop: "amounts" },
  ];

  // 重置分页参数
  summaryQueryParams.value.page = 1;
  summaryQueryParams.value.rows = 10;

  getSummaryDetailList();
};

// 获取汇总表详情列表
const getSummaryDetailList = () => {
  const params = {
    warehouseSummaryId: currentSummaryId.value,
    page: summaryQueryParams.value.page,
    rows: summaryQueryParams.value.rows,
  };

  getSummaryList(params).then((response) => {
    summaryDetailList.value = response.data.records || response.data.list || [];
    summaryDetailTotal.value = response.data.total || 0;
  });
};

// 汇总表详情分页处理
const handleSummaryPagination = () => {
  getSummaryDetailList();
};

// 导出Excel
const handleSummaryExportExcel = () => {
  postForExcel(
    "/bdh-agric-invest-api/invest/farmWarehouseSummary/exportSummaryExcel",
    { warehouseSummaryId: currentSummaryId.value },
    props.summaryRow.outboundTargetType == 1
      ? "农业科技服务中心投入品出库汇总表"
      : "管理区投入品出库汇总表"
  );
};

// 处理收货单位点击事件
const handleOrganizationClick = (row, label) => {
  // 只有当 label 是"规模家庭农场"且当前行的type等于1时才允许点击
  if (label == "收货单位" || (label == "规模家庭农场" && row.type != 1)) {
    return;
  }
  emit("organization-click", row, label);
};

// 关闭对话框
const handleDialogClose = () => {
  dialogVisible.value = false;
};

// 对话框关闭后的回调
const handleDialogClosed = () => {
  emit("closed");
  summaryDetailList.value = [];
  summaryColumns.value = [];
  summaryDetailTotal.value = 0;
  // 重置分页参数
  summaryQueryParams.value.page = 1;
  summaryQueryParams.value.rows = 10;
};
</script>

<style scoped></style>
