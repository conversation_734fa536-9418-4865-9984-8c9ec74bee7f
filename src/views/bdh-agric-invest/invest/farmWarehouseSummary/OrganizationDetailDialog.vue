<template>
  <el-dialog
    :title="title"
    v-model="dialogVisible"
    width="1200px"
    append-to-body
    :close-on-click-modal="false"
    @closed="handleClosed"
  >
    <el-table :data="tableData" border :height="height">
      <el-table-column
        label="序号"
        type="index"
        width="60"
        align="center"
        :index="(index) => (queryParams.page - 1) * queryParams.rows + index + 1"
        v-if="showIndex"
      />
      <template v-for="column in columns" :key="column.prop">
        <el-table-column
          :label="column.label"
          :prop="column.prop"
          :width="column.width"
          :min-width="column.minWidth"
          :align="column.align || 'center'"
          :formatter="column.formatter"
        >
          <template #default="scope" v-if="column.slot">
            <slot :name="column.slot" :row="scope.row"></slot>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <!-- 分页组件 -->
    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.rows"
      @pagination="handlePagination"
    />

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleExportExcel" type="primary" icon="Download"
          >导出</el-button
        >
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { defineProps, defineEmits, ref, watch } from "vue";
import { getSummaryDetailList } from "@/api/bdh-agric-invest/invest/farmWarehouseSummary";
import { postForExcel } from "@/api/bdh-agric-invest/invest/excel";

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  rowData: {
    type: Object,
    default: () => ({
      warehouseSummaryId: null,
      aiTypeSubId: null,
      aiTypeSubName: "",
      aiTypeSubSpecs: "",
      aiTypeSubWeight: null,
      aiTypeSubUnit: "",
      aiGuidingPrice: null,
      organizationNo: "",
      organizationName: "",
      processCostUnit: null,
    }),
  },
  // 表格高度
  height: {
    type: [String, Number],
    default: "auto",
  },
  // 是否显示序号列
  showIndex: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits(["update:visible", "closed"]);

const dialogVisible = ref(false);
const tableData = ref([]);
const columns = ref([]);
const total = ref(0);

const title = ref("");

// 分页参数
const queryParams = ref({
  page: 1,
  rows: 10,
});

// 监听visible变化
watch(
  () => props.visible,
  (val) => {
    dialogVisible.value = val;
    if (val) {
      // 重置分页参数
      queryParams.value.page = 1;
      queryParams.value.rows = 10;
      loadData();
      title.value = "农业科技服务中心投入品出库明细表";
    }
  }
);

// 监听dialogVisible变化
watch(dialogVisible, (val) => {
  if (val !== props.visible) {
    emit("update:visible", val);
  }
});

// 加载数据
const loadData = async () => {
  try {
    const params = {
      warehouseSummaryId: props.rowData.warehouseSummaryId,
      aiTypeSubId: props.rowData.aiTypeSubId,
      aiTypeSubName: props.rowData.aiTypeSubName,
      aiTypeSubSpecs: props.rowData.aiTypeSubSpecs,
      aiTypeSubWeight: props.rowData.aiTypeSubWeight,
      aiTypeSubUnit: props.rowData.aiTypeSubUnit,
      aiGuidingPrice: props.rowData.aiGuidingPrice,
      organizationNo: props.rowData.organizationNo,
      organizationName: props.rowData.organizationName,
      processCostUnit: props.rowData.processCostUnit,
      page: queryParams.value.page,
      rows: queryParams.value.rows,
    };

    const response = await getSummaryDetailList(params);
    tableData.value = response.data.list || response.data.records || [];
    total.value = response.data.total || 0;

    // 固定设置列
    columns.value = [
       {label:"地号",prop:"plotNo",minWidth: "160" },
      { label: "商品名称", prop: "aiTypeSubName", minWidth: "160" },
      { label: "原材料名称", prop: "aiTypeComName", minWidth: "160" },
      { label: "商品规格", prop: "aiTypeSubSpecs", minWidth: "100" },
      { label: "商品单位", prop: "aiTypeSubUnit", minWidth: "80" },
      { label: "商品数量", prop: "operateStock", minWidth: "80" },
      { label: "单价", prop: "aiGuidingPrice", minWidth: "80" },
      { label: "金额", prop: "amounts", minWidth: "80" },
    ];
  } catch (error) {
    console.error("Failed to load data:", error);
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};

// 分页处理
const handlePagination = () => {
  loadData();
};

const handleExportExcel = () => {
  postForExcel(
    "/bdh-agric-invest-api/invest/farmWarehouseSummary/exportDetailExcel",
    props.rowData,
    "农业科技服务中心投入品出库明细表"
  );
};

// 对话框关闭后的回调
const handleClosed = () => {
  emit("closed");
  tableData.value = [];
  columns.value = [];
  total.value = 0;
  // 重置分页参数
  queryParams.value.page = 1;
  queryParams.value.rows = 10;
};
</script>

<style scoped>
.summary-row {
  font-weight: bold;
}

.summary-row :deep(.el-table__row) {
  background-color: #f5f7fa;
}
</style>
