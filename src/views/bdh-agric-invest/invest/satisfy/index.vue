<!--
@name: 满意度调研
@description: 商城满意度调研PC端对应回复功能
@author: y<PERSON><PERSON><PERSON>
@time: 2023-09-05
-->
<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form :model="queryParams" ref="queryRef" v-show="showSearch" label-width="100px" class="form-line">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="农户归属" prop="orgCode">
                <org-select
                    ref="queryOrgRef"
                    :level="1"
                    @handleOrgCode="handleOrgCodeQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="用户姓名" prop="farmerName">
                <el-input
                    v-model="queryParams.farmerName"
                    placeholder="请输入用户姓名"
                    clearable
                    @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="评价时间" style="font-weight: bold" prop="taskDateArr">
                <el-date-picker
                    v-model="taskDateArr"
                    end-placeholder="结束日期"
                    range-separator="至"
                    start-placeholder="开始日期"
                    type="daterange"
                    clearable
                    value-format="YYYY-MM-DD"
                    style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="反馈状态" prop="feedbackType">
                <el-select
                    v-model="queryParams.feedbackType"
                    placeholder="请选择反馈状态"
                    @change="handleQuery"
                    clearable
                >
                  <el-option
                      v-for="dict in feedbackTypeOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="商品质量星级" style="font-weight: bold;" prop="prodQualityBegin">
                <div style="display: flex;">
                  <el-select
                    v-model="queryParams.prodQualityBegin"
                    placeholder="商品质量星级"
                    @change="handleQuery"
                    clearable
                    style="width:calc(50% - 5px) !important "
                >
                  <el-option
                      v-for="dict in starOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>~
                <el-select
                    style="width:calc(50% - 5px) !important "
                    v-model="queryParams.prodQualityEnd"
                    placeholder="商品质量星级"
                    @change="handleQuery"
                    clearable
                >
                  <el-option
                      v-for="dict in starOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="商家服务星级" style="font-weight: bold;" prop="busiServeBegin">
                <div style="display: flex;">
                  <el-select
                    v-model="queryParams.busiServeBegin"
                    placeholder="商家服务星级"
                    @change="handleQuery"
                    clearable
                    style="width:calc(50% - 5px) !important "
                >
                  <el-option
                      v-for="dict in starOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>~
                <el-select
                    style="width:calc(50% - 5px) !important "
                    v-model="queryParams.busiServeEnd"
                    placeholder="商家服务星级"
                    @change="handleQuery"
                    clearable
                >
                  <el-option
                      v-for="dict in starOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12" style="text-align: right">
              <el-form-item>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>
    <!---->
    <el-row :gutter="10" class="mb8" style="height: 40px;">
      <el-col :span="1.5">
        <el-button
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['satisfy:export']"
        >导出
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table  :data="satisfyList"  :height="tableHeight">
      <el-table-column label="农户归属" align="center" prop="orgName" v-if="columns[0].visible"/>
      <el-table-column label="反馈状态" align="center" prop="feedbackType" :formatter="feedbackTypeFmt" v-if="columns[1].visible"/>
      <el-table-column label="用户姓名" align="center" prop="farmerName" v-if="columns[2].visible"/>
      <el-table-column label="商品质量星级" align="center" prop="prodQuality" v-if="columns[3].visible">
        <template #default="scope">
          {{scope.row.prodQuality+"星"}}
        </template>
      </el-table-column>
      <el-table-column label="商家服务星级" align="center" prop="busiServe" v-if="columns[4].visible">
        <template #default="scope">
          {{scope.row.busiServe+"星"}}
        </template>
      </el-table-column>
      <el-table-column label="用户反馈时间" align="center" prop="createTime" v-if="columns[5].visible"/>
      <el-table-column label="用户反馈内容" align="center" prop="suggesMsg" v-if="columns[6].visible"/>
      <el-table-column label="回复内容" align="center" prop="farmReport" v-if="columns[7].visible"/>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-link
              icon="Edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['satisfy:update']"
          >修改
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.rows"
        @pagination="getList"
    />

    <!-- 添加或修改满意度调查对话框 -->
    <el-dialog :title="title" v-model="open" width="900px" :close-on-click-modal="false" append-to-body>
      <el-form ref="satisfyRef" :model="form" :rules="rules" label-width="100px" label-position="top" >
        <el-row :gutter="20" style="display: flex;flex-wrap: wrap">
          <el-col :span="8">
            <el-form-item label="农户姓名" prop="statYear">
              <el-input v-model="form.farmerName" placeholder="身份证号" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="农户归属" prop="orgName">
              <el-input v-model="form.orgName" :title="form.orgName" placeholder="农户归属" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系方式" prop="orgName">
              <el-input v-model="form.farmerTelephone" placeholder="联系方式" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号" prop="farmerIccid">
              <el-input v-model="form.farmerIccid" placeholder="身份证号" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="商品质量评价等级" prop="prodQuality">
              <el-tag size="large">{{form.prodQuality+'星'}}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="商家服务评价等级" prop="busiServe">
              <el-tag size="large">{{form.busiServe+'星'}}</el-tag>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item  style="font-weight: bold;display: block;" label="订单购买记录" prop="orders">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-descriptions v-if="form.orders&&form.orders.length>0" column="2" title=" ">
                      <el-descriptions-item >订单号:{{form.orders[0].orderNo}}</el-descriptions-item>
                      <el-descriptions-item ><el-tag size="small">{{orderStatusOpinionFmt(form.orders[0].orderStatus)}}</el-tag></el-descriptions-item>
                      <template v-for="detail in form.orders[0].orderDetails">
                        <el-descriptions-item >{{detail.aiTypeSubName}}</el-descriptions-item>
                        <el-descriptions-item >￥{{detail.salePrice+'x'+detail.subscribeNum}}</el-descriptions-item>
                      </template>
                      <el-descriptions-item >下单时间:{{ form.orders[0].createTime }}</el-descriptions-item>
                      <el-descriptions-item ><el-tag size="large">合计:￥{{form.orders[0].salesAmountTotal}}</el-tag></el-descriptions-item>
                    </el-descriptions>
                  </el-col>
                  <el-col :span="12">
                    <el-descriptions v-if="form.orders&&form.orders.length>1" column="2" title=" ">
                      <el-descriptions-item >订单号:{{form.orders[1].orderNo}}</el-descriptions-item>
                      <el-descriptions-item ><el-tag size="small">{{orderStatusOpinionFmt(form.orders[1].orderStatus)}}</el-tag></el-descriptions-item>
                      <template v-for="detail in form.orders[1].orderDetails">
                        <el-descriptions-item >{{detail.aiTypeSubName}}</el-descriptions-item>
                        <el-descriptions-item >￥{{detail.salePrice+'x'+detail.subscribeNum}}</el-descriptions-item>
                      </template>
                      <el-descriptions-item >下单时间:{{ form.orders[1].createTime }}</el-descriptions-item>
                      <el-descriptions-item ><el-tag size="large">合计:￥{{form.orders[1].salesAmountTotal}}</el-tag></el-descriptions-item>
                    </el-descriptions>
                  </el-col>
                </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="24"  style="text-align:right">
            <el-link type="primary" @click="openList=true" >更多</el-link>
          </el-col>
          <el-col :span="24">
            <el-form-item label="用户评价信息" prop="suggesMsg">
              <el-input
                  v-model="form.suggesMsg"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  type="textarea"
                  placeholder="用户评价信息"
                  disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="问题截图" style="font-weight: bold" prop="picUrl">
              <el-image
                  style="width: 100px; height: 100px"
                  :src="form.picUrl?form.picUrl:''"
                  :zoom-rate="1.2"
                  :preview-src-list="form.picUrl?form.picUrl.split(','):[]"
                  :initial-index="4"
                  fit="cover"
              >
                <template #error>
                  <div class="image-slot">
                    <el-icon><picture-filled /></el-icon>
                  </div>
                </template>
              </el-image>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="农场回复" prop="farmReport">
              <el-input
                  v-model="form.farmReport"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  maxlength="200"
                  type="textarea"
                  placeholder="请输入农场回复"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="反馈状态" prop="feedbackType">
              <el-radio-group v-model="form.feedbackType">
                <el-radio :label="'0'">未反馈</el-radio>
                <el-radio :label="'1'">已反馈</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <statisfyorders
        v-model:dialogOpen="openList"
        :dialogTitle="openListTitle"
        v-model:idIccid="form.farmerIccid"
        v-if="openList"
    />
  </div>
</template>

<script setup name="/invest/satisfy/queryByPage">
import {onMounted, ref} from "vue";
import statisfyorders from './orders'
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import {listSatisfy, getSatisfy, updateSatisfy} from "@/api/bdh-agric-invest/invest/satisfy";
import {getDicts} from "@/api/bdh-agric-invest/system/dict/data";
import {postForExcel,postForExcelXls} from "@/api/bdh-agric-invest/invest/excel";
const {proxy} = getCurrentInstance();
const colors = ref(['#FF9900', '#FF9900', '#FF9900'])
const satisfyList = ref([]);
const open = ref(false);
const openList = ref(false);
const curIdIccid=ref("");
const openListTitle=ref("订单列表");
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const taskDateArr = ref([]);//上架日期
const daterangeControlTime = ref([]);
const feedbackTypeOption = ref([]);//反馈状态
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const orderStatusOpinion = ref([]) //订单状态
const starOption = ref([
    {code:0,name:"0星"},
    {code:1,name:"1星"},
    {code:2,name:"2星"},
    {code:3,name:"3星"},
    {code:4,name:"4星"},
    {code:5,name:"5星"},
  ])
const columns = ref([
  {key: 0, label: `归属`, visible: true},
  {key: 1, label: `反馈状态`, visible: true},
  {key: 2, label: `用户姓名`, visible: true},
  {key: 3, label: `商品质量星级`, visible: true},
  {key: 4, label: `商家服务星级`, visible: true},
  {key: 5, label: `用户反馈时间`, visible: true},
  {key: 6, label: `用户反馈内容`, visible: true},
  {key: 7, label: `回复内容`, visible: true}
]);

const data = reactive({
  form: {},
  queryParams: {
    rows: 10,
    page: 1,
    statYear: null,
    orgCode: null,
    orgName: null,
    prodQuality: null,
    busiServe: null,
    suggesMsg: null,
    picUrl: null,
    statusCd: null,
    prodQualityBegin: null,
    prodQualityEnd:  null,
    busiServeBegin: null,
    busiServeEnd: null,
    beginDate:null,
    endDate:null,
    feedbackType:null
  },
  rules: {
    farmReport: [{ required: true, message: '请输入农场反馈信息', trigger: "change" }],
    feedbackType: [{ required: true, message: '请选择反馈状态', trigger: "change" }],
  }
});
const queryOrgRef = ref();
const {queryParams, form, rules} = toRefs(data);

onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
      ? window.innerHeight - searchHeight.value - 220
      : window.innerHeight - 220;
});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
      ? window.innerHeight - searchHeight.value - 220
      : window.innerHeight - 220;
});

//所在单位更改
const handleOrgCodeQuery = (orgInfo) => {
  console.info(orgInfo);
  if (!orgInfo.orgCode) queryOrgRef.value.clear();
  queryParams.value.orgCode = orgInfo.orgCode;
  queryParams.value.orgName = orgInfo.orgName;
};


/** 查询满意度调查列表 */
function getList() {
  loading.value = true;
  if (taskDateArr.value&&taskDateArr.value.length != 0) {
    queryParams.value.beginDate = taskDateArr.value[0];
    queryParams.value.endDate = taskDateArr.value[1];
  }else{
    queryParams.value.beginDate = null;
    queryParams.value.endDate = null;
  }
  listSatisfy(queryParams.value).then(response => {
    satisfyList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    satisfyResearchId: null,
    statYear: null,
    orgCode: null,
    orgName: null,
    prodQuality: null,
    busiServe: null,
    suggesMsg: null,
    picUrl: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    statusCd: null,
    prodQualityBegin: null,
    prodQualityEnd: null,
    busiServeBegin:null,
    busiServeEnd:null,
    beginDate:null,
    endDate:null,
    feedbackType:null
  };
  proxy.resetForm("satisfyRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value={
        rows: 10,
        page: 1,
        statYear: null,
        orgCode: null,
        orgName: null,
        prodQuality: null,
        busiServe: null,
        suggesMsg: null,
        picUrl: null,
        statusCd: null,
        prodQualityBegin: null,
        prodQualityEnd: null,
        busiServeBegin:null,
        busiServeEnd:null,
        beginDate:null,
        endDate:null,
        feedbackType:null
  }
  queryOrgRef.value.clear()
  taskDateArr.value=[]
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.satisfyResearchId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

function feedbackTypeFmt(row){
  for(let i=0;i<feedbackTypeOption.value.length;i++){
    if(feedbackTypeOption.value[i].code==row.feedbackType){
      return feedbackTypeOption.value[i].name
    }
  }
  return "未反馈";
}

function orderStatusOpinionFmt(orderStatus){
  for(let i=0;i<orderStatusOpinion.value.length;i++){
    if(orderStatusOpinion.value[i].code==orderStatus){
      return orderStatusOpinion.value[i].name
    }
  }
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const satisfyResearchId = row.satisfyResearchId || ids.value
  getSatisfy(satisfyResearchId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "农户评价回复";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["satisfyRef"].validate(valid => {
    if (valid) {
      updateSatisfy(form.value).then(response => {
        proxy.$modal.msgSuccess("修改成功");
        open.value = false;
        getList();
      });
    }
  });
}


/** 导出按钮操作 */
const handleExport = () => {
  postForExcelXls('/bdh-agric-invest-api/invest/satisfy/export', queryParams.value,`满意度_${new Date().getTime()}.xlsx`);
}

/** 获取字典 */
const getOptions = () => {
  getDicts("satisfy_feedback_type").then((response) => {
    feedbackTypeOption.value = response.data;
  });

  //orderStatusOpinion
  getDicts("invest_order_status").then((response) => {
    orderStatusOpinion.value = response.data;
  });
}

getOptions();
getList();
</script>
<style scoped lang="scss">
  :deep(.el-form-item__content) {
  display: block;
  flex-wrap: wrap;
  align-items: center;
  flex: 1;
  line-height: 32px;
  position: relative;
  font-size: var(--font-size);
  min-width: 0;
}
</style>
