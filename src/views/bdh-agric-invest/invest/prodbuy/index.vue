<template>
    <div class="app-container">
      <div ref="searchDom">
        <el-collapse-transition>
          <el-form :model="queryParams" ref="queryRef" v-show="showSearch" label-width="80px" class="form-line">
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-form-item label="年份" prop="year">
                        <el-select v-model="queryParams.year" clearable placeholder="请选择年份">
                            <el-option
                                v-for="dict in yearOption"
                                :key="dict.code"
                                :label="dict.name"
                                :value="dict.code"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="6">
                    <el-form-item label="所在单位" prop="organizationNo">
                        <org-select ref="queryOrgRef" @handleOrgCode="handleOrgCodeQuery" :level="3" :checkStrictly="false"/>
                    </el-form-item>
                </el-col>

                <el-col :span="6">
                    <el-form-item label="企业简称" prop="aiCompanyId">
                        <el-select v-model="queryParams.aiCompanyId" clearable placeholder="请选择企业简称">
                            <el-option
                                v-for="dict in companyAbbrOption"
                                :key="dict.code"
                                :label="dict.name"
                                :value="dict.code"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
              <el-col :span="6">
                <el-form-item label="审核状态" prop="auditStatus">
                  <el-select v-model="queryParams.auditStatus"   clearable placeholder="全部" >
                    <el-option
                        v-for="dict in auditStatusOption"
                        :key="dict.code"
                        :label="dict.name"
                        :value="dict.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
                <el-col :span="6">
                  <el-form-item label="作业季节" prop="fertilizeSeason">
                    <el-select
                        v-model="queryParams.fertilizeSeason"
                        clearable
                        placeholder="请选择作业季节">
                      <el-option
                          v-for="dict in fertilizes"
                          :key="dict.code"
                          :label="dict.name"
                          :value="dict.code"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="18" align="right">
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                </el-col>
            </el-row>
          </el-form>
        </el-collapse-transition>
      </div>


        <!-- 新增、修改、删除、导出 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    icon="Plus"
                    @click="handleAdd"
                    v-hasPermi="['prodbuy:insert']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    icon="Edit"
                    :disabled="canEdit"
                    @click="handleUpdate"
                    v-hasPermi="['prodbuy:update']"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleBatchDelete"
                    v-hasPermi="['prodbuy:logicDeleteByIds']"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    icon="Download"
                    @click="handleExport"
                    v-hasPermi="['prodbuy:exportExcel']"
                >导出
                </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                  icon="Upload"
                  @click="handleExcelImport"
                  v-hasPermi="['buy:importExcel']"
              >批量导入
              </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    icon="Check"
                    :disabled="single"
                    @click="handleApprove"
                    v-hasPermi="['prodbuy:approve']"
                >审核
                </el-button>
            </el-col>
<!--          <el-col :span="1.5">
            <el-button
                type="success"
                plain
                icon="Check"
                :disabled="cancelShipFlag"
                @click="handleCancelReturn"
                v-hasPermi="['prodShipInfo:approve']"
            >撤回发货
            </el-button>
          </el-col>-->
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!--审核-->
        <el-dialog :title="title" v-model="openApproveView"  append-to-body :close-on-click-modal="false">
            <el-form ref="approveForm" :model="form" :rules="rulesApprove" label-width="100px">
                <el-form-item label="是否通过" prop="agree">
                    <el-radio v-model="form.agree" :label="true">同意</el-radio>
                    <el-radio v-model="form.agree" :label="false">不同意</el-radio>
                </el-form-item>
                <el-form-item label="审核意见" prop="opinion">
                    <el-input v-model="form.opinion" type="textarea"/>
                </el-form-item>
                <el-form-item v-if="ckFgsFh()" label="分公司复函" prop="fgsAnswerFileArray">
                  <fileUpload :limit="1" v-model:fileType="fileType"
                              v-model="form.fgsAnswerFileArray"></fileUpload>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitApproveForm">确 定</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 分页查询表格 -->
        <el-table border :height="tableHeight"  :data="prodShipInfoList" @selection-change="handleSelectionChange" ref="mainTable">
            <el-table-column type="selection" width="55" fixed="left" align="center"/>
            <el-table-column label="ID" align="center" prop="prodShipId"/>
            <el-table-column label="年份" align="center" prop="year"/>
            <el-table-column label="作业季节" align="center" prop="fertilizeSeason"
                             :formatter="fertilizeSeasonFmt"/>
            <el-table-column label="企业简称" align="center" prop="companyAbbr" />
            <el-table-column label="所在单位" align="center" :show-overflow-tooltip="true" prop="organizationName"/>
            <el-table-column label="自采申请时间" align="center" prop="sendTime" width="120">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="申请总量" align="center" prop="sendNumTotal">
                <template #default="scope">
                    <a style="color: dodgerblue" @click="handleView(scope.row)" >{{ scope.row.sendNumTotal }}</a>
                </template>
            </el-table-column>
            <el-table-column label="审核状态" align="center" prop="auditStatus" :formatter="auditStatuFormat"/>
            <el-table-column label="当前审核人" align="center" prop="auditPerson" :show-overflow-tooltip="true" :formatter="fmtAuditName"/>
            <el-table-column label="操作" align="center" width="200" fixed="right" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        type="primary"
                        link
                        icon="Edit"
                        v-if="scope.row.auditStatus==-1"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['prodbuy:update']"
                    >修改
                    </el-button>
                    <el-button
                        type="primary"
                        link
                        icon="Delete"
                        v-if="scope.row.auditStatus==-1||scope.row.auditStatus==4"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['prodbuy:logicDeleteById']"
                    >删除
                    </el-button>
                  <el-button
                      v-if="scope.row.auditStatus==-1"
                      type="primary"
                      link
                      icon="CircleCheck"
                      v-hasPermi="['prodbuy:startflow']"
                      @click="handleStartFlow(scope.row)"
                  >发起流程
                  </el-button>
                    <el-link type="success" icon="User" @click="viewFlow(scope.row)">查看流程</el-link>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页查询底部换页栏 -->
        <pagination
            v-show="total>0"
            :total="total"
            v-model:page="queryParams.page"
            v-model:limit="queryParams.rows"
            @pagination="getList"
        />

        <!-- 查看流程对话框 -->
        <el-dialog
            v-model="processVisible"
            title="查看流程"
            width="800px"
        >
            <FlowDetail v-model="form.instId" v-if="flowDetailTag" ></FlowDetail>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="processVisible = false">关闭</el-button>
                </span>
            </template>
        </el-dialog>



      <!-- 导入对话框 -->
      <el-dialog :title="title" v-model="upload.open" :close-on-click-modal="false" width="90%" append-to-body>
        <el-form ref="prodImportFormRef" :model="importForm"  :rules="rulesImport" label-width="auto">
          <el-row  :gutter="10">
            <el-col :span="6">
              <el-form-item label="年份" prop="year">
                <el-select
                    v-model="importForm.year"
                    clearable
                    placeholder="请选择年份">
                  <el-option
                      v-for="dict in yearOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="作业季节" prop="fertilizeSeason">
                <el-select
                           v-model="importForm.fertilizeSeason"
                           clearable
                           placeholder="请选择作业季节">
                  <el-option
                      v-for="dict in fertilizes"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="所在单位" prop="orgCode">
                <org-select  v-if="orgFlag" ref="importOrgRef" :defaultOrgCode="importForm.orgCode"
                            @handleOrgCode="handleImportOrgCode"/>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="企业简称" prop="aiCompanyId" label-width="auto">
                <el-select v-model="importForm.aiCompanyId"
                           clearable placeholder="请选择企业简称">
                  <el-option
                      v-for="dict in companyAbbrOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">

          </el-col>
          <el-col :span="1.5">

          </el-col>
          <el-col>
            <el-upload
                ref="uploadRef"
                :limit="1"
                accept=".xlsx, .xls"
                :headers="upload.headers"
                :action="upload.url + '?updateSupport=' + upload.updateSupport"
                :disabled="upload.isUploading"
                :on-progress="handleFileUploadProgress"
                :on-success="handleFileSuccess"
                :auto-upload="false"
                drag
            >
              <el-icon class="el-icon--upload"><upload-filled /></el-icon>
              <div class="el-upload__text"><em>点击上传</em></div>
              <template #tip>
                <div class="el-upload__tip text-center">
                  <div class="el-upload__tip">
                    1.先填写“年份”“作业季节”“所在单位”“企业简称”再下载模板。
                    2.请不要擅自改动excel 模板中已有的数据项
                    3.如您已有模板，可直接导入，导入时不必填写 “年份”“作业季节”“所在单位”“企业简称” 选项。
                  </div>
                  <span>仅允许导入xls、xlsx格式文件。</span>
                  <el-link
                      type="primary"
                      :underline="false"
                      style="font-size: 12px; vertical-align: baseline"
                      @click="handleImportTemplate"
                  >下载模板</el-link>
                </div>
              </template>
            </el-upload>
          </el-col>
        </el-row>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitFileForm">确 定</el-button>
            <el-button @click="upload.open = false">取 消</el-button>
          </div>
        </template>
      </el-dialog>

        <!-- 添加或修改发运信息对话框 -->
        <el-dialog :title="title" v-model="open" :close-on-click-modal="false" width="90%" append-to-body>
            <el-form ref="prodShipInfoRef" :model="shipForm" :disabled="viewType" :rules="rulesShip" label-width="auto">
                <el-row  :gutter="120">
                    <el-col :span="6">
                        <el-form-item label="年份" prop="year">
                            <el-select
                                v-model="shipQueryParams.year"
                                @change="handleQueryDetail"
                                clearable
                                :disabled="insertOrEdit"
                                placeholder="请选择年份">
                                <el-option
                                    v-for="dict in yearOption"
                                    :key="dict.code"
                                    :label="dict.name"
                                    :value="dict.code"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="6">
                      <el-form-item label="作业季节" prop="regionCode">
                        <el-select :disabled="insertOrEdit"
                            v-model="shipQueryParams.fertilizeSeason"
                                   @change="handleQueryDetail"
                            clearable
                            placeholder="请选择作业季节">
                          <el-option
                              v-for="dict in fertilizes"
                              :key="dict.code"
                              :label="dict.name"
                              :value="dict.code"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>

                    <el-col :span="6">
                        <el-form-item label="所在单位" prop="organizationNo">
                            <org-select :disabled="insertOrEdit" v-if="orgFlag" ref="shipOrgRef" :defaultOrgCode="shipQueryParams.organizationNo"
                                        @handleOrgCode="handleOrgCodeQueryDetail"/>
                        </el-form-item>
                    </el-col>

                    <el-col :span="6">
                      <el-form-item label="企业简称" prop="aiCompanyId" label-width="auto">
                        <el-select v-model="shipQueryParams.aiCompanyId" :disabled="insertOrEdit" @change="handleQueryDetail"
                                   clearable placeholder="请选择企业简称">
                          <el-option
                              v-for="dict in companyAbbrOption"
                              :key="dict.code"
                              :label="dict.name"
                              :value="dict.code"
                          />
                        </el-select>
                      </el-form-item>
                    </el-col>


                </el-row>
              <template>
                <svg width="1" height="100">
                  <line x1="0" y1="0" x2="0" y2="100" stroke="black" stroke-width="1"/>
                </svg>
              </template>
                <el-row :gutter="120">
                  <el-col :span="8">
                    <el-form-item label="上报申请" prop="reportRequestUrlArray" >
                      <fileUpload :limit="1" v-model:fileType="fileType" :show="updateType=='update'"
                                  v-model="shipQueryParams.reportRequestUrlArray"
                                  urlApi="bdh-agric-invest-api"
                                  @fileUploadChange="fileUploadReportRequestUrlArray"></fileUpload>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="其他附件">
                      <fileUpload :limit="3" v-model:fileType="fileType" v-model="shipQueryParams.otherMsgUrlArray"
                                  urlApi="bdh-agric-invest-api"
                                  @fileUploadChange="fileUploadOtherMsgUrlArray"></fileUpload>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="分公司复函">
                      <fileUpload :limit="1" v-model:fileType="fileType" v-model="shipQueryParams.fgsAnswerFileArray"
                                  urlApi="bdh-agric-invest-api"
                                  @fileUploadChange="fileUploadFgsAnswerFileArray"></fileUpload>
                    </el-form-item>
                  </el-col>
                </el-row>


            </el-form>

          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" :loading="isloading" v-if="!viewType" @click="submitForm">确 定</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button @click="cancel">取 消</el-button>
            </el-col>
          </el-row>
          <el-table v-if="viewType" height="470" border  :data="detailList">
            <el-table-column label="企业简称" align="center" prop="companyAbbr"/>
            <el-table-column label="投入品分类" align="center" prop="aiTypeName"/>
            <el-table-column label="商品编码" align="center" prop="aiTypeSubCode"/>
            <el-table-column label="商品名称" align="center" prop="aiTypeSubName"/>
            <el-table-column label="商品规格" align="center" prop="aiTypeSubSpecs"/>
            <el-table-column label="供应商" align="center" width="200" prop="buySupplier"/>
            <el-table-column label="生产厂家" align="center" width="200" prop="buyManufacturers"/>
            <el-table-column label="规格" align="center" width="200" prop="buySpec"/>
            <el-table-column label="价格" align="center" width="250" prop="buyPrice"/>
            <el-table-column label="本次自采数量" align="center" prop="sendNum" width="250">
              <template #default="scope">
                {{scope.row.sendNum}}{{scope.row.aiTypeReportUnit}}
              </template>
            </el-table-column>
          </el-table>

          <el-table v-if="!viewType" height="470" border  :data="planList">
            <el-table-column label="企业简称" align="center" prop="companyAbbr"/>
            <el-table-column label="投入品分类" width="100" align="center" prop="aiTypeName"/>
            <el-table-column label="商品编码" align="center" prop="aiTypeSubCode"/>
            <el-table-column label="商品名称" align="center" prop="aiTypeSubName"/>
            <el-table-column label="商品规格" align="center" prop="aiTypeSubSpecs"/>
            <el-table-column label="商品重量" align="center" prop="aiTypeSubWeight">
              <template #default="scope">
              {{scope.row.aiTypeSubWeight}}{{scope.row.aiTypeSubUnit}}
              </template>
            </el-table-column>
            <el-table-column label="供应商" align="center" width="200" prop="buySupplier">
              <template #default="scope">
                <el-input v-model="scope.row.buySupplier" :disabled="viewType" placeholder="请输入供应商" />
              </template>
            </el-table-column>
            <el-table-column label="生产厂家" align="center" width="200" prop="buyManufacturers">
              <template #default="scope">
                <el-input v-model="scope.row.buyManufacturers" :disabled="viewType"  placeholder="请输入生产厂家" />
              </template>
            </el-table-column>
            <el-table-column label="规格" align="center" width="200" prop="buySpec">
              <template #default="scope">
                <el-input v-model="scope.row.buySpec" :disabled="viewType" placeholder="请输入规格"/>
              </template>
            </el-table-column>
            <el-table-column label="价格" align="center" width="250" prop="buyPrice">
              <template #default="scope">
                <el-input-number v-model="scope.row.buyPrice" :disabled="viewType"
                                 placeholder="请输入价格"
                                 :min="0"
                                 :max="100000"/>
              </template>
            </el-table-column>
            <el-table-column label="订购量" align="center" prop="currentNum"/>
            <el-table-column label="已发货量" align="center" prop="alreadySendNum"/>
            <el-table-column label="发货待审核量" width="120" align="center" prop="alreadySendButNotPassNumber"/>
            <el-table-column label="本次自采数量" align="center" prop="sendNum" width="250">
              <template #default="scope">
                <el-input width="200" :disabled="viewType"
                          v-model="scope.row.sendNum"
                          @change="handleSendNum(scope.row)"
                          @input="checkNumber($event,scope.row,'sendNum')"
                          placeholder="本次自采数量"
                          clearable
                          maxlength="10"
                >
                  <template #append>{{ scope.row.aiTypeReportUnit }}</template>
                </el-input>
              </template>
            </el-table-column>
            <el-table-column label="预计发货总量" width="120" align="center" prop="estimateSendTotal"/>
          </el-table>
          <!-- <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" :loading="isloading" v-if="!viewType" @click="submitForm">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </template> -->
        </el-dialog>
    </div>
</template>

<script setup name="/invest/buy/queryByPage">
import {
  listProdShipInfo,
  getProdShipInfo,
  delProdShipInfo,
  delProdShipInfos,
  addProdShipInfo,
  updateProdShipInfo,
  getPropertyFromAiConsigneeInfo,
  getProdReportPlanConfigList,
  approve, cancelShip,startFlow,getProdDetail
} from "@/api/bdh-agric-invest/invest/prodbuy";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import {reactive, ref, toRefs} from "vue";
import {allCompanyInfo} from "@/api/bdh-agric-invest/invest/companyInfo";
import {getDicts} from "@/api/bdh-agric-invest/system/dict/data";
import {queryRegionOrg} from "@/api/bdh-agric-invest/invest/consigneeInfo";
import {postForExcel} from "@/api/bdh-agric-invest/invest/excel";
import {getInfo} from '@/api/login'
import { queryAllOrgTree} from "@/api/bdh-agric-invest/system/dict/data";
import store from '@/store'
import { getToken } from "@/utils/auth";
import {checkNumber} from "@/views/bdh-agric-invest/utils/validate";
import FlowDetail from "@/views/bdh-agric-invest/components/FlowDetail/index.vue";
/*** 导入参数 */
const upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 设置上传的请求头部
  headers: { "access-token": getToken() },
  // 上传的地址
  url: window.VITE_APP_BASE_API + "/bdh-agric-invest-api/invest/buy/importExcel",
});

const fileListUpload=ref([])
const fileType = ['pdf'];
const {proxy} = getCurrentInstance();
const fertilizes = ref([]);
const prodShipInfoList = ref([]);
const open = ref(false);
const loading = ref(true);
const detailLoading = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const cancelShipFlag = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const yearOption = ref([]);
const updateType=ref('update')
const companyAbbrOption = ref([]);
const arriveTypeOption = ref([]);
const transTypeOption = ref([]);
const auditStatusOption = ref([]);
const regionOrgOption = ref([]);
const instId = ref(null);
const openApproveView = ref(false);
const canSubmit = ref(true);
const insertOrEdit = ref(true);
const isloading = ref(false)
const orgFlag = ref(false)
const flowDetailTag=ref(false);
const processVisible = ref(false);
const queryOrgRef = ref();
const shipOrgRef = ref();
const importOrgRef= ref();
const canCancelShipIdsArr = ref([]);
const selectRow=ref([])
const detailList=ref([])
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const defaultOrgCode = ref('');

//批量导入相关
const openImport=ref(false)

const regCascProps = {
    emitPath: false,
    value: "regionCode",
    label: "regionName"
}
const canEdit=ref(true)
const viewType=ref(false)
const data = reactive({

    //导入form
    importForm:{
      year: null,
      orgCode: null,
      fertilizeSeason:null,
      aiCompanyId: null,
    },

    //导入rules
    rulesImport: {
      year: [
        {required: true, message: "请输入年份", trigger: "change"}
      ],
      fertilizeSeason: [
        {required: true, message: "请选择作业季节", trigger: "change"}
      ],
      organizationNo: [
        {required: true, message: "请输入收货单位", trigger: "change"}
      ],
      aiCompanyId: [
        {required: true, message: "请选择企业", trigger: "change"}
      ],
    },


    //审核表单
    form: {},

    //新增修改对话框返回表单
    planList: [],

    //TODO  organizationNo不能默认死数据，应该为列表第一条
    shipForm: {
        year: null,
        arrivalType: null,
        organizationNo: null,
        fertilizeSeason:null,
        reportRequestUrlArray:[],
        otherMsgUrlArray:[],
        fgsAnswerFileArray:[],
        aiCompanyId: null,
    },


    //新增页面查询参数
    shipQueryParams: {
        year: null,
        aiCompanyId: null,
        organizationNo: null,
        fertilizeSeason:null,
        reportRequestUrlArray:[],
        otherMsgUrlArray:[],
        fgsAnswerFileArray:[],
    },

    //查询参数
    queryParams: {
        rows: 10,
        page: 1,
        year: null,
        arriveType: null,
        organizationNo: null,
    },

    rulesShip: {
        year: [
          {required: true, message: "请输入年份", trigger: "change"}
        ],
        fertilizeSeason: [
          {required: true, message: "请选择作业季节", trigger: "change"}
        ],
        organizationNo: [
          {required: true, message: "请输入收货单位", trigger: "change"}
        ],
        aiCompanyId: [
          {required: true, message: "请选择企业", trigger: "change"}
        ],
        reportRequestUrlArray: [
          {required: true, message: "请上传上报申请", trigger: "change"}
        ],

        //table 中的校验项目
        buySupplier:[{required: true, message: "请输入供应商", trigger: "change"}],
        buyManufacturers:[{required: true, message: "请输入生产厂家", trigger: "change"}],
        buySpec:[{required: true, message: "请输入规格", trigger: "change"}],
        buyPrice:[{required: true, message: "请输入价格", trigger: "change"}],
        sendNum:[{required: true, message: "请输入采购量", trigger: "change"}],
    },

    rulesApprove: {
        agree: [
            {required: true, message: "请选择是否同意", trigger: "change"},
        ],
        opinion: [
            {required: true, message: "请输入审核意见", trigger: "change"},
            {max: 100, message: "最多100个字符", trigger: "blur"}
        ],
        fgsAnswerFileArray:[
            {validator: function(rule, value, callback){
                              if(ckFgsFh()&&(!form.value.fgsAnswerFileArray||form.value.fgsAnswerFileArray.length==0)){
                                callback("请上传分公司复函！")
                              }else{
                                callback()
                              }
              },trigger: "blur"}
        ]
    }
});

const {queryParams, shipForm, shipQueryParams, planList, form, rulesShip, rulesApprove,importForm,rulesImport} = toRefs(data);

onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});

//表格 运输方式状态栏字典转换
const transTypeOptionFmt=(row)=>{
    let transTypeName=null
    transTypeOption.value.forEach(function(item){
        if(item.code===row.arrivalType+''){
            transTypeName =  item.name
        }
    })
    return transTypeName
}

let fileUploadReportRequestUrlArray = (value) => {
  shipQueryParams.value.reportRequestUrlArray = value;
  proxy.$refs.prodShipInfoRef.clearValidate('reportRequestUrlArray');
  // if (Array.isArray(value) && value.length > 0) {
  //   const urls = value.map(item => item.url || item);
  //   shipQueryParams.value.reportRequestUrlArray = urls;
  //   proxy.$refs.prodShipInfoRef.clearValidate('reportRequestUrlArray');
  // } else {
  //   shipQueryParams.value.reportRequestUrlArray = '';
  // }
}

let fileUploadOtherMsgUrlArray = (value) => {
  shipQueryParams.value.otherMsgUrlArray = value;
  proxy.$refs.prodShipInfoRef.clearValidate('otherMsgUrlArray');
  // if (Array.isArray(value) && value.length > 0) {
  //   const urls = value.map(item => item.url || item);
  //   shipQueryParams.value.otherMsgUrlArray = urls;
  //   proxy.$refs.prodShipInfoRef.clearValidate('otherMsgUrlArray');
  // } else {
  //   shipQueryParams.value.otherMsgUrlArray = '';
  // }
}

let fileUploadFgsAnswerFileArray = (value) => {
  shipQueryParams.value.fgsAnswerFileArray = value;
  proxy.$refs.prodShipInfoRef.clearValidate('fgsAnswerFileArray');
  // if (Array.isArray(value) && value.length > 0) {
  //   const urls = value.map(item => item.url || item);
  //   shipQueryParams.value.fgsAnswerFileArray = urls;
  //   proxy.$refs.prodShipInfoRef.clearValidate('fgsAnswerFileArray');
  // } else {
  //   shipQueryParams.value.fgsAnswerFileArray = '';
  // }
}

function fertilizeSeasonFmt(row){
  for(let i=0;i<fertilizes.value.length;i++){
    if(fertilizes.value[i].code==row.fertilizeSeason){
      return fertilizes.value[i].name
    }
  }
}

/** 批量撤回发货 */
function handleCancelReturn(){
  const ids = canCancelShipIdsArr.value;
  proxy.$modal.confirm('是否确认批量撤回发货？').then(function () {
    return cancelShip(ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("批量撤回发货成功");
  }).catch(() => {
  });
}

//撤回发货
function handleCancelShip(data){
    const arr = [];
    arr.push(data.prodShipId);
  cancelShipByIds(arr);
}


function handleStartFlow(data){
  if(!data.reportRequestUrl){
    proxy.$modal.msgError("请先在修改页中上传'上报申请'")
    return
  }
  proxy.$modal.confirm('确认发起流程么？').then(function () {
    return startFlow(data);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("发起流程成功");
  }).catch(() => {
  });
}

function cancelShipByIds(ids){
  proxy.$modal.confirm('是否确认撤回发货？').then(function () {
    return cancelShip(ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("撤回发货成功");
  }).catch(() => {
  });
}

//表格 审核状态栏字典转换
function auditStatuFormat(row) {
    if (row.auditStatus == null){
      return '未审核'
    }
    let auditStatus = null
    if (auditStatusOption.value.length > 0) {
        auditStatusOption.value.forEach((v, i) => {
            if (v.code == row.auditStatus) {
                auditStatus = v.name
            }
        })
    }
    return auditStatus;
}

//表格 到货状态字典转换
function arriveTypeFormat(row) {
  if (row.arriveType == null){
    return '已撤回'
  }
    let arriveType = null
    if (arriveTypeOption.value.length > 0) {
        arriveTypeOption.value.forEach((v, i) => {
            if (v.code == row.arriveType) {
                arriveType = v.name
            }
        })
    }
    return arriveType;
}

//表格 审核人字典转换
function fmtAuditName(row,column){
    if(row.flowMsg&&JSON.parse(row.flowMsg).auditNames){
        return JSON.parse(row.flowMsg).auditNames
    }
    return "无";
}

/** 获取字典 */
const getDict = () => {
    //查询年份
    getDicts('year_cd').then(response => {
        yearOption.value = response.data.sort((a, b) => {
            return Number(b.code) - Number(a.code)
        })
        queryParams.value.year = yearOption.value[0].code
        queryAllOrgTree("3").then((res) => {
            var orgList = res.data;
            if (orgList && orgList.length && orgList.length > 0) {
                defaultOrgCode.value = orgList[0].orgCode;
                queryParams.value.orgCode = defaultOrgCode.value;
            }
            getList()
        })
    });

    //企业简称
    allCompanyInfo({}).then(response => {
        response.data.forEach(each => {
            companyAbbrOption.value.push({
                code: each.aiCompanyId,
                name: each.companyAbbr
            })
        })
    });

    //到货状态
    getDicts('arrive_type').then(response => {
        arriveTypeOption.value = response.data
        arriveTypeOption.value.unshift({code:"-1", name:"全部"})
        queryParams.value.arriveType = arriveTypeOption.value[0].code
    });

    //运输方式
    getDicts('trp_arrival_type').then(response => {
        transTypeOption.value = response.data
    });

    //审核状态
    getDicts('agric_invest_audit_status').then(response => {
        auditStatusOption.value = response.data
    });


    getDicts("fertilize_season").then((response) => {
      fertilizes.value = response.data
    });

    //查询省市
    queryRegionOrg().then(response => {
        regionOrgOption.value = response.data
    })
}

/** 选择省市后将省市名称也加入form */
const handleRegionSelect = () => {
    let nodesInfo = proxy.$refs.regionCascader.getCheckedNodes()
    regionOrgOption.value[0].children.forEach(each => {
        if (each.regionCode === nodesInfo[0].data.regionCode) {
            shipForm.value.regionName = nodesInfo[0].data.regionName
        }
    })
}

/** 组织机构下拉 */
const handleOrgCodeQuery = (orgInfo) => {
    if(!orgInfo.orgCode) queryOrgRef.value.clear()
    queryParams.value.orgCode = orgInfo.orgCode
}

/** 新增修改对话框 —— 组织机构下拉 */
const handleOrgCodeQueryDetail = (orgInfo) => {
    if(!orgInfo.orgCode) shipOrgRef.value.clear()
    shipQueryParams.value.organizationNo = orgInfo.orgCode

    handleQueryDetail()
}

const handleImportOrgCode = (orgInfo) => {
  if(!orgInfo.orgCode) importOrgRef.value.clear()
  importForm.value.orgCode=orgInfo.orgCode
}

/** 触发查询  投入品计划、联系人信息 */
function handleQueryDetail () {
    //每次修改年份、所在单位、到达状态三个参数，重置其他
    resetShipForm()
    //将查询参数shipQueryParams填充入shipForm中，因为rules校验的是shipForm
    shipForm.value.year = shipQueryParams.value.year;
    shipForm.value.organizationNo = shipQueryParams.value.organizationNo;
    shipForm.value.aiCompanyId = shipQueryParams.value.aiCompanyId;
    shipForm.value.fertilizeSeason = shipQueryParams.value.fertilizeSeason;
    if(shipQueryParams.value.year && shipQueryParams.value.organizationNo
        &&shipQueryParams.value.fertilizeSeason
        &&shipQueryParams.value.aiCompanyId){
        getPlanInfo()
    }
}

/** 查询发运信息列表 */
function getList() {
    loading.value = true;
    if(queryParams.value.arriveType === "-1") {
        queryParams.value.arriveType = null
    }
    listProdShipInfo(queryParams.value).then(response => {
        prodShipInfoList.value = response.data.records;
        total.value = response.data.total;
        loading.value = false;
    });
}

/** 新增修改对话框 —— 新增页投入品计划列表 */
function getPlanInfo() {
    detailLoading.value = true;
    getProdReportPlanConfigList({
        year : shipQueryParams.value.year,
        orgCode : shipQueryParams.value.organizationNo,
        aiCompanyId:shipQueryParams.value.aiCompanyId,
        fertilizeSeason: shipQueryParams.value.fertilizeSeason
    }).then(response => {
        //将企业id加入shipForm
        if(response.data&&response.data[0]){
          shipForm.value.aiCompanyId = response.data[0].aiCompanyId
          planList.value = response.data;
          initialSendNum() //点开新增页面时初始化预计发货总量
        }
        detailLoading.value = false;
    }, error => {
        detailLoading.value = false;
        console.log('请求投入品计划列表失败 ', error.message)
    })
}


/** 提交按钮 */
function submitForm() {
    //先重置（避免校验不通过还重复将表格数组加入aiProdShipDetailList）
    //再将planList存入shipForm里的aiProdShipDetailList数组中
    shipForm.value.aiProdShipDetailList = []
    let isNull = false
    let isbuySupplierNull=false
    let isbuyManufacturersNull=false
    let isbuySpecNull=false
    let isbuyPriceNull=false
    let count = 0
    shipForm.value.aiCompanyId=shipQueryParams.value.aiCompanyId
    shipForm.value.fgsAnswerFileArray=shipQueryParams.value.fgsAnswerFileArray
    shipForm.value.otherMsgUrlArray=shipQueryParams.value.otherMsgUrlArray
    shipForm.value.reportRequestUrlArray=shipQueryParams.value.reportRequestUrlArray
    console.info('planList.value:',planList.value);

    var stop=false;
    planList.value.forEach(item=>{
      if(!chkCurrentNum(item)){
        stop=true;
        return;
      }

      const add = Number(item.sendNum) + Number(item.alreadySendNum) + Number(item.alreadySendButNotPassNumber)
      if(Number(add) > Number(item.currentNum)){
        stop=true;
        proxy.$modal.msgError("商品["+item.aiTypeSubName+"]自采数量不能超过订购总量");
        return ;
      }
    })

    if(stop){
      return;
    }


    planList.value.forEach( each => {
        if(each.sendNum>0){
          shipForm.value.aiProdShipDetailList.push({
            aiCompanyId : each.aiCompanyId,
            aiTypeSubId : each.aiTypeSubId,
            sendNum : each.sendNum,
            orgCode : each.orgCode,
            aiTypeId : each.aiTypeId,
            year: each.year,
            prodShipDetailId : each.prodShipDetailId,
            sendNumOldForUpdate : each.sendNumOldForUpdate,
            buySupplier:each.buySupplier,
            buyManufacturers:each.buyManufacturers,
            buySpec:each.buySpec,
            buyPrice:each.buyPrice,
          })
        }

        if(each.sendNum === null||each.sendNum<=0){
            isNull = true;
        }
        else{
            count += 1;
            if(each.buySupplier==null){
              isbuySupplierNull=true
            }
            if(each.buyManufacturers==null){
              isbuyManufacturersNull=true
            }
            if(each.buySpec==null){
              isbuySpecNull=true
            }
            if(each.buyPrice==null){
              isbuyPriceNull=true
            }
        }
    });

    if(isbuySupplierNull) {
      proxy.$modal.alertError("请输入供应商！");
      return
    }
    if(isbuyManufacturersNull){
      proxy.$modal.alertError("请输入生产厂家！");
      return
    }
    if(isbuySpecNull){
      proxy.$modal.alertError("请输入规格！");
      return
    }
    if(isbuyPriceNull){
      proxy.$modal.alertError("请输入价格！");
      return
    }

    if(count <= 0) {
      proxy.$modal.alertError("订单的总发运量必须大于零！");
      return
    }
    if(!canSubmit.value){
      proxy.$modal.alertError("发货总量超出订购量！");
      return
    }

    proxy.$refs["prodShipInfoRef"].validate(valid => {
        if (valid && canSubmit.value && count > 0) {
            isloading.value = true

            if (shipForm.value.prodShipId != null) {
                open.value = false;
                updateProdShipInfo(shipForm.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                    isloading.value = false
                },error => {
                    isloading.value = false
                    console.log('提交失败 ', error.message)
                });
            } else {open.value = false;
                addProdShipInfo(shipForm.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                    isloading.value = false
                })
            }
        }
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    resetShipQueryParams();
    resetShipForm();
}

/** 初始化新增修改页面时计算出预计发货总量 */
const initialSendNum = () =>{
    planList.value.forEach(each => {
        if(!each.sendNum){      //如果是新增，传来的sendNum为空，要赋为0，如果是修改，使用后端传来的sendNum
            //each.sendNum = Number(each.currentNum) - Number(each.alreadySendButNotPassNumber) - Number(each.alreadySendNum)
            each.sendNum=0
        }
        each.estimateSendTotal = Number(each.sendNum) + Number(each.alreadySendNum) + Number(each.alreadySendButNotPassNumber)
        each.estimateSendTotal = each.estimateSendTotal.toFixed(3)
        if( Number(each.estimateSendTotal) > Number(each.currentNum)){
            canSubmit.value = false
        }
    })
    if(!canSubmit.value) proxy.$modal.alertError("发货总量超出订购量!");
}

/** 当输入发货量时计算出预计发货总量 */
const handleSendNum = (row) =>{
    //校验是否能整除
    if(!chkCurrentNum(row)){
      return false;
    }
    const add = Number(row.sendNum) + Number(row.alreadySendNum) + Number(row.alreadySendButNotPassNumber)
    if(Number(add) <= Number(row.currentNum)){
        row.estimateSendTotal = add.toFixed(3)
    }
    else{   //如果预计发货总量超出订购量 弹窗报错
        console.log(row.sendNum)
        proxy.$modal.alertError("自采总量超出订购量!");
    }
}


import { evaluate } from 'mathjs'
function isInteger(obj) {
  return typeof obj === 'number' && obj%1 === 0
}
function chkCurrentNum(row){
  if(row.sendNum&&row.transNum&&row.transNum==-1&&!isInteger(Number(row.sendNum))){
    proxy.$modal.msgError("商品："+row.aiTypeSubName+"自采数量必须是整数");
    return false;
  }
  console.info('row:',row);
  console.info('row-transNum:',row.transNum);
  if(row.sendNum&&row.aiTypeSubWeight&&row.transNum&&row.transNum!=-1){
    var a=evaluate(row.sendNum+'*'+row.transNum+'*'+10000)
    var b=evaluate(row.aiTypeSubWeight+'*'+10000)
    console.info(evaluate(a+'%'+b));
    if(!(evaluate(a+'%'+b)==0)){
      proxy.$modal.msgError("商品："+row.aiTypeSubName+"自采数量必须是，商品重量的整数倍,请仔细检查数据");
      return false;
    }
  }
  return true;
}

// 表单重置
function reset() {
    form.value = {
        year: null,
        aiCompanyId: null,
        arrivalType: null,
        orgCode: null,
        orgName: null,
        sendTime: null,
        regionCode: null,
        regionName: null,
        shipTitle: null,
        contactName: null,
        contactPhone: null,
        arrivalName: null,
        shipCarnum: null,
        arriveType: null,
        createBy: null,
        prodShipId: null,
    };
    proxy.resetForm("prodImportFormRef");
}

function resetShipQueryParams() {
    shipQueryParams.value.year = null
    shipQueryParams.value.arrivalType = null
    shipQueryParams.value.organizationNo = null
    shipQueryParams.value.reportRequestUrlArray = null
    shipQueryParams.value.otherMsgUrlArray = null
    shipQueryParams.value.fgsAnswerFileArray = null
}

function resetShipForm(){
    shipForm.value={
        aiCompanyId: null,
        aiProdShipDetailList: [],
        arrivalName: null,
        arrivalType: null,
        arriveType: null,
        companyAbbr: null,
        companyName: null,
        contactName: null,
        contactPhone: null,
        organizationName: null,
        organizationNo: null,
        prodShipId: null,
        regionCode: null,
        regionName: null,
        sendTime: null,
        shipCarnum: null,
        shipTitle: null,
        year: null,
      reportRequestUrlArray:[],
      otherMsgUrlArray:[],
      fgsAnswerFileArray:[],
    }
    planList.value=[]
    proxy.resetForm("prodShipInfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    // queryParams.value.rows = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    queryOrgRef.value.clear()
    queryParams.value.year = yearOption.value[0].code;
    queryParams.value.orgCode = defaultOrgCode.value;
    queryOrgRef.value.setValue(defaultOrgCode.value, "");
    handleQuery();
}

//审核时是否校验分公司复函
function ckFgsFh(){
  //如果审核节点是  '分公司业务人员审核' 校验  复函附件不能为空
  return fmtAuditNode(selectRow.value[0])=='分公司业务人员审核'
}

function fmtAuditNode(row){
  console.info('row:',row);
  if(row&&row.flowMsg&&JSON.parse(row.flowMsg).node){
    return JSON.parse(row.flowMsg).node
  }
  return "无";
}

// 多选框选中数据
function handleSelectionChange(selection) {

    selectRow.value=selection

    //激活撤回发货按钮
    const canCancelShip = selection.filter(rows=>rows.auditStatus === 0 && rows.arriveType === 0);
    if(canCancelShip.length > 0 && canCancelShip.length === selection.length){
      cancelShipFlag.value = false
      canCancelShipIdsArr.value = canCancelShip.map(item => item.prodShipId);
    }else {
      cancelShipFlag.value = true
    }

    ids.value = selection.map(item => item.prodShipId);
    single.value = selection.length != 1;

    if(selection.length != 1){
        single.value=true;
    }else{
        //auditStatus==0
        let canAudit=[]
        canAudit=selection.filter(item=>item.auditStatus==0)

        single.value=!(canAudit.length==1)
        if(!single.value){
            instId.value=selection.map(item => item.instId)[0]
        }
    }
    //审核中的才可以删除
    let canRemove=[];

    if(selection&&selection.length){
        //如果选择了，就判断一下选择的数据里 是否有不允许删除的变量
        canRemove=selection.filter(item=>item.auditStatus==-1||item.auditStatus==4)
        multiple.value = !(canRemove&&canRemove.length==selection.length)
    }else{
        multiple.value = true
    }


  if(selection.length != 1){
    canEdit.value=true;
  }else{
    let editList=[]
    editList=selection.filter(item=>item.auditStatus==-1)
    canEdit.value=!(editList.length==1)
  }
}

/** 审核 */
function handleApprove(){
    if(selectRow.value[0].auditPerson
        &&store.getters.staffId
        &&selectRow.value[0].auditPerson.indexOf(store.getters.staffId)==-1){
        proxy.$modal.msgError("当前流程无需您审核！")
      return;
    }
    if(ids.value){
        form.value.prodShipId=ids.value[0]
        form.value.instId=instId;
        form.value.agree=true
        form.value.opinion=null
        form.value.fgsAnswerFileArray=[]
        openApproveView.value=true;
    }

}

/** 审核提交 */
function submitApproveForm(){
    proxy.$refs["approveForm"].validate(valid => {
        if (valid) {
            let apprveForm={
              prodShipId:form.value.prodShipId,
              agree:form.value.agree,
              instId:form.value.instId,
              opinion:form.value.opinion,
              fgsAnswerFileArray:form.value.fgsAnswerFileArray
            }
            approve(apprveForm).then(response=>{
                if(response.success){
                    openApproveView.value=false;
                    proxy.$modal.msgSuccess("成功!");
                    getList();
                }
            })
        }
    })
}

/** 新增按钮操作 */
function handleAdd() {
    detailLoading.value=false;
    resetShipQueryParams();
    resetShipForm();
    orgFlag.value = false;
    nextTick(() => {
        orgFlag.value = true;
    });
    insertOrEdit.value = false;     //新增时，年份和收货单位组件是可用的
    open.value = true;
    title.value = "添加自采信息";
    viewType.value=false
    /*let now= new Date();
    shipQueryParams.value.year = now.getFullYear()+"";
    shipQueryParams.value.arrivalType = transTypeOption.value[0].code*/

    //查询 投入品计划、联系人信息
    handleQueryDetail();
}


/** 修改按钮操作 */
function handleView(row) {
  resetShipQueryParams();
  resetShipForm();
  viewType.value=true;
  orgFlag.value = false;
  nextTick(() => {
    orgFlag.value = true;
  });
  detailLoading.value = true;
  open.value = false;
  const prodShipId = row.prodShipId || ids.value
  getProdDetail(prodShipId).then(response => {
    shipForm.value = response.data
    detailList.value = response.data.aiProdShipDetailList
    shipQueryParams.value.year = shipForm.value.year+""
    shipQueryParams.value.organizationNo = shipForm.value.organizationNo+""
    shipQueryParams.value.arrivalType = shipForm.value.arrivalType+""
    shipQueryParams.value.fertilizeSeason = shipForm.value.fertilizeSeason+""
    shipQueryParams.value.reportRequestUrlArray = shipForm.value.reportRequestUrlArray
    shipQueryParams.value.otherMsgUrlArray=shipForm.value.otherMsgUrlArray
    shipQueryParams.value.fgsAnswerFileArray=shipForm.value.fgsAnswerFileArray
    shipQueryParams.value.aiCompanyId=shipForm.value.aiCompanyId
    insertOrEdit.value = true;
    detailLoading.value = false;
    open.value = true;
    title.value = "查看自采信息";
  });
}

/** 修改按钮操作 */
function handleUpdate(row,type) {

    if(type&&type=='view'){
      viewType.value=true;
    }else{
      viewType.value=false;
    }
    //selectRow.value
    if(selectRow.value[0]&&selectRow.value[0].auditStatus&&selectRow.value[0].auditStatus!=-1){
      proxy.$modal.msgError("审核过的信息无法修改！");
      return
    }


    if(row&&row.auditStatus&&row.auditStatus!=-1){
      proxy.$modal.msgError("审核过的信息无法修改！");
      return
    }

    resetShipQueryParams();
    resetShipForm();
    orgFlag.value = false;
    nextTick(() => {
        orgFlag.value = true;
    });
    detailLoading.value = true;
    open.value = false;
    const prodShipId = row.prodShipId || ids.value
    getProdShipInfo(prodShipId).then(response => {
        if(response.data.auditStatus === 1 || response.data.auditStatus === 4){
            open.value = false;
            proxy.$modal.msgWarning("审核过的信息无法修改!");
        }
        else{
            shipForm.value = response.data
            planList.value = response.data.aiProdShipDetailList
            planList.value.forEach(each => {
                each.sendNumOldForUpdate = each.sendNum
            })
            shipQueryParams.value.year = shipForm.value.year+""
            shipQueryParams.value.organizationNo = shipForm.value.organizationNo+""
            shipQueryParams.value.arrivalType = shipForm.value.arrivalType+""
            shipQueryParams.value.fertilizeSeason = shipForm.value.fertilizeSeason+""
            shipQueryParams.value.reportRequestUrlArray = shipForm.value.reportRequestUrlArray
            shipQueryParams.value.otherMsgUrlArray=shipForm.value.otherMsgUrlArray
            shipQueryParams.value.fgsAnswerFileArray=shipForm.value.fgsAnswerFileArray
            shipQueryParams.value.aiCompanyId=shipForm.value.aiCompanyId
            initialSendNum() //点开修改页面时初始化预计发货总量

            insertOrEdit.value = true;  //修改时，年份和收货单位组件是不可用的
            detailLoading.value = false;
            open.value = true;
            title.value = "修改自采信息";
        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const prodShipIds = row.prodShipId || ids.value;
    console.info('prodShipIds->',prodShipIds);

    proxy.$modal.confirm('是否确认删除所选数据？').then(function () {
        return delProdShipInfo(prodShipIds);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
    });
}

function handleBatchDelete(){
  const prodShipIds = ids.value;
  console.info('prodShipIds->',prodShipIds);
  proxy.$modal.confirm('是否确认删除所选数据？').then(function () {
    return delProdShipInfos(prodShipIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {

  });
}


//查看流程按钮
function viewFlow(row){
    form.value=row
    flowDetailTag.value = false
    nextTick(() => {
        flowDetailTag.value=true;
    });
    processVisible.value=true
}

/** 导出按钮操作 */
function handleExport() {
    postForExcel('/bdh-agric-invest-api/invest/buy/exportExcel', queryParams.value,'自采')
}

/**excel 导入**/
function handleExcelImport(){

  //valid
  importForm.value.orgCode=null;
  importForm.value.aiCompanyId=null;
  importForm.value.fertilizeSeason=null;
  importForm.value.year=null;

  upload.open=true;
  orgFlag.value = false;
  nextTick(() => {
    orgFlag.value = true;
  });
  title.value="批量导入"
}

function handleImportTemplate(){
  proxy.$refs["prodImportFormRef"].validate(valid => {
    if(valid){
      postForExcel('/bdh-agric-invest-api/invest/buy/exportTemplate', importForm.value,'自采导入模板-请勿修改已有数据项')
    }
  })
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].clearFiles();
  proxy.$alert(
      "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
      response.msg +
      "</div>",
      "导入结果",
      { dangerouslyUseHTMLString: true }
  );
  getList();
};
/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}



getDict();
</script>

