<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form
          :model="queryParams"
          ref="queryForm"
          class="form-line"
          v-show="showSearch"
          label-width="80px"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="所在单位" prop="orgCode">
                <org-select
                  ref="queryOrgRef"
                  @handleOrgCode="handleOrgCode"
                  :defaultOrgCode="queryParams.orgCode"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="仓库名称" prop="warehouseName">
                <el-input
                  v-model="queryParams.warehouseName"
                  placeholder="请输入仓库名称"
                  clearable
                  @keyup.enter.native="handleQuery"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6" :push="6">
              <el-button icon="Refresh"  @click="resetQuery"
              >重置</el-button
              >
              <el-button
                  type="primary"
                  icon="Search"
                  @click="handleQuery"
              >搜索</el-button
              >
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['farmWarehouse:insert']"
          >新增</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
            plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
            v-hasPermi="['farmWarehouse:logicDeleteByIds']"
          >删除</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['farmWarehouse:exportExcel']"
        >导出</el-button
        >
      </el-col>

      <right-toolbar
          v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="farmWarehouseList"
      border
      :height="tableHeight"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="所在单位" align="center" prop="orgName" />
      <el-table-column label="仓库名称" align="center" prop="warehouseName" />
      <el-table-column label="负责人" align="center" prop="warehouseManager" />
      <el-table-column label="联系方式" align="center" prop="contactPhone" />
      <el-table-column
        label="仓储能力(件)"
        align="center"
      >
<!--        <template v-slot="{ row }">-->
<!--          {{ row.warehouseCapacityUsed !== null && row.warehouseCapacityUsed !== '' ? row.warehouseCapacityUsed : 0 }} /-->
<!--          {{ row.warehouseCapacity !== null && row.warehouseCapacity !== '' ? row.warehouseCapacity : 0 }}-->
<!--        </template>-->
        <template v-slot="{ row }">
          {{ row.warehouseCapacity !== null && row.warehouseCapacity !== '' ? row.warehouseCapacity : 0 }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200px"
      >
        <template #default="scope">
          <el-button
              type="primary"
              icon="Edit"
              link
              @click="handleUpdate(scope.row)"
              v-hasPermi="['farmWarehouse:update']"
          >修改</el-button
          >
          <el-button
              type="danger"
              icon="Delete"
              link
              @click="handleDelete(scope.row)"
              v-hasPermi="['farmWarehouse:logicDeleteById']"
          >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.rows"
      @pagination="getList"
    />

    <!-- 添加或修改农场投入品库房对话框 -->
    <el-dialog :title="title" v-model="open" width="764px" append-to-body :close-on-click-modal="false">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="所在单位" prop="orgCode">
              <org-select v-if="orgFlag" :orgCode="form.orgCode" @handleOrgCode="handleOrgCodeForm" :defaultOrgCode="form.orgCode" ref="formOrgRef"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="仓库名称" prop="warehouseName">
              <el-input
                v-model="form.warehouseName"
                placeholder="请输入仓库名称"
                maxlength="30"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="负责人" prop="warehouseManager">
              <el-input
                v-model="form.warehouseManager"
                placeholder="请输入负责人"
                maxlength="30"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="联系方式" prop="contactPhone">
              <el-input v-model="form.contactPhone" placeholder="请输入联系方式" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="仓储能力(件)" prop="warehouseCapacity">
              <el-input
                  v-model="form.warehouseCapacity"
                  placeholder="请输入仓储能力(件)"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer" >
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>

    </el-dialog>
  </div>
</template>

<script name="/invest/farmWarehouse/queryByPage" setup>
import {
  listFarmWarehouse,
  getFarmWarehouse,
  delFarmWarehouse,
  addFarmWarehouse,
  updateFarmWarehouse,
  delFarmWarehouses,
} from "@/api/bdh-agric-invest/invest/farmWarehouse";
import { ref } from "vue";
import {postForExcel} from "@/api/bdh-agric-invest/invest/excel";
import {getDicts, queryAllOrgTree} from "@/api/bdh-agric-invest/system/dict/data";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
const { proxy } = getCurrentInstance();

const farmWarehouseList = ref([]);
const open = ref(false);
const showSearch = ref(true);
const loading=ref(false)
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const queryOrgRef = ref();
const formOrgRef=ref();
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const defaultOrgCode = ref("");//默认初始所在单位
const orgFlag=ref(false)


const data = reactive({
  form: {},
  queryParams: {
    rows: 10,
    page: 1,
    orgCode: null,
    warehouseName: null,
  },
  rules: {
    orgCode: [{required: true, message: "请选择所在单位", trigger: "change"}],
    warehouseName: [{required: true, message: "请输入仓库名称", trigger: "blur"}],
    warehouseManager: [{required: true, message: "请输入负责人", trigger: "blur"}],
    contactPhone: [
        {required: true, message: "请输入联系方式", trigger: "blur"},
        {
        validator: (rule, value,callback) => {
          var conReg = /^((1[3|4|5|6|7|8|9][0-9]\d{8})|((\d{3,4}-)\d{7,8}))$/;
          if (value === null || value === '' || typeof value === 'undefined') {
            callback(new Error('请输入联系方式'));
          } else if (!conReg.test(value)) {
            callback(new Error('请输入正确的联系方式 手机号/座机号(区号-号码)'));
          } else {
            callback();
          }
        },
        trigger: "blur"
        }
    ],
    // warehouseCapacity: [
    //     {required: true, message: "请输入仓储能力(件)", trigger: "blur"},
    //     {
    //     validator: (rule, value, callback) =>{
    //       if (value == 0) {
    //         callback(new Error('仓储能力不能为零'));
    //         return;
    //       }
    //       if (!/^\d+$/.test(value)) {
    //         callback(new Error('请输入整数'));
    //       } else if (value.length > 9) {
    //         callback(new Error('请输入9位以内的整数'));
    //       } else {
    //         callback();
    //     }
    //     }, trigger: 'blur'
    //   }
    // ],
  },
});
const { queryParams, form, rules } = toRefs(data);

onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
      ? window.innerHeight - searchHeight.value - 220
      : window.innerHeight - 220;
});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
      ? window.innerHeight - searchHeight.value - 220
      : window.innerHeight - 220;
});

/** 查询农场投入品库房列表 */
function getList() {
  loading.value = true;
  listFarmWarehouse(queryParams.value).then((response) => {
    farmWarehouseList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}


// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    warehouseName: null,
    warehouseCapacity: null,
    // warehouseCapacityUsed: null,
    warehouseManager: null,
    contactPhone: null,
    orgCode: null,
    orgName:null,
  };
  proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  queryOrgRef.value.clear();
  queryParams.value.orgCode = defaultOrgCode.value;
  queryOrgRef.value.setValue(defaultOrgCode.value, "");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.warehouseId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  if (proxy.$refs["formOrgRef"]) {
    proxy.$refs["formOrgRef"].clear();
  }
  orgFlag.value = false;
  nextTick(() => {
    orgFlag.value = true;
  });
  open.value = true;
  title.value = "新增农场投入品库房";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  if (proxy.$refs["formOrgRef"]) {
    proxy.$refs["formOrgRef"].clear();
  }
  orgFlag.value = false;
  nextTick(() => {
    orgFlag.value = true;
  });
  const warehouseId = row.warehouseId ;
  getFarmWarehouse(warehouseId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改农场投入品库房";
  });

}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      if(form.value.warehouseCapacity){
        const warehouseCapacityVal=form.value.warehouseCapacity
        const isValidWarehouseCapacity = /^\d{1,9}$/.test(warehouseCapacityVal);
        if (!isValidWarehouseCapacity) {
          proxy.$modal.msgError("仓储能力请输入9位以内的非负整数");
          return;
        }
      }
      if (form.value.warehouseId != null) {
        updateFarmWarehouse(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addFarmWarehouse(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const warehouseIds = row.warehouseId || ids.value;
  proxy
    .$confirm("是否确认删除此数据?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(function () {
      return Array.isArray(warehouseIds)
        ? delFarmWarehouses(warehouseIds)
        : delFarmWarehouse(warehouseIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    });
}

// 导出操作按钮
function handleExport() {
  postForExcel('/bdh-agric-invest-api/invest/farmWarehouse/exportExcel', queryParams.value, '农场投入品库房');
}

const handleOrgCode = (orgCode) => {
  queryParams.value.orgCode = orgCode.orgCode;
};
const handleOrgCodeForm = ( orgCodeInfo ) => {
  form.value.orgCode = orgCodeInfo.orgCode;
  form.value.orgName = orgCodeInfo.orgName;
};

const getDict = () => {
    queryAllOrgTree("1").then((res) => {
      var orgList = res.data;
      if (orgList && orgList.length && orgList.length > 0) {
        defaultOrgCode.value = orgList[0].orgCode;
        queryParams.value.orgCode = defaultOrgCode.value;
        getList()
      }
    })
}
getDict()


</script>
<style scoped>
</style>
