<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form :model="queryParams" class="form-line" ref="queryRef" v-show="showSearch" label-width="70px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="企业名称" prop="companyName">
                <el-input v-model="queryParams.companyName" placeholder="请输入企业名称" clearable
                  @keyup.enter="handleQuery" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业简称" prop="companyAbbr">
                <el-input v-model="queryParams.companyAbbr" placeholder="请输入企业简称" clearable
                  @keyup.enter="handleQuery" />
              </el-form-item>
            </el-col>

            <el-col :span="12" align="right">
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="Plus" @click="handleAdd" v-hasPermi="['companyInfo:add']">新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="Edit" :disabled="single" @click="handleUpdate" v-hasPermi="['companyInfo:edit']">修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['companyInfo:del']">删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['companyInfo:del']">删除
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border :height="tableHeight" :data="companyInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="企业名称" align="left" prop="companyName" />
      <el-table-column label="企业简称" align="left" prop="companyAbbr" />
      <el-table-column label="企业专区大图" align="left" prop="companyZoneAddr">
        <template #default="scope">
          <el-image
            :src="scope.row.companyZoneAddr ? scope.row.companyZoneAddr.split(',')[0] : scope.row.companyZoneAddr"
            style="height: 40px"></el-image>
        </template>
      </el-table-column>
      <el-table-column label="企业法人" align="left" prop="legalPerson" />
      <el-table-column label="业务负责人" align="left" prop="contractPerson" />
      <el-table-column label="业务负责人联系方式" align="left" prop="contractPhone" width="200px" />
      <el-table-column label="是否显示" align="left" prop="isDisplay">
        <template #default="scope">
          <el-switch v-model="scope.row.isDisplay" :active-value="1" :inactive-value="0"
            @click="handleDisplayChg(scope.row)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="排序" align="left" prop="orderNo" />
      <el-table-column label="操作" align="left" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button type="primary" link size="small"  @click="handleUpdate(scope.row)"
            v-hasPermi="['companyInfo:edit']">修改
          </el-button>
          <el-button type="primary" link size="small"  @click="handleDelete(scope.row)"
            v-hasPermi="['companyInfo:del']">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.rows"
      @pagination="getList" />

    <!-- 添加或修改企业信息表对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" :close-on-click-modal="false" append-to-body>
      <el-form ref="companyInfoRef" :model="form" :rules="rules" label-width="120px" label-position="top">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="企业名称" prop="companyName">
              <el-input v-model="form.companyName" placeholder="请输入企业名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="企业简称" prop="companyAbbr">
              <el-input v-model="form.companyAbbr" placeholder="请输入企业简称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="英文简称" prop="companyEng">
              <el-input v-model="form.companyEng" placeholder="请输入英文简写" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="企业法人" prop="legalPerson">
              <el-input v-model="form.legalPerson" placeholder="请输入企业法人" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系人" prop="contractPerson">
              <el-input v-model="form.contractPerson" placeholder="请输入联系人" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系人电话" prop="contractPhone">
              <el-input v-model="form.contractPhone" placeholder="请输入联系人电话" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="归属机构" prop="orgCode">
              <org-select ref="orgRef" v-if="orgFlag" :defaultOrgCode="form.orgCode" @handleOrgCode="handleOrgCode" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="是否显示" prop="isDisplay">
              <el-switch v-model="form.isDisplay" :active-value="1" :inactive-value="0"></el-switch>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="排序" prop="orderNo">
              <el-input v-model="form.orderNo" placeholder="请输入排序" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="企业LOGO" prop="companyLogoAddr">
              <imageUpload urlApi="bdh-agric-invest-api"  :limit="1" v-model="form.companyLogoAddr" @fileUploadChange="fileUploadCompanyLogoAddr"></imageUpload>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="企业简介" prop="remark">
              <el-input type="textarea" maxlength="400" v-model="form.remark" :rows="6" placeholder="企业简介" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="企业营业执照" prop="companyLicenseName">
              <imageUpload urlApi="bdh-agric-invest-api"  :limit="1" v-model="form.companyLicenseAddr" @fileUploadChange="fileUploadCompanyLicenseAddr"></imageUpload>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="企业专区大图" prop="companyZoneAddr">
              <imageUpload urlApi="bdh-agric-invest-api"  :limit="4" v-model="form.companyZoneAddr" @fileUploadChange="fileUploadCompanyZoneAddr"></imageUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="/invest/companyInfo/queryByPage">
import {
  listCompanyInfo,
  getCompanyInfo,
  delCompanyInfo,
  addCompanyInfo,
  updateCompanyInfo
} from "@/api/bdh-agric-invest/invest/companyInfo";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import { nextTick } from "vue";

const { proxy } = getCurrentInstance();

const companyInfoList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const orgFlag = ref(true)
const data = reactive({
  form: {},
  queryParams: {
    rows: 10,
    page: 1,
    companyName: null,
    companyAbbr: null,
    legalPerson: null,
    isDisplay: null,
    orderNo: null,
    statusCd: null,
    contractPerson: null,
    contractPhone: null,
    companyLogoName: null,
    companyLogoAddr: null,
    companyLicenseName: null,
    companyLicenseAddr: null,
    companyZoneName: null,
    companyZoneAddr: null
  },
  rules: {
    companyName: [{ required: true, message: "请输入企业名称", trigger: "blur" },
    { max: 30, message: "最多30个字符", trigger: "blur" }],
    companyAbbr: [{ required: true, message: "请输入企业简称", trigger: "blur" },
    { max: 30, message: "最多15个字符", trigger: "blur" }],
    companyLogoAddr: [{ required: true, message: "请上传企业LOGO", trigger: "blur" }],
    orderNo: [{ required: true, message: "请输入排序", trigger: "blur" },
    {
      pattern: true, validator: (rule, value, callback) => {
        var reg = /^([0-9]{1}|^[1-9]{1}\d{1,15})?$/
        if (value === null || value === '' || typeof (value) == 'undefined') {
          callback(new Error('请输入排序'))
        } else if (reg.test(value) && value.toString().split('.')[0].length > 5) {
          callback(new Error('数字过大，请输入0-99999之间的数字'))
        } else if (reg.test(value)) {
          callback()
        } else {
          callback(new Error('请输入数字'))
        }
      }, trigger: "change"
    }
    ],
    companyEng: [{ required: true, message: "请输入英文简写", trigger: "blur" },
    { max: 4, message: "最多4个字符", trigger: "blur" },
    { pattern: /^[a-zA-Z]*$/, message: "请输入英文", trigger: "blur" }],
    contractPhone: [{
      pattern: true, validator: (rule, value, callback) => {
        let regPone = null
        let mobile = /^1(3|4|5|6|7|8|9)\d{9}$/ //手机正则
        let tel = /^(0[0-9]{2,3}\-)([2-9][0-9]{4,7})+(\-[0-9]{1,4})?$/ //座机正则
        if (value === null || value === '' || typeof (value) == 'undefined') {
          callback()
        } else if (mobile.test(value) || tel.test(value)) {
          callback()
        } else {
          callback(new Error('请输入正确的联系电话'))
        }
      }, trigger: "change"
    }],
    orgCode: [{ required: true, message: "请选择归属", trigger: "blur" },
    {
      pattern: true, validator: (rule, value, callback) => {
        let orgPatter = /^860[1-9].*$/
        if (value === null || value === '' || typeof (value) == 'undefined') {
          callback(new Error('请选择归属'))
        } else if (value == '86') {
          callback(new Error('不能选择集团'))
        } else if (orgPatter.test(value)) {
          callback(new Error('不能选择分公司，农场，管理区，作业站'))
        } else {
          callback()
        }
      }, trigger: "change"
    }
    ]
  }
});

const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const { queryParams, form, rules } = toRefs(data);

onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 220
    : window.innerHeight - 220;
});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 220
    : window.innerHeight - 220;
});

function handleOrgCode(obj) {
  console.info('obj:', obj);
  form.value.orgCode = obj.orgCode
  form.value.orgName = obj.orgName
}

function handleDisplayChg(row) {
  let text = row.isDisplay == 1 ? "显示" : "不显示";
  proxy.$modal.confirm('确认要"' + text + '"该企业吗?').then(function () {
    let companyInfo = { aiCompanyId: row.aiCompanyId, isDisplay: row.isDisplay };
    return updateCompanyInfo(companyInfo)
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(function () {
    row.isDisplay = row.isDisplay === 0 ? 1 : 0;
  });
}

/** 查询企业信息表列表 */
function getList() {
  loading.value = true;
  listCompanyInfo(queryParams.value).then(response => {
    companyInfoList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    aiCompanyId: null,
    companyName: null,
    companyAbbr: null,
    legalPerson: null,
    isDisplay: 1,
    orderNo: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    statusCd: null,
    contractPerson: null,
    contractPhone: null,
    companyLogoName: null,
    companyLogoAddr: null,
    companyLicenseName: null,
    companyLicenseAddr: null,
    companyZoneName: null,
    companyZoneAddr: null,
    companyEng: null
  };
  proxy.resetForm("companyInfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  // queryParams.value.rows = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.aiCompanyId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  orgFlag.value = false
  nextTick(() => {
    orgFlag.value = true;
  });
  open.value = true;
  title.value = "添加企业信息表";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const aiCompanyId = row.aiCompanyId || ids.value
  getCompanyInfo(aiCompanyId).then(response => {
    form.value = response.data;
    console.info('form.value:', form.value);
    orgFlag.value = false
    nextTick(() => {
      orgFlag.value = true;
    });
    open.value = true;
    title.value = "修改企业信息表";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["companyInfoRef"].validate(valid => {
    console.log(777,form.value)
    if (valid) {
      if (form.value.aiCompanyId != null) {
        updateCompanyInfo(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addCompanyInfo(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const aiCompanyIds = row.aiCompanyId || ids.value;
  proxy.$modal.confirm('确认删除选择的数据项？').then(function () {
    return delCompanyInfo(aiCompanyIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('invest/companyInfo/export', {
    ...queryParams.value
  }, `companyInfo_${new Date().getTime()}.xlsx`)
}
// 企业LOGO
let fileUploadCompanyLogoAddr = (value) => {
  if (Array.isArray(value) && value.length > 0) {
    const urls = value.map(item => item.url || item).join(',');
    form.value.companyLogoAddr = urls;
    proxy.$refs.companyInfoRef.clearValidate('companyLogoAddr');
  } else {
    form.value.companyLogoAddr = '';
  }
}
// 企业营业执照


let fileUploadCompanyLicenseAddr = (value) => {
  if (Array.isArray(value) && value.length > 0) {
    const urls = value.map(item => item.url || item).join(',');
    form.value.companyLicenseAddr = urls;
    proxy.$refs.companyInfoRef.clearValidate('companyLicenseAddr');
  } else {
    form.value.companyLicenseAddr = '';
  }
}
// 企业专区大图
let fileUploadCompanyZoneAddr = (value) => {
  if (Array.isArray(value) && value.length > 0) {
    const urls = value.map(item => item.url || item).join(',');
    form.value.companyZoneAddr = urls;
    proxy.$refs.companyInfoRef.clearValidate('companyZoneAddr');
  } else {
    form.value.companyZoneAddr = '';
  }
}



getList();
</script>
<style lang="scss" scoped>
</style>
