<template>
  <div class="app-container">
    <el-container>
      <el-aside width="280px">
        <el-scrollbar :style="{ maxHeight: tableHeight + 160 + 'px' }">
          <el-tree
              :data="tree"
              node-key="aiTypeName"
              :props="defaultProps"
              :default-expanded-keys="['全部']"
              @node-click="handleNodeClick"
          />
        </el-scrollbar>
      </el-aside>

      <el-main>
        <div ref="searchDom">
          <el-collapse-transition>
            <el-form
                v-show="showSearch"
                ref="queryRef"
                :inline="true"
                :model="queryParams"
                label-width="70px"
            >
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item label="企业简称"  prop="companyAbbr">
                    <el-input
                        v-model="queryParams.companyAbbr"
                        clearable
                        placeholder="企业名称/关键词"
                        :disabled="forbid"
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="分类名称"  prop="aiTypeName">
                    <el-input
                        v-model="queryParams.aiTypeName"
                        clearable
                        placeholder="分类名称/关键词"
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="是否显示"  prop="showFlag">
                    <el-select
                        v-model="queryParams.showFlag"
                        clearable
                        placeholder="请选择是否显示"
                    >
                      <el-option
                          v-for="dict in ifdisplay"
                          :key="dict.code"
                          :label="dict.name"
                          :value="dict.code"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6" align="right">
                    <el-button @click="resetQuery">重置</el-button>
                    <el-button type="primary" @click="handleQuery">搜索 </el-button>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-transition>
        </div>
        <el-divider style="margin: 20px 0 16px 0" />
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
              <el-button
                  type="primary"
                  icon="Plus"
                  @click="handleAdd"
                  v-hasPermi="['produceType:insert']"
              >新增
              </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                icon="Edit"
                @click="handleUpdate"
                :disabled="single"
                v-hasPermi="['produceType:update']"
            >修改
            </el-button>
          </el-col>
          <el-col :span="1.5">
              <el-button
                  icon="Delete"
                  :disabled="multiple"
                  @click="handleDelete"
                  v-hasPermi="['produceType:logicDeleteByIds']"
              >删除
              </el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
        <el-table  border

            :height="tableHeight"
            :data="produceTypeList"
            @selection-change="handleSelectionChange"
        >
          <el-table-column align="center" type="selection" width="55" />
          <el-table-column align="center" label="企业简称" prop="companyAbbr" />
          <el-table-column align="center" label="分类名称" prop="aiTypeName" />
          <el-table-column align="center" label="首字母" prop="aiTypeInitial" />
          <el-table-column align="center" label="是否显示" prop="showFlag">
            <template #default="scope">
              <el-switch
                  v-model="scope.row.showFlag"
                  :active-value="1"
                  :inactive-value="0"
                  @click="handleDisplayChg(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column align="center" label="排序" prop="aiTypeOrder">
          </el-table-column>
          <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              label="操作"
          >
            <template #default="scope">
              <el-button
                  type="primary"
                  link
                  size="small"
                  v-hasPermi="['produceType:update']"
                  @click="handleUpdateTable(scope.row)"
              >修改
              </el-button>
              <el-button
                  type="primary"
                  link
                  size="small"
                  v-hasPermi="['produceType:logicDeleteById']"
                  @click="handleDelete(scope.row)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:limit="queryParams.rows"
            v-model:page="queryParams.page"
            :total="total"
            @pagination="getList"
        />
        <!-- 添加或修改商品分类管理对话框 -->
        <el-dialog  v-model="open" :title="title" append-to-body width="700px" :close-on-click-modal="false">
          <el-form
              ref="produceTypeRef"
              :model="form"
              :rules="rules"
              label-width="80px"
              label-position="top"
          >
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="企业简称" prop="aiCompanyId">
                  <el-select
                      v-model="form.aiCompanyId"
                      clearable
                      :disabled="forbid"
                      placeholder="请选择企业简称"
                  >
                    <el-option
                        v-for="dict in companyAbbrOption"
                        :key="dict.aiCompanyId"
                        :label="dict.companyAbbr"
                        :value="dict.aiCompanyId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="所属分类" prop="parAiTypeId">
                  <el-cascader
                      ref="cascaderArr"
                      v-model="form.parAiTypeId"
                      :props="produceTypeProps"
                      :options="sortOption"
                      :show-all-levels="false"
                      @change="handleChange"
                      :disabled="control"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="分类名称" prop="aiTypeName">
                  <el-input
                      @input="aiTypeNameChange"
                      style="width: 215px"
                      v-model="form.aiTypeName"
                      placeholder="请输入分类名称"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="是否显示" prop="showFlag">
                  <el-select
                      v-model="form.showFlag"
                      placeholder="请选择是否显示"
                  >
                    <el-option
                        v-for="dict in ifdisplay"
                        :key="dict.code"
                        :label="dict.name"
                        :value="dict.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="首字母" prop="aiTypeInitial">
                  <el-input
                      style="width: 215px"
                      v-model="form.aiTypeInitial"
                      disabled
                      placeholder="首字母"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="排序" prop="aiTypeOrder">
                  <el-input
                      maxlength="20"
                      show-word-limit
                      style="width: 215px"
                      v-model="form.aiTypeOrder"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="分类Logo" prop="aiTypePicUrl" :limit="1">
                  <imageUpload
                      urlApi="bdh-agric-invest-api"
                      :limit="1"
                      v-model="form.aiTypePicUrl"
                      @fileUploadChange="fileUploadAnnexUrl"
                  ></imageUpload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitDebounce">提交</el-button>
              <el-button @click="cancel">取消</el-button>
            </div>
          </template>
        </el-dialog>
      </el-main>
    </el-container>
  </div>
</template>

<script setup name="/invest/produceType/queryByPage">
import {
  queryProduceSmallTree,
  addProduceType,
  delProduceType,
  delProduceTypes,
  getProduceType,
  listProduceType,
  updateProduceType,
  queryProduceTypeTree,
  queryByProduceType,
  getCompanyAbbr,
} from "@/api/bdh-agric-invest/invest/produceType";
import { pinyin } from "pinyin-pro";
import { getDicts } from "@/api/bdh-agric-invest/system/dict/data";
import { allCompanyInfo } from "@/api/bdh-agric-invest/invest/companyInfo";
import { getConsigneeInfo } from "@/api/bdh-agric-invest/invest/consigneeInfo";
import { getCompanyAbbrList } from "@/api/bdh-agric-invest/invest/bulletinConfInfoCompany";
import { ref } from "vue";
import { delConsigneeInfos } from "@/api/bdh-agric-invest/invest/consigneeInfo";
import { debounce } from "lodash";

const { proxy } = getCurrentInstance();
const props1 = {
  checkStrictly: true,
};
const link = ref([]);
link.value = document.getElementById("mylink");
link.onblur = function () {};
const control = ref(true);
const firstLevel = ref([]);
const rowCompanyAbbr = ref([]);
const value = ref([]);
const produceTypeList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const ifdisplay = ref([]);
const yearOption = ref([]);
const tree = ref([]);
const forbid = ref(false);
const name = ref("");
const sortOption = ref([]);
const companyAbbrOption = ref([]);
const submitDebounce = debounce(submitForm,500);
const companyName = ref([]);
const compId = ref("");
const keyNum = ref(0);
const produceTypeProps = {
  value: "aiTypeId",
  label: "aiTypeName",
  level: "aiTypeLevel",
  checkStrictly: true,
};
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const loginCompanyAbbr = ref({});

const data = reactive({
  form: {},
  defaultProps: {
    label: "aiTypeName",
  },
  sortParams: {
    aiTypeId: 0,
    aiTypeLvl: 0,
  },

  queryParams: {
    aiTypeName: null,
    companyAbbr: null,
    showFlag: null,
    rows: 10,
    page: 1,
  },

  rules: {
    aiCompanyId: [
      { required: true, message: "企业简称不能为空", trigger: "blur" },
    ],
    // parAiTypeId: [
    //     {required: true, message: "所属分类不能为空", trigger: "blur"},
    // ],
    aiTypeName: [
      { required: true, message: "产品分类名称不能为空", trigger: "change" },
      { max: 30, message: "最多20个字符", trigger: "blur" },
    ],
    showFlag: [
      { required: true, message: "是否显示不能为空", trigger: "change" },
    ],
    aiTypeOrder: [
      { required: true, message: "是否显示不能为空", trigger: "change" },
      { pattern: true,validator:( rule, value, callback) => {
          var reg = /^([0-9]{1}|^[1-9]{1}\d{1,15})?$/
          if(value === null || value === '' || typeof(value) == 'undefined') {
            callback(new Error('请输入排序'))
          } else if(reg.test(value) && value.toString().split('.')[0].length > 5) {
            callback(new Error('数字过大，请输入0-99999之间的数字'))
          } else if(reg.test(value)) {
            callback()
          } else {
            callback(new Error('请输入数字'))
          }
        },trigger: "change"}
    ],
    /*aiTypePicUrl: [
        {required: true, message: "分类Logo不能为空", trigger: "change"},
        { max: 2000, message: "最多2000个字符", trigger: "blur" },
    ],*/
  },
});

let { sortParams, queryParams, form, rules, defaultProps } = toRefs(data);
onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 320
        : window.innerHeight - 320;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 320
        : window.innerHeight - 320;
});

/**显示隐藏该条分类信息 */
function handleDisplayChg(row) {
  console.log("显示隐藏该条分类信息", row);
  let text = row.showFlag == 1 ? "显示" : "不显示";
  proxy.$modal
      .confirm('确认要"' + text + '"该条信息吗?')
      .then(function () {
        let sortInfo = {
          aiTypeId: row.aiTypeId,
          showFlag: row.showFlag,
        };
        return updateProduceType(sortInfo);
      })
      .then(() => {
        proxy.$modal.msgSuccess(text + "成功");
      })
      .catch(function () {
        row.showFlag = row.showFlag === 0 ? 1 : 0;
        // row.showFlag = row.showFlag === "0" ? "1" : "0";
      });
}

/** 查询商品分类管理列表 */
function getList() {
  loading.value = true;
  // queryParams.value.showFlag = queryParams.value.showFlag === '-1' ? '-1' : queryParams.value.showFlag
  listProduceType(queryParams.value).then((response) => {
    produceTypeList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
    // firstLevel.value = produceTypeList.value.map((item) => item.aiTypeLvl == 1 ? item.aiTypeId:null)
    // firstLevel.value = firstLevel.value.filter(item => item!=null)
    console.log("商品分类管理列表", response.data);
  });
}

/** 获取商品分类 */
function getAiTypeSort() {
  queryProduceTypeTree().then((response) => {
    console.log("全部", response.data);
    tree.value = [
      {
        aiTypeName: "全部",
        children: response.data,
      },
    ];
    if(tree.value[0].aiTypeName=="全部"){
      tree.value[0].aiTypeId = 0;
      tree.value[0].aiTypeLvl = 0;

    }
    console.log(33445566, tree.value);
  });

}

/** 获取所属分类级联信息  */
function sortUpType() {
  queryProduceSmallTree().then((response) => {
    sortOption.value = response.data;
    console.log(44, sortOption.value);
  });
}

/** 获取企业简称 */
function getCompanyAb() {
  getCompanyAbbr().then((response) => {
    console.log(222, response.data);
    queryParams.value.companyAbbr = response.data.companyAbbr;
    queryParams.value.aiCompanyId = response.data.aiCompanyId;
    form.value.companyAbbr = response.data.companyAbbr;
    form.value.aiCompanyId = response.data.aiCompanyId;
    compId.value = response.data.aiCompanyId;
    loginCompanyAbbr.value = response.data;
    if (queryParams.value.companyAbbr != null) {
      forbid.value = true;
    }
    companyName.value = response.data.companyAbbr;
    console.log(232, companyName.value);
  });
}

/** 查询商品分类管理详细 */
function getCompSortInfo(data) {
  getProduceType(1).then((response) => {
    response.data.aiTypeName = "水稻";
    console.log("商品分类管理详情", response.data.aiTypeName);
    console.log("商品分类管理详情", response.data);
    return response.data.aiTypeName;
  });
}

/** 获取分类节点信息 */
function handleChange() {
  const checkedNode = proxy.$refs["cascaderArr"].getCheckedNodes();
  // console.log(12, checkedNode[0].data); //获取当前点击节点的值,
  // console.log(34,checkedNode[0].pathLabels); //获取由label组成的数组
  form.value.parAiTypeId = checkedNode[0].data.aiTypeId;
  form.value.aiTypeLvl = checkedNode[0].data.aiTypeLvl + 1;
}

/** 按分类分页查询 */
const handleNodeClick = (data) => {
  console.log('data',data)
  // if (data.aiTypeName != "全部") {
  sortParams.value.aiTypeId = data.aiTypeId;
  sortParams.value.aiTypeLvl = data.aiTypeLvl;
  // }
  sortParams.value.rows = queryParams.value.rows;
  sortParams.value.page = queryParams.value.page;
  queryByProduceType(sortParams.value).then((response) => {
    produceTypeList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
};

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    // companyAbbr: null,
    aiTypeId: null,
    aiCompanyId: null,
    aiTypeCode: null,
    aiTypeName: null,
    parAiTypeId: null,
    aiTypeLvl: null,
    aiTypePicName: null,
    aiTypeInitial: null,
    showFlag: null,
    aiTypeOrder: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    statusCd: null,
    aiTypePicUrl: null,
  };
  proxy.resetForm("produceTypeRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  // queryParams.value.rows = 1;
  getList();
}

/** 是否显示 */
function getDisplay() {
  getDicts("pub_if").then((response) => {
    // response.data.unshift({code: -1, name: "全部"});
    const arr = response.data;
    arr.forEach((obj) => {
      obj.code = Number.parseInt(obj.code);
    });
    ifdisplay.value = arr;
    console.log(33333333333333, ifdisplay.value);
    // queryParams.value.showFlag = response.data.name;
  });
  // getDicts("year_cd").then((response) => {
  //     yearOption.value = response.data;
  // });
}

/** 重置按钮操作 */
function resetQuery() {
  console.log(33, companyName.value);
  proxy.resetForm("queryRef");
  queryParams.value.companyAbbr = companyName.value;
  console.log(11, queryParams.value.companyAbbr);
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.aiTypeId);
  rowCompanyAbbr.value = selection.map((item) => item.companyAbbr);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  control.value = false;
  // const name = companyName.value;
  reset();
  getAiTypeSort();
  sortUpType();
  // form.value.companyAbbr = name;
  // console.log('name',typeof(name),name)
  if(forbid.value == true){
    form.value.aiCompanyId = Number(compId.value);
    console.log('compId.value',typeof (compId.value),compId.value)
  }

  open.value = true;
  title.value = "商品分类信息";

}

/** 获取企业简称下拉框 */
function getCompanyAbb() {
  queryParams.value.showFlag = -1;
  listProduceType(queryParams.value).then((response) => {
    produceTypeList.value = response.data.records;
    produceTypeList.value.forEach((each) => {
      companyAbbrOption.value.push({
        code: each.aiCompanyId,
        name: each.companyAbbr,
      });
    });
    let map = new Map();
    for (let item of companyAbbrOption.value) {
      if (!map.has(item.code)) {
        map.set(item.code, item);
      }
    }
    companyAbbrOption.value = [...map.values()];
  });
}

console.log(323, companyAbbrOption.value);
/**修改操作按钮*/
function handleUpdate(row) {
  control.value = true;
  reset();
  const aiTypeId = row.aiTypeId || ids.value;
  getProduceType(aiTypeId).then((response) => {
    form.value = response.data;
    console.log(3434343, response.data);
    console.log('rowCompanyAbbr.value',rowCompanyAbbr.value[0])
    form.value.companyAbbr = rowCompanyAbbr.value[0];
    // form.value.showFlag = response.data.showFlag.toString();
    form.value.showFlag = response.data.showFlag;
    if (form.value.parAiTypeId != "0")
      form.value.parAiTypeId = Number(response.data.parAiTypeId);
    open.value = true;
    title.value = "修改商品分类管理";
  });
}

/** 行内修改键 */
function handleUpdateTable(row) {
  control.value = true;
  reset();
  const aiTypeId = row.aiTypeId || ids.value;
  getProduceType(aiTypeId).then((response) => {
    form.value = response.data;
    form.value.companyAbbr = row.companyAbbr;
    if (form.value.parAiTypeId != "0")
      form.value.parAiTypeId = Number(response.data.parAiTypeId);
    console.log("row", row);
    open.value = true;
    title.value = "修改商品分类管理";
  });
}

// function test(){
//     queryProduceSmallTree().then((response) => {
//       console.log(1, response.data);
//       let id = response.data.filter(
//         (item) => item.aiTypeId == form.value.parAiTypeId
//       );
//       console.log(2, form.value.parAiTypeId);
//       child.value = id[0].children.map((item) => item.aiTypeName);
//       // console.log(3, child.value);
//       inde = child.value.indexOf(form.value.aiTypeName);
//       console.log("index2", inde);
//     });
//     console.log("index", inde);
//     if (inde != -1) {
//       proxy.$modal.msgError("分类名重复");
//       // return false;
//     }
// }

/** 提交按钮 */
function submitForm() {
  // keyNum.value++;
  proxy.$refs["produceTypeRef"].validate((valid) => {
    if (valid) {
      if (form.value.aiTypeId != null) {
        updateProduceType(form.value).then((response) => {
          console.log("成功", form.value);
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
          getAiTypeSort();
        });
      } else {
        addProduceType(form.value).then((response) => {
          if (form.value.aiTypeLvl <= 3) {
            console.log("新增成功", form.value);
            proxy.$modal.msgSuccess("新增成功");
          } else {
            proxy.$modal.msgError("分类级数超出限制，最多支持三级");
          }
          open.value = false;
          getList();
          getAiTypeSort();
        });
      }
    }
  });
}

//获取首字母
function aiTypeNameChange() {
  const sortName = form.value.aiTypeName;
  const first = sortName.charAt(0);
  const firstLogo = pinyin(first, {
    pattern: "first",
    toneType: "none",
  });
  form.value.aiTypeInitial = firstLogo.toUpperCase();
  // form.value.aiTypeInitial =
}

/** 删除按钮操作 */
function handleDelete(row) {
  const aiTypeIds = row.aiTypeId || ids.value;
  proxy.$modal
      .confirm("确认删除选择的数据项？")
      .then(function () {
        if (ids.value.length > 1) {
          return delProduceTypes(aiTypeIds);
        } else {
          return delProduceType(aiTypeIds);
        }
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
      .catch(() => {});
}

// /** 删除按钮操作 */
// function handleDelete1(row) {
//     const aiTypeIds = row.aiTypeId || ids.value;
//     if (aiTypeIds == row.aiTypeId) {
//         proxy.$modal
//             .confirm('是否确认删除商品分类管理编号为"' + aiTypeIds + '"的数据项？')
//             .then(function () {
//                 return delProduceType(aiTypeIds);
//             })
//             .then(() => {
//                 getList();
//                 proxy.$modal.msgSuccess("删除成功");
//             })
//             .catch(() => {});
//     } else {
//         proxy.$modal
//             .confirm('是否确认删除商品分类管理编号为"' + aiTypeIds + '"的数据项？')
//             .then(function () {
//                 return delProduceTypes(aiTypeIds);
//             })
//             .then(() => {
//                 getList();
//                 proxy.$modal.msgSuccess("删除成功");
//             })
//             .catch(() => {});
//     }
// }

/** 导出按钮操作 */
function handleExport() {
  proxy.download(
      "invest/produceType/export",
      {
        ...queryParams.value,
      },
      `produceType_${new Date().getTime()}.xlsx`
  );
}
let fileUploadAnnexUrl = (value) => {
  if (Array.isArray(value) && value.length > 0) {
    const urls = value.map(item => item.url || item).join(',');
    form.value.aiTypePicUrl = urls;
    proxy.$refs.produceTypeRef.clearValidate('aiTypePicUrl');
  } else {
    form.value.aiTypePicUrl = '';
  }
}

/** 获取企业列表 */
function getCompanyAbbrListDict() {
  getCompanyAbbrList().then((response) => {
    getCompanyAbbr().then((res) => {
      let obj = res.data;
      let arr = response.data;
      console.log("企业列", response.data);
      if (obj.aiCompanyId) {
        companyAbbrOption.value = arr.filter(
            (item) => item.aiCompanyId == obj.aiCompanyId,
            (item) => item.companyAbbr == obj.companyAbbr
        );
      } else {
        companyAbbrOption.value = response.data;
      }
      console.log("companyAbbrOption.value", companyAbbrOption.value);
    });
  });
}

console.log(3, companyAbbrOption.value);
sortUpType();
getDisplay();
getAiTypeSort();
getList();
getCompanyAb();
// getCompanyAbb();
getCompSortInfo();
getCompanyAbbrListDict();
</script>
<style scoped lang="scss">
@import "@/views/bdh-agric-invest/assets/styles/index.scss";
:deep(.el-aside){
  padding: 12px;
}
:deep(.el-tree){
  padding: 10px;
}
</style>
