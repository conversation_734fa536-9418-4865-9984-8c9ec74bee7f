<template>
  <div class="app-container">
    <el-container>
      <el-aside width="200px">
        <el-scrollbar>
          <el-form :model="formCompany">
            <el-form-item style="width: 152px" prop="companyId">
              <el-select
                v-model="formCompany.companyId"
                placeholder="请选择企业简称"
                @change="getAiTypeSort"
              >
                <el-option
                  v-for="dict in companyAbbrOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-form>
          <el-tree
            :data="tree"
            :props="defaultProps"
            accordion
            @node-click="handleNodeClick"
          />
        </el-scrollbar>
      </el-aside>

      <el-main>
        <div ref="searchDom">
          <el-collapse-transition>
            <el-form
              v-show="showSearch"
              ref="queryRef"
              :model="queryParams"
              label-width="80px"
              class="form-line"
            >
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item label="所在单位" prop="orgCode">
                    <org-select
                      ref="queryOrgRef"
                      @handleOrgCode="handleOrgCode"
                      :defaultOrgCode="queryParams.orgCode"
                      clearable
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="商品名称" prop="aiTypeSubName">
                    <el-input
                      v-model="queryParams.aiTypeSubName"
                      clearable
                      placeholder="请输入商品名称"
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="商品规格" prop="aiTypeSubSpecs">
                    <el-input
                      v-model="queryParams.aiTypeSubSpecs"
                      clearable
                      placeholder="请输入商品规格"
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="是否启用" prop="useFlag">
                    <el-select
                      v-model="queryParams.useFlag"
                      placeholder="请选择是否启用"
                      clearable
                    >
                      <el-option
                        v-for="dict in useFlagOptions"
                        :key="dict.code"
                        :label="dict.name"
                        :value="dict.code"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-transition>
        </div>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="primary" icon="Plus" @click="handleAdd"
                       v-hasPermi="['produceProcessInfo:insert']"
              >新增</el-button
            >
          </el-col>

          <el-col :span="1.5">
            <el-button
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['produceProcessInfo:logicDeleteByIds']"
              >删除</el-button
            >
          </el-col>

          <el-col :span="1.5">
            <el-button
              plain
              type="success"
              :disabled="multiple"
              @click="handleOpen"
              v-hasPermi="['produceProcessInfo:batchOnOff']"
              >批量启用</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              plain
              type="danger"
              :disabled="multiple"
              @click="handleClose"
              v-hasPermi="['produceProcessInfo:batchOnOff']"
              >批量禁用</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button plain icon="Download" @click="handleExport"
                       v-hasPermi="['produceProcessInfo:exportExcel']"
              >导出</el-button
            >
          </el-col>

          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          border
          :data="produceProcessInfoList"
          @selection-change="handleSelectionChange"
          :height="tableHeight"
          ref="tableRef"
        >
          <el-table-column align="center" type="selection" width="55" />
          <el-table-column
            align="center"
            label="商品编号"
            prop="aiTypeSubCode"
          />
          <el-table-column align="center" label="所在单位" prop="orgName" />
          <el-table-column align="center" label="企业简称" prop="companyAbbr" />
          <el-table-column align="center" label="分类名称" prop="aiTypeName" />
          <el-table-column
            align="center"
            label="加工商品名称"
            prop="aiTypeSubName"
          />
          <el-table-column align="center" label="规格" prop="aiTypeSubSpecs" />
          <el-table-column align="center" label="重量" prop="aiTypeSubWeight">
            <template #default="scope">
              {{ scope.row.aiTypeSubWeight }}{{ scope.row.productWeightUnit }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="排序" prop="aiTypeSubOrder" />
          <el-table-column align="center" label="是否启用" prop="useFlag">
            <template #default="scope">
              <el-switch
                v-model="scope.row.useFlag"
                :active-value="1"
                :inactive-value="0"
                @click="openEnable(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            class-name="small-padding fixed-width"
            label="操作"
            width="150px"
          >
            <template #default="scope">
              <el-button
                icon="Edit"
                link
                size="small"
                type="primary"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['produceProcessInfo:update']"
                >编辑
              </el-button>
              <el-button
                icon="Delete"
                link
                size="small"
                type="primary"
                @click="handleDelete(scope.row)"
                v-hasPermi="['produceProcessInfo:logicDeleteById']"
                >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          v-model:limit="queryParams.rows"
          v-model:page="queryParams.page"
          :total="total"
          @pagination="getList"
        />

        <!-- 添加或修改加工商品信息对话框 -->
        <el-dialog
          :title="title"
          v-model="open"
          width="900px"
          append-to-body
          :close-on-click-modal="false"
        >
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="80px"
            label-position="top"
          >
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="所在单位" prop="orgCode">
                  <org-select
                    @handleOrgCode="handleOrgCodeForm"
                    :defaultOrgCode="form.orgCode"
                    :orgCode="form.orgCode"
                    v-if="orgFlag"
                    ref="formOrgRef"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="企业简称" prop="aiCompanyId">
                  <el-select
                    v-model="form.aiCompanyId"
                    @change="getFormAiTypeSort"
                    placeholder="请选择企业简称"
                  >
                    <el-option
                      v-for="dict in companyAbbrOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="加工商品分类" prop="aiTypeId">
                  <el-cascader
                    ref="aiTypeNameRef"
                    v-model="form.aiTypeId"
                    :props="produceTypeProps"
                    :options="sortOption"
                    :show-all-levels="false"
                    @change="handleParAiTypeChange"
                    placeholder="请选择所属分类"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="商品名称" prop="aiTypeSubName">
                  <el-input
                    v-model="form.aiTypeSubName"
                    placeholder="请输入商品名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="规格" prop="aiTypeSubSpecs">
                  <el-input
                    v-model="form.aiTypeSubSpecs"
                    placeholder="请输入规格"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="商品重量" prop="aiTypeSubWeight">
                    <el-input style="width: 100%;"
                      v-model="form.aiTypeSubWeight"
                      placeholder="请输入商品重量"
                      class="input-with-select"
                      clearable>
                      <template #append>

                          <el-select
                              v-model="form.productWeightUnit"
                              style="width: 100px"
                              placeholder="单位">
                            <el-option
                                v-for="dict in specUnitOptions"
                                :key="dict.srcUnit"
                                :label="dict.srcUnit"
                                :value="dict.srcUnit"
                            />
                          </el-select>
                      </template>
                    </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="是否启用" prop="useFlag">
                  <el-select
                    v-model="form.useFlag"
                    clearable
                    placeholder="请选择是否启用"
                  >
                    <el-option
                      v-for="dict in useFlagOptions"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="排序" prop="aiTypeSubOrder">
                  <el-input
                    v-model="form.aiTypeSubOrder"
                    placeholder="请输入排序"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="8">
                <el-form-item label="加工费标准" prop="processCostUnit">
                  <el-input
                    v-model="form.processCostUnit"
                    placeholder="请输入加工费标准"
                    style="
                      width: 220px !important;
                      margin-right: 10px !important;
                    "
                  />
                  <span>元/{{ form.productWeightUnit }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>

          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" icon="Plus" @click="handleAddProduct"
                >增加商品</el-button
              >
            </el-col></el-row
          >

          <el-row style="margin-bottom: 10px" justify="space-between">
            <el-col :span="12">
              <span style="color: red">注意：比例值必须合计值必须为100%</span>
            </el-col>
            <el-col :span="12" style="text-align: right">
              <span style="color: #409EFF; font-weight: bold; font-size: 14px">
                总价：{{ totalPrice.toFixed(2) }} 元
              </span>
            </el-col>
          </el-row>

          <el-table border :data="addProduceList" ref="addProduceTableRef">
   
            <el-table-column
              align="center"
              label="商品名称"
              prop="aiTypeSubName"
            />
            <el-table-column
              align="center"
              label="规格"
              prop="aiTypeComSpecs"
            />
            <el-table-column align="center" label="重量" prop="aiTypeComWeight">
              <template #default="scope">
                {{ scope.row.aiTypeComWeight }}
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="比例"
              prop="aiTypeComPercent"
              width="140px"
            >
              <template #default="scope">
                <el-input
                  v-model="scope.row.aiTypeComPercent"
                  placeholder="请输入"
                  @blur="() => validateTotalRatio(scope.row)"
                  style="width: 80px !important; margin-right: 5px !important"
                >
                </el-input
                ><span>%</span>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="数量"
              prop="num"
            >
              <template #default="scope">
                <el-input
                  v-model="scope.row.num"
                  placeholder="请输入"
                  @blur="() => validateNum(scope.row)"
                  style="width: 80px !important; margin-right: 5px !important"
                >
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="单价(元/kg)"
              prop="aiGuidingPrice"
            >
              <template #default="scope">
                <el-input
                  v-model="scope.row.aiGuidingPrice"
                  placeholder="请输入"
                  @blur="() => validatePrice(scope.row)"
                  style="width: 100px !important; margin-right: 5px !important"
                  required
                >
                </el-input>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              label="操作"
              width="150px"
            >
              <template #default="scope">
                <el-button
                  icon="Delete"
                  link
                  size="small"
                  type="danger"
                  @click="handleDeleteProduct(scope.row)"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <template #footer>
            <div class="dialog-footer">
              <el-button
                type="primary"
                @click="submitForm"
                :disabled="!dataIsValid"
                >确 定</el-button
              >
              <el-button @click="cancel">取 消</el-button>
            </div>
          </template>
        </el-dialog>

        <!--        增加商品对话框-->
        <el-dialog
          title="增加商品"
          v-model="openProduct"
          width="900px"
          append-to-body
          :close-on-click-modal="false"
        >
          <el-form
            ref="productRef"
            :model="productForm"
            :rules="productRules"
            label-width="80px"
            label-position="top"
          >
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="企业简称" prop="aiCompanyId">
                  <el-select
                    v-model="productForm.aiCompanyId"
                    disabled
                    placeholder="请选择企业简称"
                  >
                    <el-option
                      v-for="dict in companyAbbrOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <!--              <el-col :span="8">-->
              <!--                <el-form-item label="商品分类" prop="aiTypeId">-->
              <!--                  <el-cascader-->
              <!--                    ref="parAiTypeRef"-->
              <!--                    v-model="productForm.aiTypeId"-->
              <!--                    :props="produceTypeProps"-->
              <!--                    :options="sortOption"-->
              <!--                    :show-all-levels="false"-->
              <!--                    @change="handleParAiTypeChangeProduct"-->
              <!--                    placeholder="请选择商品分类"-->
              <!--                  />-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
              <el-col :span="8">
                <el-form-item label="商品名称" prop="aiTypeSubName">
                  <el-input
                    v-model="productForm.aiTypeSubName"
                    placeholder="请输入商品名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="24" style="text-align: right">
                <el-button icon="Refresh" @click="resetProductQuery"
                  >重置</el-button
                >
                <el-button
                  icon="Search"
                  type="primary"
                  @click="handleProductQuery"
                  >搜索</el-button
                >
              </el-col>
            </el-row>
          </el-form>

          <el-table
            border
            :data="produceList"
            ref="produceTableRef"
            @selection-change="handleSelectionChoosen"
            style="margin-top: 10px"
          >
            <el-table-column
              align="center"
              type="selection"
              width="55"
              :selectable="isSelect"
            />
            <!--            <el-table-column-->
            <!--              align="center"-->
            <!--              label="分类名称"-->
            <!--              prop="aiTypeName"-->
            <!--            />-->
            <el-table-column
              align="center"
              label="商品名称"
              prop="aiTypeSubName"
            />
            <el-table-column
              align="center"
              label="规格"
              prop="aiTypeSubSpecs"
            />
            <el-table-column align="center" label="重量" prop="aiTypeSubWeight">
              <template #default="scope">
                {{ scope.row.aiTypeSubWeight }}
              </template>
            </el-table-column>
          </el-table>
          <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitProduct">确 定</el-button>
              <el-button @click="cancelProduct">取 消</el-button>
            </div>
          </template>
        </el-dialog>
      </el-main>
    </el-container>
  </div>
</template>

<script name="/invest/produceProcessInfo/queryByPage" setup>
import {
  queryAllCompanyInfo,
  listProduceProcessInfo,
  getProduceProcessInfo,
  delProduceProcessInfo,
  addProduceProcessInfo,
  updateProduceProcessInfo,
  delProduceProcessInfos,
  getProduceProcessTypeTreeByCompanyId,
  getAlreadyArrivalWaresList,
  batchOnOff,
} from "@/api/bdh-agric-invest/invest/produceProcessInfo";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import { onMounted, reactive, ref, toRefs, computed } from "vue";
import { specUnitList } from "@/api/bdh-agric-invest/invest/unittrans";
import { queryAllOrgTree } from "@/api/bdh-agric-invest/system/dict/data";
import { postForExcel } from "@/api/bdh-agric-invest/invest/excel";
import { ifShowProduceProcessType } from "@/api/bdh-agric-invest/invest/produceProcessType";
const { proxy } = getCurrentInstance();
//弹窗
const open = ref(false); //弹窗是否显示
const openProduct = ref(false); //增加商品弹窗是否显示
const formRef = ref();

const showSearch = ref(true); //搜索是否显示
const ids = ref([]); //多选选中的id
const names = ref([]); //多选选中的aiTypeSubName
const cascaderArr = ref(); //商品分类
const multiple = ref(true); //非多选禁用
const total = ref(0); //列表条数
const title = ref(""); //弹窗标题
const viewOnly=ref(false);

const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const orgFlag=ref(false)

const produceTypeProps = {
  //商品分类
  value: "aiTypeId",
  label: "aiTypeName",
  level: "aiTypeLevel",
  checkStrictly: true,
};
const orgList = ref([]);

//列表
const produceProcessInfoList = ref([]); //加工商品库列表
const addProduceList = ref([]); //新增页的加工商品列表
const produceList = ref([]); //增加商品弹出页中的查询列表

const produceTableRef = ref(); //增加商品弹出页中的tableRef
const productId = ref([]); //增加商品弹出页中选中的id
const selectedProducts = ref([]); //增加商品弹出页中选中的商品信息

const dataIsValid = ref(true); //比例input值的校验

// 单位转换函数：将指定单位的重量转换为千克
const convertToKg = (weight, unit) => {
  if (!unit || !weight) return 0;
  
  // 首先尝试从系统的单位选项中查找转换比例
  const unitOption = specUnitOptions.value.find(option => option.srcUnit === unit);
  if (unitOption && unitOption.convertRatio) {
    return weight * parseFloat(unitOption.convertRatio);
  }
  
  // 如果系统中没有找到，使用默认的转换比例
  const conversionRates = {
    'kg': 1,      // 千克
    'g': 0.001,   // 克
    't': 1000,    // 吨
    '斤': 0.5,    // 斤
    '两': 0.05,   // 两
    '公斤': 1,    // 公斤
    '克': 0.001,  // 克
    '吨': 1000,   // 吨
  };
  
  const rate = conversionRates[unit] || 1; // 如果找不到对应单位，默认为1
  return weight * rate;
};

// 计算总价
const totalPrice = computed(() => {
  return addProduceList.value.reduce((total, item) => {
    // 规格转换为对应Kg的数量 × 单价(元/kg) × 数量
    const weight = parseFloat(item.aiTypeComWeight) || 0; // 重量
    const unit = item.productWeightUnit; // 单位
    const weightInKg = convertToKg(weight, unit); // 转换为千克
    const price = parseFloat(item.aiGuidingPrice) || 0; // 单价(元/kg)
    const quantity = parseFloat(item.num) || 0; // 数量
    
    return total + (weightInKg * price * quantity);
  }, 0);
});

//组织机构
const queryOrgRef = ref(); //列表页组织机构ref
const defaultOrgCode = ref(""); //默认初始所在单位code
const defaultOrgName = ref(""); //默认初始所在单位name

//商品分类树
const tree = ref([]); //商品分类树的数据
const sortOption = ref([]); //所属分类字典
const specUnitOptions = ref([]);

//各种下拉选字典
const companyAbbrOption = ref([]); //企业信息下拉选
const useFlagOptions = ref([
  {
    code: 1,
    name: "是",
  },
  {
    code: 0,
    name: "否",
  },
]); //是否启用字典
const data = reactive({
  //企业信息表单
  formCompany: {
    companyId: null,
  },
  //商品分类树的props
  defaultProps: {
    value: "aiTypeId",
    label: "aiTypeName",
  },
  //加工商品分类树与列表查询相关联后的参数
  sortParams: {
    page: 1,
    rows: 10,
    aiTypeId: null,
    aiCompanyId: null,
    orgCode: null,
    aiTypeSubName: null,
    aiTypeSubSpecs: null,
    useFlag: null,
  },
  //列表查询参数
  queryParams: {
    rows: 10,
    page: 1,
    orgCode: null,
    aiTypeSubName: null,
    aiTypeSubSpecs: null,
    useFlag: null,
    aiTypeId: null,
    aiCompanyId: null,
  },
  //新增表单参数
  form: {},
  //增加商品表单参数
  productForm: {},
  //新增表单校验
  rules: {
    orgCode: [
      { required: true, message: "所在单位不能为空", trigger: "change" },
    ],
    aiCompanyId: [
      { required: true, message: "企业简称不能为空", trigger: "change" },
    ],
    aiTypeId: [
      { required: true, message: "加工商品分类不能为空", trigger: "change" },
    ],
    aiTypeSubName: [
      { required: true, message: "商品名称不能为空", trigger: "blur" },
    ],
    aiTypeSubSpecs: [
      { required: true, message: "规格不能为空", trigger: "blur" },
    ],
    aiTypeSubWeight: [
      { required: true, message: "商品重量不能为空", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (!/^(?:[1-9]\d{0,}(\.\d{1,2})?|0(\.\d{1,2})?|0)?$/.test(value)) {
            callback(new Error("请输入数字，允许最多两位小数"));
          } else {
            callback();
          }
        },
        trigger: "blur",
      },
    ],
    // processCostUnit: [
    //   {
    //     validator: (rule, value, callback) => {
    //       if (!/^(?:[1-9]\d{0,}(\.\d{1,2})?|0(\.\d{1,2})?|0)?$/.test(value)) {
    //         callback(new Error("请输入数字，允许最多两位小数"));
    //       } else {
    //         callback();
    //       }
    //     },
    //     trigger: "blur",
    //   },
    // ],
    useFlag: [
      { required: true, message: "是否启用不能为空", trigger: "change" },
    ],
  },
  //增加商品表单校验
  productRules: {},
});

const {
  queryParams,
  form,
  rules,
  defaultProps,
  sortParams,
  formCompany,
  aiTypeSubName,
  productForm,
  productRules,
} = toRefs(data);

onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 250
    : window.innerHeight - 250;
});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 250
    : window.innerHeight - 250;
});

/** 查询加工商品库列表 */

//查询获取列表
function getList() {
  queryParams.value.aiCompanyId = formCompany.value.companyId;
  queryParams.value.aiTypeId = 0;
  listProduceProcessInfo(queryParams.value).then((response) => {
    produceProcessInfoList.value = response.data.records;
    total.value = response.data.total;
  });
}

//点击分类树节点获取列表
const handleNodeClick = (data) => {
  sortParams.value.aiTypeId = data.aiTypeId;
  sortParams.value.aiCompanyId = formCompany.value.companyId;
  sortParams.value.rows = queryParams.value.rows;
  sortParams.value.page = queryParams.value.page;
  sortParams.value.orgCode = queryParams.value.orgCode;
  listProduceProcessInfo(sortParams.value).then((response) => {
    produceProcessInfoList.value = response.data.records;
    total.value = response.data.total;
  });
};

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

function cancelProduct() {
  openProduct.value = false;
}

// 表单重置
function reset() {
  form.value = {
    productWeightUnit: null,
    aiCompanyId: null,
    orgCode: null,
    orgName: null,
    aiTypeId: null,
    aiTypeSubName: null,
    aiTypeSubSpecs: null,
    aiTypeSubWeight: null,
    processCostUnit: null,
    aiTypeSubOrder: null,
    useFlag: null,
    companyAbbr: null,
    companyName: null,
    aiTypeName: null,
  };
  proxy.resetForm("formRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryOrgRef.value.clear();
  queryParams.value.orgCode = defaultOrgCode.value;
  queryOrgRef.value.setValue(defaultOrgCode.value, "");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.aiTypeSubId);
  multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  addProduceList.value = [];
  form.value.aiCompanyId = formCompany.value.companyId;
  // form.value.orgCode = queryParams.value.orgCode;
  // form.value.orgName = queryParams.value.orgName;
  getFormAiTypeSort();
  specUnitOpt();
  orgFlag.value = false;
  nextTick(() => {
    orgFlag.value = true;
  });
  title.value = "新增加工商品信息";
  open.value = true;
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const aiTypeSubId = row.aiTypeSubId;
  orgFlag.value = false;
  nextTick(() => {
    orgFlag.value = true;
  });
  getProduceProcessInfo(aiTypeSubId).then((response) => {
    form.value = response.data;
    addProduceList.value = response.data.produceProcessCompList;
    getFormAiTypeSort();
    open.value = true;
    title.value = "修改加工商品信息";
  });
}
/** 提交按钮 */

function findInTree(tree, aiTypeId) {
  for (let item of tree) {
    // 检查当前项的 aiTypeId 是否匹配
    if (item.aiTypeId === aiTypeId) {
      return item; // 如果找到匹配项，返回该项
    }

    // 如果当前项有 children，递归地在子项中查找
    if (item.children) {
      const foundInChildren = findInTree(item.children, aiTypeId);
      if (foundInChildren) {
        return foundInChildren; // 如果在子项中找到匹配项，返回该项
      }
    }
  }

  return null; // 如果没有找到匹配项，返回 null
}

function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      // 非空校验
      if (addProduceList.value.length === 0) {
        proxy.$modal.msgError("请添加至少一条商品信息");
        return;
      }
      const emptyInput = addProduceList.value.some(
        (row) => !row.aiTypeComPercent
      );
      if (emptyInput) {
        proxy.$modal.msgError("请输入比例");
        return;
      }
      
      // 验证数量
      for (const row of addProduceList.value) {
        if (!row.num) {
          proxy.$modal.msgError("请输入数量");
          return;
        }
        const num = Number(row.num);
        if (isNaN(num) || num <= 0 || num > 999999 || !/^\d{1,6}(\.\d{1,2})?$/.test(row.num)) {
          proxy.$modal.msgError("数量格式不正确，范围必须在0-999999之间，最多2位小数");
          return;
        }
      }
      
      // 验证单价
      for (const row of addProduceList.value) {
        if (!row.aiGuidingPrice) {
          proxy.$modal.msgError("请输入单价");
          return;
        }
        const price = Number(row.aiGuidingPrice);
        if (isNaN(price) || price < 0.01 || price > 99999 || !/^\d{1,5}(\.\d{1,2})?$/.test(row.aiGuidingPrice)) {
          proxy.$modal.msgError("单价格式不正确，范围必须在0.01-99999之间，最多2位小数");
          return;
        }
      }
      //排序只能为数字
      const orderVal=form.value.aiTypeSubOrder
      if (isNaN(orderVal)) {
        proxy.$modal.msgError("排序请输入数字");
        return
      }
      //加工费校验
      const processCostUnitVal=form.value.processCostUnit
      if (!/^(?:[1-9]\d{0,}(\.\d{1,2})?|0(\.\d{1,2})?|0)?$/.test(processCostUnitVal)){
        proxy.$modal.msgError("加工费标准请输入数字,最多两位小数");
        return
      }
      if (form.value.aiTypeSubId != null) {
        form.value.produceProcessCompList = addProduceList.value;
        updateProduceProcessInfo(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        // 从 companyAbbrOption 中查找选中的 aiCompanyId 对应的 companyAbbr
        const selectedCompany = companyAbbrOption.value.find(
          (item) => item.code == form.value.aiCompanyId
        );
        // 如果找到了对应的公司简称，则将其添加到 form 中
        if (selectedCompany) {
          form.value.companyAbbr = selectedCompany.name;
          form.value.companyName = selectedCompany.wholeName;
        }
        const selectedSort = findInTree(sortOption.value, form.value.aiTypeId);
        if (selectedSort) {
          form.value.aiTypeName = selectedSort.aiTypeName;
        }
        form.value.produceProcessCompList = addProduceList.value;
        addProduceProcessInfo(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  const aiTypeSubIds = row.aiTypeSubId || ids.value;
  proxy
    .$confirm("是否确认删除此数据?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(function () {
      return Array.isArray(aiTypeSubIds)
        ? delProduceProcessInfos(aiTypeSubIds)
        : delProduceProcessInfo(aiTypeSubIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    });
}

//增加商品弹出页
function handleAddProduct() {
  productForm.value.aiCompanyId = form.value.aiCompanyId;
  getAlreadyArrivalWaresList(productForm.value).then((response) => {
    produceList.value = response.data;
    openProduct.value = true;
  });
  proxy.$refs.produceTableRef.clearSelection();
}

//新增商品页的查询
function handleProductQuery() {
  productForm.value.aiCompanyId = form.value.aiCompanyId
  getAlreadyArrivalWaresList(productForm.value).then((response) => {
    produceList.value = response.data;
  });
}

//新增商品页的重置
function resetProductQuery() {
  productForm.value.aiTypeSubName = null;
  productForm.value.aiCompanyId = form.value.aiCompanyId
  handleProductQuery();
}

//增加商品弹出页中的列表数据是否可以选择
function isSelect(row) {
  if (addProduceList.value.some((item) => item.aiTypeComId === row.aiTypeId)) {
    return false;
  } else {
    return true;
  }
}

//增加商品里的列表多选
function handleSelectionChoosen(selection) {
  productId.value = selection.map((item) => item.aiTypeId);
  selectedProducts.value = selection.map((item) => ({
    aiTypeComId: item.aiTypeSubId,
    // aiTypeName: item.aiTypeName,
    aiTypeSubName: item.aiTypeSubName,
    aiTypeComSpecs: item.aiTypeSubSpecs,
    aiTypeComWeight: item.aiTypeSubWeight,
    productWeightUnit: item.aiTypeSubUnit,
    num: "",
    aiGuidingPrice: ""
  }));
}

//将增加商品里的列表选中商品放到新增中
function submitProduct() {
  if (!addProduceList.value) {
    addProduceList.value = [];
  }
  selectedProducts.value.forEach((item) => {
    if (!addProduceList.value.some((i) => i.aiTypeComId === item.aiTypeComId)) {
      addProduceList.value.push(item);
    }
  });
  openProduct.value = false;
}

//删除新增中的商品
function handleDeleteProduct(row){
  const index = addProduceList.value.findIndex(
    (item) => item.aiTypeComId === row.aiTypeComId
  );
  if (index !== -1) {
    addProduceList.value.splice(index, 1);
  }
}

//比例-校验
const validateTotalRatio = (row) => {
  const ratioVal = row.aiTypeComPercent;
  const totalRatio = addProduceList.value.reduce(
    (sum, row) => sum + Number(row.aiTypeComPercent),
    0
  );
  if (isNaN(ratioVal)) {
    dataIsValid.value = false;
    proxy.$modal.msgError("请输入数字");
  } else if (
    ratioVal < 0 ||
    ratioVal > 100 ||
    !/^\d{1,3}(\.\d{1,2})?$/.test(ratioVal)
  ) {
    dataIsValid.value = false;
    proxy.$modal.msgError("请输入合法的数字，最多2位小数");
  } else if (totalRatio !== 100) {
    dataIsValid.value = false;
    proxy.$modal.msgError("输入数字之和必须等于100");
  } else {
    dataIsValid.value = true;
  }
};

//数量-校验
const validateNum = (row) => {
  const numVal = row.num;
  if (!numVal) {
    proxy.$modal.msgError("数量不能为空");
    return;
  }
  const num = Number(numVal);
  if (isNaN(num)) {
    proxy.$modal.msgError("数量必须是数字");
    row.num = "";
  } else if (num <= 0 || num > 999999) {
    proxy.$modal.msgError("数量范围必须在0-999999之间");
    row.num = "";
  } else if (!/^\d{1,6}(\.\d{1,2})?$/.test(numVal)) {
    proxy.$modal.msgError("数量最多2位小数");
    row.num = "";
  }
};

//单价-校验
const validatePrice = (row) => {
  const priceVal = row.aiGuidingPrice;
  if (!priceVal) {
    proxy.$modal.msgError("单价不能为空");
    return;
  }
  const price = Number(priceVal);
  if (isNaN(price)) {
    proxy.$modal.msgError("单价必须是数字");
    row.aiGuidingPrice = "";
  } else if (price < 0.01 || price > 99999) {
    proxy.$modal.msgError("单价范围必须在0.01-99999之间");
    row.aiGuidingPrice = "";
  } else if (!/^\d{1,5}(\.\d{1,2})?$/.test(priceVal)) {
    proxy.$modal.msgError("单价最多2位小数");
    row.aiGuidingPrice = "";
  }
};

//获取企业信息
const getCompanyId = () => {
  queryAllCompanyInfo({}).then((response) => {
    response.data.forEach((each) => {
      companyAbbrOption.value.push({
        code: each.aiCompanyId,
        name: each.companyAbbr,
        wholeName: each.companyName,
      });
    });
    formCompany.value.companyId = companyAbbrOption.value[0].code;
    getAiTypeSort();
  });
};

//获取商品分类树
const getAiTypeSort = () => {
  getProduceProcessTypeTreeByCompanyId(formCompany.value.companyId).then(
    (response) => {
      // sortOption.value = response.data;
      tree.value = [
        {
          aiTypeName: "全部",
          aiTypeId: 0,
          aiTypeLevel: 0,
          children: response.data,
        },
      ];
      getList();
    }
  );
};

//表单中获取商品分类
function getFormAiTypeSort() {
  getProduceProcessTypeTreeByCompanyId(form.value.aiCompanyId).then(
    (response) => {
      //级联选择器
      // 创建 sortOption 的副本
      // const sortOptionCopy = JSON.parse(JSON.stringify(response.data));
      // // 限制只保留第一级和第二级的数据
      // sortOptionCopy.forEach((level1) => {
      //   level1.children.forEach((level2) => {
      //     level2.children = null;
      //   });
      // });
      // 将处理过的数据赋值给 sortOption
      sortOption.value = response.data;
    }
  );
}

//获取组织机构
const handleOrgCode = (orgCode) => {
  queryParams.value.orgCode = orgCode.orgCode;
  queryParams.value.orgName = orgCode.orgName;
};
const handleOrgCodeForm = (orgCodeInfo) => {
  form.value.orgCode = orgCodeInfo.orgCode;
  form.value.orgName = orgCodeInfo.orgName;
};

const getDict = () => {
  queryAllOrgTree("1").then((res) => {
    var orgList = res.data;
    if (orgList && orgList.length && orgList.length > 0) {
      defaultOrgCode.value = orgList[0].orgCode;
      defaultOrgName.value = orgList[0].orgName;
      queryParams.value.orgCode = defaultOrgCode.value;
      queryParams.value.orgName = defaultOrgName.value;
    }
  });
};

//获取商品单位
const specUnitOpt = (init) => {
  specUnitList({}).then((response) => {
    specUnitOptions.value = response.data;
    if (specUnitOptions.value && specUnitOptions.value.length > 0) {
      if (!form.value.productWeightUnit) {
        form.value.productWeightUnit = specUnitOptions.value[0].srcUnit;
      }
    }
  });
};

//点击所属分类级联选择器//新增表单
function handleParAiTypeChange() {
  const checkedNode = proxy.$refs["aiTypeNameRef"].getCheckedNodes();
  form.value.aiTypeId = checkedNode[0].data.aiTypeId;
  // form.value.aiTypeLvl = checkedNode[0].data.aiTypeLvl + 1;
}
//点击所属分类级联选择器//新增表单中的新增商品
function handleParAiTypeChangeProduct() {
  const checkedNode = proxy.$refs["parAiTypeRef"].getCheckedNodes();
  form.value.parAiTypeId = checkedNode[0].data.aiTypeId;
  form.value.aiTypeLvl = checkedNode[0].data.aiTypeLvl + 1;
}

// 导出操作按钮
function handleExport() {
  postForExcel(
    "/bdh-agric-invest-api/invest/produceProcessInfo/exportExcel",
    queryParams.value,
    "加工商品库"
  );
}

//启用与禁用
function openEnable(row) {
  let text = row.useFlag == 1 ? "启用" : "禁用";
  proxy.$modal
    .confirm('确认要"' + text + '"该条信息吗?')
    .then(function () {
      let sortInfo = {
        aiTypeSubIdList: [row.aiTypeSubId],
        useFlag: row.useFlag,
      };
      return batchOnOff(sortInfo);
    })
    .then(() => {
      proxy.$modal.msgSuccess(text + "成功");
    })
    .catch(function () {
      row.useFlag = row.useFlag === 0 ? 1 : 0;
    });
}

function handleOpen() {
  proxy.$modal
    .confirm("确认要启用信息吗?")
    .then(function () {
      let sortInfo = {
        aiTypeSubIdList: ids.value,
        useFlag: 1,
      };
      return batchOnOff(sortInfo);
    })
    .then(() => {
      proxy.$modal.msgSuccess("启用成功");
      getList();
    });
}
function handleClose() {
  proxy.$modal
    .confirm("确认要禁用信息吗?")
    .then(function () {
      let sortInfo = {
        aiTypeSubIdList: ids.value,
        useFlag: 0,
      };
      return batchOnOff(sortInfo);
    })
    .then(() => {
      proxy.$modal.msgSuccess("禁用成功");
      getList();
    });
}

getCompanyId(); //获取企业简称
specUnitOpt(); //获取重量单位
getDict(); //获取组织机构字典
</script>
