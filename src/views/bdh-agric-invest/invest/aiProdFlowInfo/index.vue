<!--
@name: 收货人管理
@description:
@author: donglu
@time: 2022-10-06 11:01:59
-->
<template>
  <div class="app-container">
    <!-- 筛选条件及搜索栏 -->
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form
          v-show="showSearch"
          ref="queryRef"
          :model="queryParams"
          label-width="80px"
          class="form-line"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年份" prop="year">
                <el-select
                  v-model="queryParams.year"
                  clearable
                  placeholder="请选择年份"
                  @change="handleQuery"
                >
                  <el-option
                    v-for="dict in yearOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="作业季节" prop="fertilizeSeason">
                <el-select
                  v-model="queryParams.fertilizeSeason"
                  placeholder="请选择作业季节"
                  @change="handleQuery"
                  clearable
                >
                  <el-option
                    v-for="dict in fertilizes"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="厂商" prop="vendorsName">
                <el-input
                  v-model="queryParams.vendorsName"
                  placeholder="请输入厂商"
                  maxlength="50"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="投入品分类" prop="aiTypeId" label-width="100px">
                <ProduceType ref="produceTypeRef" @handleTypeId="handleTypeId"></ProduceType>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="商品名称" prop="aiTypeSubName">
                <el-input
                  v-model="queryParams.aiTypeSubName"
                  placeholder="请输入商品名称"
                  maxlength="100"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="收货单位" prop="organizationNo">
                <org-select
                  :defaultOrgCode="true"
                  ref="queryOrgRef"
                  @handleOrgCode="handleOrgCodeQuery"
                />
              </el-form-item>

              <!-- <el-form-item label="收货农场" prop="orgCode" >
                <org-select
                  ref="queryOrgRef"
                  @handleOrgCode="handleOrgCodeQuery"
                  :level="3"
                  :checkStrictly="false"
                />
              </el-form-item> -->
            </el-col>
            <el-col :span="6">
              <b>
                <el-form-item label="流向日期" prop="sendTime">
                  <el-date-picker
                    v-model="dataRange"
                    type="daterange"
                    value-format="YYYY-MM-DD"
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                  />
                </el-form-item>
              </b>
            </el-col>
            <el-col :span="6" align="right">
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <!-- 新增、修改、删除、导出 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          icon="Plus"
          type="primary"
          @click="handleAdd"
          v-hasPermi="['aiProdFlowInfo#insert']"
          >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          :disabled="single"
          icon="Edit"
          @click="handleUpdate"
          v-hasPermi="['aiProdFlowInfo#update']"
          >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          :disabled="multiple"
          icon="Delete"
          v-hasPermi="['aiProdFlowInfo#logicDeleteById']"
          @click="handleDelete"
          >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['aiProdFlowInfo#postForExcel']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="Download"
          @click="handleExport"
          v-hasPermi="['aiProdFlowInfo#postForExcel']"
          >导出
        </el-button>
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 分页查询表格 -->
    <el-table
      border
      :height="tableHeight"

      :data="consigneeInfoList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        align="center"
        type="selection"
        width="55"
        fixed="left"
      />
      <template v-for="item in columns">
        <el-table-column
          :label="item.label"
          :align="item.align"
          :prop="item.prop"
          :key="item.prop"
          :width="item.width || 'auto'"
          v-if="showParentsColumn(item)"
        >
          <template v-if="item.childs && item.childs.length > 0">
            <template v-for="it in item.childs">
              <el-table-column
                :label="it.label.replace(item.label, '')"
                :align="it.align"
                :prop="it.prop"
                :key="it.prop"
                v-if="it.show"
                :width="it.width || 'auto'"
              />
            </template>
          </template>
        </el-table-column>
      </template>
      <el-table-column
        align="center"
        width="150"
        fixed="right"
        class-name="small-padding fixed-width"
        label="操作"
      >
        <template #default="scope">
          <el-link
            icon="Edit"
            type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['aiProdFlowInfo#update']"
            >修改
          </el-link>
          <el-link
            icon="Delete"
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['aiProdFlowInfo#logicDeleteById']"
            >删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页查询底部选页栏 -->
    <pagination
      v-show="total > 0"
      v-model:limit="queryParams.rows"
      v-model:page="queryParams.page"
      :total="total"
      @pagination="getList"
    />

    <!-- 添加或修改收货人对话框 -->
    <el-dialog v-model="open" :title="title" append-to-body style="width: 800px" :close-on-click-modal="false">
      <el-form
        ref="consigneeInfoRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="年份" prop="year">
              <el-select v-model="form.year" clearable placeholder="请选择年份">
                <el-option
                  v-for="dict in yearOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="作业季节" prop="fertilizeSeason">
              <el-select
                v-model="form.fertilizeSeason"
                placeholder="请选择作业季节"
              >
                <el-option
                  v-for="dict in fertilizes"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="厂商" prop="vendorsName">
              <el-input
                v-model="form.vendorsName"
                maxlength="50"
                placeholder="请输入厂商"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="流向日期" prop="flowDate">
              <el-date-picker
                  ref="picker"
                  @focus="focus"
                  v-model="form.flowDate"
                  :editable="false"
                  type="date"
                  value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
					<el-col :span="8">
            <el-form-item label="收货单位" prop="orgCode" v-if="open">
              <org-select
                ref="queryOrgRefForm"
                :defaultOrgCode="data.defaultOrgCode"
                @handleOrgCode="handleOrgCodeQueryForm"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
                label="投入品分类"
                prop="aiTypeId"
                label-width="100px"
            >
              <el-cascader
                style="width: 100%;"
                  placeholder="请选择投入品分类"
                  v-model="form.aiTypeId"
                  @change="getProductsGroupByPruduceTypeFn"
                  :props="produceTypeProps"
                  :options="sortOption"
                  :show-all-levels="false"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="商品名称" prop="aiTypeSubId">
              <el-cascader
                placeholder="请选择商品名称"
                v-model="form.aiTypeSubId"
                :options="data.pruduceTypes"
                :props="GroupByPruduceTypeProps"
                :show-all-levels="false"
              />
            </el-form-item>
          </el-col>


          <el-col :span="8">
            <el-form-item label="投入品数量" prop="prodNum">
              <el-input-number
              style="width: 100%;"
                v-model="form.prodNum"
                :min="0"
								:precision="2"
                :max="99999"
                :controls="false"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 用户导入对话框 -->
    <el-dialog
      :title="upload.title"
      v-model="upload.open"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text"><em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">

            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link
              type="primary"
              :underline="false"
              style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate"
              >下载模板</el-link
            >
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="/invest/aiProdFlowInfo/queryByPage" setup>
import {
  listAiProdFlowInfo,
  getAiProdFlowInfo,
  delAiProdFlowInfo,
  addAiProdFlowInfo,
  updateAiProdFlowInfo,
  delAiProdFlowInfos,
  getProductsGroupByPruduceType,
} from "@/api/bdh-agric-invest/invest/aiProdFlowInfo";
import { queryProduceTypeTree } from "@/api/bdh-agric-invest/invest/produceType";
import { getOrgTreeApi, queryAllOrgTree } from "@/api/bdh-agric-invest/system/dict/data";
import { getToken } from "@/utils/auth";
import { getDicts } from "@/api/bdh-agric-invest/system/dict/data";
import { postForExcel } from "@/api/bdh-agric-invest/invest/excel";
import { ref } from "vue";

const { proxy } = getCurrentInstance();
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import ProduceType from "@/views/bdh-agric-invest/components/ProduceType/index.vue";
const sortOption = ref([]); //所属分类字典
const consigneeInfoList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const yearOption = ref([]);
const picker = ref();
const queryOrgRef = ref();
const queryOrgRefForm = ref();
const dataRange = ref([]);
const fertilizes = ref([]);
const columns = ref([
  {
    label: "年份",
    align: "center",
    show: true,
    prop: "year",
  },
  {
    label: "作业季节",
    align: "center",
    show: true,
    prop: "fertilizeSeasonName",
  },
  {
    label: "厂商",
    align: "center",
    show: true,
    prop: "vendorsName",
  },
  {
    label: "投入品分类",
    align: "center",
    show: true,
    prop: "aiTypeName",
  },

  {
    label: "商品名称",
    align: "center",
    show: true,
    prop: "aiTypeSubName",
  },
  {
    label: "收货单位",
    align: "center",
    show: true,
    prop: "orgName",
  },
  {
    label: "流向日期",
    align: "center",
    show: true,
    prop: "flowDateParse",
  },
  {
    label: "数量（吨）",
    align: "center",
    show: true,
    prop: "prodNum",
  },
]);

const produceTypeProps = {
  //商品分类
  value: "aiTypeId",
  label: "aiTypeName",
  expandTrigger: "hover",
  emitPath: false,
};
const GroupByPruduceTypeProps = {
  //商品分类
  value: "value",
  label: "lable",
  expandTrigger: "hover",
  emitPath: false,
};
const data = reactive({
  defaultOrgCode: true,
  pruduceTypes: [],
  //企业简称表单
  formCompany: {
    companyId: null,
  },
  form: {
    year: null, //年份
    fertilizeSeason: null, //作业季节
    vendorsName: null, //厂商
    aiTypeId: null, //商品分类
    aiTypeSubId: null, //商品名称
    orgCode: null, //收货农场
    orgName: null,
    prodNum: null, //投入品数量
    flowDate: "",
  },
  queryParams: {
    rows: 10,
    page: 1,
    year: null, //年份
    fertilizeSeason: null, //作业季节
    vendorsName: null, //厂商
    aiTypeId: null, //商品分类
    aiTypeSubName: null, //商品名称
    orgCode: null, //收货农场
    flowDateStart: null, //流向日期开始时间
    flowDateEnd: null, //流向日期结束时间
  },
  rules: {
    year: [{ required: true, message: "请输入年份", trigger: "change" }],
    fertilizeSeason: [
      { required: true, message: "请选择季节", trigger: "change" },
    ],
    aiTypeId: [
      { required: true, message: "请选择商品分类", trigger: "change" },
    ],
    orgCode: [{ required: true, message: "请选择收货农场", trigger: "change" }],
    flowDate: [
      { required: true, message: "请选择流向日期", trigger: "change" },
    ],
    prodNum: [
      { required: true, message: "投入品数量不能为空" },
      { type: "number", message: "数量只能是数字" },
    ],
		aiTypeSubId:[
			{ required: true, message: "请输入商品名称", trigger: "change" }
		]
  },
});
/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { "access-token": getToken() },
  // 上传的地址
  url: window.VITE_APP_BASE_API + "/bdh-agric-invest-api/invest/aiProdFlowInfo/importExcel",
});
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const { queryParams, form, rules, formCompany } = toRefs(data);
onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
function focus() {
  picker.value.handleOpen();
}
function dreduceAiTypeSort(arr) {
  if (arr.length > 0) {
    if (arr[0].children.length == 0) {
      //queryParams.value.aiTypeId = arr[0].aiTypeId;
    } else {
      dreduceAiTypeSort(arr[0].children);
    }
  }
}

function handleTypeId(type){
  queryParams.value.aiTypeId=type.aiTypeId
}

/** 获取商品分类 */
const getAiTypeSort = () => {
  queryProduceTypeTree(formCompany.value.companyId).then((response) => {
    sortOption.value = response.data;
    dreduceAiTypeSort(sortOption.value);
    getList();
  });
};
//控制父级Column是否展示根据子show是否都是true
const showParentsColumn = (item) => {
  if (item.childs && item.childs.length > 0) {
    return item.childs.some((it) => {
      return it.show == true;
    });
  } else {
    return item.show;
  }
};
/** 导入按钮操作 */
function handleImport() {
  upload.title = "导入";
  upload.open = true;
}
/** 下载模板操作 */
function importTemplate() {
  postForExcel(
      "/bdh-agric-invest-api/invest/aiProdFlowInfo/downloadTemplate",
      queryParams.value,
      "投入品流向导入模板"
  );
}
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].clearFiles();
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
      response.msg +
      "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};
/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}

/** 获取字典 */
const getDict = () => {
  // queryAllOrgTree("3").then((res) => {
  //   var arr = res.data;
  //   function dreduceAiType(arr) {
  //     if (arr.length > 0) {
  //       if (arr[0].children === null || arr[0].children.length == 0) {
  //         data.defaultOrgCode = arr[0].orgCode;
  //       } else {
  //         dreduceAiType(arr[0].children);
  //       }
  //     }
  //   }
  //   dreduceAiType(arr);
  // });
  //查询年份
  getDicts("year_cd").then((response) => {
    yearOption.value = response.data.sort((a, b) => {
      return Number(b.code) - Number(a.code);
    });
    getList()
    queryParams.value.year = yearOption.value[0].code
  });

  //查询季节
  getDicts("fertilize_season").then((response) => {
    fertilizes.value = response.data;
    if (fertilizes.value.length > 0) {
      queryParams.value.fertilizeSeason = fertilizes.value[0].code;
    }
    getAiTypeSort();
  });
};

function getProductsGroupByPruduceTypeFn() {
  form.value.aiTypeSubId = null;
  getProductsGroupByPruduceType(form.value.aiTypeId).then((response) => {
    data.pruduceTypes = response;
  });
}
const handleOrgCodeQuery = (orgInfo) => {
  if (!orgInfo.orgCode) queryOrgRef.value.clear();
  queryParams.value.orgCode = orgInfo.orgCode;
};

const handleOrgCodeQueryForm = (orgInfo) => {
  if (!orgInfo.orgCode) queryOrgRefForm.value.clear();
  form.value.orgCode = orgInfo.orgCode;
  form.value.orgName = orgInfo.orgName;
};

/** 查询收货人列表 */
function getList() {
  loading.value = true;
  var obj = Object.assign({}, queryParams.value);
  if (obj.orgCode == null) {
    obj.orgCode = 0;
  }
  listAiProdFlowInfo(obj).then((response) => {
    var data = response.data.records;
    data.forEach((item) => {
      var fertilizeSeason = fertilizes.value.find((it) => {
        return it.code == item.fertilizeSeason;
      });
      if (fertilizeSeason) {
        item.fertilizeSeasonName = fertilizeSeason.name;
      }
      item.flowDateParse = proxy.parseTime(item.flowDate, "{y}-{m}-{d}");
    });
    consigneeInfoList.value = data;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    year: null, //年份
    fertilizeSeason: null, //作业季节
    vendorsName: null, //厂商
    aiTypeId: null, //商品分类
    aiTypeSubId: null, //商品名称
    orgCode: null, //收货农场
    orgName: null,
    prodNum: null, //投入品数量
    flowDate: "",
  };
  proxy.resetForm("consigneeInfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  if (dataRange.value && dataRange.value.length == 2) {
    queryParams.value.flowDateStart = dataRange.value[0];
    queryParams.value.flowDateEnd = dataRange.value[1];
  }

  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  proxy.$refs['produceTypeRef'].clear();
  queryOrgRef.value.clear();
  dataRange.value = [];
  queryParams.value.flowDateStart = null;
  queryParams.value.flowDateEnd = null;
  queryParams.value.fertilizeSeason = "";
  queryParams.value.orgCode = null;
  dreduceAiTypeSort(sortOption.value);
  queryParams.value.year = yearOption.value[0].code;
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.prodFlowId);
  console.log("选中：", ids);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  data.defaultOrgCode = true;
  open.value = true;
  title.value = "添加投入品流向";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const aiConsigneeId = row.prodFlowId || ids.value;
  getAiProdFlowInfo(aiConsigneeId).then((response) => {
    form.value = response.data;
    getProductsGroupByPruduceType(form.value.aiTypeId).then((response) => {
      data.pruduceTypes = response;
    });
    data.defaultOrgCode = form.value.orgCode;
    form.value.flowDate = proxy.parseTime(form.value.flowDate, "{y}-{m}-{d}");
    open.value = true;
    title.value = "修改投入品流向";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["consigneeInfoRef"].validate((valid) => {
    if (valid) {
      var obj = Object.assign({}, form.value);
      if (form.value.prodFlowId != null) {
        updateAiProdFlowInfo(obj).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addAiProdFlowInfo(obj).then((response) => {
          console.info("form.value", form.value);
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const aiConsigneeIds = row.prodFlowId || ids.value;
  console.log("删除：", ids.value);
  proxy.$modal
    .confirm("确认删除选择的数据项？")
    .then(function () {
      if (ids.value.length > 1) {
        return delAiProdFlowInfos(aiConsigneeIds);
      } else {
        return delAiProdFlowInfo(aiConsigneeIds);
      }
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  postForExcel(
    "/bdh-agric-invest-api/invest/aiProdFlowInfo/postForExcel",
    queryParams.value,
    "投入品流向"
  );
}

getDict();
// getList();
</script>
<style scoped>
.el-link {
  margin-right: 8px;
}

.el-link .el-icon--right.el-icon {
  vertical-align: text-bottom;
}
</style>
