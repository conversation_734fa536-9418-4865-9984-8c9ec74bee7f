<template>
  <div class="app-container">
    <el-container>
      <el-aside width="280px">
        <el-scrollbar>
          <el-tree
            :data="produceTypeTree"
            :props="defaultProduceTypeProps"
            node-key="aiTypeName"
            :default-expanded-keys="['全部']"
            @node-click="handleNodeClick"
          />
        </el-scrollbar>
      </el-aside>

      <el-main>
        <div ref="searchDom">
          <el-collapse-transition>
            <el-form
              :model="queryParams"
              ref="queryForm"
              class="form-line"
              v-show="showSearch"
              label-width="80px"
            >
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item label="企业简称" prop="companyAbbr">
                    <el-input
                      v-model="queryParams.companyAbbr"
                      placeholder="企业名称/关键词"
                      clearable
                      @keyup.enter.native="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="分类名称" prop="aiTypeName">
                    <el-input
                      v-model="queryParams.aiTypeName"
                      placeholder="分类名称/关键词"
                      clearable
                      @keyup.enter.native="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="是否显示" prop="showFlag">
                    <el-select
                      v-model="queryParams.showFlag"
                      clearable
                      placeholder="请选择是否显示"
                    >
                      <el-option
                        v-for="dict in ifdisplay"
                        :key="dict.code"
                        :label="dict.name"
                        :value="dict.code"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-button type="primary" icon="Search" @click="handleQuery"
                    >搜索</el-button
                  >
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-transition>
        </div>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" icon="Plus" @click="handleAdd"
                       v-hasPermi="['produceProcessType:insert']"
              >新增</el-button
            >
          </el-col>
          <el-col :span="1.5">
            <el-button
              plain
              icon="Delete"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['produceProcessType:logicDeleteByIds']"
              >删除</el-button
            >
          </el-col>
          <right-toolbar
            v-model:showSearch="showSearch"
            @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table
          :data="produceProcessTypeList"
          :height="tableHeight"
          border
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="企业简称" align="center" prop="companyAbbr" />
          <el-table-column label="分类名称" align="center" prop="aiTypeName" />
          <el-table-column label="首字母" align="center" prop="aiTypeInitial" />
          <el-table-column label="是否显示" align="center" prop="showFlag">
            <template #default="scope">
              <el-switch
                v-model="scope.row.showFlag"
                :active-value=1
                :inactive-value=0
                @click="handleShowFlagChange(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="排序" align="center" prop="aiTypeOrder" />
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button
                size="mini"
                type="primary"
                link
                icon="Edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['produceProcessType:update']"
                >修改</el-button
              >
              <el-button
                size="mini"
                type="danger"
                link
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['produceProcessType:logicDeleteById']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total > 0"
          :total="total"
          v-model:limit="queryParams.rows"
          v-model:page="queryParams.page"
          @pagination="getList"
        />
        <!-- 添加或修改农场商品分类对话框 -->
        <el-dialog
          :title="title"
          v-model="open"
          width="700px"
          append-to-body
          :close-on-click-modal="false"
        >
          <el-form
            ref="formRef"
            :model="form"
            :rules="rules"
            label-width="80px"
            label-position="top"
          >
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="企业简称" prop="aiCompanyId">
                  <el-select
                    v-model="form.aiCompanyId"
                    clearable
                    placeholder="请选择企业简称"
                  >
                    <el-option
                      v-for="dict in companyAbbrOption"
                      :key="dict.aiCompanyId"
                      :label="dict.companyAbbr"
                      :value="dict.aiCompanyId"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所属分类" prop="parAiTypeId">
                  <el-cascader
                      ref="parAiTypeRef"
                      v-model="form.parAiTypeId"
                      :props="produceTypeProps"
                      :options="sortOption"
                      :show-all-levels="false"
                      @change="handleParAiTypeChange"
                      placeholder="请选择所属分类"
                      :disabled="viewOnly"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="分类名称" prop="aiTypeName">
                  <el-input
                    @input="getInitial"
                    v-model="form.aiTypeName"
                    placeholder="请输入分类名称"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="是否显示" prop="showFlag">
                  <el-select
                    v-model="form.showFlag"
                    clearable
                    placeholder="请选择是否显示"
                  >
                    <el-option
                      v-for="dict in ifdisplay"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="首字母" prop="aiTypeInitial">
                  <el-input
                    v-model="form.aiTypeInitial"
                    placeholder="请输入首字母"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="排序" prop="aiTypeOrder">
                  <el-input
                    v-model="form.aiTypeOrder"
                    placeholder="请输入排序"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitForm">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </template>
        </el-dialog>
      </el-main>
    </el-container>
  </div>
</template>

<script setup name="/invest/produceProcessType/queryByPage">
import {
  listProduceProcessType,
  getProduceProcessType,
  delProduceProcessType,
  addProduceProcessType,
  updateProduceProcessType,
  delProduceProcessTypes,
  ifShowProduceProcessType,
  getCompanyAbbr,
  queryProduceTypeTree,
  queryByProduceProcessType,
} from "@/api/bdh-agric-invest/invest/produceProcessType";
import { ref } from "vue";
import { pinyin } from "pinyin-pro";
const { proxy } = getCurrentInstance();
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const produceProcessTypeList = ref([]);

const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const formRef = ref();//表单Ref
const parAiTypeRef=ref();//所属分类Ref

const showFlag = ref(true); //表格中的是否显示
const viewOnly=ref(false);
const ifdisplay = ref([
  {
    code: 1,
    name: "是",
  },
  {
    code: 0,
    name: "否",
  },
]); //显示下拉选

const companyAbbrOption = ref([]); //企业简称下拉选

const produceTypeTree = ref([]); //加工商品分类树
const defaultProduceTypeProps = {
  children: "children",
  label: "aiTypeName",
};//所属加工商品分类树Props

const sortOption=ref([]);//所属加工商品分类下拉选
const produceTypeProps = {
  value: "aiTypeId",
  label: "aiTypeName",
  level: "aiTypeLevel",
  checkStrictly: true,
};//所属加工商品分类级联选择器Props

const data = reactive({
  sortParams: {
    page: 1,
    rows: 10,
    aiTypeId: null,
    aiTypeLvl: null,
  },
  queryParams: {
    page: 1,
    rows: 10,
    aiCompanyId: null,
    aiTypeCode: null,
    aiTypeName: null,
    parAiTypeId: null,
    aiTypeLvl: null,
    aiTypePicName: null,
    aiTypePicUrl: null,
    aiTypeInitial: null,
    showFlag: null,
    aiTypeOrder: null,
    statusCd: null,
    orgCode: null,
    orgName: null,
    warehouseId: null,
  },
  // 表单参数
  form: {},
  // 表单校验
  rules: {
    aiCompanyId: [
      { required: true, message: "企业简称不能为空", trigger: "change" },
    ],
    // parAiTypeId: [
    //   { required: true, message: "所属分类不能为空", trigger: "change" },
    // ],
    aiTypeName: [
      { required: true, message: "分类名称不能为空", trigger: "blur" },
    ],
    showFlag: [
      { required: true, message: "是否显示不能为空", trigger: "change" },
    ],
  },
});
let { sortParams, queryParams, form, rules } = toRefs(data);

onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 250
    : window.innerHeight - 250;
});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 250
    : window.innerHeight - 250;
});

/** 查询农场商品分类列表 */
function getList() {
  loading.value = true;
  listProduceProcessType(queryParams.value).then((response) => {
    produceProcessTypeList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    aiTypeId: null,
    aiCompanyId: null,
    aiTypeCode: null,
    aiTypeName: null,
    parAiTypeId: null,
    aiTypeLvl: null,
    aiTypePicName: null,
    aiTypePicUrl: null,
    aiTypeInitial: null,
    showFlag: null,
    aiTypeOrder: null,
    warehouseId: null,
  };
  proxy.resetForm("formRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.aiTypeId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  viewOnly.value=false;
  open.value = true;
  title.value = "新增加工商品分类信息";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const aiTypeId = row.aiTypeId || ids.value;
  getProduceProcessType(aiTypeId).then((response) => {
    form.value = response.data;
    viewOnly.value=true;
    open.value = true;
    title.value = "修改加工商品分类信息";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      if (form.value.aiTypeId != null) {
        updateProduceProcessType(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
          getProduceTypeTree();
        });
      } else {
        // 从 companyAbbrOption 中查找选中的 aiCompanyId 对应的 companyAbbr
        const selectedCompany = companyAbbrOption.value.find(
          (item) => item.aiCompanyId === form.value.aiCompanyId
        );
        // 如果找到了对应的公司简称，则将其添加到 form 中
        if (selectedCompany) {
          form.value.companyAbbr = selectedCompany.companyAbbr;
          form.value.companyName = selectedCompany.companyName;
        }
        if(form.value.parAiTypeId==null){
          form.value.parAiTypeId = '0'
        }
        addProduceProcessType(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
          getProduceTypeTree();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  const aiTypeIds = row.aiTypeId || ids.value;
  proxy
    .$confirm("是否确认删除此数据?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(function () {
      return Array.isArray(aiTypeIds)
        ? delProduceProcessTypes(aiTypeIds)
        : delProduceProcessType(aiTypeIds);
    })
    .then(() => {
      getList();
      getProduceTypeTree();
      proxy.$modal.msgSuccess("删除成功");
    });
}

//点击表格中是否显示按钮
function handleShowFlagChange(row) {
  let text = row.showFlag == 1 ? "显示" : "不显示";
  proxy.$modal
    .confirm('确认要"' + text + '"该条信息吗?')
    .then(function () {
      let sortInfo = {
        aiTypeId: row.aiTypeId,
        showFlag: row.showFlag,
      };
      return ifShowProduceProcessType(sortInfo);
    })
    .then(() => {
      proxy.$modal.msgSuccess(text + "成功");
    })
    .catch(function () {
      row.showFlag = row.showFlag === 0 ? 1 : 0;
    });
}

//点击分类树节点
const handleNodeClick = (data) => {
  sortParams.value.aiTypeId = data.aiTypeId;
  sortParams.value.aiTypeLvl = data.aiTypeLvl;
  sortParams.value.rows = queryParams.value.rows;
  sortParams.value.page = queryParams.value.page;
  queryByProduceProcessType(sortParams.value).then((response) => {
    produceProcessTypeList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
};
//点击所属分类级联选择器
function handleParAiTypeChange() {
  const checkedNode = proxy.$refs["parAiTypeRef"].getCheckedNodes();
  form.value.parAiTypeId = checkedNode[0].data.aiTypeId;
  form.value.aiTypeLvl = checkedNode[0].data.aiTypeLvl + 1;
}

//获取首字母
function getInitial() {
  const sortName = form.value.aiTypeName;
  const first = sortName.charAt(0);
  const firstLogo = pinyin(first, {
    pattern: "first",
    toneType: "none",
  });
  form.value.aiTypeInitial = firstLogo.toUpperCase();
}

//获取企业简称
function getCompanyAbb() {
  getCompanyAbbr().then((response) => {
    companyAbbrOption.value = response.data;
  });
}

//获取分类树
function getProduceTypeTree() {
  queryProduceTypeTree().then((response) => {
    produceTypeTree.value = [
      {
        aiTypeName: "全部",
        children: response.data,
      },
    ];
    if (produceTypeTree.value[0].aiTypeName == "全部") {
      produceTypeTree.value[0].aiTypeId = 0;
      produceTypeTree.value[0].aiTypeLvl = 0;
    }
    //级联选择器
    // 创建 sortOption 的副本
    const sortOptionCopy = JSON.parse(JSON.stringify(response.data));
    // 限制只保留第一级和第二级的数据
    sortOptionCopy.forEach(level1 => {
      level1.children.forEach(level2 => {
        level2.children = null;
      });
    });
    // 将处理过的数据赋值给 sortOption
    sortOption.value = sortOptionCopy;
  });
}



getList();
getCompanyAbb();
getProduceTypeTree();
</script>

<style scoped></style>
