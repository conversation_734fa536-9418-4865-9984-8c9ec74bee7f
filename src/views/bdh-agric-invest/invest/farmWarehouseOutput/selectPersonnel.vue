<!--
@name: 收货人管理
@description:
@author: manchunyu
@time: 2022-12-02
-->
<template>
  <div class="app-container">
    <!-- 添加或修改公示配置管理表对话框 -->
    <el-dialog
      :title="dialogTitle"
      :model-value="dialogOpen"
      width="80vw"
      append-to-body
      @closed="cancel"
      :close-on-click-modal="false"
    >
      <el-tabs v-model="activeTab">
        <!-- 规模家庭农场标签页 -->
        <el-tab-pane label="规模家庭农场" name="farm">
          <!-- 筛选条件及搜索栏 -->
          <div ref="farmSearchDom" class="app-container">
            <el-collapse-transition>
              <el-form
                ref="farmQueryRef"
                :model="farmQueryParams"
                label-width="100px"
                label-position="top"
              >
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-form-item label="年份" prop="year">
                      <el-select
                        v-model="farmQueryParams.year"
                        placeholder="请选择年份"
                        @change="handleFarmQuery"
                      >
                        <el-option
                          v-for="dict in _this.yearOption"
                          :key="dict.code"
                          :label="dict.name"
                          :value="dict.code"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="所在单位" prop="orgCode">
                      <org-select
                        ref="farmQueryOrgRef"
                        :defaultOrgCode="true"
                        @handleOrgCode="handleFarmOrgCodeQuery"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="场长姓名" prop="name">
                      <el-input
                        v-model="farmQueryParams.name"
                        placeholder="请输入场长姓名"
                        maxlength="100"
                        clearable
                        @keyup.enter="handleFarmQuery"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="身份证号" prop="idNumber">
                      <el-input
                        v-model="farmQueryParams.idNumber"
                        placeholder="请输入身份证号"
                        maxlength="100"
                        clearable
                        @keyup.enter="handleFarmQuery"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" align="right" style="margin-bottom: 20px">
                    <el-button icon="Refresh" @click="resetFarmQuery">重置</el-button>
                    <el-button icon="Search" type="primary" @click="handleFarmQuery"
                      >搜索</el-button
                    >
                  </el-col>
                </el-row>
              </el-form>
            </el-collapse-transition>
          </div>
          <!-- 查询表格 -->
          <el-table
            border
            height="470"
            ref="farmTableRef"
            :data="farmList"
            @select="selectFarmClick"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="年度" align="center" prop="yearNo" />
            <el-table-column
              label="所在单位名称"
              align="center"
              prop="organizationName"
            />
            <el-table-column label="场长姓名" align="center" prop="farmerName" />
            <el-table-column label="身份证号" align="center" prop="idNumberFormat" />
            <el-table-column label="联系方式" align="center" prop="phoneFormat" />
          </el-table>
          <pagination
            style="position: relative !important"
            v-show="farmTotal > 0"
            v-model:limit="farmQueryParams.rows"
            v-model:page="farmQueryParams.page"
            :total="farmTotal"
            @pagination="getFarmList"
          />
        </el-tab-pane>
        <!-- 农户标签页 -->
        <el-tab-pane label="农户" name="farmer">
          <!-- 筛选条件及搜索栏 -->
          <div ref="searchDom" class="app-container">
            <el-collapse-transition>
              <el-form
                ref="queryRef"
                :model="_this.queryParams"
                label-width="100px"
                label-position="top"
              >
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-form-item label="年份" prop="year">
                      <el-select
                        v-model="_this.queryParams.year"
                        placeholder="请选择年份"
                        @change="handleQuery"
                      >
                        <el-option
                          v-for="dict in _this.yearOption"
                          :key="dict.code"
                          :label="dict.name"
                          :value="dict.code"
                        />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="身份证" prop="idNumber">
                      <el-input
                        v-model="_this.queryParams.idNumber"
                        placeholder="请输入农户身份证"
                        maxlength="100"
                        clearable
                        @keyup.enter="handleQuery"
                      >
                      </el-input>
                    </el-form-item>
                  </el-col>

                  <el-col :span="6">
                    <el-form-item label="农户姓名" prop="name">
                      <el-input
                        v-model="_this.queryParams.name"
                        placeholder="请输入农户姓名"
                        maxlength="100"
                        clearable
                        @keyup.enter="handleQuery"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="6">
                    <el-form-item label="资格所在单位" prop="orgCode">
                      <org-select
                        ref="queryOrgRef"
                        :defaultOrgCode="true"
                        @handleOrgCode="handleOrgCodeQuery"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" align="right" style="margin-bottom: 20px">
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                    <el-button icon="Search" type="primary" @click="handleQuery"
                      >搜索</el-button
                    >
                  </el-col>
                </el-row>
              </el-form>
            </el-collapse-transition>
          </div>
          <!-- 查询表格 -->
          <el-table
            border
            height="470"
            ref="farmerTableRef"
            :data="_this.consigneeInfoList"
            @select="selectClick"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="所在单位" align="center" prop="organizationName" />
            <el-table-column label="姓名" align="center" prop="name" />
            <el-table-column label="身份证号" align="center" prop="idNumberformat" />
            <el-table-column label="性别" align="center" prop="sexNoName" />
            <el-table-column label="联系方式" align="center" prop="phoneformat" />
          </el-table>
          <pagination
            style="position: relative !important"
            v-show="_this.total > 0"
            v-model:limit="_this.queryParams.rows"
            v-model:page="_this.queryParams.page"
            :total="_this.total"
            @pagination="getList"
          />
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="farmWarehouseOutputselectPersonnel" setup>
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import { queryPage, config } from "@/api/bdh-agric-invest/invest/aiProdAgentInfo";
import {
  personList,
  getResidentInfo,
  familyFarmList,
} from "@/api/bdh-agric-invest/invest/landcontract";
import { allCompanyInfo } from "@/api/bdh-agric-invest/invest/companyInfo";
import { getDicts } from "@/api/bdh-agric-invest/system/dict/data";
import { ref, reactive, watch, nextTick } from "vue";

const { proxy } = getCurrentInstance();
const farmerTableRef = ref(); // 农户表格ref
const farmTableRef = ref(); // 规模家庭农场表格ref
const emits = defineEmits(["update:dialogOpen", "select"]);

// 标签页切换
const activeTab = ref("farm");

// 所在单位
const queryOrgRef = ref();
const farmQueryOrgRef = ref();

// Update the props to receive defaultOrgCode and defaultOrgName
const props = defineProps({
  dialogOpen: {
    type: Boolean,
    default: false,
  },
  dialogTitle: {
    type: String,
    default: "",
  },
  defaultOrgCode: {
    type: String,
    default: "",
  },
  defaultOrgName: {
    type: String,
    default: "",
  },
});

// 农户页面数据
const _this = reactive({
  sex: [],
  //表格数据
  consigneeInfoList: [],
  //加载状态
  loading: true,
  showSearch: true,
  //年份
  yearOption: [],
  //季节数据
  fertilizes: [],
  //企业数据
  companyAbbrOption: [],
  total: 0,
  //查询参数
  queryParams: {
    year: "", //年份
    orgCode: "", //组织机构编码
    name: "", //农户姓名
    idNumber: "", //身份证号
    type: 1,
    page: 1, //请求页码
    rows: 10, //每页条数
    orgAreaType: "all",
  },
  current: null, // 当前选中的农户
});

// 规模家庭农场数据
const farmList = ref([]);
const farmTotal = ref(0);
const farmLoading = ref(false);
const farmCurrent = ref(null);
const farmQueryParams = reactive({
  year: "", // 年份
  orgCode: "", // 组织机构编码
  name: "", // 场长姓名
  idNumber: "", // 身份证号
  page: 1, // 请求页码
  rows: 10, // 每页条数
});

//农户单选事件
function selectClick(selection, row) {
  if (selection.length > 1) {
    let del_row = selection.shift();
    farmerTableRef.value.toggleRowSelection(del_row, false);
  }
  if (selection.length > 0) {
    _this.current = selection[0];
    farmCurrent.value = null;
    if (farmTableRef.value) {
      farmTableRef.value.clearSelection();
    }
  } else {
    _this.current = null;
  }
}

//规模家庭农场单选事件
function selectFarmClick(selection, row) {
  if (selection.length > 1) {
    let del_row = selection.shift();
    farmTableRef.value.toggleRowSelection(del_row, false);
  }
  if (selection.length > 0) {
    farmCurrent.value = selection[0];
    _this.current = null;
    if (farmerTableRef.value) {
      farmerTableRef.value.clearSelection();
    }
  } else {
    farmCurrent.value = null;
  }
}

//确定按钮
function submitForm() {
  if (activeTab.value === "farmer" && _this.current) {
    // 农户数据
    const data = {
      ..._this.current,
      familyType: 1, // 农户类型为1
    };
    emits("select", data);
    cancel();
  } else if (activeTab.value === "farm" && farmCurrent.value) {
    // 规模家庭农场数据
    const data = {
      id: farmCurrent.value.farmerId,
      name: farmCurrent.value.farmerName,
      idNumber: farmCurrent.value.farmerIdNumber,
      organizationName: farmCurrent.value.organizationName,
      organizationNo: farmCurrent.value.organizationNo,
      familyType: 3, // 规模家庭农场类型为3
      landNumberList: farmCurrent.value.landNumberList || [], // 添加土地列表
    };
    emits("select", data);
    cancel();
  } else {
    proxy.$modal.msgError("请选择人员");
  }
}

//弹层关闭
function cancel() {
  emits("update:dialogOpen", false);
}

/** 获取字典 */
const getDict = () => {
  getDicts("sex").then((response) => {
    _this.sex = response.data;
  });
  getDicts("year_cd").then((response) => {
    _this.yearOption = response.data.sort((a, b) => {
      return Number(b.code) - Number(a.code);
    });
    initSearch();
    getList();

    // 初始化家庭农场查询
    initFarmSearch();
    getFarmList();
  });
};

/** 重置农户查询按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryOrgRef.value.clear();
  // Re-initialize with default values
  initSearch();
  handleQuery();
}

/** 重置规模家庭农场查询按钮操作 */
function resetFarmQuery() {
  proxy.resetForm("farmQueryRef");
  farmQueryOrgRef.value.clear();
  // Re-initialize with default values
  initFarmSearch();
  handleFarmQuery();
}

//农户默认值赋值
function initSearch() {
  if (_this.yearOption.length > 0) {
    _this.queryParams.year = _this.yearOption[0].code;
  } else {
    _this.queryParams.year = null;
  }
  // Use the default organization code if provided
  _this.queryParams.orgCode = props.defaultOrgCode || null;

  // Set the organization selector with the default value if available
  if (props.defaultOrgCode && queryOrgRef.value) {
    nextTick(() => {
      queryOrgRef.value.setValue(props.defaultOrgCode, props.defaultOrgName);
    });
  }
}

//规模家庭农场默认值赋值
function initFarmSearch() {
  if (_this.yearOption.length > 0) {
    farmQueryParams.year = _this.yearOption[0].code;
  } else {
    farmQueryParams.year = null;
  }
  // Use the default organization code if provided
  farmQueryParams.orgCode = props.defaultOrgCode || null;
  farmQueryParams.name = "";
  farmQueryParams.idNumber = "";

  // Set the organization selector with the default value if available
  if (props.defaultOrgCode && farmQueryOrgRef.value) {
    nextTick(() => {
      farmQueryOrgRef.value.setValue(props.defaultOrgCode, props.defaultOrgName);
    });
  }
}

/** 农户搜索按钮操作 */
function handleQuery() {
  _this.queryParams.page = 1;
  getList();
}

/** 规模家庭农场搜索按钮操作 */
function handleFarmQuery() {
  farmQueryParams.page = 1;
  getFarmList();
}

//农户所在单位更改
const handleOrgCodeQuery = (orgInfo) => {
  if (!orgInfo.orgCode) queryOrgRef.value.clear();
  _this.queryParams.orgCode = orgInfo.orgCode;
};

//规模家庭农场所在单位更改
const handleFarmOrgCodeQuery = (orgInfo) => {
  if (!orgInfo.orgCode) farmQueryOrgRef.value.clear();
  farmQueryParams.orgCode = orgInfo.orgCode;
};

/** 查询农户列表 */
function getList() {
  _this.loading = true;
  var obj = Object.assign({}, _this.queryParams);
  personList(obj)
    .then((response) => {
      var data = response.data;
      data.records.map((item) => {
        if (item.idNumber) {
          item.idNumberformat = item.idNumber.replace(
            /(\w{4})\w*(\w{2})/,
            "$1**************$2"
          );
        } else {
          item.idNumberformat = "";
        }
        if (item.phone) {
          item.phoneformat = item.phone.replace(
            /(\w{4})\w*(\w{2})/,
            "$1**************$2"
          );
        } else {
          item.phoneformat = "";
        }

        _this.sex.forEach((element) => {
          if (item.sexNo == element.code) {
            item.sexNoName = element.name;
          }
        });
      });
      _this.consigneeInfoList = data.records;
      _this.total = data.total;
      _this.loading = false;
    })
    .catch((err) => {
      _this.loading = false;
      _this.total = 0;
      _this.consigneeInfoList = [];
    });
}

/** 查询规模家庭农场列表 */
function getFarmList() {
  farmLoading.value = true;

  familyFarmList(farmQueryParams)
    .then((response) => {
      var data = response.data;

      // 处理数据 - 格式化身份证和电话
      data.records.forEach((item) => {
        // 格式化身份证号
        item.idNumberFormat = item.farmerIdNumber
          ? item.farmerIdNumber.replace(/(\w{4})\w*(\w{2})/, "$1**************$2")
          : "";

        // 格式化电话
        item.phoneFormat = item.phone
          ? item.phone.replace(/(\w{4})\w*(\w{2})/, "$1**************$2")
          : "";
      });

      farmList.value = data.records;
      farmTotal.value = data.total;
    })
    .catch((err) => {
      console.error("获取规模家庭农场列表失败", err);
      farmList.value = [];
      farmTotal.value = 0;
    })
    .finally(() => {
      farmLoading.value = false;
    });
}

// 监听标签页切换
watch(activeTab, (newVal) => {
  if (newVal === "farmer") {
    // 切换到农户，清空规模家庭农场选择
    if (farmTableRef.value) {
      farmTableRef.value.clearSelection();
    }
    farmCurrent.value = null;
  } else {
    // 切换到规模家庭农场，清空农户选择
    if (farmerTableRef.value) {
      farmerTableRef.value.clearSelection();
    }
    _this.current = null;
  }
});

getDict();
</script>
<style scoped lang="scss">
.el-link {
  margin-right: 8px;
}

.el-link .el-icon--right.el-icon {
  vertical-align: text-bottom;
}
.mb8 {
  min-height: 30px;
}
.title {
  font-size: 20px;
  text-align: center;
  margin-bottom: 4px;
  font-weight: 600;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}
</style>
