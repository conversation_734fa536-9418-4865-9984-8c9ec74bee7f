<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form
          class="form-line"
          :model="queryParams"
          ref="queryForm"
          v-show="showSearch"
          label-width="80px"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年份" prop="year">
                <el-select v-model="queryParams.year" clearable placeholder="请选择年份">
                  <el-option
                    v-for="dict in yearOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所在单位" prop="orgCode">
                <org-select
                  ref="queryOrgRef"
                  @handleOrgCode="handleOrgCode"
                  :defaultOrgCode="queryParams.orgCode"
                  clearable
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业简称" prop="aiCompanyId">
                <el-select
                  v-model="queryParams.aiCompanyId"
                  clearable
                  placeholder="请选择企业简称"
                >
                  <el-option
                    v-for="dict in companyAbbrOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="作业季节" prop="fertilizeSeason">
                <el-select
                  v-model="queryParams.fertilizeSeason"
                  placeholder="请选择作业季节"
                  clearable
                >
                  <el-option
                    v-for="dict in fertilizeSeasonOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="出库单号" prop="inoutOrderNo">
                <el-input
                  v-model="queryParams.inoutOrderNo"
                  placeholder="请输入出库单号"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="出库对象" prop="outboundTargetType">
                <el-select
                  v-model="queryParams.outboundTargetType"
                  placeholder="请选择出库对象"
                  clearable
                >
                  <el-option
                    v-for="dict in outboundTargetTypeOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="queryParams.outboundTargetType == '1'">
              <el-form-item label="农户姓名" prop="outboundTargetName">
                <el-input
                  v-model="queryParams.outboundTargetName"
                  placeholder="请输入农户姓名"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="queryParams.outboundTargetType == '1'">
              <el-form-item label="身份证号" prop="outboundTargetCode">
                <el-input
                  v-model="queryParams.outboundTargetCode"
                  placeholder="请输入身份证号"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6" v-if="queryParams.outboundTargetType == '2'">
              <el-form-item label="出库到库房" prop="outboundTargetCode">
                <el-select
                  v-model="queryParams.outboundTargetCode"
                  placeholder="请选择出库库房"
                  clearable
                >
                  <el-option
                    v-for="dict in farmWarehouseOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="商品类型" prop="outboundProdType">
                <el-select
                  v-model="queryParams.outboundProdType"
                  placeholder="请选择商品类型"
                  clearable
                >
                  <el-option
                    v-for="dict in outboundProdTypeOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="出库状态" prop="outboundStatus">
                <el-select
                  v-model="queryParams.outboundStatus"
                  clearable
                  placeholder="请选择出库状态"
                >
                  <el-option
                    v-for="dict in outboundStatusOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['farmWarehouseOutput:insert']"
          >新增</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          plain
          icon="Delete"
          :disabled="deleteBatch"
          @click="handleDelete"
          v-hasPermi="['farmWarehouseOutput:logicDeleteByIds']"
          >删除</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          plain
          icon="Upload"
          @click="handleImport"
          v-hasPermi="['farmWarehouseOutput:importExcel']"
          >导入</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          plain
          @click="importTemplate"
          v-hasPermi="['farmWarehouseOutput:exportTemplate']"
          >下载模板</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <el-button
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['farmWarehouseOutput:exportExcel']"
          >导出</el-button
        >
      </el-col>

       

       <el-col :span="1.5">
         <el-button
           plain
           icon="Download"
           @click="handlePrintOutboundOrder"
           :disabled="printDisabled"
           v-hasPermi="['farmWarehouseOutput:printOutboundOrder']"
           >导出出库单</el-button
         >
       </el-col>

      <el-col :span="1.5">
        <el-button
          plain
          icon="CircleCheck"
          @click="handleConfirm"
          :disabled="confirmDisabled"
          v-hasPermi="['farmWarehouseOutput:confirmWarehouseOut']"
          >确认出库</el-button
        >
      </el-col>

      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      :data="farmWarehouseOutputList"
      border
      :height="tableHeight"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="年份" align="center" prop="year" />
      <el-table-column
        label="作业季节"
        align="center"
        prop="fertilizeSeason"
        :formatter="fertilizeSeasonFormatter"
      />
      <el-table-column label="企业简称" align="center" prop="companyAbbr" />
      <el-table-column label="单号" align="center" prop="inoutOrderNo" />
      <el-table-column label="出库库房" align="center" prop="warehouseName">
        <template #default="scope">
          <div style="color: dodgerblue">{{ scope.row.warehouseName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="出库时间" align="center" prop="createTime">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="出库对象"
        align="center"
        prop="outboundTargetType"
        :formatter="outboundTargetTypeFormatter"
      />
      <el-table-column label="出库对象名称" align="center" prop="outboundTargetName" />
      <el-table-column
        label="出库商品类型"
        align="center"
        prop="outboundProdType"
        :formatter="outboundProdTypeFormatter"
      />
      <el-table-column label="出库总量" align="center" prop="operateStock">
        <template #default="scope">
          <a style="color: dodgerblue" @click="handleView(scope.row)">{{
            scope.row.operateStock
          }}</a>
        </template>
      </el-table-column>
      <el-table-column
        label="出库状态"
        align="center"
        prop="outboundStatus"
        :formatter="outboundStatusFormatter"
      />
      <el-table-column
        label="审核状态"
        align="center"
        prop="auditStatus"
        :formatter="auditStatusFormatter"
      />
      <el-table-column
        label="签收状态"
        align="center"
        prop="signStatus"
        :formatter="signStatusFormatter"
      />
      <el-table-column label="当前审核人" align="center" prop="operatePerson" />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="200px"
      >
        <template #default="scope">
          <el-button
            type="primary"
            icon="Edit"
            link
            v-if="scope.row.outboundStatus == 0"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['farmWarehouseOutput:update']"
            >修改</el-button
          >
          <el-button
            type="danger"
            icon="Delete"
            link
            v-if="scope.row.outboundStatus == 0"
            @click="handleDelete(scope.row)"
            v-hasPermi="['farmWarehouseOutput:logicDeleteById']"
            >删除</el-button
          >
          <!--          <el-button-->
          <!--              type="primary"-->
          <!--              icon="Edit"-->
          <!--              link-->
          <!--              @click="handleUpdate(scope.row)"-->
          <!--          >修改</el-button-->
          <!--          >-->
          <!--          <el-button-->
          <!--              type="danger"-->
          <!--              icon="Delete"-->
          <!--              link-->
          <!--              @click="handleDelete(scope.row)"-->
          <!--          >删除</el-button-->
          <!--          >-->
          <el-button
            type="primary"
            icon="View"
            link
            @click="handleView(scope.row)"
            v-hasPermi="['farmWarehouseOutput:info']"
            >查看详情</el-button
          >

                     <el-button
             type="primary"
             icon="View"
             link
             @click="handleViewOutboundOrder(scope.row)"
             v-hasPermi="['farmWarehouseOutput:queryOutboundOrder']"
             >查看出库单</el-button
           >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.rows"
      @pagination="getList"
    />

    <!-- 新增出库对话框 -->
    <el-dialog
      :title="title"
      v-model="openOutput"
      width="900px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="processedFormRef"
        :model="outputForm"
        :rules="processedRules"
        label-width="80px"
        label-position="top"
        :disabled="viewDetailOnly"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="年份" prop="year">
              <el-select v-model="outputForm.year" clearable placeholder="请选择年份">
                <el-option
                  v-for="dict in yearOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所在单位" prop="orgCode">
              <org-select
                v-if="orgFlag"
                @handleOrgCode="handleOrgCodeForm"
                :defaultOrgCode="outputForm.orgCode"
                :orgCode="outputForm.orgCode"
                ref="processedFormOrgRef"
                @handleChange="handleChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="企业简称" prop="aiCompanyId">
              <el-select
                v-model="outputForm.aiCompanyId"
                clearable
                placeholder="请选择企业简称"
                @change="handleChange"
              >
                <el-option
                  v-for="dict in companyAbbrOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="作业季节" prop="fertilizeSeason">
              <el-select
                v-model="outputForm.fertilizeSeason"
                clearable
                placeholder="请选择作业季节"
              >
                <el-option
                  v-for="dict in fertilizeSeasonOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库库房" prop="warehouseId">
              <el-select
                v-model="outputForm.warehouseId"
                clearable
                placeholder="请选择出库库房"
                @change="handleChange"
              >
                <el-option
                  v-for="dict in farmWarehouseOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库商品类型" prop="outboundProdType">
              <el-select
                v-model="outputForm.outboundProdType"
                clearable
                placeholder="请选择出库商品类型"
                @change="handleChange"
              >
                <el-option
                  v-for="dict in filteredoutboundProdTypeOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="出库对象" prop="outboundTargetType">
              <el-select
                v-model="outputForm.outboundTargetType"
                clearable
                placeholder="请选择出库对象"
                @change="handleChangeObj"
              >
                <el-option
                  v-for="dict in filteredOutboundTargetTypeOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8" v-if="outputForm.outboundTargetType == '1'">
            <el-form-item label="收货单位" prop="organizationName">
              <el-input
                v-model="outputForm.organizationName"
                placeholder="收货单位"
                clearable
                disabled
                :style="{ width: viewOnly ? '100%' : 'calc(100% - 60px)' }"
              />
              <el-button
                type="primary"
                @click="handleAddFarmer"
                v-if="!viewOnly"
                style="width: 60px"
                >选择</el-button
              >
            </el-form-item>
          </el-col>

          <el-col
            :span="8"
            v-if="
              outputForm.outboundTargetType == '1' &&
              (!outputForm.familyType || outputForm.familyType == 1)
            "
          >
            <el-form-item label="农户姓名" prop="outboundTargetName">
              <el-input
                v-model="outputForm.outboundTargetName"
                placeholder="请输入农户姓名"
                clearable
                disabled
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>
          <el-col
            :span="8"
            v-if="outputForm.outboundTargetType == '1' && outputForm.familyType == 3"
          >
            <el-form-item label="场长" prop="outboundTargetName">
              <el-input
                v-model="outputForm.outboundTargetName"
                placeholder="场长"
                clearable
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col
            :span="8"
            v-if="
              outputForm.outboundTargetType == '1' &&
              (!outputForm.familyType || outputForm.familyType == 1)
            "
          >
            <el-form-item label="身份证号" prop="outboundTargetCode">
              <el-input
                v-model="outputForm.outboundTargetCode"
                placeholder="请输入身份证号"
                clearable
                disabled
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>

          <!-- 农户的耕地地号输入框 -->
          <el-col :span="8" v-if="outputForm.outboundTargetType == '1' && (!outputForm.familyType || outputForm.familyType == 1)">
            <el-form-item label="耕地地号" prop="plotName">
              <el-input
                v-model="outputForm.plotName"
                placeholder="请输入耕地地号"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
          </el-col>

          <!-- 规模家庭农场的耕地地号下拉选择 -->
          <el-col :span="8" v-if="outputForm.outboundTargetType == '1' && outputForm.familyType == 3">
            <el-form-item label="耕地地号" prop="plotName" >
              <el-select
                v-model="outputForm.plotName"
                placeholder="请选择耕地地号"
                @change="handlePlotChange"
              >
                <el-option
                  v-for="plot in plotNameOptions"
                  :key="plot"
                  :label="plot"
                  :value="plot"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col
            :span="8"
            v-if="outputForm.outboundTargetType == '1' && outputForm.familyType == 3"
          >
            <el-form-item label="质量监督员" prop="qualitySupervisor" >
              <el-input
                v-model="outputForm.qualitySupervisor"
                placeholder="请输入质量监督员"
                disabled
              />
            </el-form-item>
          </el-col>



        
          <el-col :span="8" v-if="outputForm.outboundTargetType == '2'">
            <el-form-item label="出库到库房" prop="outboundTargetCode">
              <el-select
                v-model="outputForm.outboundTargetCode"
                placeholder="请选择出库到库房"
                clearable
                 @change="handleFormWarehouseChange"
              >
                <el-option
                  v-for="dict in farmWarehouseOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 新增车号字段 -->
          <el-col :span="8">
            <el-form-item label="车号" prop="plateNumber">
              <div style="display: flex; align-items: center; width: 100%">
                <el-input
                  v-model="outputForm.plateNumber"
                  placeholder="请选择车号"
                  clearable
                  disabled
                  :style="{
                    width: viewOnly ? '100%' : 'calc(100% - 60px)',
                  }"
                />
                <el-button
                  type="primary"
                  @click="handleSelectVehicle"
                  v-if="!viewOnly"
                  style="width: 60px"
                  >选择</el-button
                >
              </div>
            </el-form-item>
          </el-col>

          <!-- 新增司机字段 -->
          <el-col :span="8">
            <el-form-item label="司机" prop="driverName">
              <el-input v-model="outputForm.driverName" placeholder="司机信息" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-table border :data="outputDetailList" ref="addProduceTableRef">
        <el-table-column
          align="center"
          label="商品类型"
          prop="outboundProdType"
          :formatter="outboundProdTypeFormatter"
        />
        <el-table-column align="center" label="商品名称" prop="aiTypeSubName" />

        <el-table-column align="center" label="投入品分类" prop="aiTypeName" />
        <el-table-column align="center" label="规格" prop="aiTypeSubSpecs" />
        <el-table-column align="center" label="库存量" prop="curInStock" />
        <!-- <el-table-column align="center" label="已出库量" prop="totalOutStock" /> -->
        <el-table-column align="center" label="本次出库量" prop="operateStock">
          <template #default="scope">
         
            <el-input
              v-model="scope.row.operateStock"
              placeholder="请输入"
              :disabled="viewOnly"
            >
            </el-input>
          </template>
        </el-table-column>

        <el-table-column align="center" label="商品重量" prop="aiTypeSubWeight" />
        <el-table-column align="center" label="重量单位" prop="aiTypeSubUnit" />
        <el-table-column align="center" label="操作" width="120">
          <template #default="scope">
            <el-button
              type="primary"
              link
              size="small"
              @click="handleViewMaterialComposition(scope.row,viewDetailOnly)"
              v-if="scope.row.outboundProdType == 2"
            >
              查看原料组成
            </el-button>
          </template>
        </el-table-column>

    
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel" v-if="!viewOnly">取 消</el-button>
          <el-button
            type="primary"
            @click="submitForm"
            :disabled="!dataIsValid"
            v-if="!viewOnly"
            >确 定</el-button
          >
          <el-button @click="cancel" v-if="viewOnly">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 用户导入对话框 -->
    <el-dialog
      :title="upload.title"
      v-model="upload.open"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text"><em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip"></div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <!--            <el-link-->
            <!--              type="primary"-->
            <!--              :underline="false"-->
            <!--              style="font-size: 12px; vertical-align: baseline"-->
            <!--              @click="importTemplate"-->
            <!--              >下载模板</el-link-->
            <!--            >-->
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 选择农户-->
    <selectPersonnel
      @select="select"
      v-model:dialogOpen="selectPersonnelOpen"
      :dialogTitle="selectPersonnelTitle"
      :defaultOrgCode="selectPersonnelProps.defaultOrgCode"
      :defaultOrgName="selectPersonnelProps.defaultOrgName"
      v-if="selectPersonnelOpen"
    />

    <!-- 选择车辆-->
    <selectVehicle
      @select="selectVehicleData"
      v-model:dialogOpen="selectVehicleOpen"
      :dialogTitle="selectVehicleTitle"
      v-if="selectVehicleOpen"
    />

    <!-- 查看原料组成弹窗 -->
    <el-dialog
      title="查看原料组成"
      v-model="materialCompositionDialog"
      width="900px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-table border :data="materialCompositionList" style="margin-top: 10px">
        <el-table-column align="center" label="序号" type="index" width="55" />
        <el-table-column align="center" label="商品名称" prop="aiTypeSubName" />
        <el-table-column align="center" label="商品规格" prop="aiTypeComSpecs" />
        <el-table-column align="center" label="商品单位" prop="productWeightUnit" />
        <el-table-column align="center" label="单价（元）" prop="aiGuidingPrice" />
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="materialCompositionDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看出库单弹窗 -->
    <el-dialog
      title="查看出库单"
      v-model="outboundOrderDialog"
      width="1200px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-table border :data="outboundOrderList" style="margin-top: 10px">
        <el-table-column align="center" label="序号" prop="num" width="80" />
        <el-table-column align="center" label="商品名称" prop="aiTypeSubName" />
        <el-table-column align="center" label="原材料名称" prop="aiTypeComName" />
        <el-table-column align="center" label="商品规格" prop="aiTypeSubSpecs" />
        <el-table-column align="center" label="商品单位" prop="aiTypeSubUnit" />
        <el-table-column align="center" label="商品数量" prop="operateStock" />
        <el-table-column align="center" label="单价" prop="aiGuidingPrice" />
        <el-table-column align="center" label="金额" prop="amounts" />
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="outboundOrderDialog = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导出出库单弹窗 -->
    <el-dialog
      title="导出出库单"
      v-model="printDialog"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="printFormRef" :model="printForm" :rules="printRules" label-width="100px">
        <el-form-item label="导出类型" prop="copyTypeList">
          <el-select 
            v-model="printForm.copyTypeList" 
            placeholder="请选择导出类型" 
            multiple
            clearable
          >
            <el-option
              v-for="dict in printTypeOptions"
              :key="dict.code"
              :label="dict.name"
              :value="dict.code"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="printDialog = false">取消</el-button>
          <el-button type="primary" @click="submitPrint">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script name="/invest/farmWarehouseOutput/queryByPage" setup>
import { ref } from "vue";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import { postForExcel,postForPdf } from "@/api/bdh-agric-invest/invest/excel";
import { getToken } from "@/utils/auth";
import { getDicts, queryAllOrgTree } from "@/api/bdh-agric-invest/system/dict/data";
import {
  getFarmWarehouseAll,
  queryAllCompanyInfoInput,
} from "@/api/bdh-agric-invest/invest/farmWarehouseInput";
import {
  confirmWarehouseOut,
  getFarmWareOutDetailList,
  infoOutput,
  insertOutput,
  listfarmWarehouseOutput,
  logicDeleteByIdsOOutput,
  logicDeleteByWarehouseRecordIdOutput,
  updateOutput,
  queryOutboundOrder,
  queryQualitySupervisors,
} from "@/api/bdh-agric-invest/invest/farmWarehouseOutput";
import { getProcessDetailList } from "@/api/bdh-agric-invest/invest/farmWarehouseInput";
import selectPersonnel from "./selectPersonnel";
import selectVehicle from "./selectVehicle";
import { queryVehicleInfoByPage } from "@/api/bdh-agric-invest/invest/vehicleInfo";
import { getProduceProcessInfo } from "@/api/bdh-agric-invest/invest/produceProcessInfo";

const { proxy } = getCurrentInstance();

const farmWarehouseOutputList = ref([]); //主列表
const open = ref(false);
const openOutput = ref(false); //新增出库弹出页
const showSearch = ref(true);
// const loading = ref(false);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const queryOrgRef = ref();
const processedFormOrgRef = ref();
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const defaultOrgCode = ref(""); //默认初始所在单位
const orgFlag = ref(false); //渲染所在单位ref
const viewOnly = ref(false); //表单只读
const viewDetailOnly = ref(false); //修改时上方表单不能修改
//控制批量删除按钮
const hasTypeAndStatusOne = ref(true);
const deleteBatch = ref(true); //控制批量删除按钮
const confirmDisabled = ref(true); //控制确认出库按钮
//控制累计回显
const totalStokeAdd = ref(false);
const totalStoke = ref(false);

//字典们
const yearOption = ref([]); //年份字典
const fertilizeSeasonOption = ref([]); //作业季节字典
const companyAbbrOption = ref([]); //企业简称列表
const farmWarehouseOption = ref([]); //农场库房列表
const outboundTargetTypeOption = ref([]); //出库对象字典
const outboundStatusOption = ref([
  {
    code: 0,
    name: "待确认",
  },
  {
    code: 1,
    name: "已确认",
  },
]); //出库状态字典
const auditStatusOption = ref([
  {
    code: 0,
    name: "待确认",
  },
  {
    code: 1,
    name: "已确认",
  },
]); //审核状态字典

const signStatusOption = ref([
  {
    code: 0,
    name: "待出库",
  },
  {
    code: 1,
    name: "待收货",
  },
  {
    code: 2,
    name: "待验收",
  },
  {
    code: 3,
    name: "已完成",
  },
]); //签收状态字典

const outboundProdTypeOption = ref([
  {
    code: 1,
    name: "统供商品",
  },
  {
    code: 2,
    name: "加工商品",
  },
]); //出库商品类型字典

const filteredOutboundTargetTypeOption = computed(() => {
  if (outputForm.value.outboundProdType == "2") {
    return outboundTargetTypeOption.value.filter((dict) =>
      ["1", "2"].includes(dict.code)
    );
  }
  return outboundTargetTypeOption.value;
});
const filteredoutboundProdTypeOption = computed(() => {
  if (outputForm.value.outboundTargetType == "3") {
    return outboundProdTypeOption.value.filter((dict) => [1].includes(dict.code));
  }
  return outboundProdTypeOption.value;
});

const outputDetailList = ref([]); //出库商品列表
const dataIsValid = ref(true); //出库量-校验
const orgListDetail = ref([]); //org列表
const groupIdArr = ref([]); //每个库存单里的商品id

//选择农户
const selectPersonnelOpen = ref(false);
const selectPersonnelTitle = ref("选择人员");
const selectPersonnelProps = ref({
  defaultOrgCode: "",
  defaultOrgName: "",
});

//耕地地号选项
const plotNameOptions = ref([]);

//选择车辆
const selectVehicleOpen = ref(false);
const selectVehicleTitle = ref("选择车号和司机");

//查看原料组成
const materialCompositionDialog = ref(false);
const materialCompositionList = ref([]);

//查看出库单
const outboundOrderDialog = ref(false);
const outboundOrderList = ref([]);

//导出出库单
const printDialog = ref(false);
const printDisabled = ref(true);
const selectedInoutOrderNos = ref([]);
const printForm = ref({
  copyTypeList: []
});
const printRules = ref({
  copyTypeList: [
    { 
      required: true,
      validator: (rule, value, callback) => {
        if (!value || value.length === 0) {
          callback(new Error("请至少选择一种导出类型"));
        } else {
          callback();
        }
      },
      trigger: "change"
    }
  ]
});
const printTypeOptions = ref([
  { code: 1, name: "记账联" },
  { code: 2, name: "农户留存" },
  { code: 3, name: "管理区联" }
]);

const data = reactive({
  outputForm: {},
  processedRules: {
    orgCode: [{ required: true, message: "请选择所在单位", trigger: "change" }],
    year: [{ required: true, message: "请选择年份", trigger: "change" }],
    aiCompanyId: [{ required: true, message: "请选择企业简称", trigger: "change" }],
    fertilizeSeason: [{ required: true, message: "请选择作业季节", trigger: "change" }],
    warehouseId: [{ required: true, message: "请选择入库库房", trigger: "change" }],
    outboundProdType: [
      { required: true, message: "请选择出库商品类型", trigger: "change" },
    ],
    outboundTargetType: [
      { required: true, message: "请选择出库对象", trigger: "change" },
    ],
    outboundTargetName: [{ required: true, message: "请选择农户姓名", trigger: "blur" }],
    outboundTargetCode: [
      { required: true, message: "请选择库房/身份证", trigger: "change" },
    ],
    plotName: [
    ],
    qualitySupervisor: [
      { required: true, message: "请选择质量监督员", trigger: "change" },
    ],
  },
  form: {},
  queryParams: {
    rows: 10,
    page: 1,
    orgCode: "",
    year: "",
    aiCompanyId: "",
    fertilizeSeason: "",
    inoutOrderNo: "",
    outboundProdType: null,
    outboundStatus: null,
  },
  queryParamsRules: {},
  rules: {},
});
const { queryParams, form, rules, processedRules, outputForm } = toRefs(data);

/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { "access-token": getToken() },
  // 上传的地址
  url:
    window.VITE_APP_BASE_API +
    "/bdh-agric-invest-api/invest/farmWarehouseOutput/importExcel",
});

onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 220
    : window.innerHeight - 220;
});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 220
    : window.innerHeight - 220;
});

/** 查询农场投入品库房列表 */
function getList() {
  listfarmWarehouseOutput(queryParams.value).then((response) => {
    farmWarehouseOutputList.value = response.data.records;
    total.value = response.data.total;
  });
}
// getList();

// 取消按钮
function cancel() {
  openOutput.value = false;
  reset();
}

// 表单重置
function reset() {
  outputForm.value = {
    year: "",
    orgCode: "",
    orgName: "",
    aiCompanyId: "",
    fertilizeSeason: "",
    warehouseId: "",
    outboundProdType: "",
    outboundTargetType: "",
    outboundTargetCode: "",
    outboundTargetName: "",
    outboundTargetId: null,
    plateNumber: "",
    driverName: "",
    vehicleInfoId: null,
    familyType: null,
    organizationName: "",
    organizationNo: "",
    qualitySupervisor: "",
    qualitySupervisorId: "",
    plotName: "",
    plotNo: "",
  };
  plotNameOptions.value = [];
  proxy.resetForm("processedFormRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  queryOrgRef.value.clear();
  queryParams.value.orgCode = defaultOrgCode.value;
  queryOrgRef.value.setValue(defaultOrgCode.value, "");
  queryParams.value.year = yearOption.value[0].code;
  handleQuery();
}

//控制批量删除按钮
function hasItemWithTypeAndStatusOne(selection) {
  for (const item of selection) {
    if (item.outboundStatus !== 0) {
      return true; // 如果找到任何不满足条件的item，返回true
    }
  }
  return false; // 没有找到满足条件的item，返回false
}

function hasOutboundTargetTypeOne(selection) {
  for (const item of selection) {
    if (item.outboundTargetType === 1) {
      return true; // 如果找到出库对象为个人的item，返回true
    }
  }
  return false; // 没有找到满足条件的item，返回false
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  groupIdArr.value = selection.flatMap((item) => item.groupIdArr);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
  hasTypeAndStatusOne.value = hasItemWithTypeAndStatusOne(selection); // 更新新的响应式值
  
  // 检查是否有出库对象为个人的选项
  const hasPersonalTarget = hasOutboundTargetTypeOne(selection);
  
  // 更新删除按钮禁用状态
  deleteBatch.value = !(!multiple.value && !hasTypeAndStatusOne.value);
  
  // 更新确认出库按钮禁用状态：有出库对象为个人的选项或者没有选中项时禁用
  confirmDisabled.value = multiple.value || hasPersonalTarget;
  
  // 更新导出按钮禁用状态：没有选中项时禁用，或者选中项不满足审核状态和签字状态条件时禁用
  const hasInvalidAuditOrSignStatus = selection.some(item => 
    item.auditStatus !== 1 || item.signStatus <2
  );
  printDisabled.value = multiple.value || hasInvalidAuditOrSignStatus;
  
  // 收集选中的出库单号
  selectedInoutOrderNos.value = selection.map((item) => item.inoutOrderNo);
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  if (proxy.$refs["processedFormOrgRef"]) {
    proxy.$refs["processedFormOrgRef"].clear();
  }
  orgFlag.value = false;
  nextTick(() => {
    orgFlag.value = true;
  });
  if (farmWarehouseOption.value.length === 0) {
    proxy.$modal.msgError("当前无库房信息");
    return;
  }
  outputForm.value.warehouseId = farmWarehouseOption.value[0].code;
  outputForm.value.aiCompanyId = companyAbbrOption.value[0].code;
  outputForm.value.outboundProdType = outboundProdTypeOption.value[0].code;
  outputForm.value.orgCode = defaultOrgCode.value;
  const warehouseId = outputForm.value.warehouseId;
  const orgCode = outputForm.value.orgCode;
  const aiCompanyId = outputForm.value.aiCompanyId;
  const outboundProdType = outputForm.value.outboundProdType;
  getFarmWareOutDetailList({
    warehouseId: warehouseId,
    orgCode: orgCode,
    aiCompanyId: aiCompanyId,
    outboundProdType: outboundProdType,
  }).then((response) => {
    // 新增时设置默认值为0，查看和修改时保持原数据
    outputDetailList.value = response.data.map(item => ({
      ...item,
      operateStock: item.operateStock !== undefined && item.operateStock !== null ? item.operateStock : 0
    }));
    viewOnly.value = false;
    viewDetailOnly.value = false;
    openOutput.value = true;
    title.value = "出库管理";
    totalStokeAdd.value = true;
    totalStoke.value = false;
  });
}
//每次更改筛选框时下方list联动更改
function handleChange() {
  const warehouseId = outputForm.value.warehouseId;
  const orgCode = outputForm.value.orgCode;
  const aiCompanyId = outputForm.value.aiCompanyId;
  const outboundProdType = outputForm.value.outboundProdType;
  getFarmWareOutDetailList({
    warehouseId: warehouseId,
    orgCode: orgCode,
    aiCompanyId: aiCompanyId,
    outboundProdType: outboundProdType,
  }).then((response) => {
    // 新增时设置默认值为0，查看和修改时保持原数据
    outputDetailList.value = response.data.map(item => ({
      ...item,
      operateStock: item.operateStock !== undefined && item.operateStock !== null ? item.operateStock : 0
    }));
  });
}

//每次更改筛选框时重置出库对象
function handleChangeObj() {
  outputForm.value.outboundTargetCode = "";
  outputForm.value.outboundTargetName = "";
  outputForm.value.outboundTargetId = null;
  outputForm.value.plotName = "";
  outputForm.value.plotNo = "";
  plotNameOptions.value = [];
  outputForm.value.qualitySupervisor = "";
  outputForm.value.qualitySupervisorId = "";
}

//获取质量监管员列表
function getQualitySupervisors() {
  if (!outputForm.value.organizationNo || !outputForm.value.plotName) {
    return;
  }
  
  const params = {
    page: "1",
    rows: "9999",
    organizationNo: outputForm.value.organizationNo,
    landNumberList: [outputForm.value.plotName]
  };
  
  // 调用质量监管员接口
  queryQualitySupervisors(params).then(response => {
    if (response.data && response.data.records && response.data.records.length > 0) {
      const record = response.data.records[0]; // 取第一条记录
      
      // 设置 plotNo
      if (record.plotNo) {
        outputForm.value.plotNo = record.plotNo;
      }
      
      // 处理质量监管员信息
      if (record.supervisorInfoList && record.supervisorInfoList.length > 0) {
        const supervisorNames = [];
        const supervisorIds = [];
        
        record.supervisorInfoList.forEach(supervisor => {
          if (supervisor.amSuperName) {
            supervisorNames.push(supervisor.amSuperName);
          }
          if (supervisor.staffId) {
            supervisorIds.push(supervisor.staffId);
          }
        });
        
        // 设置质量监督员名称
        outputForm.value.qualitySupervisor = supervisorNames.join(',');
        // 设置质量监督员ID
        outputForm.value.qualitySupervisorId = supervisorIds.join(',');
      } else {
        outputForm.value.qualitySupervisor = "";
        outputForm.value.qualitySupervisorId = "";
      }
    } else {
      outputForm.value.qualitySupervisor = "";
      outputForm.value.qualitySupervisorId = "";
      outputForm.value.plotNo = "";
    }
  }).catch(error => {
    console.error('获取质量监管员失败:', error);
    outputForm.value.qualitySupervisor = "";
    outputForm.value.qualitySupervisorId = "";
    outputForm.value.plotNo = "";
  });
}

//处理耕地地号变化
function handlePlotChange() {
  outputForm.value.qualitySupervisor = "";
  outputForm.value.qualitySupervisorId = "";
  outputForm.value.plotNo = "";
  getQualitySupervisors();
}



//处理出库到库房变化（表单）
function handleFormWarehouseChange(selectedWarehouseId) {
  if (selectedWarehouseId) {
    // 从farmWarehouseOption中找到选中的库房信息
    const selectedWarehouse = farmWarehouseOption.value.find(
      warehouse => warehouse.code === selectedWarehouseId
    );
    
    if (selectedWarehouse) {
      // 将库房信息赋值给相关字段
      outputForm.value.outboundTargetId = selectedWarehouse.warehouseId;
      outputForm.value.organizationNo = selectedWarehouse.orgCode;
      outputForm.value.organizationName = selectedWarehouse.orgName;
    }
  } else {
    // 清空相关字段
    outputForm.value.outboundTargetId = null;
    outputForm.value.organizationNo = "";
    outputForm.value.organizationName = "";
  }
  console.log(outputForm.value);
}



//选择农户弹出页
function handleAddFarmer() {
  selectPersonnelOpen.value = true;
  // Pass the current organization information to the selectPersonnel component
  selectPersonnelProps.value = {
    defaultOrgCode: outputForm.value.orgCode,
    defaultOrgName: outputForm.value.orgName,
  };
}
function select(obj) {
  if (obj) {
    outputForm.value.outboundTargetCode = obj.idNumber;
    outputForm.value.outboundTargetName = obj.name;
    outputForm.value.familyType = obj.familyType;
    outputForm.value.organizationName = obj.organizationName;
    outputForm.value.organizationNo = obj.organizationNo;
    // 如果是规模家庭农场，处理土地列表
    if (obj.familyType == 3 && obj.landNumberList && obj.landNumberList.length > 0) {
      // 将farmerId赋值给outboundTargetId
      outputForm.value.outboundTargetId = obj.id;
      plotNameOptions.value = obj.landNumberList;
      // 默认选择第一个
      outputForm.value.plotName = obj.landNumberList[0];
      // 获取质量监管员列表
      getQualitySupervisors();
    } else {
      outputForm.value.outboundTargetId = obj.id; // 农户的id
      plotNameOptions.value = [];
      outputForm.value.plotName = "";
      outputForm.value.plotNo = "";
      outputForm.value.qualitySupervisor = "";
      outputForm.value.qualitySupervisorId = "";
    }
  }
}

//选择车辆弹出页
function handleSelectVehicle() {
  selectVehicleOpen.value = true;
}
function selectVehicleData(obj) {
  if (obj) {
    outputForm.value.vehicleInfoId = obj.vehicleInfoId;
    outputForm.value.plateNumber = obj.plateNumber;
    outputForm.value.driverName = obj.driverName;
  }
}

//查看原料组成
function handleViewMaterialComposition(row,viewDetailOnly) {
  if(viewDetailOnly){
    getProcessDetailList({ warehouseRecordId: row.warehouseRecordId }).then((response) => {
      materialCompositionList.value = response.data;
      materialCompositionDialog.value = true;
    });
  }else{
    getProduceProcessInfo(row.aiTypeSubId).then((response) => {
      materialCompositionList.value = response.data.produceProcessCompList;
      materialCompositionDialog.value = true;
    });
  }
}

//查看出库单
function handleViewOutboundOrder(row) {
  if (row.inoutOrderNo) {
    queryOutboundOrder({ inoutOrderNo: row.inoutOrderNo }).then((response) => {
      outboundOrderList.value = response.data;
      outboundOrderDialog.value = true;
    });
  } else {
    proxy.$modal.msgError("无法获取出库单信息");
  }
}

//导出出库单
function handlePrintOutboundOrder() {
  if (selectedInoutOrderNos.value.length === 0) {
    proxy.$modal.msgError("请先选择要导出的出库单");
    return;
  }
  printForm.value.copyTypeList = [];
  printDialog.value = true;
}

//提交导出
function submitPrint() {
  proxy.$refs["printFormRef"].validate((valid) => {
    if (valid) {
      if (printForm.value.copyTypeList.length === 0) {
        proxy.$modal.msgError("请至少选择一种导出类型");
        return;
      }
      
      const params = {
          inoutOrderNoList: selectedInoutOrderNos.value,
          copyTypeList: printForm.value.copyTypeList
        };
        
      const fileName = `管理区投入品出库单`;
        
        // 使用postForPdf处理文件流下载
        postForPdf(
          "/bdh-agric-invest-api/invest/farmWarehouseOutput/printOutboundOrder",
          params,
          fileName
        );
      
      printDialog.value = false;
    }
  });
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  if (proxy.$refs["processedFormOrgRef"]) {
    proxy.$refs["processedFormOrgRef"].clear();
  }
  orgFlag.value = false;
  nextTick(() => {
    orgFlag.value = true;
  });
  const inoutOrderNo = row.inoutOrderNo;
  const year = row.year;
  const aiCompanyId = row.aiCompanyId;
  infoOutput({ inoutOrderNo: inoutOrderNo, year: year, aiCompanyId: aiCompanyId }).then(
    (response) => {
      outputForm.value = response.data;
      outputDetailList.value = response.data.farmWarehouseRecordOutDetailPOList;
      if (Array.isArray(response.data.farmWarehouseRecordOutDetailPOList)) {
        response.data.farmWarehouseRecordOutDetailPOList.forEach((item) => {
          item.outboundProdType = response.data.outboundProdType;
        });
      }
      outputForm.value.outboundTargetType = response.data.outboundTargetType.toString();
      outputForm.value.outboundTargetCode = Number(response.data.outboundTargetCode);
      openOutput.value = true;
      viewOnly.value = false;
      viewDetailOnly.value = true;
      title.value = "修改出库信息";
      totalStokeAdd.value = true;
      totalStoke.value = false;
    }
  );
}

//出库量-校验
const validateTotalRatio = (row) => {
  const ratioVal = row.operateStock;
  const totalVal = row.totalOutStock + (+row.operateStock || 0);
  if (isNaN(ratioVal)) {
    // dataIsValid.value = false;
    proxy.$modal.msgError("请输入数字");
    return false;
  } else if (ratioVal < 0 || ratioVal > 999999999 || !/^\d+$/.test(ratioVal)) {
    // dataIsValid.value = false;
    proxy.$modal.msgError("请输入9位以内的非负整数");
    return false;
  } else if (ratioVal > row.curInStock) {
    // dataIsValid.value = false;
    proxy.$modal.msgError("当前库存不足");
    return false;
  } else {
    // dataIsValid.value = true;
    return true;
  }
};

/** 提交按钮 */
function submitForm() {
  proxy.$refs["processedFormRef"].validate((valid) => {
    if (valid) {
      // 非空校验
      if (outputDetailList.value.length === 0) {
        proxy.$modal.msgError("当前无出库商品信息");
        return;
      }
      //合计出库量校验
      let totalStock = outputDetailList.value.reduce((acc, row) => {
        const value =
          typeof row.operateStock === "string" && !isNaN(row.operateStock)
            ? parseFloat(row.operateStock)
            : row.operateStock;
        if (value != null && value !== "" && !isNaN(value)) {
          return acc + value;
        }
        return acc;
      }, 0);
      // 校验每一行的operateStock
      const hasInvalidRow = outputDetailList.value.some(
        (row) => !validateTotalRatio(row)
      );
      if (hasInvalidRow) {
        return;
      }
      const emptyInput = outputDetailList.value.some((row) => {
        return (
          row.operateStock === null ||
          row.operateStock === undefined ||
          row.operateStock === "" ||
          Number.isNaN(row.operateStock)
        );
      });
      if (emptyInput || totalStock == 0) {
        proxy.$modal.msgError("请输入本次商品出库量,合计出库量不能为零");
        return;
      }
      if (outputForm.value.warehouseRecordId != null) {
        const farmWarehouseRecordOutDetailPO = outputDetailList.value.map((item) => ({
          aiTypeName: item.aiTypeName,
          aiTypeSubName: item.aiTypeSubName,
          aiTypeSubSpecs: item.aiTypeSubSpecs,
          operateStock: item.operateStock,
          aiTypeSubId: item.aiTypeSubId,
          curInStock: null,
          warehouseStockId: null,
          warehouseRecordId: null,
        }));
        outputForm.value.farmWarehouseRecordOutDetailPOList = farmWarehouseRecordOutDetailPO;
        updateOutput(outputForm.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          openOutput.value = false;
          getList();
        });
      } else {
        //查找对应的companyAbbr
        const selectedCompany = companyAbbrOption.value.find(
          (item) => item.code == outputForm.value.aiCompanyId
        );
        if (selectedCompany) {
          outputForm.value.companyAbbr = selectedCompany.name;
        }
        //查找对应的warehouseName
        const selectedWarehouse = farmWarehouseOption.value.find(
          (item) => item.code == outputForm.value.warehouseId
        );
        if (selectedWarehouse) {
          outputForm.value.warehouseName = selectedWarehouse.name;
        }
        //查找对应的targetName
        const selectedOutboundTargetName = farmWarehouseOption.value.find(
          (item) => item.code == outputForm.value.outboundTargetCode
        );
        if (selectedOutboundTargetName) {
          outputForm.value.outboundTargetName = selectedOutboundTargetName.name;
        }
        //查找对应的orgName
        const selectedOrgName = orgListDetail.value.find(
          (item) => item.orgCode == outputForm.value.orgCode
        );
        if (selectedOrgName) {
          outputForm.value.orgName = selectedOrgName.orgName;
        }
        const farmWarehouseRecordOutDetailPO = outputDetailList.value.map((item) => ({
          aiTypeName: item.aiTypeName,
          aiTypeSubName: item.aiTypeSubName,
          aiTypeSubSpecs: item.aiTypeSubSpecs,
          operateStock: item.operateStock,
          aiTypeSubId: item.aiTypeSubId,
          curInStock: null,
          warehouseStockId: null,
          warehouseRecordId: null,
        }));
        outputForm.value.farmWarehouseRecordOutDetailPOList = farmWarehouseRecordOutDetailPO;
        console.log(outputForm.value,'outputForm.value');
        insertOutput(outputForm.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          openOutput.value = false;
          getList();
        });
      }
    }
  });
}

//查看详情
function handleView(row) {
  orgFlag.value = false;
  nextTick(() => {
    orgFlag.value = true;
  });
  const year = row.year;
  const aiCompanyId = row.aiCompanyId;
  const inoutOrderNo = row.inoutOrderNo;
  infoOutput({ inoutOrderNo: inoutOrderNo, year: year, aiCompanyId: aiCompanyId }).then(
    (response) => {
      outputForm.value = response.data;
      outputForm.value.outboundTargetType = response.data.outboundTargetType.toString();
      outputForm.value.outboundTargetCode = Number(response.data.outboundTargetCode);
      outputDetailList.value = response.data.farmWarehouseRecordOutDetailPOList;
      if (Array.isArray(response.data.farmWarehouseRecordOutDetailPOList)) {
        response.data.farmWarehouseRecordOutDetailPOList.forEach((item) => {
          item.outboundProdType = response.data.outboundProdType;
        });
      }
      title.value = "出库信息";
      viewOnly.value = true;
      viewDetailOnly.value = true;
      totalStokeAdd.value = false;
      totalStoke.value = true;
      openOutput.value = true;
    }
  );
}

/** 删除按钮操作 */
function handleDelete(row) {
  const groupIdArrIds = row.groupIdArr || groupIdArr.value;
  proxy
    .$confirm("是否确认删除此数据?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(function () {
      return Array.isArray(groupIdArrIds)
        ? logicDeleteByIdsOOutput(groupIdArrIds)
        : logicDeleteByWarehouseRecordIdOutput(groupIdArrIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    });
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "导入";
  upload.open = true;
}
/** 下载模板操作 */
function importTemplate() {
  postForExcel(
    "/bdh-agric-invest-api/invest/farmWarehouseOutput/exportTemplate",
    queryParams.value,
    "投入品出库导入模板"
  );
}
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].clearFiles();
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
      response.msg +
      "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getList();
};
/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}

// 导出操作按钮
function handleExport() {
  postForExcel(
    "/bdh-agric-invest-api/invest/farmWarehouseOutput/exportExcel",
    queryParams.value,
    "农场投入品出库管理"
  );
}



//确认出库
function handleConfirm() {
  const warehouseRecordIdList = groupIdArr.value;
  confirmWarehouseOut({
    outboundStatus: 1,
    warehouseRecordIdList: warehouseRecordIdList,
  }).then((response) => {
    proxy.$modal.msgSuccess("确认成功");
    getList();
  });
}

const handleOrgCode = (orgCode) => {
  queryParams.value.orgCode = orgCode.orgCode;
};
const handleOrgCodeForm = (orgCodeInfo) => {
  outputForm.value.orgCode = orgCodeInfo.orgCode;
  outputForm.value.orgName = orgCodeInfo.orgName;
};

const getDict = () => {
  getDicts("year_cd").then((response) => {
    yearOption.value = response.data.sort((a, b) => {
      return Number(b.code) - Number(a.code);
    });
    queryParams.value.year = yearOption.value[0].code;
  });
  getDicts("fertilize_season").then((res) => {
    fertilizeSeasonOption.value = res.data;
  });
  getDicts("outbound_target_type").then((res) => {
    outboundTargetTypeOption.value = res.data.slice(0, 3);
    // queryParams.value.outboundTargetType = outboundTargetTypeOption.value[0].code
  });
  queryAllOrgTree("1").then((res) => {
    var orgList = res.data;
    orgListDetail.value = orgList;
    if (orgList && orgList.length && orgList.length > 0) {
      defaultOrgCode.value = orgList[0].orgCode;
      queryParams.value.orgCode = defaultOrgCode.value;
      // outputForm.value.orgCode= defaultOrgCode.value;
      getList();
    }
  });
  queryAllCompanyInfoInput({}).then((response) => {
    response.data.forEach((each) => {
      companyAbbrOption.value.push({
        code: each.aiCompanyId,
        name: each.companyAbbr,
        wholeName: each.companyName,
      });
    });
  });
  getFarmWarehouseAll({ orgCode: outputForm.value.orgCode }).then((response) => {
    response.data.forEach((each) => {
      farmWarehouseOption.value.push({
        code: each.warehouseId,
        name: each.warehouseName,
        warehouseId: each.warehouseId,
        orgCode: each.orgCode || outputForm.value.orgCode,
        orgName: each.orgName || each.warehouseName,
      });
    });
  });
};

function fertilizeSeasonFormatter(row) {
  var name = "";
  fertilizeSeasonOption.value.forEach((item) => {
    if (item.code == row.fertilizeSeason) {
      name = item.name;
    }
  });
  return name;
}
function outboundTargetTypeFormatter(row) {
  var name = "";
  outboundTargetTypeOption.value.forEach((item) => {
    if (item.code == row.outboundTargetType) {
      name = item.name;
    }
  });
  return name;
}
function outboundProdTypeFormatter(row) {
  var name = "";
  outboundProdTypeOption.value.forEach((item) => {
    if (item.code == row.outboundProdType) {
      name = item.name;
    }
  });
  return name;
}
function outboundStatusFormatter(row) {
  var name = "";
  outboundStatusOption.value.forEach((item) => {
    if (item.code == row.outboundStatus) {
      name = item.name;
    }
  });
  return name;
}
function auditStatusFormatter(row) {
  var name = "";
  auditStatusOption.value.forEach((item) => {
    if (item.code == row.auditStatus) {
      name = item.name;
    }
  });
  return name;
}

function signStatusFormatter(row) {
  var name = "";
  signStatusOption.value.forEach((item) => {
    if (item.code == row.signStatus) {
      name = item.name;
    }
  });
  return name;
}

getDict();
</script>
<style scoped></style>
