<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form :model="queryParams" ref="queryRef" v-show="showSearch" label-width="80px" class="form-line">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年份" prop="year">
                  <el-select v-model="queryParams.year" clearable placeholder="请选择年份">
                      <el-option
                          v-for="dict in yearOption"
                          :key="dict.code"
                          :label="dict.name"
                          :value="dict.code"
                      />
                  </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所在单位" prop="orgCode">
                <org-select @handleOrgCode="handleOrgCode" ref="queryOrgRef" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业名称" prop="aiCompanyId">
                <el-select v-model="queryParams.aiCompanyId"  clearable placeholder="请选择企业简称">
                  <el-option
                      v-for="dict in companyNameOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="作业季节" prop="fertilizeSeason">
                <el-select
                    v-model="queryParams.fertilizeSeason"
                    clearable
                    placeholder="请选择作业季节">
                  <el-option
                      v-for="dict in fertilizes"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="审核状态" prop="auditStatus">
                <el-select v-model="queryParams.auditStatus"   clearable placeholder="全部" >
                  <el-option
                      v-for="dict in auditStatusOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="18" align="right">
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>

        </el-form>
      </el-collapse-transition>
    </div>

    <!-- 多种操作 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['reportInfo:reportTypeSubList']"
        >投入品上报
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            icon="Delete"
            :disabled="multiple"
            @click="handleBatchDelete"
            v-hasPermi="['reportInfo:logicDeleteByIds']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="Search"
          @click="handleReportAll"
          v-hasPermi="['reportInfo:reported']"
        >投入品采购情况
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            icon="Check"
            :disabled="single"
            @click="handleApprove"
            v-hasPermi="['reportInfo:approve']"
        >审核
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            icon="Delete"
            @click="handleBatchDelete"
            v-hasPermi="['reportInfo:logicDeleteByIdsSpec']"
        >删除【运维专用】
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 分页查询表单 -->
    <el-table border :height="tableHeight"  :data="reportInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="ID" align="center" width="55" prop="prodReportId"/>
      <el-table-column label="关联计划ID" align="center" width="100" prop="reportPlanId"/>
      <el-table-column label="年份" align="center" prop="year" width="100" />
      <el-table-column label="作业季节" align="center" :formatter="fertilizeSeasonFmt" prop="fertilizeSeason"/>
      <el-table-column label="所在单位" align="center" prop="orgName"/>
      <el-table-column label="企业名称" align="center" prop="companyAbbr"/>
      <el-table-column label="本次订购数量" align="center" prop="currentNum"/>
      <el-table-column label="上报时间" align="center" prop="reportTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.reportTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="审核状态" prop="auditStatus" :formatter="auditStatuFormat"/>
      <el-table-column label="当前审核人" :show-overflow-tooltip="true" align="center" :formatter="fmtAuditName" />
      <el-table-column label="当前流程节点" align="center" :formatter="fmtAuditNode" />
      <el-table-column label="操作" width="200" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-link v-if="scope.row.auditStatus == -1||scope.row.auditStatus == 4"  type="primary" icon="edit" @click="handleEditView(scope.row)">修改</el-link>
          <el-button
              v-if="scope.row.auditStatus === -1||scope.row.auditStatus === 4"
              type="primary"
              link
              icon="CircleCheck"
              @click="handleStartFlow(scope.row)"
          >发起流程
          </el-button>
          <el-link v-if="scope.row.auditStatus !== -1&&scope.row.auditStatus !== 4" type="primary" icon="View" @click="handleView(scope.row)">查看</el-link>
          <el-link type="danger" icon="Delete" v-hasPermi="['reportInfo:logicDeleteByIds']" v-if="scope.row.auditStatus==4" @click="handleDelete(scope.row)">删除</el-link>
          <el-link type="success" icon="User" @click="viewFlow(scope.row)">查看流程</el-link>
        </template>
      </el-table-column>
    </el-table>

    <!--   分页查询底部栏     -->
    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.rows"
      @pagination="getList"
    />

    <!-- 添加或修改投入品采购上报对话框 -->
    <el-dialog :title="title" v-model="open" :width="1200"   :close-on-click-modal="false" append-to-body>
      <report-detail v-if="flag" ref="detail"  @cancelFn="cancel" @submitFormFn="submitForm"></report-detail>
      <!-- <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="submitFormDis||!open"  @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template> -->
    </el-dialog>


    <el-dialog :title="title" v-model="openInfoViewForEdit" :width="1200"  :close-on-click-modal="false" append-to-body>
      <ReportInfoNewForUpdate :prodReportId="form.prodReportId" ref="editDetail"  @cancelFn="cancel" @submitFormFn="submitFormEdit" v-if="openInfoFlagForEdit"></ReportInfoNewForUpdate>
      <!-- <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="submitFormDis||!openInfoViewForEdit"  @click="submitFormEdit">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template> -->
    </el-dialog>


    <el-dialog :title="title" v-model="openInfoView" :width="1200"  :close-on-click-modal="false" append-to-body>
      <report-info-view :prodReportId="form.prodReportId" v-if="openInfoFlag"></report-info-view>
    </el-dialog>

    <el-dialog :title="title" v-model="openInfoAllView" :width="1200"  :close-on-click-modal="false" append-to-body>
      <report-info-all v-if="openInfoAllFlag"></report-info-all>
    </el-dialog>

    <!--审核-->
    <el-dialog title="审核" v-model="openApproveView"  append-to-body :close-on-click-modal="false">
      <el-form ref="approveForm" :model="form" :rules="rules" label-width="120px">
        <el-form-item label="是否通过" prop="agree">
          <el-radio v-model="form.agree" :label="true">同意</el-radio>
          <el-radio v-model="form.agree" :label="false">不同意</el-radio>
        </el-form-item>
        <el-form-item label="审核意见" prop="opinion">
           <el-input v-model="form.opinion" type="textarea"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary"  @click="submitApproveDebounce">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看流程对话框 -->
    <el-dialog
      v-model="processVisible"
      title="查看流程"
      width="800px"
      :close-on-click-modal="false"
    >
      <FlowDetail v-model="form.instId" v-if="flowDetailTag" ></FlowDetail>
      <template #footer>
                <span class="dialog-footer">
                    <el-button @click="processVisible = false">关闭</el-button>
                </span>
      </template>
    </el-dialog>

  </div>
</template>

<script setup name="reportInfo">

import reportDetail from './reportDetail'
import reportInfoView from './reportInfoView'
import reportInfoAll from './reportInfoAll'
import ReportInfoNewForUpdate from './reportInfoNewForUpdate'
import {approve,listReportInfo, getReportInfo, delReportInfo, addReportInfo,
  delReportInfos, updateReportInfo,getReportTypeSubList,startFlow} from "@/api/bdh-agric-invest/invest/reportInfo";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import FlowDetail from "@/views/bdh-agric-invest/components/FlowDetail/index.vue";
import {allCompanyInfo} from '@/api/bdh-agric-invest/invest/companyInfo'
import { queryAllOrgTree} from "@/api/bdh-agric-invest/system/dict/data";
import {getDicts} from '@/api/bdh-agric-invest/system/dict/data'
import {debounce} from "lodash";
import {ref} from "vue";
const fertilizes = ref([]);
const reportLoading=ref(false)
const {proxy} = getCurrentInstance();
const submitFormDis = ref(false);
const reportInfoList = ref([]);
const flowDetailTag = ref(false);
const open = ref(false);
const openInfoView = ref(false);
const openInfoFlag = ref(true);
const openInfoViewForEdit = ref(false);
const openInfoFlagForEdit = ref(true);
const openInfoAllView = ref(false);
const openInfoAllFlag = ref(true);
const openApproveView = ref(false);
const openApproveFlag = ref(true);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const yearOption = ref([]);
const companyInfoOption = ref([]);
const companyNameOption = ref([]);
const companyNameValue = ref("");
const treeOptions = ref([]);
const auditStatusOption = ref([]);
const processVisible = ref(false);
const detail=ref(null)
const instId=ref(null)
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const selectProps = ref({
  value: "orgCode",
  label: "orgName",
  checkStrictly : true
});
const data = reactive({
  flag:true,
  form: {},
  year: null,
  queryParams: {
    rows: 10,
    page: 1,
    year: null,
    orgName: null,
    orgCode: null,
    aiCompanyId: null,
    companyAbbr: null,
    auditStatus: null,
  },
  rules:{
    agree: [
      {required: true, message: "请选择是否同意", trigger: "change"}
    ],
    opinion:[{message:"请输入审批意见",
      validator:function(rule, value, callback){
        if(!value&&!form.value.agree){
          callback("请输入审核意见！")
        }else{
          callback()
        }
      },blur},{max:100,message: "最多100个字符",trigger:"blur"}]
  }
});
const queryOrgRef = ref()
const defaultOrgCode = ref('');

const {queryParams, form,year,flag,rules} = toRefs(data);

onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});

function fmtAuditName(row,column){
  if(row.flowMsg&&JSON.parse(row.flowMsg).auditNames){
    return JSON.parse(row.flowMsg).auditNames
  }
  return "无";
}

function fertilizeSeasonFmt(row){
  for(let i=0;i<fertilizes.value.length;i++){
    if(fertilizes.value[i].code==row.fertilizeSeason){
      return fertilizes.value[i].name
    }
  }
}

function fmtAuditNode(row,column){
  if(row.flowMsg&&JSON.parse(row.flowMsg).node){
    return JSON.parse(row.flowMsg).node
  }
  return "无";
}

function viewFlow(row){
  form.value=row
  processVisible.value=true
  flowDetailTag.value = false
  nextTick(() => {
    flowDetailTag.value=true;
  });
}


//表格审核状态栏字典转换
function auditStatuFormat(row, column) {
  let auditStatus = null
  if (auditStatusOption.value.length > 0) {
    auditStatusOption.value.forEach((v, i) => {
      if (v.code == row.auditStatus) {
        auditStatus = v.name
      }
    })
  }
  return auditStatus;
}

const handleOrgCode = ({orgCode,orgName})=>{
  queryParams.value.orgCode=orgCode
}
// 获取字典
const getDict = () => {
  //查询年份
  getDicts('year_cd').then(response => {
    yearOption.value = response.data
    queryParams.value.year = yearOption.value[0].code
    queryAllOrgTree("3").then((res) => {
        var orgList = res.data;
        if (orgList && orgList.length && orgList.length > 0) {
            defaultOrgCode.value = orgList[0].orgCode;
            queryParams.value.orgCode = defaultOrgCode.value;
        }
        getList()
    })
  });

  //企业简称
  allCompanyInfo({}).then(response => {
    companyInfoOption.value = response.data
    //console.info(companyInfoOption.value);
    response.data.forEach(each => {
      companyNameOption.value.push({
        code: each.aiCompanyId,
        name: each.companyAbbr
      })
      //console.log(each)
    })
  });

  //审核状态
  getDicts('agric_invest_audit_status').then(response => {
    auditStatusOption.value = response.data
  });

  getDicts("fertilize_season").then((response) => {
    fertilizes.value = response.data
  });
}

/** 查询投入品采购上报列表 */
function getList() {
  if(year.value){
    queryParams.value.year=year.value.getFullYear()
  }
  listReportInfo(queryParams.value).then(response => {
    reportInfoList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  openInfoViewForEdit.value=false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    updateBy: null,
    updateTime: null,
    prodReportId: null,
    reportPlanId: null,
    year: null,
    orgCode: null,
    orgName: null,
    aiCompanyId: null,
    reportTime: null,
    auditStatus: null,
    auditPerson: null,
    remark: null,
    createBy: null,
    createTime: null,
    statusCd: null,
    detail:null,
    agree:true,
    opinion:null
  };
  proxy.resetForm("reportInfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  // queryParams.value.rows = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  year.value=null
  proxy.$refs['queryOrgRef'].clear()
  proxy.resetForm("queryRef");
  queryParams.value.year = yearOption.value[0].code;
  queryParams.value.orgCode = defaultOrgCode.value;
  queryOrgRef.value.setValue(defaultOrgCode.value, "");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.prodReportId);
  single.value = selection.length != 1;
  if(selection.length != 1){
    single.value=true;
  }else{
    //auditStatus==0
    let canAudit=[]
    canAudit=selection.filter(item=>item.auditStatus==0 || item.auditStatus==-1)
    single.value=!(canAudit.length==1)
    if(!single.value){
      instId.value=selection.map(item => item.instId)[0]
    }
  }
  //审核不通过的才可以删除
  let canRemove=[];
  if(selection&&selection.length){
    //如果选择了，就判断一下选择的数据里 是否有不允许删除的变量
    //2022-11-25 改为也可以删除 auditStatus = null 即流程为未发起的数据
    canRemove=selection.filter(item=>item.auditStatus==4 || item.auditStatus==-1)
    multiple.value = !(canRemove&&canRemove.length==selection.length)
  }else{
    multiple.value = true
  }
}

function handleApprove(){
  if(ids.value){
    form.value.prodReportId=ids.value[0]
    form.value.instId=instId;
    form.value.agree=true
    form.value.opinion=null
    openApproveView.value=true;
  }
}

const submitApproveDebounce = debounce(submitApproveForm,500);
function submitApproveForm(){
  proxy.$refs["approveForm"].validate(valid => {
    if (valid) {
      approve({'instId':form.value.instId,'opinion':form.value.opinion,'agree':form.value.agree,'prodReportId':form.value.prodReportId}).then(response=>{
        if(response.success){
          openApproveView.value=false;
          proxy.$modal.msgSuccess("成功!");
          getList();
        }
      })
    }
  })

}


/** 新增按钮操作 */
function handleAdd() {
  reset();
  submitFormDis.value=false
  flag.value=false;
  nextTick(() => {
    flag.value=true;
  });
  open.value = true;
  title.value = "添加投入品采购上报";
}

/** 查看按钮操作 */
function handleView(row) {
  reset();
  const prodReportId = row.prodReportId || ids.value
  getReportInfo(prodReportId).then(response => {
    form.value = response.data;
    openInfoFlag.value=false;
    nextTick(() => {
      openInfoFlag.value=true;
    });
    title.value = "查看投入品采购上报";
    openInfoView.value=true;
  });
}

/** 修改按钮操作 */
function handleEditView(row) {
  reset();
  const prodReportId = row.prodReportId || ids.value
  getReportInfo(prodReportId).then(response => {
    form.value = response.data;
    if(form.value.auditStatus!=-1&&form.value.auditStatus!=4){
      proxy.$modal.msgError("当前工单状态已变化不允许修改，请刷新列表页面重新查看!");
      return;
    }
    openInfoFlagForEdit.value=false;
    nextTick(() => {
      openInfoFlagForEdit.value=true;
    });
    title.value = "修改投入品采购上报";
    openInfoViewForEdit.value=true;
  });
}


function handleReportAll(){
  openInfoAllFlag.value=false;
  nextTick(() => {
    openInfoAllFlag.value=true;
  });
  title.value = "查看投入品采购上报";
  openInfoAllView.value=true;
}

/** 提交按钮 */
const submitFormDebounce = debounce(submitForm,100);
function submitForm() {

  proxy.$refs.detail.validForm((form)=>{
    proxy.$refs.detail.doloading();
    var obj={};
    Object.assign(obj,form);
    obj.details=obj.details.filter(m=>{
      return m.currentNum&&m.currentNum!=0
    })
    addReportInfo(obj).then(response=>{
      if(response.success){
        proxy.$modal.msgSuccess("成功!");
        open.value = false;
        getList();
      }else{
        proxy.$modal.msgError(response.msg)
      }
      proxy.$refs.detail.unloading();
    }).catch(e=>{
      proxy.$refs.detail.unloading();
    })
  });


}

import {bignumber, evaluate, multiply} from 'mathjs'
function isInteger(obj) {
  return typeof obj === 'number' && obj%1 === 0
}
function chkCurrentNum(row){

  if(row.currentNum&&row.transNum==-1&&!isInteger(Number(row.currentNum))){
    proxy.$modal.msgError("商品["+row.aiTypeSubName+"]本次订购量必须是整数")
    return true
  }

  if(row.currentNum&&row.aiTypeSubWeight&&row.transNum&&row.transNum!=-1){
    var a=multiply(bignumber(row.currentNum),bignumber(row.transNum))
    var b=bignumber(row.aiTypeSubWeight)
    //console.info('a:'+a+'b:'+b);
    //console.info('a mod b: isZero',a.mod(b).isZero());
    if(!a.mod(b).isZero()){
      proxy.$modal.msgError("商品["+row.aiTypeSubName+"]本次订购量必须是商品重量的整数倍")
      return true;
    }
  }
  return false
}

function submitFormEdit() {

  proxy.$refs.editDetail.validForm((form)=>{
    console.info('out form,',form);
    proxy.$refs.editDetail.doloading();
    console.info('out form,',form);
    form.details=form.details.filter(m=>{
      return m.currentNum&&m.currentNum!=0
    })
    addReportInfo(form).then(response=>{
      if(response.success){
        proxy.$modal.msgSuccess("成功!");
        openInfoViewForEdit.value = false;
        getList();
      }else{
        proxy.$modal.msgError(response.msg)
      }
      proxy.$refs.editDetail.unloading();
    }).catch(e=>{
      proxy.$refs.editDetail.unloading();
    })
  });

}



function handleBatchDelete() {
  const prodReportIds =  ids.value;
  if(!ids.value||ids.value.length==0){
    proxy.$modal.msgError("请选择待删除的数据");
    return;
  }

  proxy.$modal.confirm('是否确认删除？').then(function () {
    return delReportInfos(prodReportIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const prodReportIds = row.prodReportId || ids.value;
  proxy.$modal.confirm('是否确认删除？').then(function () {
    return delReportInfo(prodReportIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('invest/reportInfo/export', {
    ...queryParams.value
  }, `reportInfo_${new Date().getTime()}.xlsx`)
}

function handleStartFlow(data){

  proxy.$modal.confirm('确认发起流程么？').then(function () {
    return startFlow({"prodReportId":data.prodReportId});
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("发起流程成功");
  }).catch(() => {
  });
}

getDict();
</script>

<style scoped>
.el-link {
  margin-right: 8px;
}
.el-link .el-icon--right.el-icon {
  vertical-align: text-bottom;
}
</style>
