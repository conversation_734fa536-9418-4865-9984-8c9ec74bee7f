<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form :model="queryParams" ref="queryRef" v-show="showSearch" label-width="80px" class="form-line">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="仓库名称" prop="storehouseName">
                <el-input v-model="queryParams.storehouseName" clearable placeholder="请输入仓库名称"
                  @keyup.enter="handleQuery" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业简称" prop="aiCompanyId">
                <el-select style="width: 100%" v-model="queryParams.aiCompanyId" placeholder="请选择企业简称">
                  <el-option v-for="dict in companyAbbrOption" :key="dict.aiCompanyId" :label="dict.companyAbbr"
                    :value="dict.aiCompanyId" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="服务范围" prop="orgCode" style="width: 100%">
                <org-select style="width: 100%" ref="queryOrgRef" show-all-levels="false" clearable
                  placeholderText="请选择服务范围" :defaultOrgCode="true" @handleOrgCode="handleOrgCodeQuery" :level="3" />
              </el-form-item>
            </el-col>
            <el-col :span="6" align="right">
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8" style="min-height: 32px;">
      <el-col :span="1.5">
        <el-button type="primary" icon="Plus" @click="handleAdd" v-hasPermi="['storehouseInfo:insert']">新增
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table border :height="tableHeight" :data="bulletinConfInfoList">
      <el-table-column type="index" width="80" label="序号" />
      <el-table-column align="center" label="库点名称" prop="storehouseName" />
      <el-table-column align="center" label="所属机构" prop="companyName" />
      <el-table-column align="center" label="仓库管理员" prop="managerName" :formatter="scopLstFmtManger" />
      <el-table-column align="center" label="服务范围" prop="scopLst" :formatter="scopLstFmt" />
      <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="250px">
        <template #default="scope">
          <el-button link size="small" type="primary" @click="handleUpdate(scope.row)"
            v-hasPermi="['storehouseInfo:update']">修改
          </el-button>
          <el-button link size="small" type="primary" @click="handleDelete(scope.row)"
            v-hasPermi="['storehouseInfo:logicDeleteById']">删除
          </el-button>
          <el-button link size="small" type="primary" @click="handleProductinfo(scope.row)"
            v-hasPermi="['storehouseProdInfo:queryByPage']">查看商品及库存
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.rows"
      @pagination="getList" />
    <!-- 添加或修改仓库信息管理表对话框 -->
    <el-dialog :title="title" v-model="open" width="514px" append-to-body :close-on-click-modal="false" @closed="cancel"
      :destroy-on-close="true">
      <div class="form-wrapper">
        <el-form ref="bulletinConfInfoRef" :disabled="updateType == 'view'" :model="form" :rules="rules"
          label-width="100px" label-position="top">
          <el-form-item label="库点名称" prop="storehouseName">
            <el-input v-model="form.storehouseName" clearable placeholder="请输入仓库名称" maxlength="40" />
          </el-form-item>
          <el-form-item label="所属机构" prop="aiCompanyId">
            <el-select style="width: 100%" disabled v-model="form.aiCompanyId" clearable placeholder="请选择所属机构">
              <el-option v-for="dict in companyAbbrOption" :key="dict.aiCompanyId" :label="dict.companyAbbr"
                :value="dict.aiCompanyId" />
            </el-select>
          </el-form-item>
          <el-form-item label="服务范围" prop="orgCode" class="item">
            <el-tree-select v-model="form.orgCode" :data="orgList" multiple :render-after-expand="false" show-checkbox
              check-strictly check-on-click-node :props="orgTreeProp" :node-key="orgTreeProp.value"
              @check="handleNodeClick" @removeTag="handleRemoveNode" placeholder="请选择服务范围" />
          </el-form-item>
          <el-form-item label="仓库管理员" prop="managerId">
            <el-select style="width: 100%" v-model="form.managerId" multiple collapse-tags clearable filterable
              placeholder="请选择仓库管理员">
              <el-option v-for="dict in allWarehouseKeeperList" :key="dict.staffId" :label="dict.staffName"
                :value="dict.staffId" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" v-if="updateType == 'update'" @click="submitDebounce">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 关联商品 -->
    <el-dialog title="关联商品" v-model="openCorrelation" width="960px" append-to-body :close-on-click-modal="false"
      :destroy-on-close="true" @closed="cancelCorrelation">
      <el-form :model="queryParamsCorrelation" ref="queryCorrelationRef" label-width="80px" class="form-line">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="商品名称" prop="aiTypeSubName">
              <el-input v-model="queryParamsCorrelation.aiTypeSubName" clearable placeholder="请输入商品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="商品规格" prop="aiTypeSubSpecs">
              <el-input v-model="queryParamsCorrelation.aiTypeSubSpecs" clearable placeholder="请输入商品规格" />
            </el-form-item>
          </el-col>
          <el-col :span="8" align="right">
            <el-button icon="Refresh" @click="resetQueryCorrelation">重置</el-button>
            <el-button type="primary" icon="Search" @click="handleQueryCorrelation">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button @click="addstoreAll" :disabled="multipleCorrelation"
            v-hasPermi="['storehouseProdInfo:batchAddRel2store']">批量添加入库
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button @click="deletestoreAll" :disabled="multipleCorrelation"
            v-hasPermi="['storehouseProdInfo:logicDeleteByIds']">批量清空出库
          </el-button>
        </el-col>
      </el-row>
      <el-row :gutter="10" class="mb8">
        <div>
          库点名称：<span class="link">{{
            formProductInfo.storehouseName
          }}</span>
        </div>
      </el-row>
      <el-table border :height="500" :data="correlationList" ref="tableProductRef"
        @selection-change="handleSelectionCorrelationChange" row-key="aiTypeSubId">
        <el-table-column align="center" type="selection" width="55" reserve-selection />
        <el-table-column align="center" label="商品名称" prop="aiTypeSubName" />
        <el-table-column align="center" label="商品编号" prop="aiTypeSubCode" />
        <el-table-column align="center" label="规格" prop="aiTypeSubSpecs" />
        <el-table-column align="center" label="重量" prop="aiTypeSubWeight">
          <template #default="scope">
            {{ scope.row.aiTypeSubWeight }}{{ scope.row.aiTypeSubUnit }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="外部编码" prop="outsideCode" />
        <el-table-column align="center" label="总入库" prop="totalInStock" />
        <el-table-column align="center" label="总出库" prop="totalOutStock" />
        <el-table-column align="center" label="现库存" prop="curInStock" />
        <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="250px">
          <template #default="scope">
            <el-button link size="small" type="primary" @click="addstore(scope.row)"
              v-hasPermi="['storehouseProdInfo:addRel2store']" v-if="!scope.row.storehouseProdId">添加入库
            </el-button>
            <el-button link size="small" type="primary" @click="deletestore(scope.row)"
              v-hasPermi="['storehouseProdInfo:logicDeleteById']" v-if="scope.row.storehouseProdId">清空出库
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="totalCorrelation > 0" :total="totalCorrelation" v-model:page="queryParamsCorrelation.page"
        v-model:limit="queryParamsCorrelation.rows" @pagination="getCorrelationList" />
    </el-dialog>
    <!-- 查看商品及库存 -->
    <el-dialog title="商品及库存信息" v-model="openProductInfo" width="514px" append-to-body :close-on-click-modal="false"
      :destroy-on-close="true" @closed="cancelProductInfo" fullscreen>
      <el-form :model="queryParamsProductInfo" ref="queryProductInfoRef" label-width="80px" class="form-line">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="商品名称" prop="aiTypeSubName">
              <el-input v-model="queryParamsProductInfo.aiTypeSubName" clearable placeholder="请输入商品名称" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="商品规格" prop="aiTypeSubSpecs">
              <el-input v-model="queryParamsProductInfo.aiTypeSubSpecs" clearable placeholder="请输入商品规格" />
            </el-form-item>
          </el-col>
          <el-col :span="12" align="right">
            <el-button icon="Refresh" @click="resetQueryProductInfo">重置</el-button>
            <el-button type="primary" icon="Search" @click="handleQueryProductInfo">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button @click="handleCorrelation" v-hasPermi="['storehouseProdInfo:queryAllProByPage']">关联商品 </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button @click="handleBatchIn" :disabled="multiple"
            v-hasPermi="['storehouseProdInfo:manualStoreBatchInput']">入库
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button @click="handleBatchOut" :disabled="multiple"
            v-hasPermi="['storehouseProdInfo:manualStoreBatchOutput']">出库
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button @click="handleImportIn" v-hasPermi="['storehouseProdInfo:excelInput']">excel批量入库 </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button @click="handleImportOut" v-hasPermi="['storehouseProdInfo:excelOutput']">excel批量出库 </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button @click="handleExport" v-hasPermi="['storehouseProdInfo:exportProdInfo']">导出 </el-button>
        </el-col>
      </el-row>
      <el-row :gutter="10" class="mb8">
        <div>
          库点名称：<span class="link">{{
            formProductInfo.storehouseName
          }}</span>
        </div>
        <div>
          所属机构：<span class="link"> {{ formProductInfo.companyName }}</span>
        </div>
        <div>
          服务范围：<span class="link">{{ formatOrgName() }}</span>
        </div>
        <div style="margin-left: auto">{{ totalProductInfo }}个商品</div>
      </el-row>
      <el-table border :height="tableHeight - 20" :data="productInfoList" ref="tableProductInfoRef"
        @selection-change="handleSelectionChange" row-key="aiTypeSubId">
        <el-table-column align="center" type="selection" width="55" reserve-selection />
        <el-table-column align="center" label="商品名称" prop="aiTypeSubName" />
        <el-table-column align="center" label="商品编号" prop="aiTypeSubCode" />
        <el-table-column align="center" label="规格" prop="aiTypeSubSpecs" />
        <el-table-column align="center" label="重量" prop="aiTypeSubWeight">
          <template #default="scope">
            {{ scope.row.aiTypeSubWeight }}{{ scope.row.aiTypeSubUnit }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="外部编码" prop="outsideCode" />
        <el-table-column align="center" label="总入库" prop="totalInStock" />
        <el-table-column align="center" label="总出库" prop="totalOutStock" />
        <el-table-column align="center" label="现库存" prop="curInStock" />
        <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="250px">
          <template #default="scope">
            <el-button link size="small" type="primary" @click="handleStoreOut(scope.row)" v-if="scope.row.curInStock > 0"
              v-hasPermi="['storehouseProdInfo:manualStoreOutput']">出库
            </el-button>
            <el-button link size="small" type="primary" @click="handleStoreIn(scope.row)"
              v-hasPermi="['storehouseProdInfo:manualStoreInput']">入库
            </el-button>
            <el-button link size="small" type="primary" @click="getStorehouseProdId(scope.row)">出入库记录
            </el-button>
            <el-button link size="small" type="primary" @click="handleProdsell(scope.row)">查看销售去向
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="totalProductInfo > 0" :total="totalProductInfo" v-model:page="queryParamsProductInfo.page"
        v-model:limit="queryParamsProductInfo.rows" @pagination="getProductInfoList" />
    </el-dialog>
    <!-- 入库弹窗 -->
    <el-dialog v-model="openIn" title="入库" append-to-body width="514px" :destroy-on-close="true" @closed="cancelIn">
      <el-form ref="formOutRef" :model="formOut" :rules="rules" label-width="80px" label-position="top">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="原库存" prop="curInStock">
              <el-input disabled v-model="formOut.curInStock" clearable placeholder="" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="入库数量" prop="count">
              <el-input v-model="formOut.count" clearable @input="formInCountChange2" placeholder="请输入入库数量" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="入库后" prop="new">
              <el-input disabled v-model="formOut.new" clearable placeholder="" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitIn">确 定</el-button>
          <el-button @click="cancelIn">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 出库弹窗 -->
    <el-dialog v-model="openOut" title="出库" append-to-body width="514px" :destroy-on-close="true" @closed="cancelOut">
      <el-form ref="formOutRef" :model="formOut" :rules="rules" label-width="80px" label-position="top">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="原库存" prop="curInStock">
              <el-input disabled v-model="formOut.curInStock" clearable placeholder="" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="出库数量" prop="count">
              <el-input v-model="formOut.count" clearable @input="formOutCountChange2" placeholder="请输入出库数量" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="出库后" prop="new">
              <el-input disabled v-model="formOut.new" clearable placeholder="" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitOut">确 定</el-button>
          <el-button @click="cancelOut">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 出入库记录弹窗 -->
    <el-dialog v-model="openRecords" title="出入库记录" append-to-body width="960px" :destroy-on-close="true"
      @closed="cancelRecords">
      <div style="margin-bottom: 10px">服务范围：{{ formatOrgName() }}</div>
      <el-row :gutter="10" style="margin-bottom: 10px;">
        <el-col :span="8" style="margin-bottom: 10px;font-weight: 600;">
          总入库：{{ formRecords.totalInStock }}
        </el-col>
        <el-col :span="8" style="font-weight: 600;"> 总出库：{{ formRecords.totalOutStock }} </el-col>
        <el-col :span="8" style="font-weight: 600;"> 现库存：{{ formRecords.curInStock }} </el-col>
        <el-col :span="12" style="margin-bottom: 10px;color: rgb(0, 0, 255);">
          商城入库小计：{{ formRecords.shopInStock }}
        </el-col>
        <el-col :span="12" style="color: rgb(0, 0, 255);">
          商城出库小计：{{ formRecords.shopOutStock }}
        </el-col>
        <el-col :span="12" style="color: rgb(0, 0, 255);">
          人工入库小计：{{ formRecords.laborInStock }}
        </el-col>
        <el-col :span="12" style="color: rgb(0, 0, 255);">
          人工出库小计：{{ formRecords.laborOutStock }}
        </el-col>
      </el-row>
      <div style="margin-bottom: 10px">人工出入库记录：</div>
      <el-table border :data="formRecords.records">
        <el-table-column align="center" label="操作人" prop="operatePerson" />
        <el-table-column align="center" label="操作时间" prop="operateTime" width="180">
          <template #default="scope">
            <span>{{
              parseTime(scope.row.operateTime, "{y}-{m}-{d} {h}:{i}:{s}")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="出库/入库" prop="operateType">
          <template #default="scope">
            <span v-if="scope.row.operateType == '02'">入库</span>
            <span v-else>出库</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="原库存" prop="originalStock" />
        <el-table-column align="center" label="出入库数量" prop="operateStock" />
        <el-table-column align="center" label="现库存" prop="curStock" />
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelRecords">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 批量入库弹窗 -->
    <el-dialog v-model="openBatchIn" title="入库" append-to-body width="960px" :destroy-on-close="true"
      @closed="cancelBatchIn">
      <div>添加以下商品入库信息，请填写入库数量</div>
      <el-form ref="formBatchRef" :model="formBatch" :rules="rules" label-width="120px">
        <el-table border :data="formBatch.listBatch" ref="tableBatchRef" :height="300">
          <el-table-column type="index" width="60" label="序号" />
          <el-table-column align="center" label="商品名称" prop="aiTypeSubName" />
          <el-table-column align="center" label="商品编号" prop="aiTypeSubCode" />
          <el-table-column align="center" label="规格" prop="aiTypeSubSpecs" />
          <el-table-column align="center" label="重量" prop="aiTypeSubWeight">
            <template #default="scope">
              {{ scope.row.aiTypeSubWeight }}{{ scope.row.aiTypeSubUnit }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="原库存" prop="curInStock" />
          <el-table-column align="center" label="入库数量" prop="count" width="160">
            <template #default="scope">
              <el-form-item label-width="0px" :prop="`listBatch.${scope.$index}.count`" :rules="rules.count">
                <el-input v-model="scope.row.count" placeholder="请输入入库数量" clearable
                  @input="formInCountChange($event, scope.row)" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column align="center" label="入库后" prop="new" />
        </el-table>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchIn">确 定</el-button>
          <el-button @click="cancelBatchIn">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 批量出库弹窗 -->
    <el-dialog v-model="openBatchOut" title="出库" append-to-body width="960px" :destroy-on-close="true"
      @closed="cancelBatchOut">
      <div>添加以下商品出库信息，请填写出库数量</div>
      <el-form ref="formBatchRef" :model="formBatch" :rules="rules" label-width="120px">
        <el-table border :data="formBatch.listBatch" ref="tableBatchRef" :height="300">
          <el-table-column type="index" width="60" label="序号" />
          <el-table-column align="center" label="商品名称" prop="aiTypeSubName" />
          <el-table-column align="center" label="商品编号" prop="aiTypeSubCode" />
          <el-table-column align="center" label="规格" prop="aiTypeSubSpecs" />
          <el-table-column align="center" label="重量" prop="aiTypeSubWeight">
            <template #default="scope">
              {{ scope.row.aiTypeSubWeight }}{{ scope.row.aiTypeSubUnit }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="原库存" prop="curInStock" />
          <el-table-column align="center" label="出库数量" prop="count" width="160">
            <template #default="scope">
              <el-form-item label-width="0px" :prop="`listBatch.${scope.$index}.count`" :rules="rules.count"
                v-if="scope.row.curInStock > 0">
                <el-input v-model="scope.row.count" placeholder="请输入出库数量" clearable
                  @input="formOutCountChange($event, scope.row)" />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column align="center" label="出库后" prop="new" />
        </el-table>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchOut">确 定</el-button>
          <el-button @click="cancelBatchOut">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="800px" append-to-body :close-on-click-modal="false">
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers" :action="upload.url +
        '?updateSupport=' +
        upload.updateSupport +
        '&storehouseId=' +
        formProductInfo.storehouseId
        " :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
        :auto-upload="false" drag>
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text"><em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip"></div>
            <span>仅允许导入xls、xlsx格式文件。为保证商品信息正常识别，请按照模板格式填写。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 商品销售去向 -->
    <el-dialog title="商品销售去向" v-model="openProdsell" width="514px" append-to-body :close-on-click-modal="false"
      :destroy-on-close="true" @closed="cancelProdsell" fullscreen>
      <el-form :model="queryParamsProdsell" ref="formProdsellRef" label-width="80px">
        <el-row :gutter="20">
          <el-col :span="6" class="queryOrgRef">
            <el-form-item label="销售去向" prop="orgCode" style="width: 100%">
              <org-select style="width: 100%" ref="queryProdsellRef" clearable placeholderText="请选择销售去向"
                :defaultOrgCode="true" @handleOrgCode="handleProdsellCodeQuery" :level="3" />
            </el-form-item>
          </el-col>
          <el-col :span="10" style="display: flex; align-items: center">
            <el-form-item label="商品指导价格" prop="guidePriceBg" style="width: calc(50% - 15px)" label-width="120px">
              <el-input v-model="queryParamsProdsell.guidePriceBg" clearable placeholder="请输入商品指导价格" />
            </el-form-item>
            <div style="width: 20px; padding: 0 5px 15px">~</div>
            <el-form-item label="" prop="guidePriceEd" style="width: calc(50% - 135px)" label-width="0px">
              <el-input v-model="queryParamsProdsell.guidePriceEd" clearable placeholder="请输入商品指导价格" />
            </el-form-item>
          </el-col>
          <el-col :span="8" align="right">
            <el-button icon="Refresh" @click="resetQueryProdsell">重置</el-button>
            <el-button type="primary" icon="Search" @click="handleQueryProdsell">搜索</el-button>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button @click="addDirections" v-hasPermi="['invest:prodsell:addOrUpdate']">添加销售去向 </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button @click="handlePrice" v-hasPermi="['invest:prodsell:price']">{{ priceEdit ? '设置完成' : '批量设置价格' }}
            </el-button>
          </el-col>
        </el-row>
        <el-row :gutter="10" class="mb8">
          <div>
            商品名称：<span class="link">{{ formProdsell.aiTypeSubName }}</span>
          </div>
          <div>
            规格：<span class="link"> {{ formProdsell.aiTypeSubSpecs }}</span>
          </div>
          <div>
            商品重量：<span class="link">{{ formProdsell.aiTypeSubWeight }}{{ formProdsell.aiTypeSubUnit }}</span>
          </div>
          <div>
            库点名称：<span class="link">{{ formProdsell.storehouseName }}</span>
          </div>
        </el-row>
        <!-- @selection-change="handleSelectionProdsellChange" -->
        <el-table border :height="tableHeight - 20" :data="queryParamsProdsell.ProdsellList" ref="tableProdsellRef"
          row-key="aiTypeSubId">
          <el-table-column align="center" label="商品名称" prop="aiTypeSubName" />
          <el-table-column align="center" label="规格" prop="aiTypeSubSpecs" />
          <el-table-column align="center" label="重量" prop="aiTypeSubWeight">
            <template #default="scope">
              {{ scope.row.aiTypeSubWeight }}{{ scope.row.aiTypeSubUnit }}
            </template>
          </el-table-column>
          <el-table-column align="center" label="销售去向" prop="sellPlaces" :formatter="sellPlacesFmt" />
          <el-table-column align="center" label="商品指导价格(元)" prop="guidePrice">
            <template #default="scope">
              <el-form-item v-show="scope.row.isedit" label-width="0px" :prop="`ProdsellList.${scope.$index}.guidePrice`"
                :rules="rules.aiGuidingPrice">
                <el-input v-model="scope.row.guidePrice" placeholder="请输入商品指导价格" clearable />
              </el-form-item>
              <span v-show="!scope.row.isedit">{{ scope.row.guidePrice }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" class-name="small-padding fixed-width" label="操作" width="250px">
            <template #default="scope">
              <el-button link size="small" type="primary" @click="handleGuidePrice(scope.row, scope.$index)"
                v-hasPermi="['invest:prodsell:price']">{{
                  scope.row.isedit ? "确定" : "设置指导价格" }}
              </el-button>
              <el-button link size="small" type="primary" @click="handleProdSellIdDelete(scope.row)"
                v-hasPermi="['invest:prodsell:logicDeleteById']">删除销售去向
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <pagination v-show="totalProdsell > 0" :total="totalProdsell" v-model:page="queryParamsProdsell.page"
        v-model:limit="queryParamsProdsell.rows" @pagination="getProdsellList" />
    </el-dialog>
    <!-- 销售去向选择 -->
    <el-dialog v-model="openDirection" title="添加销售去向" append-to-body width="1200px" :destroy-on-close="true"
      @closed="cancelDirection">
      <el-form :model="formProdsell" ref="formDirectionRef" label-width="80px" :rules="rules">
        <el-row :gutter="20">
          <el-col :span="8" style="display: flex; align-items: center">
            <el-form-item label="商品指导价格" prop="guidePrice" style="width: 100%" label-width="120px"
              :rules="rules.aiGuidingPrice">
              <el-input v-model="formProdsell.guidePrice" clearable placeholder="请输入商品指导价格" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-tree :data="orgList" :props="orgTreeProp" show-checkbox check-strictly @check="check" ref="orgRef"
        node-key="orgCode" />
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitDirection">确 定</el-button>
          <el-button @click="cancelDirection">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="/invest/storehouseInfo/queryByPage">
import {
  storeQueryByPage,
  queryAllWarehouseKeeper,
  insert,
  update,
  logicDeleteById,
  storehouseProdInfoQueryByPage,
  manualStoreOutput,
  manualStoreInput,
  storehouseProdId,
  manualStoreBatchInput,
  manualStoreBatchOutput,
  queryAllProByPage,
  addRel2store,
  deleteStorehouse,
  addRel2storeAll,
  deleteStorehouseAll,
  prodsellQueryByPage,
  addOrUpdate,
  prodSellIdDeleteById,
  addOrUpdateProdSells
} from "@/api/bdh-agric-invest/invest/storehouseInfo";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";

import { updateproducetypesub } from "@/api/bdh-agric-invest/invest/producetypesub";
import { postForExcel } from "@/api/bdh-agric-invest/invest/excel";
import { getCurrentInstance, nextTick, reactive, ref, toRefs } from "vue";
import { getCompanyAbbrList } from "@/api/bdh-agric-invest/invest/bulletinConfInfoCompany";
import { allCompanyInfo } from "@/api/bdh-agric-invest/invest/companyInfo";
const { proxy } = getCurrentInstance();
import { debounce, map } from "lodash";
import { queryAllOrgTree } from "@/api/bdh-agric-invest/system/dict/data";
import { getDicts } from "@/api/bdh-agric-invest/system/dict/data";
import { getToken } from "@/utils/auth";
const bulletinConfInfoList = ref([]);
const managerList = ref([]);
const open = ref(false);
const openProductInfo = ref(false);
const openProdsell = ref(false);
const openDirection = ref(false);


const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const yearOption = ref([]);
const selectionProdsellData = ref([]);

const single = ref(true);
const singleProdsell = ref(true);

const multiple = ref(true);
const multipleProdsell = ref(true);


const total = ref(0);
const totalProductInfo = ref(0);
const totalProdsell = ref(0);

const totalCorrelation = ref(0);

const openOut = ref(false);

const openIn = ref(false);
const openBatchIn = ref(false);
const openBatchOut = ref(false);
const openRecords = ref(false);
const openCorrelation = ref(false);

const priceEdit = ref(false);


const title = ref("");
const keyNum = ref(0);
const rootOrgCode = ref(null);
const orgList = ref([]);
const productInfoList = ref([]);

const correlationList = ref([]);

const storehouseProdIdList = ref([]);

const allWarehouseKeeperList = ref([]);
const selectionData = ref([]);
const selectionCorrelationData = ref([]);
const singleCorrelation = ref(true);
const multipleCorrelation = ref(true);
const updateType = ref("update");
const orgTreeProp = ref({
  value: "orgCode",
  label: "orgName",
  children: "children", // 子级字段名
});
/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { "access-token": getToken() },
  // 上传的地址
  url:
    window.VITE_APP_BASE_API + "/bdh-agric-invest-api/invest/storehouseProdInfo/excelInput",
});
const data = reactive({
  // 01出库 02 入库
  operateType: null,
  formBatch: {
    listBatch: [],
  },
  formRecords: {
    records: [],
  },
  formOut: {
    aiTypeSubId: null,
    storehouseId: null,
    curInStock: null,
    count: null,
    new: 0,
  },
  formProductInfo: {},
  formProdsell: {},
  form: {
    storehouseName: null,
    aiCompanyId: null,
    scopLst: null,
  },
  queryParamsCorrelation: {
    aiTypeSubName: null,
    aiTypeSubSpecs: null,
    page: 1,
    rows: 10,
    storehouseId: null,
  },
  queryParamsProductInfo: {
    aiTypeSubName: null,
    aiTypeSubSpecs: null,
    page: 1,
    rows: 10,
    storehouseId: null,
  },
  queryParamsProdsell: {
    storehouseId: null,
    aiTypeSubId: null,
    page: 1,
    rows: 10,
    guidePriceBg: null,
    guidePriceEd: null,
    params: {
      orgCodes: [],
    },
  },
  queryParams: {
    rows: 10,
    page: 1,
    aiCompanyId: null,
    orgCode: null,
    storehouseName: null,
    params: {
      orgCodes: [],
    },
  },
  rules: {
    storehouseName: [
      { required: true, message: "请输入仓库名称", trigger: "blur" },
    ],
    aiCompanyId: [
      { required: true, message: "请选择企业简称", trigger: "blur" },
    ],
    orgCode: [
      {
        type: "array",
        required: true,
        message: "请选择服务范围",
        trigger: "change",
      },
    ],
    managerId: [
      {
        type: "array",
        required: true,
        message: "请选择仓库管理员",
        trigger: "change",
      },
    ],
    aiGuidingPrice: [
      {
        required: true,
        message: "请输入商品指导价格",
        trigger: ["change", "blur"],
      },
      {
        pattern: true,
        validator: (rule, value, callback) => {
          var reg = /^([0-9]{1}|^[1-9]{1}\d{1,15})(\.\d{1,2})?$/;
          if (value === null || value === "" || typeof value == "undefined") {
            callback(new Error("价格必须输入数字"));
          } else if (
            reg.test(value) &&
            value.toString().split(".")[0].length > 5
          ) {
            callback(new Error("数字过大，请确认"));
          } else if (reg.test(value)) {
            callback();
          } else {
            callback(new Error("请输入数字(可带两位小数)"));
          }
        },
        trigger: ["change", "blur"],
      },
    ],
    count: [
      { required: true, message: "请输入数量", trigger: ["blur"] },
      {
        pattern: true,
        validator: (rule, value, callback) => {
          var reg = /^([1-9]{1}|^[1-9]{1}\d{1,15})$/;
          if (value === null || value === "" || typeof value == "undefined") {
            callback(new Error("数量必须输入数字"));
          } else if (
            reg.test(value) &&
            value.toString().split(".")[0].length > 8
          ) {
            callback(new Error("数字过大，请确认"));
          } else if (reg.test(value)) {
            callback();
          } else {
            callback(new Error("请输入正整数"));
          }
        },
        trigger: ["blur", "change"],
      },
    ],
  },
});
const {
  queryParams,
  form,
  rules,
  queryParamsProductInfo,
  formProductInfo,
  formOut,
  formRecords,
  formBatch,
  operateType,
  queryParamsCorrelation,
  queryParamsProdsell,
  formProdsell,
} = toRefs(data);
const getOrgTree = () => {
  queryAllOrgTree("3").then((res) => {
    orgList.value = res.data;
    if (orgList.value && orgList.value.length && orgList.value.length > 0) {
      rootOrgCode.value = orgList.value[0].orgCode;
    }
  });
};
const fileType = ["pdf"];
const queryDateRange = ref([]);
const companyAbbrOption = ref([]);
//发布状态 已发布1 未发布0
const isPublish = [
  {
    value: 0,
    label: "未发布",
  },
  {
    value: 1,
    label: "已发布",
  },
];
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const qorgRef = ref();
const defaultOrgCode = ref("");

const submitDebounce = debounce(submitForm, 500);
const checkNodes = ref([]);
onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 220
    : window.innerHeight - 220;
  // proxy.updateButtonStyle();
});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 220
    : window.innerHeight - 220;
});
// 取消子级选中
function uncheck(element) {
  element.forEach((el) => {
    proxy.$refs['orgRef'].setChecked(el, false)
    if (el.children) {
      uncheck(el.children);
    }
  });
}
// 递归添加属性
function bindkey(element, bool = false, parent) {
  element.forEach((el) => {
    el.disabled = bool
    el.parent = parent
    if (el.children) {
      bindkey(el.children, bool, el);
    }
  });
}
function addDirections() {
  openDirection.value = true
  bindkey(orgList.value, false)
  formProdsell.value.guidePrice = null
}
function check(data, obj) {
  let checkedKeys = obj.checkedKeys
  let bool = checkedKeys.indexOf(data.orgCode) > -1
  if (data.children) {
    bindkey(data.children, bool)
    uncheck(data.children)
  }
}
// 去向提交事件
function submitDirection() {
  proxy.$refs["formDirectionRef"].validate((valid) => {
    if (valid) {
      let parr = []
      let nodes = proxy.$refs['orgRef'].getCheckedNodes()
      if (nodes.length > 0) {
        nodes.forEach(item => {
          let sellPlaces = {
            orgCode: item.orgCode,
            orgName: item.orgName,
          }
          parr.push(sellPlaces)
        })
        let obj = {
          storehouseId: formProdsell.value.storehouseId,
          storehouseName: formProdsell.value.storehouseName,
          aiTypeSubId: formProdsell.value.aiTypeSubId,
          guidePrice: formProdsell.value.guidePrice,
          sellPlaces: parr
        }
        addOrUpdate(obj).then(res => {
          openDirection.value = false
          proxy.$modal.msgSuccess("添加销售去向成功");
          getProdsellList()
        })
      } else {
        proxy.$modal.msgError("请选择销售去向");
      }
    }
  });
}
function cancelDirection() {
  openDirection.value = false
}
function cancelOut() {
  openOut.value = false;
}
function cancelIn() {
  openIn.value = false;
}
function cancelBatchIn() {
  openBatchIn.value = false;
}
function cancelBatchOut() {
  openBatchOut.value = false;
}

function cancelRecords() {
  openRecords.value = false;
}
function cancelCorrelation() {
  proxy.$refs["tableProductRef"].clearSelection();
  openCorrelation.value = false;
}
function handleCorrelation() {
  openCorrelation.value = true;
  proxy.$refs["tableProductInfoRef"].clearSelection();
  resetQueryCorrelation();
}
function handleBatchIn() {
  let arr = []
  selectionData.value.forEach(item => {
    arr.push(Object.assign({}, item))
  })
  formBatch.value.listBatch = arr;
  formBatch.value.listBatch.forEach((item) => {
    item.new = item.curInStock;
  });
  openBatchIn.value = true;
}
function handleBatchOut() {
  let arr = []
  selectionData.value.forEach(item => {
    arr.push(Object.assign({}, item))
  })
  formBatch.value.listBatch = arr;
  formBatch.value.listBatch.forEach((item) => {
    item.new = item.curInStock;
  });
  openBatchOut.value = true;
}

function handlePrice() {
  priceEdit.value = !priceEdit.value

  queryParamsProdsell.value.ProdsellList.forEach(item => {
    item.isedit = priceEdit.value
    if(item.isedit == false){
      item.guidePrice = item.oldPrice
    }
  })
}
/** 批量入库按钮操作 */
function handleImportIn() {
  operateType.value = "02";
  upload.title = "excel批量入库";
  upload.open = true;
  proxy.$refs["tableProductInfoRef"].clearSelection();
  upload.url =
    window.VITE_APP_BASE_API + "/bdh-agric-invest-api/invest/storehouseProdInfo/excelInput";
}
/** 批量出库按钮操作 */
function handleImportOut() {
  operateType.value = "01";
  upload.title = "excel批量出库";
  upload.open = true;
  proxy.$refs["tableProductInfoRef"].clearSelection();
  upload.url =
    window.VITE_APP_BASE_API +
    "/bdh-agric-invest-api/invest/storehouseProdInfo/excelOutput";
}
/** 下载模板操作 */
function importTemplate() {
  if (operateType.value == "02") {
    postForExcel(
      "/bdh-agric-invest-api/invest/storehouseProdInfo/inputTemplate",
      { storehouseId: formProductInfo.value.storehouseId },
      "批量入库excel模板"
    );
  }
  if (operateType.value == "01") {
    postForExcel(
      "/bdh-agric-invest-api/invest/storehouseProdInfo/outputTemplate",
      { storehouseId: formProductInfo.value.storehouseId },
      "批量出库Excel模板"
    );
  }
}
/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
};
/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false;
  upload.isUploading = false;
  proxy.$refs["uploadRef"].clearFiles();
  proxy.$alert(
    "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
    response.msg +
    "</div>",
    "导入结果",
    { dangerouslyUseHTMLString: true }
  );
  getProductInfoList();
};
/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit();
}
// 点击出库按钮
function handleStoreOut(row) {
  formOut.value = {
    aiTypeSubId: row.aiTypeSubId,
    storehouseId: row.storehouseId,
    curInStock: row.curInStock,
    count: null,
    new: row.curInStock,
  };
  openOut.value = true;
}
// 点击入库按钮
function handleStoreIn(row) {
  formOut.value = {
    aiTypeSubId: row.aiTypeSubId,
    storehouseId: row.storehouseId,
    curInStock: row.curInStock,
    count: null,
    new: row.curInStock,
  };
  openIn.value = true;
}

function handleGuidePrice(row, index) {
  if (!row.isedit) {
    row.isedit = !row.isedit
  } else {
    proxy.$refs["formProdsellRef"].validate((valid) => {
      if (valid) {
        row.isedit = !row.isedit;
        let obj = {
          prodSellId: row.prodSellId,
          storehouseId: row.storehouseId,
          storehouseName: formProdsell.value.storehouseName,
          aiTypeSubId: row.aiTypeSubId,
          guidePrice: row.guidePrice,
          sellPlaces: [

          ],
        };
        row.sellPlaces.forEach((item) => {
          obj.sellPlaces.push({
            orgCode: item.orgCode,
            orgName: item.orgName,
          });
        });
        addOrUpdate(obj).then(() => {
          getProdsellList()
          proxy.$modal.msgSuccess("设置指导价格成功");
        })
      }
    });
  }

}

function handleProdSellIdDelete(row) {

  proxy.$modal
    .confirm(
      "请确认该销售去向下，已无此商品的销售计划,删除销售去向不影响库点中商品的库存量,确认删除？"
    )
    .then(function () {
      return prodSellIdDeleteById(row.prodSellId);
    })
    .then(() => {
      getProdsellList()
      proxy.$modal.msgSuccess("删除销售去向成功");
    })
    .catch(() => { });

}

function addstore(row) {
  proxy.$modal
    .confirm(
      "确认将已选商品添加入库？添加入库后，默认库存为0。您可通过出/入库操作更改库存量。"
    )
    .then(function () {
      return addRel2store({
        aiTypeSubId: row.aiTypeSubId,
        storehouseId: queryParamsCorrelation.value.storehouseId,
      });
    })
    .then(() => {
      getCorrelationList();
      getProductInfoList();
      proxy.$modal.msgSuccess("入库成功");
    })
    .catch(() => { });
}

function deletestore(row) {
  proxy.$modal
    .confirm(
      "请确认该库点下，已无任何已选商品的库存计划,清空出库后，本库点中将不再有已选商品的库存信息,确认清空出库？"
    )
    .then(function () {
      return deleteStorehouse(row.storehouseProdId);
    })
    .then(() => {
      getCorrelationList();
      getProductInfoList();
      proxy.$modal.msgSuccess("清空出库成功");
    })
    .catch(() => { });
}

function addstoreAll() {
  proxy.$modal
    .confirm(
      "确认将已选商品添加入库？添加入库后，默认库存为0。您可通过出/入库操作更改库存量。"
    )
    .then(function () {
      let arr = [];
      selectionCorrelationData.value.forEach((item) => {
        arr.push({
          aiTypeSubId: item.aiTypeSubId,
          storehouseId: queryParamsCorrelation.value.storehouseId,
        });
      });
      console.log(arr);
      return addRel2storeAll(arr);
    })
    .then(() => {
      getCorrelationList();
      getProductInfoList();
      proxy.$modal.msgSuccess("入库成功");
    })
    .catch(() => { });
}

function deletestoreAll(row) {
  proxy.$modal
    .confirm(
      "请确认该库点下，已无任何已选商品的库存计划,清空出库后，本库点中将不再有已选商品的库存信息,确认清空出库？"
    )
    .then(function () {
      let arr = [];
      console.log(selectionCorrelationData);
      selectionCorrelationData.value.forEach((item) => {
        arr.push(item.storehouseProdId);
      });
      return deleteStorehouseAll(arr);
    })
    .then(() => {
      getCorrelationList();
      getProductInfoList();
      proxy.$modal.msgSuccess("清空出库成功");
    })
    .catch(() => { });
}

function getStorehouseProdId(row) {
  let id = row.storehouseProdId;
  storehouseProdId(id).then((res) => {
    formRecords.value = res.data;
    openRecords.value = true;
  });
}
function formOutCountChange(value, row) {
  var reg = /^([1-9]{1}|^[1-9]{1}\d{1,15})$/;
  if (value === null || value === "" || typeof value == "undefined") {
    row.new = row.curInStock;
  } else if (reg.test(value) && value.toString().split(".")[0].length > 8) {
    row.new = row.curInStock;
  } else if (reg.test(value)) {
    if (value > row.curInStock) {
      row.count = row.curInStock;
    }
    row.new = row.curInStock - row.count;
  } else {
    row.new = row.curInStock;
  }
}

function formInCountChange(value, row) {
  var reg = /^([1-9]{1}|^[1-9]{1}\d{1,15})$/;
  if (value === null || value === "" || typeof value == "undefined") {
    row.new = row.curInStock;
  } else if (reg.test(value) && value.toString().split(".")[0].length > 8) {
    row.new = row.curInStock;
  } else if (reg.test(value)) {
    row.new = Number(row.curInStock) + Number(row.count);
  } else {
    row.new = row.curInStock;
  }
}

function formInCountChange2(value) {
  var reg = /^([1-9]{1}|^[1-9]{1}\d{1,15})$/
  if (value === null || value === '' || typeof (value) == 'undefined') {
    formOut.value.new = formOut.value.curInStock
  } else if (reg.test(value) && value.toString().split('.')[0].length > 8) {
    formOut.value.new = formOut.value.curInStock
  } else if (reg.test(value)) {
    formOut.value.new = Number(formOut.value.curInStock) + Number(formOut.value.count)
  } else {
    formOut.value.new = formOut.value.curInStock
  }
}
function formOutCountChange2(value) {
  var reg = /^([1-9]{1}|^[1-9]{1}\d{1,15})$/
  if (value === null || value === '' || typeof (value) == 'undefined') {
    formOut.value.new = formOut.value.curInStock
  } else if (reg.test(value) && value.toString().split('.')[0].length > 8) {
    formOut.value.new = formOut.value.curInStock
  } else if (reg.test(value)) {
    if (value > formOut.value.curInStock) {
      formOut.value.count = formOut.value.curInStock
    }
    formOut.value.new = formOut.value.curInStock - formOut.value.count
  } else {
    formOut.value.new = formOut.value.curInStock
  }
}
function submitOut() {
  proxy.$refs["formOutRef"].validate((valid) => {
    if (valid) {
      let obj = {
        aiTypeSubId: formOut.value.aiTypeSubId,
        storehouseId: formOut.value.storehouseId,
        curInStock: formOut.value.count,
      };
      manualStoreOutput(obj).then((res) => {
        proxy.$modal.msgSuccess("出库成功");
        openOut.value = false;
        getProductInfoList();
      });
    }
  });
}

function submitIn() {
  proxy.$refs["formOutRef"].validate((valid) => {
    if (valid) {
      let obj = {
        aiTypeSubId: formOut.value.aiTypeSubId,
        storehouseId: formOut.value.storehouseId,
        curInStock: formOut.value.count,
      };
      manualStoreInput(obj).then((res) => {
        proxy.$modal.msgSuccess("入库成功");
        openIn.value = false;
        getProductInfoList();
      });
    }
  });
}

function submitBatchIn() {
  proxy.$refs["formBatchRef"].validate((valid) => {
    if (valid) {
      let arr = [];
      formBatch.value.listBatch.forEach((item) => {
        arr.push({
          aiTypeSubId: item.aiTypeSubId,
          storehouseId: item.storehouseId,
          curInStock: item.count,
        });
      });
      manualStoreBatchInput(arr).then((res) => {
        proxy.$modal.msgSuccess("入库成功");
        proxy.$refs["tableProductInfoRef"].clearSelection();
        openBatchIn.value = false;
        getProductInfoList();
      });
    }
  });
}

function submitBatchOut() {
  proxy.$refs["formBatchRef"].validate((valid) => {
    if (valid) {
      let arr = [];
      formBatch.value.listBatch.forEach((item) => {
        if (item.curInStock > 0) {
          arr.push({
            aiTypeSubId: item.aiTypeSubId,
            storehouseId: item.storehouseId,
            curInStock: item.count,
          });
        }
      });
      manualStoreBatchOutput(arr).then((res) => {
        proxy.$modal.msgSuccess("出库成功");
        proxy.$refs["tableProductInfoRef"].clearSelection();
        openBatchOut.value = false;
        getProductInfoList();
      });
    }
  });
}

function handleNodeClick(data, node) {
  checkNodes.value = node.checkedNodes.map((item) => {
    return { orgCode: item.orgCode, orgName: item.orgName };
  });
}
function scopLstFmt(row) {
  let arr = [];
  row.scopLst.forEach((item) => {
    if (item.fillType == "02") {
      arr.push(item.orgName);
    }
  });
  return arr.join();
}
function scopLstFmtManger(row) {
  let arr = [];
  row.scopLst.forEach((item) => {
    if (item.fillType == "01") {
      arr.push(item.managerName);
    }
  });
  return arr.join();
}
function sellPlacesFmt(row) {
  let arr = [];
  row.sellPlaces.forEach((item) => {
    arr.push(item.orgName);
  });
  return arr.join();
}

function formatManger() {
  let arr = [];
  formProductInfo.value.scopLst.forEach((item) => {
    if (item.fillType == "01") {
      arr.push(item.managerName);
    }
  });
  return arr.join();
}

function formatOrgName() {
  let arr = [];
  formProductInfo.value.scopLst.forEach((item) => {
    if (item.fillType == "02") {
      arr.push(item.orgName);
    }
  });
  return arr.join();
}

function handleRemoveNode(ids, tag) {
  let index = null;
  for (let i = 0; i < checkNodes.value.length; i++) {
    if (ids === checkNodes.value[i].orgCode) {
      index = i;
    }
  }
  checkNodes.value.splice(index, 1);
}

/** 选择所在单位 */
const handleOrgCodeQuery = (orgInfo) => {
  queryParams.value.params.orgCodes = orgInfo.orgCode ? [
    orgInfo.orgCode
  ] : [];
  handleQuery();
};

/** 选择所在单位 */
const handleProdsellCodeQuery = (orgInfo) => {
  queryParamsProdsell.value.params.orgCodes = orgInfo.orgCode ? [
    orgInfo.orgCode
  ] : [];
  handleQueryProdsell();
};

/** 查询仓库信息管理表列表 */
function getList() {
  storeQueryByPage(queryParams.value).then((response) => {
    bulletinConfInfoList.value = response.data.records;
    total.value = response.data.total;
  });
}

/** 仓库中的商品及库存信息 */
function getProductInfoList() {
  storehouseProdInfoQueryByPage(queryParamsProductInfo.value).then(
    (response) => {
      productInfoList.value = response.data.records;
      totalProductInfo.value = response.data.total;
    }
  );
}
/** 关联商品分页查询 */
function getCorrelationList() {
  queryAllProByPage(queryParamsCorrelation.value).then((response) => {
    correlationList.value = response.data.records;
    totalCorrelation.value = response.data.total;
  });
}
function handleProdsell(row) {
  formProdsell.value = row;
  formProdsell.value.storehouseName = formProductInfo.value.storehouseName;
  openProdsell.value = true;
  resetQueryProdsell();
}

function getProdsellList() {
  let obj = Object.assign({}, queryParamsProdsell.value)
  delete obj.ProdsellList
  prodsellQueryByPage(obj).then((response) => {
    response.data.records.forEach((item) => {
      item.isedit = priceEdit.value;
      item.oldPrice = item.guidePrice
    });
    queryParamsProdsell.value.ProdsellList = response.data.records;
    totalProdsell.value = response.data.total;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}
function cancelProductInfo() {
  proxy.$refs["tableProductInfoRef"].clearSelection();
  openProductInfo.value = false;
}
function cancelProdsell() {
  openProdsell.value = false;
}
// 表单重置
function reset() {
  nextTick(() => {
    proxy.$refs['bulletinConfInfoRef'].resetFields()
  })
  form.value = {
    storehouseName: null,
    aiCompanyId: queryParams.value.aiCompanyId,
  };
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  getList();
}

/** 搜索按钮操作 */
function handleQueryProductInfo() {
  // nextTick(() => {
  //   proxy.$refs["tableProductInfoRef"].clearSelection();
  // })
  queryParamsProductInfo.value.page = 1;
  getProductInfoList();
}

/** 搜索按钮操作 */
function handleQueryCorrelation() {
  // nextTick(() => {
  //   proxy.$refs["tableProductRef"].clearSelection();
  // })
  queryParamsCorrelation.value.page = 1;
  getCorrelationList();
}
/** 搜索按钮操作 */
function handleQueryProdsell() {
  queryParamsProdsell.value.page = 1;
  getProdsellList();
}

/** 重置按钮操作 */
function resetQuery() {
  nextTick(() => {
    proxy.$refs["queryOrgRef"].clear();
    proxy.resetForm("queryRef");
  });
  queryParams.value = {
    rows: 10,
    page: 1,
    aiCompanyId: null,
    orgCode: null,
    storehouseName: null,
    params: {
      orgCodes: [],
    },
  };
  if (companyAbbrOption.value.length > 0) {
    queryParams.value.aiCompanyId = companyAbbrOption.value[0].aiCompanyId
  }
  handleQuery();
}
/** 重置按钮操作 */
function resetQueryProductInfo() {
  proxy.resetForm("queryProductInfoRef");
  queryParamsProductInfo.value = {
    aiTypeSubName: null,
    aiTypeSubSpecs: null,
    page: 1,
    rows: 10,
    storehouseId: formProductInfo.value.storehouseId,
  };
  handleQueryProductInfo();
}
/** 重置按钮操作 */
function resetQueryCorrelation() {
  proxy.resetForm("queryCorrelationRef");
  queryParamsCorrelation.value = {
    aiTypeSubName: null,
    aiTypeSubSpecs: null,
    page: 1,
    rows: 10,
    storehouseId: formProductInfo.value.storehouseId,
  };
  handleQueryCorrelation();
}

/** 重置按钮操作 */
function resetQueryProdsell() {
  nextTick(() => {
    proxy.$refs["queryProdsellRef"].clear();
    proxy.resetForm("formProdsellRef");
  });
  queryParamsProdsell.value = {
    storehouseId: formProdsell.value.storehouseId,
    aiTypeSubId: formProdsell.value.aiTypeSubId,
    page: 1,
    rows: 10,
    guidePriceBg: null,
    guidePriceEd: null,
    params: {
      orgCodes: [],
    },
  };
  handleQueryProdsell();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  selectionData.value = selection;
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
function handleSelectionProdsellChange(selection) {
  selectionProdsellData.value = selection;
  singleProdsell.value = selection.length != 1;
  multipleProdsell.value = !selection.length;
}

// 多选框选中数据
function handleSelectionCorrelationChange(selection) {
  selectionCorrelationData.value = selection;
  singleCorrelation.value = selection.length != 1;
  multipleCorrelation.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  open.value = true;
  reset();
  updateType.value = "update";
  title.value = "添加仓库信息";
}

/** 修改按钮操作 */
function handleUpdate(row, type) {
  open.value = true;
  let orgCode = [];
  let managerId = [];
  checkNodes.value = []
  row.scopLst.forEach((item) => {
    if (item.fillType == "02") {
      orgCode.push(item.orgCode);
      checkNodes.value.push({ orgCode: item.orgCode, orgName: item.orgName });
    }
    if (item.fillType == "01") {
      managerId.push(Number(item.managerId));
    }
  });
  form.value.storehouseName = row.storehouseName;
  form.value.aiCompanyId = row.aiCompanyId;
  form.value.storehouseId = row.storehouseId;
  form.value.orgCode = orgCode;
  form.value.managerId = managerId;
  nextTick(() => {
    proxy.$refs['bulletinConfInfoRef'].clearValidate()
  })

  if (type == "update") {
    title.value = "修改仓库信息";
  } else {
    title.value = "修改仓库信息";
  }
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["bulletinConfInfoRef"].validate((valid) => {
    if (valid) {
      let obj = Object.assign({}, form.value);
      let scopLst = [];
      console.log(checkNodes);
      checkNodes.value.forEach((item) => {
        scopLst.push({
          fillType: "02",
          ...item,
          managerId: null,
          managerName: null,
        });
      });
      obj.managerId.forEach((item) => {
        let keeper = allWarehouseKeeperList.value.find((it) => {
          return it.staffId == item;
        });
        if (keeper) {
          scopLst.push({
            fillType: "01",
            managerId: keeper.staffId,
            managerName: keeper.staffName,
            orgCode: null,
            orgName: null,
          });
        }
      });
      delete obj.managerId;
      delete obj.orgCode;
      obj.scopLst = scopLst;
      if (form.value.storehouseId != null) {
        update(obj).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        insert(obj).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}
// 查看商品信息及库存
function handleProductinfo(row) {
  openProductInfo.value = true;
  formProductInfo.value = row;
  resetQueryProductInfo();
}

/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm(
      "请确认该仓库下已无任何商品库存，删除后商品及库存信息将同步清空确认删除？"
    )
    .then(function () {
      return logicDeleteById(row.storehouseId);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  postForExcel('/bdh-agric-invest-api/invest/storehouseProdInfo/exportProdInfo', queryParamsProductInfo.value, '商品及库存信息');
}

function getBulletinObjectFromDict() {
  allCompanyInfo({}).then((response) => {
    companyAbbrOption.value = response.data;
    if (response.data.length > 0) {
      queryParams.value.aiCompanyId = response.data[0].aiCompanyId;
    }
    getList();
  });
}
function getAllWarehouseKeeper() {
  queryAllWarehouseKeeper({}).then((response) => {
    allWarehouseKeeperList.value = response.data;
  });
}
getAllWarehouseKeeper();
getBulletinObjectFromDict();
queryAllOrgTree("3").then((res) => {
  orgList.value = res.data;
});

</script>
<style lang="scss" scoped>
.el-form-item_label {
  font-weight: 700;
}

.el-link {
  margin-right: 8px;
}

.el-link .el-icon--right.el-icon {
  vertical-align: text-bottom;
}

.upload-file {
  width: 100%;
}

.link {
  font-size: 14px;
  color: #409eff;
  padding-right: 10px;
}

.queryOrgRef {
  :deep(.el-tooltip__trigger) {
    display: flex;
    flex: 1;
  }

  :deep(.el-cascader) {
    flex: 1;
  }
}
</style>
