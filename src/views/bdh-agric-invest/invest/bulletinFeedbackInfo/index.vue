<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form
            :model="queryParams"
            ref="queryRef"
            v-show="showSearch"
            label-width="80px"
            class="form-line"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年度" prop="year">
                <el-select v-model="queryParams.year" clearable placeholder="请选择年度">
                  <el-option
                      v-for="dict in yearOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所在单位" prop="orgCode">
                <org-select
                    ref="queryOrgRef"
                    style="width: 220px"
                    @handleOrgCode="handleOrgCodeQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="公示标题" prop="bulletinTitle">
                <el-input
                    maxlength="50"
                    show-word-limit
                    v-model="queryParams.bulletinTitle"
                    placeholder="请输入公示标题"
                    clearable
                    @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="农户姓名" prop="farmerName">
                <el-input
                    maxlength="20"
                    show-word-limit
                    v-model="queryParams.farmerName"
                    placeholder="请输入农户姓名"
                    clearable
                    @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="身份证号" prop="idIccid">
                <el-input
                    maxlength="18"
                    show-word-limit
                    v-model="queryParams.idIccid"
                    placeholder="请输入身份证号"
                    clearable
                    @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="反馈状态" prop="feedbackStatus">
                <el-select
                    v-model="queryParams.feedbackStatus"
                    placeholder="请选择反馈状态"
                    clearable
                    @keyup.enter="handleQuery"
                >
                  <el-option
                      v-for="item in feedbackStatusOptions"
                      :key="item.code"
                      :label="item.name"
                      :value="item.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" align="right">
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            icon="Download"
            @click="handleExport"
            v-hasPermi="['bulletinFeedbackInfo:export']"
        >导出
        </el-button
        >
      </el-col>
      <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table border

              :height="tableHeight"
              :data="bulletinFeedbackInfoList"
              @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="年度" align="center" prop="year">
      </el-table-column>
      <el-table-column label="公示标题" align="center" prop="bulletinTitle">
      </el-table-column>
      <el-table-column label="所在单位" align="center" prop="orgName">
      </el-table-column>
      <el-table-column label="农户姓名" align="center" prop="farmerName">
      </el-table-column>
      <el-table-column label="身份证号" align="center" prop="idIccid">
      </el-table-column>
      <el-table-column label="反馈状态" align="center" :formatter="feedbackStatusOptionsFMT" prop="feedbackStatus">
      </el-table-column>
      <el-table-column label="反馈内容" align="center" prop="feedbackMsg">
      </el-table-column>
      <el-table-column label="农场回复" align="center" prop="farmReport">
      </el-table-column>
      <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
              type="primary"
              link
              size="small"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['bulletinFeedbackInfo:update']"
          >修改
          </el-button
          >
          <!--                    <el-button-->
          <!--                        type="primary"-->
          <!--                        link-->
          <!--                        size="small"-->
          <!--                        icon="Delete"-->
          <!--                        @click="handleDelete(scope.row)"-->
          <!--                        v-hasPermi="['bulletinFeedbackInfo:logicDeleteById']"-->
          <!--                    >删除</el-button-->
          <!--                    >-->
        </template>
      </el-table-column>
    </el-table>
    <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.rows"
        @pagination="getList"
    />

    <!-- 添加或修改公示反馈管理表对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <div class="form-wrapper">
        <el-form
            ref="bulletinFeedbackInfoRef"
            :model="form"
            :rules="rules"
            label-width="100px"
        >
          <el-row>
            <el-col :span="24">
              <el-form-item label="年度" prop="year">
                <el-input
                    style="width: 220px"
                    v-model="form.year"
                    value-format="YYYY"
                    type="year"
                    placeholder="请选择年度"
                    disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="公示标题" prop="bulletinTitle">
                <el-input
                    style="width: 600px"
                    maxlength="50"
                    v-model="form.bulletinTitle"
                    placeholder="请输入公示标题"
                    disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="所在单位" prop="orgName">
                <el-input
                    style="width: 220px"
                    v-model="form.orgName"
                    disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="农户姓名" prop="farmerName">
                <el-input
                    style="width: 220px"
                    v-model="form.farmerName"
                    placeholder="请输入农户姓名"
                    disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份证号" prop="idIccid">
                <el-input
                    style="width: 220px"
                    v-model="form.idIccid"
                    placeholder="请输入身份证号"
                    disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="反馈状态" prop="feedbackStatus">
                <el-input
                    style="width: 600px"
                    v-model="form.feedbackStatusStr"
                    placeholder="请选择反馈状态"
                    disabled>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="农户反馈信息" prop="feedbackMsg">
                <el-input
                    style="width: 600px"
                    type="textarea"
                    v-model="form.feedbackMsg"
                    placeholder="请输入农户反馈信息"
                    disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="农场回复" prop="farmReport">
                <el-input
                    style="width: 600px"
                    type="textarea"
                    v-model="form.farmReport"
                    placeholder="请输入农场回复"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitDebounce">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="/invest/bulletinFeedbackInfo/queryByPage">
import {
  listBulletinFeedbackInfo,
  getBulletinFeedbackInfo,
  delBulletinFeedbackInfo,
  addBulletinFeedbackInfo,
  updateBulletinFeedbackInfo,
} from "@/api/bdh-agric-invest/invest/bulletinFeedbackInfo";
import {allCompanyInfo} from "@/api/bdh-agric-invest/invest/companyInfo";
import {getDicts} from "@/api/bdh-agric-invest/system/dict/data";
import { queryAllOrgTree} from "@/api/bdh-agric-invest/system/dict/data";
import {postForExcel} from "@/api/bdh-agric-invest/invest/excel";
import {debounce} from "lodash";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";

const {proxy} = getCurrentInstance();

const bulletinFeedbackInfoList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const yearOption = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const feedbackStatusOptions = ref([]);
const statusOptions = ref([]);
const submitDebounce = debounce(submitForm, 500);
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
// const  validCode = (rule, value, callback) =>{
//     value = value.toString();
//     if(value.length == 15 || value.length == 18){
//         callback()
//     }else {
//         callback(new Error('身份证号码不正确'))
//     }
// };

// 函数参数必须是字符串，因为二代身份证号码是十八位，而在javascript中，十八位的数值会超出计算范围，造成不精确的结果，导致最后两位和计算的值不一致，从而该函数出现错误。
// 详情查看javascript的数值范围
const checkIDCard = (rule, value, callback) => {
  value = value.toString();
  // 加权因子
  var weight_factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
  // 校验码
  var check_code = ["1", "0", "X", "9", "8", "7", "6", "5", "4", "3", "2"];
  var code = value + "";
  var last = value[17]; //最后一个
  var seventeen = code.substring(0, 17);
  // ISO 7064:1983.MOD 11-2
  // 判断最后一位校验码是否正确
  var arr = seventeen.split("");
  var len = arr.length;
  var num = 0;
  for (var i = 0; i < len; i++) {
    num = num + arr[i] * weight_factor[i];
  }

  // 获取余数
  var resisue = num % 11;
  var last_no = check_code[resisue];
  var idcard_patter =
      /^[1-9][0-9]{5}([1][9][0-9]{2}|[2][0][0|1][0-9])([0][1-9]|[1][0|1|2])([0][1-9]|[1|2][0-9]|[3][0|1])[0-9]{3}([0-9]|[X])$/;
  // 判断格式是否正确
  var format = idcard_patter.test(value);
  // 返回验证结果，校验码和格式同时正确才算是合法的身份证号码
  const result = last_no && format ? true : false;
  // return last === last_no && format ? true : false;
  //  console.log(111,last)
  if (result == true) {
    callback();
  } else {
    callback(new Error("身份证号码不正确"));
  }
};

const data = reactive({
  form: {},
  queryParams: {
    rows: 10,
    page: 1,
    year: null,
    bulletinTitle: null,
    orgName: null,
    farmerName: null,
    idIccid: null,
    feedbackStatus: null,
    feedbackStatusStr: null,
    statusCd: null,
  },
  rules: {
    // idIccid: [
    //     { required: true, message: "身份证号填写错误", trigger: "change" },
    //     { validator: checkIDCard, trigger: "blur" },
    // ],
    bulletinTitle: [{required: true, message: "请输入公示标题", trigger: "blur"},
      {max: 50, message: "最多50个字符", trigger: "blur"}],
    farmerName: [{required: true, message: "请输入农户姓名", trigger: "blur"},
      {max: 20, message: "最多20个字符", trigger: "blur"}],
    farmReport: [{required: true, message: "请输入农场回复", trigger: "blur"},
      {max: 2000, message: "最多2000个字符", trigger: "blur"}],
    orgCode: [{required: true, message: "请选择所在单位", trigger: "blur"}],
    year: [{required: true, message: "请选择年份", trigger: "blur"},],
  },
});
const queryOrgRef = ref();
const defaultOrgCode = ref('');
const {queryParams, form, rules} = toRefs(data);

onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 280
        : window.innerHeight - 280;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 280
        : window.innerHeight - 280;
});

allCompanyInfo({}).then((response) => {
  response.data.forEach((each) => {
    statusOptions.value.push({
      code: each.aiCompanyId,
      name: each.companyAbbr,
    });
  });
});

function feedbackStatusFMT(value) {
  let res = feedbackStatusOptions.value.filter(item => item.code == value);
  return res[0].name ||  ''
}

/**查询反馈状态*/
function getSatusOptions() {
  getDicts("bulletinFeedbackStatus").then((response) => {
    response.data.forEach((each) => {
      feedbackStatusOptions.value.push({
        code: each.code,
        name: each.name,
      });
    });
  });
}

function feedbackStatusOptionsFMT(row, value) {
  var name = '';
  for (let i = 0; i < feedbackStatusOptions.value.length; i++) {
    if (feedbackStatusOptions.value[i].code == row.feedbackStatus + '') {
      name = feedbackStatusOptions.value[i].name;
    }
  }
  return name;
}


/** 查询页面的所在单位 */
const handleOrgCodeQuery = (orgInfo) => {
  queryParams.value.orgCode = orgInfo.orgCode;
  // queryParams.value.orgName = orgInfo.orgName;
};
/** 新增页面的所在单位 */
const handleOrgCodeDetail = (orgInfo) => {
  form.value.orgCode = orgInfo.orgCode;
  form.value.orgName = orgInfo.orgName;
};

/** 查询公示反馈管理表列表 */
function getList() {
  loading.value = true;
  listBulletinFeedbackInfo(queryParams.value).then((response) => {
    bulletinFeedbackInfoList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    bulletinFeedbackId: null,
    year: null,
    bulletinTitle: null,
    orgCode: null,
    orgName: null,
    farmerName: null,
    idIccid: null,
    feedbackStatus: null,
    feedbackMsg: null,
    farmReport: null,
    remark: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    statusCd: null,
  };
  proxy.resetForm("bulletinFeedbackInfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  // queryParams.value.rows = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  proxy.$refs['queryOrgRef'].clear()
  queryParams.value.year = yearOption.value[0].code
  queryParams.value.orgCode = defaultOrgCode.value;
  queryOrgRef.value.setValue(defaultOrgCode.value, "");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.bulletinFeedbackId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加公示反馈管理表";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const bulletinFeedbackId = row.bulletinFeedbackId || ids.value;
  getBulletinFeedbackInfo(bulletinFeedbackId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = "修改公示反馈管理表";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["bulletinFeedbackInfoRef"].validate((valid) => {
    console.log(valid, 2)
    if (valid) {
      if (form.value.bulletinFeedbackId != null) {
        updateBulletinFeedbackInfo(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addBulletinFeedbackInfo(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const bulletinFeedbackIds = row.bulletinFeedbackId || ids.value;
  proxy.$modal
      .confirm(
          '是否确认删除公示反馈管理表编号为"' + bulletinFeedbackIds + '"的数据项？'
      )
      .then(function () {
        return delBulletinFeedbackInfo(bulletinFeedbackIds);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
      .catch(() => {
      });
}

// /** 导出按钮操作 */
// function handleExport1() {
//     proxy.download(
//         "invest/bulletinFeedbackInfo/export",
//         {
//             ...queryParams.value,
//         },
//         `bulletinFeedbackInfo_${new Date().getTime()}.xlsx`
//     );
// }

/** 导出按钮操作 */
function handleExport() {
  postForExcel(
      "/bdh-agric-invest-api/invest/bulletinFeedbackInfo/export",
      queryParams.value,
      "公示反馈管理"
  );
}

/** 获取字典 */
const getDict = () => {
  //查询年份
  getDicts('year_cd').then(response => {
    yearOption.value = response.data.sort((a, b) => {
      return Number(b.code) - Number(a.code)
    })
    queryParams.value.year = yearOption.value[0].code
    queryAllOrgTree("1").then((res) => {
        var orgList = res.data;
        if (orgList && orgList.length && orgList.length > 0) {
            defaultOrgCode.value = orgList[0].orgCode;
            queryParams.value.orgCode = defaultOrgCode.value;
        }
        getList()
    })
  });
}

getDict();
// checkIDCard(checkIDCard);
getSatusOptions();
</script>
<style scoped lang="scss">
.bulletinConfInfo{
  :deep(.el-form-item--default){
    margin-bottom: 18px !important;
  }
}
</style>