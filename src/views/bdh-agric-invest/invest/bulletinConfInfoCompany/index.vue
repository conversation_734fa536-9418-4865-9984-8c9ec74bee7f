<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form :model="queryParams" ref="queryRef" v-show="showSearch" label-width="80px" class="form-line">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年度" prop="year" >
                  <el-select v-model="queryParams.year" clearable placeholder="请选择年度">
                      <el-option
                          v-for="dict in yearOption"
                          :key="dict.code"
                          :label="dict.name"
                          :value="dict.code"
                      />
                  </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="公示标题" prop="bulletinTitle">
                <el-input
                    v-model="queryParams.bulletinTitle"
                    maxlength="50"
                    show-word-limit
                    placeholder="请输入公示标题"
                    clearable
                    @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="公示日期">
                <el-date-picker
                    v-model="queryDateRange"
                    type="daterange"
                    value-format="YYYY-MM-DD"
                    range-separator="~"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"

                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业简称" prop="bulletinTitle">
                <el-select v-model="queryParams.aiCompanyId" clearable placeholder="请选择企业简称">
                  <el-option
                      v-for="dict in companyAbbrOption"
                      :key="dict.aiCompanyId"
                      :label="dict.companyAbbr"
                      :value="dict.aiCompanyId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="公示对象" prop="orgCode" >
                <org-select @handleOrgCode="handleOrgCodeQuery" ref="qorgRef" :key="keyNum" :level="3"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="发布状态" prop="isPublish">
                <el-select v-model="queryParams.isPublish" placeholder="请选择公示发布状态">
                  <el-option
                      v-for="item in isPublish"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                      :disabled="item.disabled"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" align="right">
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['bulletinConfInfoCompany:insert']"
        >新增
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            icon="Edit"
            :disabled="single"
            @click="handleUpdate(null,'update')"
            v-hasPermi="['bulletinConfInfo:update']"
        >修改
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['bulletinConfInfo:logicDeleteByIds']"
        >删除
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table border :height="tableHeight"  :data="bulletinConfInfoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="年度" align="center" prop="year" >
      </el-table-column>
      <el-table-column label="企业简称" align="center" prop="companyAbbr" >
        <template #default="scope">
          {{getCompanyAbbrById(scope.row)}}
        </template>
      </el-table-column>
      <el-table-column label="公示标题" align="center" prop="bulletinTitle" >
      </el-table-column>
      <el-table-column label="公示对象" align="center" prop="orgNameList">
      </el-table-column>
      <el-table-column label="公示时间" align="center" width="220" >
        <template #default="scope">
          {{geBulletinDateRange(scope.row)}}
        </template>
      </el-table-column>
      <el-table-column label="发布状态" align="center" prop="isPublish">
        <template #default="scope">
          {{scope.row.isPublish === '1' ? '已发布' : '未发布'}}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="250" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-link type="primary"
              @click="handleUpdate(scope.row,'view')"
              v-hasPermi="['bulletinConfInfo:update']"
          >查看
          </el-link>
          <el-link type="primary"
              @click="handleUpdate(scope.row,'update')"
              v-hasPermi="['bulletinConfInfo:update']"
          >修改
          </el-link>
          <el-link type="primary"
              @click="handleDelete(scope.row)"
              v-hasPermi="['bulletinConfInfo:logicDeleteById']"
          >删除
          </el-link>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.rows"
        @pagination="getList"
    />
    <!-- 添加或修改公示配置管理表对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body :close-on-click-modal="false">
      <div class="form-wrapper bulletinConfInfoCompany">
        <el-form ref="bulletinConfInfoRef" :disabled="updateType=='view'" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="年度" prop="year">
              <el-select v-model="form.year" clearable placeholder="请选择年度">
                  <el-option
                      v-for="dict in yearOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
              </el-select>
          </el-form-item>
          <el-form-item label="企业简称" prop="aiCompanyId">
            <el-select v-model="form.aiCompanyId" clearable placeholder="请选择企业简称">
              <el-option
                  v-for="dict in companyAbbrOption"
                  :key="dict.aiCompanyId"
                  :label="dict.companyAbbr"
                  :value="dict.aiCompanyId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="公示标题" prop="bulletinTitle">
            <el-input v-model="form.bulletinTitle"       maxlength="50"
                      show-word-limit placeholder="请输入公示标题"/>
          </el-form-item>
          <el-form-item label="公示日期" prop="formDateRange" class="item">
            <el-date-picker
                v-model="form.formDateRange"
                type="daterange"
                value-format="YYYY-MM-DD"
                range-separator="~"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
            />
          </el-form-item>
          <el-form-item label="公示对象" prop="orgCode" class="item">
            <el-tree-select
                v-model="form.orgCode"
                :data="orgList"
                multiple
                :render-after-expand="false"
                show-checkbox
                check-strictly
                check-on-click-node
                :props="orgTreeProp"
                :node-key="orgTreeProp.value"
                @check="handleNodeClick"
                @removeTag="handleRemoveNode"
            />
          </el-form-item>
          <el-form-item label="是否发布" prop="isPublish">
            <el-switch
                v-model="form.isPublish"
                active-value="1"
                inactive-value="0"
            />
          </el-form-item>
          <el-form-item label="公示附件" prop="annexUrlArray">
            <fileUpload urlApi="bdh-agric-invest-api" :limit="1" :show="updateType=='update'" v-model:fileType="fileType" v-model="form.annexUrlArray"
                        @fileUploadChange="fileUploadAnnexUrl"
            ></fileUpload>
          </el-form-item>

          <el-form-item label="备注">
            <el-input type="textarea"        maxlength="500"
                      show-word-limit v-model="form.remark"></el-input>
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" v-if="updateType=='update'" @click="submitDebounce">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="/invest/bulletinConfInfoCompany">
import {
  listBulletinConfInfo,
  getBulletinConfInfo,
  delBulletinConfInfo,
  delBulletinConfInfos,
  addBulletinConfInfo,
  updateBulletinConfInfo
} from "@/api/bdh-agric-invest/invest/bulletinConfInfoCompany";
import {getCurrentInstance, reactive, ref, toRefs} from "vue";
import {getCompanyAbbrList} from "@/api/bdh-agric-invest/invest/bulletinConfInfoCompany";
import {allCompanyInfo} from "@/api/bdh-agric-invest/invest/companyInfo";
const {proxy} = getCurrentInstance();
import { debounce,map } from "lodash";
import { queryAllOrgTree} from "@/api/bdh-agric-invest/system/dict/data";
import {getDicts} from "@/api/bdh-agric-invest/system/dict/data";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";

const bulletinConfInfoList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const yearOption = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const keyNum = ref(0);
const rootOrgCode=ref(null)
const orgList=ref([])
const updateType=ref('update')
const orgTreeProp=ref({
  value: 'orgCode',
  label: 'orgName',
  children: 'children' // 子级字段名
})
const data = reactive({
  form: {formDateRange: [],orgCode:[],orgName:[]},
  queryParams: {
    rows: 10,
    page: 1,
    year: null,
    bulletinTitle: null,
    beginTime: null,
    endTime: null,
    orgCode: null,
    orgName: null,
    isPublish: null,
    annexUrl: null,
    statusCd: null,
    bulletin: null,
  },
  rules: {
    bulletinTitle: [{required: true, message: "请输入公示标题", trigger: "blur"},
      {length: 30, message: "最多30个字符", trigger: "blur"}],
    year: [{required: true, message: "请选择年度", trigger: "blur"}],
    aiCompanyId: [{required: true, message: "请选择企业简称", trigger: "change"}],
    beginTime: [{required: true, message: "请选择公示开始时间", trigger: "blur"}],
    endTime: [{required: true, message: "请选择公示结束时间", trigger: "blur"}],
    orgCode: [{type:'array',required: true, message: "请选择公示对象", trigger: "change"}],
    formDateRange: [{required: true, message: "请选择公示日期", trigger: "blur"}],
  }
});
const getOrgTree = () => {
  queryAllOrgTree('3').then((res) => {
    orgList.value = res.data;
    if(orgList.value&&orgList.value.length&&orgList.value.length>0){
      rootOrgCode.value=orgList.value[0].orgCode
    }
  });
};
const fileType = ['pdf'];
const queryDateRange = ref([]);
const companyAbbrOption = ref([]);
//发布状态 已发布1 未发布0
const isPublish = [
  {
    value: 0,
    label: '未发布',
  },
  {
    value: 1,
    label: '已发布',
  }];
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const qorgRef = ref();
const defaultOrgCode = ref('');
const {queryParams, form, rules} = toRefs(data);

const submitDebounce = debounce(submitForm,500);
const checkNodes=ref([])
onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 280
        : window.innerHeight - 280;
    // proxy.updateButtonStyle()
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 280
        : window.innerHeight - 280;
});
function handleNodeClick(data,node) {
  checkNodes.value=node.checkedNodes.map(item=>{return {"orgCode":item.orgCode,"orgName":item.orgName}})
}

function handleRemoveNode(ids,tag){
  let index=null;
  for(let i=0;i<checkNodes.value.length;i++){
    if(ids===checkNodes.value[i].orgCode){
      index=i;
    }
  }
  checkNodes.value.splice(index,1);
}

function handleOrgCodeQuery(arr){
    if(arr.orgCode){
      queryParams.value.orgCode = [arr.orgCode];
    }else{
      queryParams.value.orgCode = []
    }
    // if(arr.orgName){
    //   queryParams.value.orgName = [arr.orgName];
    // }else{
    //   queryParams.value.orgName = [];
    // }
}


function handleOrgCodeForm(arr){
  console.info('arr--->',arr);
  if(arr&&arr.length>0){
    const orgNameList = arr.map(item=>item.orgName);
    const orgCodeList = arr.map(item=>item.orgCode);
    form.value.orgName = [arr.orgCode];
    form.value.orgCode = [arr.orgName];
  }
}

function getCompanyAbbrById(obj){
  let arr = companyAbbrOption.value;
  return   arr.filter(item=>item.aiCompanyId === obj.aiCompanyId)[0]?.companyAbbr
}

/** 查询公示配置管理表列表 */
function getList() {
  loading.value = true;
  if(queryDateRange.value&&queryDateRange.value.length>0){
    queryParams.value.beginTime = queryDateRange.value[0];
    queryParams.value.endTime = queryDateRange.value[1];
  }else{
    queryParams.value.beginTime=null
    queryParams.value.endTime=null
  }
  listBulletinConfInfo(queryParams.value).then(response => {
    bulletinConfInfoList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    year: null,
    bulletinTitle: null,
    beginTime: null,
    endTime: null,
    orgCode: null,
    orgName: null,
    isPublish: null,
    annexUrl: null,
    remark: null,
    createBy: null,
    updateBy: null,
    updateTime: null,
    bulletinConfId: null,
    createTime: null,
    statusCd: null,
    bulletin: '1',
    aiCompanyId: null,
    formDateRange: []
  };
  proxy.resetForm("bulletinConfInfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  // queryParams.value.rows = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value.aiCompanyId=null;
  queryDateRange.value=null
  proxy.$refs['qorgRef'].clear()
  proxy.resetForm("queryRef");
  queryParams.value.year = yearOption.value[0].code
  queryParams.value.orgCode = [defaultOrgCode.value];
  qorgRef.value.setValue(defaultOrgCode.value, []);
  handleQuery();
}
const selectionData=ref([])
// 多选框选中数据
function handleSelectionChange(selection) {
  selectionData.value=selection
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  updateType.value='update'
  //keyNum.value++;
  checkNodes.value=[]
  title.value = "添加公示配置";
}

/** 修改按钮操作 */
function handleUpdate(row,type) {

  let bulletinConfId = null;
  if(!row){
    //校验数据状态
    if(!selectionData.value||selectionData.value.length==0){
      proxy.$modal.msgError("请选择待修改的数据");
      return
    }
    ids.value=selectionData.value.filter(item=>item.isPublish=='0')
    if(!ids.value||ids.value.length!=selectionData.value.length){
      proxy.$modal.msgError("已发布的公告不能修改");
      return
    }
    bulletinConfId=ids.value[0].bulletinConfId
  }else{
    bulletinConfId=row.bulletinConfId
  }
  updateType.value=type
  reset();
  checkNodes.value=[]

  getBulletinConfInfo(bulletinConfId).then(response => {
    form.value = response.data;
    let begin = new Date(form.value.beginTime).toISOString().split('T')[0];
    let end = new Date(form.value.endTime).toISOString().split('T')[0];
    let arr = [begin,end];
    form.value.formDateRange = arr;
    if(form.value.orgCodeList){
      form.value.orgCode=form.value.orgCodeList.split(",");
    }
    if(form.value.orgNameList){
      form.value.orgName=form.value.orgNameList.split(",");
    }

    if(form.value.orgCode.length==form.value.orgName.length){
      for(let i=0;i<form.value.orgCode.length;i++){
        let tmp={orgCode:form.value.orgCode[i],orgName:form.value.orgName[i]}
        checkNodes.value.push(tmp);
      }
    }

    delete form.value.orgCodeList;
    delete form.value.orgNameList;
    delete form.value.bulletinOrgCode;
    open.value = true;
    if(type=='update'){
      title.value = "修改公示配置";
    }else{
      title.value = "查看公示配置";
    }
  });
}

/** 提交按钮 */
function submitForm() {
  form.value.beginTime = form.value.formDateRange[0];
  form.value.endTime = form.value.formDateRange[1];
  form.value.orgName=checkNodes.value.map(item=>{return item.orgName});
  //产业公司及农场查看公示类型
  form.value.bulletin = '2';
  proxy.$refs["bulletinConfInfoRef"].validate(valid => {
    if (valid) {
      if (form.value.bulletinConfId != null) {
        updateBulletinConfInfo(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        if(!form.value.isPublish){
          form.value.isPublish='0'
        }
        addBulletinConfInfo(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {

  var bulletinConfIds=[];
  if(row&&row.bulletinConfId){
    bulletinConfIds=[row.bulletinConfId]
  }else{
    //校验数据状态
    if(!selectionData.value||selectionData.value.length==0){
      proxy.$modal.msgError("请选择待删除的数据");
      return
    }
    ids.value=selectionData.value.filter(item=>item.isPublish=='0')
    if(!ids.value||ids.value.length!=selectionData.value.length){
      proxy.$modal.msgError("已发布的公告不能删除");
      return
    }
    bulletinConfIds=ids.value.map(item=>item.bulletinConfId)
  }
  const bulletinTitle = row.bulletinTitle;
  proxy.$modal.confirm('是否确认删除公示信息？').then(function () {
    return delBulletinConfInfos(bulletinConfIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('invest/bulletinConfInfo/export', {
    ...queryParams.value
  }, `bulletinConfInfo_${new Date().getTime()}.xlsx`)
}


function geBulletinDateRange(obj){
  const beginTime = new Date(obj.beginTime);
  const endTime = new Date(obj.endTime);
  return  beginTime.toISOString().split('T')[0]
          + '至' +
          endTime.toISOString().split('T')[0];
}

function getBulletinObjectFromDict(){
  allCompanyInfo({}).then(response=>{
    companyAbbrOption.value = response.data;
  })
}

/** 获取字典 */
const getDict = () => {
    //查询年份
    getDicts('year_cd').then(response => {
        yearOption.value = response.data.sort((a, b) => {
            return Number(b.code) - Number(a.code)
        })
        queryParams.value.year = yearOption.value[0].code
        queryAllOrgTree("3").then((res) => {
          orgList.value = res.data;
          if(orgList.value&&orgList.value.length&&orgList.value.length>0){
            rootOrgCode.value=orgList.value[0].orgCode
            defaultOrgCode.value = orgList.value[0].orgCode;
            queryParams.value.orgCode = [defaultOrgCode.value];
          }
          getList()
        })
    });
}
let fileUploadAnnexUrl = (value) => {
  if (Array.isArray(value) && value.length > 0) {
    // const urls = value.map(item => item.url || item).join(',');
    const urls = value.map(item => item.url || item);
    form.value.annexUrlArray = urls;
    proxy.$refs.bulletinConfInfoRef.clearValidate('annexUrlArray');
  } else {
    form.value.companyLicenseAddr = '';
  }
}

getDict();
getBulletinObjectFromDict();
// getOrgTree();
</script>
<style scoped lang="scss">
.bulletinConfInfoCompany{
  :deep(.el-form-item--default){
    margin-bottom: 18px !important;
  }
}
.el-form-item_label{
    font-weight: 700;
}
.el-link {
  margin-right: 8px;
}
.el-link .el-icon--right.el-icon {
  vertical-align: text-bottom;
}
.upload-file{
  width: 100%;
}
</style>
