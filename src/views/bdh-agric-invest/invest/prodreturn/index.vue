<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form class="form-line" :model="queryParams" ref="queryRef"  v-show="showSearch" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item  label="年份" prop="year">
                  <el-select v-model="queryParams.year" clearable placeholder="请选择年份">
                      <el-option
                          v-for="dict in yearOption"
                          :key="dict.code"
                          :label="dict.name"
                          :value="dict.code"
                      />
                  </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item  label="所在单位" prop="orgCode">
                <org-select ref="queryOrgRef" @handleOrgCode="handleOrgCode" :level="1"/>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item  label="企业名称" prop="aiCompanyId">
                <el-select v-model="queryParams.aiCompanyId"  clearable placeholder="企业名称"  @change="chgCompany">
                  <el-option
                      v-for="dict in companyNameOption"
                      :key="dict.aiCompanyId"
                      :label="dict.companyAbbr"
                      :value="dict.aiCompanyId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item  label="审核状态" prop="auditStatus">
                <el-select v-model="queryParams.auditStatus" clearable placeholder="全部" >
                  <el-option
                      v-for="dict in auditStatusOption"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item  label="投入品分类" prop="aiTypeId">
                <ProduceTypeDynamic :companyId="queryParams.aiCompanyId" @handleTypeId="handleTypeId" ref="produceTypeRef"></ProduceTypeDynamic>
                <!-- <ProduceType ref="produceTypeRef" @handleTypeId="handleTypeId"></ProduceType> -->
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item  label="商品名称" prop="aiTypeSubName">
                <el-input
                    v-model="queryParams.aiTypeSubName"
                    placeholder="请输入商品名称"
                    clearable
                    @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="作业季节" prop="regionCode">
                <el-select v-model="queryParams.fertilizeSeason"
                           clearable
                           placeholder="请选择作业季节">
                  <el-option
                      v-for="dict in fertilizes"
                      :key="dict.code"
                      :label="dict.name"
                      :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6" align="right">
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['prodreturn:insert']"
        >新增
        </el-button>
      </el-col>
<!--      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate({},'update')"
            v-hasPermi="['prodreturn:update']"
        >修改
        </el-button>
      </el-col>-->
      <el-col :span="1.5">
        <el-button
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['prodreturn:logicDeleteByIds']"
        >删除
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            icon="Check"
            :disabled="single"
            @click="handleApprove"
            v-hasPermi="['prodreturn:approve']"
        >审核
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            v-hasPermi="['prodreturn:export']"
            icon="Download"
            @click="handleExport"
        >导出
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border :height="tableHeight"  :data="prodreturnList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="ID" align="center" prop="prodReturnId"/>
      <el-table-column label="年份" align="center" prop="year"/>
      <el-table-column label="作业季节" align="center" prop="fertilizeSeason"
                       :formatter="fertilizeSeasonFmt"/>
      <el-table-column label="所在单位" align="center" prop="orgName"  width="200"/>
      <el-table-column label="企业简称" align="center" prop="companyAbbr"/>
      <el-table-column label="投入品名称" align="center" prop="aiTypeSubName" width="150"/>
      <el-table-column label="规格" align="center" prop="aiTypeSubSpecs"/>
      <el-table-column label="退货数量" align="center" prop="returnNum"/>
      <el-table-column label="上报单位" align="center" prop="aiTypeReportUnit"/>
      <el-table-column label="退货原因" align="center" prop="returnReason"  width="150"  :formatter="returnReasonFmt"/>
      <el-table-column label="上报时间" align="center" prop="createTime"  width="100"/>
      <el-table-column label="审核状态" align="center" prop="auditStatus" :formatter="auditStatusFmt"/>
      <el-table-column label="当前审核人" align="center" prop="auditPerson"  width="100" :show-overflow-tooltip="true" :formatter="fmtAuditName"/>
      <el-table-column label="流程状态详情" align="center" prop="flowMsg"  width="140" :formatter="fmtAuditNode">
      </el-table-column>
      <el-table-column label="操作" align="left" width="200px" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-link
              type="primary"
              icon="View"
              @click="handleUpdate(scope.row,'view')"
              v-hasPermi="['prodreturn:view']"
          >查看详情
          </el-link>
<!--          <el-link
              type="primary"
              icon="Edit"
              v-if="scope.row.auditStatus===0"
              @click="handleUpdate(scope.row,'update')"
              v-hasPermi="['prodreturn:update']"
          >修改
          </el-link>-->
          <el-link
              v-if="scope.row.auditStatus===4"
              type="error"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['prodreturn:logicDeleteById']"
          >删除
          </el-link>
          <el-link type="success" icon="User" @click="viewFlow(scope.row)">查看流程</el-link>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.rows"
        @pagination="getList"/>

    <!-- 添加或修改投入品退货管理对话框 -->
    <el-dialog :title="title" :close-on-click-modal="false" v-model="open" width="800px" append-to-body>
      <el-form ref="prodreturnRef" :disabled="type=='view'" :model="form" :rules="rules" label-width="100px" label-position="top">
        <el-row :gutter="20">
           <el-col :span="8">
             <el-form-item label="年份" prop="year">
                 <el-select v-model="form.year" clearable placeholder="请选择年份">
                     <el-option
                         v-for="dict in yearOption"
                         :key="dict.code"
                         :label="dict.name"
                         :value="dict.code"
                     />
                 </el-select>
             </el-form-item>
           </el-col>
           <el-col :span="8">
             <el-form-item label="所在单位" prop="orgCode">
               <org-select @handleOrgCode="handleOrgCode" v-if="rstorg" :defaultOrgCode="form.orgCode" ref="formOrgRef"/>
             </el-form-item>
           </el-col>
           <el-col :span="8">
             <el-form-item label="企业名称" prop="aiCompanyId">
               <el-select v-model="form.aiCompanyId" clearable placeholder="请选择企业名称">
                 <el-option
                     v-for="dict in companyNameOption"
                     :key="dict.aiCompanyId"
                     :label="dict.companyAbbr"
                     :value="dict.aiCompanyId"
                 />
               </el-select>
             </el-form-item>
           </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="作业季节" prop="fertilizeSeason">
              <el-select
                  v-model="form.fertilizeSeason"
                  clearable
                  placeholder="请选择作业季节">
                <el-option
                    v-for="dict in fertilizes"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发货单" prop="prodShipId">
              <el-input
                  v-model="form.prodShipId"
                  readonly
                  class="input-with-select"
                  placeholder="请选择发货单">
                <template #append>
                  <el-button type="danger" @click="handleShipInfo">选择</el-button>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="投入品分类" prop="aiTypeName">
              <el-input v-model="form.aiTypeName" disabled placeholder="请输入品分类名称"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="商品名称" prop="aiTypeSubName">
              <el-input v-model="form.aiTypeSubName" disabled placeholder="请输入投入品名称"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="规格" prop="aiTypeSubSpecs">
              <el-input v-model="form.aiTypeSubSpecs" disabled placeholder="请输入规格"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="退货原因" prop="returnReason">
              <el-select v-model="form.returnReason" clearable placeholder="请选择退货原因">
                <el-option
                    v-for="dict in returnReasonDict"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="退货数量" prop="returnNum">
              <el-input v-model="form.returnNum" placeholder="请输入退货数量">
                <template #append>{{ form.aiTypeReportUnit }}</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="问题描述" prop="quesDesc">
              <el-input v-model="form.quesDesc" type="textarea" placeholder="请输入问题描述"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="商品图片" prop="prodPicUrl1">
              <imageUpload urlApi="bdh-agric-invest-api" :limit="3" v-model="form.prodPicUrl1" @fileUploadChange="fileUpload"></imageUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" v-show="type=='update'" @click="submitFormDebounce">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog title="发货单信息" v-model="showShipInfo" :width="1200"  :close-on-click-modal="false" append-to-body>
      <ship-info v-if="shipInfoFlag" @shipInfo="ckShipInfo" v-model="queryShipForm"></ship-info>
    </el-dialog>

    <!--审核-->
    <el-dialog :title="title" v-model="openApproveView"  append-to-body :close-on-click-modal="false">
      <el-form ref="approveForm" :model="apform" :rules="aprules" label-width="120px">
        <el-form-item label="是否通过" prop="agree">
          <el-radio v-model="apform.agree" :label="true">同意</el-radio>
          <el-radio v-model="apform.agree" :label="false">不同意</el-radio>
        </el-form-item>
        <el-form-item label="审核意见" prop="opinion">
          <el-input v-model="apform.opinion" type="textarea"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitApproveDebounce">确 定</el-button>
        </div>
      </template>
    </el-dialog>


    <!-- 查看流程对话框 -->
    <el-dialog
        v-model="processVisible"
        title="查看流程"
        width="800px"
        :close-on-click-modal="false"
    >
      <FlowDetail v-model="form.instId" v-if="flowDetailTag" ></FlowDetail>
      <template #footer>
                <span class="dialog-footer">
                    <el-button @click="processVisible = false">关闭</el-button>
                </span>
      </template>
    </el-dialog>


  </div>
</template>

<script setup name="/invest/prodreturn/queryByPage">
import {
  addProdreturn,
  approve,
  delProdreturns,
  getProdreturn,
  listProdreturn,
  updateProdreturn
} from "@/api/bdh-agric-invest/invest/prodreturn";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import ProduceTypeDynamic from "@/views/bdh-agric-invest/components/ProduceTypeDynamic/index.vue";
import {allCompanyInfo} from '@/api/bdh-agric-invest/invest/companyInfo'
import {getDicts, queryAllOrgTree} from '@/api/bdh-agric-invest/system/dict/data'
import {postForExcel} from '@/api/bdh-agric-invest/invest/excel'
import FlowDetail from "@/views/bdh-agric-invest/components/FlowDetail/index.vue";
import ShipInfo from "./shipInfo";
import {nextTick, ref} from "vue";
import {debounce} from "lodash";
const rstorg = ref(true)
const {proxy} = getCurrentInstance();
const fertilizes = ref([]);
const processVisible=ref(false)
const prodreturnList = ref([]);
const returnReasonDict = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const flowDetailTag=ref(true);
const ids = ref([]);
const yearOption = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const showShipInfo = ref(false);
const instId=ref(null)
const companyNameOption = ref([]);
const auditStatusOption = ref([]);
const shipInfoFlag=ref(true);
const shipDetailInfo=ref({})
const openApproveView=ref(false)
const openApproveFlag = ref(true);
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const type=ref('update')
const data = reactive({
  form: {},
  queryShipForm: {
    year:new Date(),
    orgCode:null,
    aiCompanyId:null,
    fertilizeSeason:null
  },
  apform: {},
  year: new Date(),
  dialogYear: new Date(),
  queryParams: {
    rows: 10,
    page: 1,
    aiTypeSubId: null, aiTypeId: null, year: null, orgCode: null, aiCompanyId: null, auditStatus: null
  },
  rules:{
    year:[
      {required: true, message: "请选择年份", trigger: "change"}
    ],
    orgCode:[
      {required: true, message: "请选择所在单位", trigger: "change"}
    ],
    aiCompanyId:[
      {required: true, message: "请选择企业", trigger: "change"}
    ],
    aiTypeName: [
      {required: true, message: "请输入投入品分类", trigger: "change"}
    ],
    aiTypeSubName: [
      {required: true, message: "请输入投入品名称", trigger: "change"}
    ],
    aiTypeSubId: [
      {required: true, message: "请输入投入品名称", trigger: "change"}
    ],
    prodShipId: [
      {required: true, message: "请选择发货单", trigger: "change"}
    ],
    returnReason: [
      {required: true, message: "请选择退货原因", trigger: "change"}
    ],
    fertilizeSeason: [
      {required: true, message: "请选择作业季节", trigger: "change"}
    ],
    aiTypeSubSpecs: [
      {required: true, message: "请输入规格", trigger: "change"}
    ],
    returnNum: [
      {required: true, message: "请输入退货数量", trigger: "change"},
      {pattern: true,validator:( rule, value, callback) => {
          var reg = /^([0-9]{1}|^[1-9]{1}\d{1,15})(\.\d{1,2})?$/
          if(value === null || value === '' || typeof(value) == 'undefined') {
            callback(new Error('请输入退货数量'))
          } else if(reg.test(value) && value.toString().split('.')[0].length > 6) {
            callback(new Error('数字过大，请输入1-999999之间的数字'))
          } else if(reg.test(value)) {
            if (!checkReturnNum()){
                callback(new Error('退货数量换算异常，请确认退货数量'))
            }
            callback()
          } else {
            callback(new Error('请输入大于0的数字,最多保留两位小数'))
          }
        },trigger: "change"}
    ],
    quesDesc:[
      {required: true, message: "请输入问题描述", trigger: "change"},
      {max: 100, message: "最多100个字符", trigger: "change" }
    ],
    prodPicUrl1:[
      {required: true, message: "请上传商品图片", trigger: "change"}
    ],
  },
  aprules: {
    agree: [
      {required: true, message: "请选择是否同意", trigger: "blur"}
    ],
    opinion:[{validator:function(rule, value, callback){
        if(!value&&!apform.value.agree){
          callback("请输入审核意见！")
        }else{
          callback()
        }
      },blur},{max:100,message: "最多100个字符",trigger:"blur"}]
  }
});
const queryOrgRef = ref();
const defaultOrgCode = ref('');
const {queryParams, form, rules,year,apform,aprules,queryShipForm,dialogYear} = toRefs(data);

onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});

/**
 * 校验退货数量
 */
function isInteger(obj) {
  return typeof obj === 'number' && obj%1 === 0
}
function checkReturnNum(){
  console.log(form.value);
  console.log(form.value.transNum)
  console.log('Number(form.value.returnNum)',Number(form.value.returnNum));
  if(form.value.transNum&&form.value.transNum==-1){
    console.info('1111',isInteger(Number(form.value.returnNum)));
    return isInteger(Number(form.value.returnNum))
  }
  // 判断转换数量是否为空
  if (form.value.transNum&&form.value.transNum!=-1){
    console.log(form.value.transNum)
    // 数量乘以转换数量
    let number = (form.value.returnNum * form.value.transNum) % form.value.aiTypeSubWeight;
    console.log(number)
    if (number !== 0){
      return false
    }
  }
  return true;
}
function viewFlow(row){
  form.value=row
  processVisible.value=true
  flowDetailTag.value = false
  nextTick(() => {
    flowDetailTag.value=true;
  });
}

function chgDialogYear(val){
  if(val){
    form.value.year=val.getFullYear()
  }
}

function ckShipInfo(detail){
  shipDetailInfo.value=detail
  form.value.aiTypeId=shipDetailInfo.value.aiTypeId
  form.value.aiTypeSubName=shipDetailInfo.value.aiTypeSubName
  form.value.aiTypeSubSpecs=shipDetailInfo.value.aiTypeSubSpecs
  form.value.aiTypeSubId=shipDetailInfo.value.aiTypeSubId
  form.value.aiTypeName=shipDetailInfo.value.aiTypeName
  form.value.prodShipDetailId=shipDetailInfo.value.prodShipDetailId
  form.value.prodShipId=shipDetailInfo.value.prodShipId
  form.value.returnNum=shipDetailInfo.value.sendNum
  form.value.aiTypeReportUnit=shipDetailInfo.value.aiTypeReportUnit
  form.value.aiTypeSubUnit=shipDetailInfo.value.aiTypeSubUnit
  form.value.transNum=shipDetailInfo.value.transNum
  form.value.aiTypeSubWeight=shipDetailInfo.value.aiTypeSubWeight
  showShipInfo.value=false
}

const submitApproveDebounce = debounce(submitApproveForm,500);
function submitApproveForm(){
  proxy.$refs["approveForm"].validate(valid => {
    if (valid) {
      approve({'instId':apform.value.instId,'opinion':apform.value.opinion,'agree':apform.value.agree}).then(response=>{
        if(response.success){
          openApproveView.value=false;
          proxy.$modal.msgSuccess("成功!");
          getList();
        }
      })
    }
  })

}

function handleTypeId(type){
  queryParams.value.aiTypeId=type.aiTypeId
  form.value.aiTypeId=type.aiTypeId
  form.value.aiTypeName=type.aiTypeName
}

function fertilizeSeasonFmt(row){
  for(let i=0;i<fertilizes.value.length;i++){
    if(fertilizes.value[i].code==row.fertilizeSeason){
      return fertilizes.value[i].name
    }
  }
}

function handleApprove(){
  if(ids.value){
    apform.value.prodReturnId=ids.value[0]
    apform.value.instId=instId.value;
    apform.value.agree=true
    apform.value.opinion=null
    openApproveView.value=true;
  }
}

/** 查询投入品退货管理列表 */
function getList() {
  loading.value = true;
  listProdreturn(queryParams.value).then(response => {
    prodreturnList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}

const handleOrgCode = ({orgCode,orgName})=>{
  queryParams.value.orgCode=orgCode
  form.value.orgCode=orgCode
  form.value.orgName=orgName
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    createBy: null,
    updateBy: null,
    updateTime: null,
    aiTypeSubId: null,
    aiTypeId: null,
    prodReturnId: null,
    createTime: null,
    statusCd: null,
    returnReason: null,
    prodPicUrl1: null,
    prodPicUrl2: null,
    instId: null,
    prodPicUrl3: null,
    auditPerson: null,
    flowMsg: null,
    year: null,
    orgCode: null,
    orgName: null,
    aiCompanyId: null,
    aiTypeName: null,
    aiTypeSubName:null,
    returnNum: null,
    quesDesc: null,
    auditStatus: null,
    prodShipDetailId:null
  };
  proxy.resetForm("prodreturnRef");
}
function chgCompany(){
  queryParams.value.aiTypeId=null
  form.value.aiTypeId=null
  form.value.aiTypeName=null
  getList();
}
const getDict = () => {
  //查询年份
  getDicts('year_cd').then(response => {
      yearOption.value = response.data.sort((a, b) => {
          return Number(b.code) - Number(a.code)
      })
      queryParams.value.year = yearOption.value[0].code
        queryAllOrgTree("1").then((res) => {
            var orgList = res.data;
            if (orgList && orgList.length && orgList.length > 0) {
                defaultOrgCode.value = orgList[0].orgCode;
                queryParams.value.orgCode = defaultOrgCode.value;
            }
            getList()
        })
  });
  //企业简称
  allCompanyInfo({}).then(response => {
    companyNameOption.value = response.data
  });

  //审核状态
  getDicts('agric_invest_audit_status').then(response => {
    auditStatusOption.value = response.data
  });

  //returnReasonDict
  getDicts('return_reason_dict').then(response => {
    returnReasonDict.value = response.data
  });

  getDicts("fertilize_season").then((response) => {
    fertilizes.value = response.data
  });
}


function fmtAuditName(row,column){
  if(row.flowMsg&&JSON.parse(row.flowMsg).auditNames){
    return JSON.parse(row.flowMsg).auditNames
  }
  return "无";
}

function fmtAuditNode(row,column){
  if(row.flowMsg&&JSON.parse(row.flowMsg).node){
    return JSON.parse(row.flowMsg).node
  }
  return "无";
}

function returnReasonFmt(row,value){
  let reasonMsg=""
  returnReasonDict.value.forEach(function(item){
    if(row.returnReason==item.code){
      reasonMsg=item.name
    }
  })
  return reasonMsg;
}

function auditStatusFmt(row,value){
  let auditStatusName=""
  auditStatusOption.value.forEach(function(item){
    if(row.auditStatus==item.code){
      auditStatusName=item.name
    }
  })
  return auditStatusName;
}


/** 搜索按钮操作 */
function handleQuery() {
  // queryParams.value.rows = 1;
  getList();
}
let fileUpload = (value) => {
  if (Array.isArray(value) && value.length > 0) {
    const urls = value.map(item => item.url || item).join(',');
    form.value.prodPicUrl1 = urls;
    proxy.$refs.prodreturnRef.clearValidate('prodPicUrl1');
  } else {
    form.value.prodPicUrl1 = '';
  }
}
//选择发运单
function handleShipInfo(){

  if(!form.value.year){
    proxy.$modal.msgError("请选择年份")
    return
  }
  if(!form.value.orgCode){
    proxy.$modal.msgError("请选择所在单位")
    return
  }
  if(!form.value.aiCompanyId){
    proxy.$modal.msgError("请选择企业名称")
    return;
  }
  if(!form.value.fertilizeSeason){
    proxy.$modal.msgError("请选择作业季节")
    return;
  }

  queryShipForm.value={
    'year':form.value.year,
    'orgCode':form.value.orgCode,
    'aiCompanyId':form.value.aiCompanyId,
    'fertilizeSeason':form.value.fertilizeSeason
  }



  //refresh shipInfo 刷新发货单选择模块
  shipInfoFlag.value=false
  nextTick(() => {
    shipInfoFlag.value=true;
  });
  showShipInfo.value=true
}


// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.prodReturnId);
  single.value = selection.length != 1;
  if(selection.length != 1){
    single.value=true;
  }else{
    //auditStatus==0
    let canAudit=[]
    canAudit=selection.filter(item=>item.auditStatus==0)
    single.value=!(canAudit.length==1)
    if(!single.value){
      instId.value=selection.map(item => item.instId)[0]
    }
  }
  //审核不通过的才可以删除
  let canRemove=[];
  if(selection&&selection.length){
    //如果选择了，就判断一下选择的数据里 是否有不允许删除的变量
    canRemove=selection.filter(item=>item.auditStatus==4)
    multiple.value = !(canRemove&&canRemove.length==selection.length)
  }else{
    multiple.value = true
  }

}


/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  year.value=null
  queryParams.value.aiCompanyId=null
  queryParams.value.auditStatus=null
  queryParams.value.aiTypeSubName=null
  queryParams.value.year=null
  queryParams.value.fertilizeSeason=null
  proxy.$refs['queryOrgRef'].clear()
  proxy.$refs['produceTypeRef'].clear()
  queryParams.value.year = yearOption.value[0].code;
  queryParams.value.orgCode = defaultOrgCode.value;
  queryOrgRef.value.setValue(defaultOrgCode.value, "");
  handleQuery();
}


/** 新增按钮操作 */
function handleAdd() {
  type.value='update'
  dialogYear.value=new Date()
  form.value.aiCompanyId=null
  form.orgCode=null
  reset();
  form.value.year=new Date().getFullYear()
  open.value = true;
  title.value = "添加投入品退货管理";
  nextTick(() => {
    proxy.$refs['formOrgRef'].clear()
  })
}

/** 修改按钮操作 */
function handleUpdate(row,addtype) {
  type.value=addtype;
  reset();
  const prodReturnId = row.prodReturnId || ids.value
  getProdreturn(prodReturnId).then(response => {
    form.value = response.data;
    year.value = new Date(form.value.year);
    open.value = true;
    if(addtype=='view'){
      title.value = "查看投入品退货";
    }else{
      title.value = "修改投入品退货";
    }
    rstorg.value=false;
    nextTick(() => {
      rstorg.value=true;
    })
  });
}

/** 提交按钮 */
const submitFormDebounce = debounce(submitForm,500);
function submitForm() {
  proxy.$refs["prodreturnRef"].validate(valid => {
    if (valid) {
      var subform=new Object(form.value);
      if (form.value.prodReturnId != null) {
        updateProdreturn(subform).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          year.value=null
          form.value.aiCompanyId=null
          proxy.$refs['formOrgRef'].clear()
          getList();
        });
      } else {
        addProdreturn(subform).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          year.value=null
          form.value.aiCompanyId=null
          proxy.$refs['formOrgRef'].clear()
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  var prodReturnIds=[]
  if(row.prodReturnId){
    prodReturnIds=[row.prodReturnId]
  }else{
    prodReturnIds=ids.value
  }

  proxy.$modal.confirm('是否确认删除投入品退货信息？').then(function () {
    return delProdreturns(prodReturnIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  postForExcel('/bdh-agric-invest-api/invest/prodreturn/postForExcel', queryParams.value, '投入品退货');
}

getDict();
</script>
<style scoped>
.el-descriptions {
  margin-top: 20px;
}
.cell-item {
  display: flex;
  align-items: center;
}
.margin-top {
  margin-top: 20px;
}
.input-with-select{
  background-color: var(--el-fill-color-blank);
}
</style>
