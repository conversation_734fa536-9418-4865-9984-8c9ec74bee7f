<!--
@name: 订购计划表
@description:
@author: liuying
@time: 2022-12-06
-->
<template>
    <div class="app-container">
        <div ref="searchDom">
            <el-collapse-transition>
                <el-form
                    class="form-line"
                    :rules="rules"
                    v-show="showSearch"
                    ref="queryRef"
                    :model="queryParams"
                    label-width="80px"
                >
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="年份" prop="year">
                                <el-select
                                    v-model="queryParams.year"
                                    placeholder="请选择年份"
                                    @change="handleQuery"
                                    clearable
                                >
                                    <el-option
                                        v-for="dict in yearOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="企业名称" prop="aiCompanyId">
                                <el-select
                                    v-model="queryParams.aiCompanyId"
                                    placeholder="请选择企业名称"
                                    @change="chgCompany"
                                    clearable
                                >
                                    <el-option
                                        v-for="dict in companyAbbrOption"
                                        :key="dict.aiCompanyId"
                                        :label="dict.companyAbbr"
                                        :value="dict.aiCompanyId"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="作业季节" prop="fertilizeSeason">
                                <el-select
                                    v-model="queryParams.fertilizeSeason"
                                    placeholder="请选择作业季节"
                                    @change="handleQuery"
                                    clearable
                                >
                                    <el-option
                                        v-for="dict in fertilizesOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                          <el-form-item label="所在单位" prop="orgCode">
                            <org-select
                                ref="queryOrgRef"
                                @handleOrgCode="handleOrgCodeQuery" :defaultOrgCode="queryParams.orgCode" :level="3" :checkStrictly="false" />
                          </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="生资分类" prop="aiTypeId">
                                <ProduceTypeDynamic
                                    :companyId="queryParams.aiCompanyId"
                                    @handleTypeId="handleTypeId"
                                    ref="produceTypeRef"
                                    clearable
                                ></ProduceTypeDynamic>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="商品名称" prop="aiTypeSubName">
                                <el-input
                                    v-model="queryParams.aiTypeSubName"
                                    placeholder="请输入商品名称"
                                    maxlength="100"
                                    clearable
                                    @keyup.enter="handleQuery"
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="审核状态" prop="auditStatus">
                                <el-select
                                    v-model="queryParams.auditStatus"
                                    placeholder="请选择审核状态"
                                    @change="handleQuery"
                                    clearable
                                >
                                    <el-option
                                        v-for="dict in auditStatusOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="结束日期" prop="endDate">
                                <el-date-picker
                                    type="date"
                                    clearable
                                    value-format="YYYY-MM-DD"
                                    v-model="queryParams.endDate"
                                    placeholder="结束日期"
                                    style="width: 450px;"
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="统计范围" prop="unitType">
                                <el-select
                                        v-model="queryParams.unitType"
                                        placeholder="请选择统计范围"
                                        @change="handleQuery"
                                >
                                    <el-option
                                            v-for="dict in unitTypeOption"
                                            :key="dict.code"
                                            :label="dict.name"
                                            :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="18" align="right">
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                            <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
                        </el-col>
                    </el-row>
                </el-form>
            </el-collapse-transition>
        </div>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    icon="Download"
                    @click="handleExport"
                    v-hasPermi="['addOrderPlan#export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar
                v-model:showSearch="showSearch"
                @queryTable="getList"
            ></right-toolbar>
        </el-row>
        <!-- 查询表格 -->
        <div v-text="title" class="title"></div>
        <div v-text="unit" class="unit"></div>
        <el-table border :height="tableHeight" :data="orderPlanList">
            <ColumnItem :columns="columns" />
        </el-table>
    </div>

</template>

<script name="/report/addOrderPlan/rpt" setup>
import {getOrderPlan} from "@/api/bdh-agric-invest/invest/addOrderPlan"
import {reactive, ref, toRefs} from "vue";
import {postForExcel} from "@/api/bdh-agric-invest/invest/excel";
import {allCompanyInfo} from "@/api/bdh-agric-invest/invest/companyInfo";
import {getDicts} from "@/api/bdh-agric-invest/system/dict/data";
import ColumnItem from "@/views/bdh-agric-invest/components/columnItem";
import "@/views/bdh-agric-invest/assets/styles/index.scss";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import ProduceTypeDynamic from "@/views/bdh-agric-invest/components/ProduceTypeDynamic";

const { proxy } = getCurrentInstance();
const title = ref("");//表格标题
const unit = ref("")//单位
const showSearch = ref(true);//搜索是否显示
const yearOption = ref([]);//年份字典
const companyAbbrOption = ref([]);//企业名称字典
const auditStatusOption = ref([{'code':'0','name':'审核中'},{'code':'1','name':'审核通过'}]);//审核状态字典
const fertilizesOption = ref([]);//作业季节字典
const orderPlanList = ref([]);//订购计划表数据
const columns = ref([]);//动态列头
const produceTypeRef = ref();//生资分类ref
const queryOrgRef = ref();//组织机构ref
const queryRef = ref();//查询ref
const unitTypeOption=[
    {
        code: "1",
        name: '数量',
    },
    {
        code: "2",
        name: '重量',
    },
]

const data = reactive({
    //查询参数
    queryParams: {
        aiCompanyId: null, //企业
        aiTypeSubName: null, //商品名称
        aiTypeId: null, //商品id
        fertilizeSeason: '1', //季节  1 春季  3 秋季
        endDate: null, //结束日期
        orgCode: null, //组织机构
        year: null, //年份
        auditStatus: '1',//审核状态
        unitType:"2",//统计单位
    },
    rules: {
        aiCompanyId: [{ required: true, message: "请选择企业名称", trigger: "change"}],
        year: [{ required: true, message: "请选择年份", trigger: "change"}],
    }
})
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const {queryParams, rules} = toRefs(data);
onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 240
        : window.innerHeight - 240;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 240
        : window.innerHeight - 240;
});
/** 选择所在单位 */
const handleOrgCodeQuery = (orgInfo) => {
    queryParams.value.orgCode = orgInfo.orgCode
    handleQuery();
}

const chgCompany = () => {
    queryParams.value.aiTypeId = null;
    handleQuery();
}

/** 导出按钮操作 */
function handleExport() {
    postForExcel(
        "/bdh-agric-invest-api/report/addOrderPlan/export",
        queryParams.value,
        "订购计划表"
    );
}

/** 获取字典 */
const getDict = () => {
    //获取企业简称字典
    allCompanyInfo({}).then((response) => {
        companyAbbrOption.value = response.data;
        queryParams.value.aiCompanyId = companyAbbrOption.value[0].aiCompanyId;
        //获取季节字典
        getDicts("fertilize_season").then((response) => {
            fertilizesOption.value = response.data;
            //获取年份字典
            getDicts("year_cd").then((response) => {
                yearOption.value = response.data.sort((a, b) => {
                    return Number(b.code) - Number(a.code);
                });
                queryParams.value.year = yearOption.value[0].code;
                //获取审核状态字典
                /*getDicts("agric_invest_audit_status").then((response) => {
                    auditStatusOption.value = response.data;

                });*/
                getList();
            });
        });
    })
};

/** 搜索按钮操作 */
const handleQuery = () => {
  if(queryRef.value){
    queryRef.value.validate(valid => {
      if (valid) {
        getList();
      }
    })
  }
};

/** 获取订购计划表格数据 */
const getList = () => {
    if (queryParams.value.endDate) {
        queryParams.value.endDate = queryParams.value.endDate + ' 23:59:59'
    }
    getOrderPlan(queryParams.value).then((response) => {
        orderPlanList.value = response.data.dataList;
        columns.value = response.data.columnList;
        title.value = response.data.title;
        unit.value = "单位："+response.data.unit
    })
}

/** 选择生资分类 */
const handleTypeId = (aiTypeList) => {
    queryParams.value.aiTypeId = aiTypeList.aiTypeId;
    handleQuery();
}

/** 重置按钮操作 */
const resetQuery = () => {
    proxy.resetForm("queryRef");
    queryOrgRef.value.clear()
    produceTypeRef.value.clear()
    queryParams.value.year = yearOption.value[0].code;
    queryParams.value.aiCompanyId = companyAbbrOption.value[0].aiCompanyId;
    queryParams.value.unitType="2"
    handleQuery();
}

getDict();

</script>

<style scoped lang="scss">
.el-link {
    margin-right: 8px;
}

.el-link .el-icon--right.el-icon {
    vertical-align: text-bottom;
}
.mb8 {
    min-height: 30px;
}
.title {
    font-size: 20px;
    text-align: center;
    margin-bottom: 4px;
    font-weight: 600;
}
.unit {
  font-size: 10px;
  text-align: right;
  margin-bottom: 4px;
  font-weight: 600;
}
:deep(.el-input__wrapper) {
  width: 190px !important;
}

</style>
