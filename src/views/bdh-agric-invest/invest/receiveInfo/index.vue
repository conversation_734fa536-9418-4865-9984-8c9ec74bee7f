<!--
@name: 农场接货信息汇总表
@description:
@author: liuying
@time: 2022-12-06
-->
<template>
    <div class="app-container">
        <div ref="searchDom">
            <el-collapse-transition>
                <el-form :rules="rules" :model="queryParams" ref="queryRef" v-show="showSearch" label-width="80px" class="form-line">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="年份" prop="year">
                                <el-select
                                    v-model="queryParams.year"
                                    placeholder="请选择年份"
                                    @change="handleQuery"
                                    clearable
                                >
                                    <el-option
                                        v-for="dict in yearOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="企业名称" prop="aiCompanyId">
                                <el-select
                                    v-model="queryParams.aiCompanyId"
                                    placeholder="请选择企业名称"
                                    clearable
                                >
                                    <el-option
                                        v-for="dict in companyAbbrOption"
                                        :key="dict.aiCompanyId"
                                        :label="dict.companyAbbr"
                                        :value="dict.aiCompanyId"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="所在单位" prop="orgCode">
                                <org-select
                                    clearable
                                    ref="queryOrgRef"
                                    defaultOrgCode=true
                                    @handleOrgCode="handleOrgCodeQuery"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="运输方式" prop="transportType">
                                <el-select
                                    v-model="queryParams.transportType"
                                    placeholder="请选择运输方式"
                                    clearable>
                                    <el-option
                                        v-for="dict in transportTypeOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </el-collapse-transition>
        </div>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
          </el-col>
            <el-col :span="1.5">
                <el-button
                    icon="Download"
                    @click="handleExport"
                    v-hasPermi="['receiveInfo#export']"
                >导出
                </el-button>
            </el-col>
            <right-toolbar
                v-model:showSearch="showSearch"
                @queryTable="getList"
            ></right-toolbar>
        </el-row>

        <!-- 查询表格 -->
        <div v-text="title" class="title"></div>
        <el-table border :height="tableHeight" :data="receiveInfoList" :span-method="objectSpanMethod">
            <el-table-column align="center" label="所在分公司" prop="fatherOrgName" />
            <el-table-column align="center" label="所在单位" prop="orgName" />
            <el-table-column align="center" label="运输方式" prop="transportType" :formatter="transportFormat"/>
            <el-table-column align="center" label="所在城市" prop="regionName" />
            <el-table-column align="center" label="到站" prop="arriveStation" />
            <el-table-column align="center" label="专用线" prop="specialLine" />
            <el-table-column align="center" label="接货单位" prop="acceptOrgName" />
            <el-table-column align="center" label="接货机构" prop="acceptOrgFullname" />
            <el-table-column align="center" label="收货人姓名" prop="contractPerson" />
            <el-table-column align="center" label="联系方式" prop="contractPhone" />

        </el-table>
    </div>
</template>

<script name="/report/receiveInfo/rpt" setup>
import {onMounted, reactive, ref, toRefs} from "vue";
import {postForExcel,postForExcelXls} from "@/api/bdh-agric-invest/invest/excel";
import {allCompanyInfo} from "@/api/bdh-agric-invest/invest/companyInfo";
import {getDicts} from "@/api/bdh-agric-invest/system/dict/data";
import {getReceiveInfo} from "@/api/bdh-agric-invest/invest/receiveInfo"
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";

const { proxy } = getCurrentInstance();
const receiveInfoList = ref([]);//农场接货信息列表
const yearOption = ref([]);//年份字典
const companyAbbrOption = ref([]);//企业简称字典
const transportTypeOption = ref([]);//运输方式字典
const spanArr = ref([]);//合并
const showSearch = ref(true);//搜索是否显示
const title = ref("");//表格标题
const queryOrgRef = ref();//组织机构ref
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom

const data = reactive({
    queryParams: {
        year:"2023",
        aiCompanyId: 1,
        orgCode: null,
        transportType: null
    },
    rules: {
        aiCompanyId: [{ required: true, message: "请选择企业名称", trigger: "change"}],
        year: [{ required: true, message: "请选择年份", trigger: "change"}],
    }
})

const {queryParams, rules} = toRefs(data);

onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});

/** 查询农场接货信息列表 */
const getList = () => {
    getReceiveInfo(queryParams.value).then((response) => {
        receiveInfoList.value = response.data.dataList;
        title.value = response.data.title;
        getSpanArr(receiveInfoList.value)
    })
}

//获取单元格的合并行数
const getSpanArr = (list) => {
    let position = null  //当前合并的行位置（连续相同名称的第一条数据位置）
    spanArr.value = [];
    list.forEach((item, index) => {
        if (index === 0) {  // 第一行， 不进行处理
            spanArr.value.push(1)
            position = 0
        } else {
            if (list[index].fatherOrgName === list[index - 1].fatherOrgName) {
                // 当条数据跟上一条数据名称相同，要合并
                spanArr.value[position] += 1  // 首条相同名称行合并行数增加
                spanArr.value.push(0) // 当前行消除
            } else { // 不需要处理的数据
                spanArr.value.push(1)
                position = index
            }
        }
    })
}

//合并单元格
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
    if (columnIndex < 1) {  // 对第一列进行操作
        const _row = spanArr.value[rowIndex]  // 合并行数
        const _col = _row > 0 ? 1 : 0 // 合并列数，1：不改变，0：被消除
        return {
            rowspan: _row,
            colspan: _col
        }
    }
}

//设置表格行的样式类（去除合并行内多余的线条）
const getRowClass = ({ row, rowIndex }) => {
    if (spanArr.value[rowIndex] > 1) { // 相同名称排列的首行数据
        return 'show-span-row'
    } else if (spanArr.value[rowIndex] === 0 && spanArr.value[rowIndex + 1] === 0) {  // 相同名称不处于末尾的被合并数据
        return 'center-span'
    }
}

/** 搜索按钮操作 */
const handleQuery = () => {
    getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
    proxy.resetForm("queryRef");
    queryOrgRef.value.clear();
    handleQuery();
}

/** 选择所在单位 */
const handleOrgCodeQuery = (orgInfo) => {
    queryParams.value.orgCode = orgInfo.orgCode;
};

/** 导出按钮操作 */
const handleExport = () => {
  postForExcelXls('/bdh-agric-invest-api/report/receiveInfo/export', queryParams.value,'农场接货信息汇总表');
}





/** 获取字典 */
const getDict = () => {
    //获取企业名称字典
    allCompanyInfo({}).then((response) => {
        companyAbbrOption.value = response.data;
        //获取年份字典
        getDicts("year_cd").then((response) => {
            yearOption.value = response.data.sort((a, b) => {
                return Number(b.code) - Number(a.code);
            });
            //获取运输方式字典
            getDicts("trp_arrival_type").then((response) => {
                transportTypeOption.value = response.data;
                getList();
            });
        });
    });
};

//表格 运输方式状态栏字典转换
const transportFormat=(row)=>{
    let transTypeName=null
    transportTypeOption.value.forEach(function(item){
        if(item.code===row.transportType+''){
            transTypeName =  item.name
        }
    })
    return transTypeName
}

getDict();
</script>

<style lang="scss" scoped>
.el-link {
    margin-right: 8px;
}

.el-link .el-icon--right.el-icon {
    vertical-align: text-bottom;
}
.mb8 {
    min-height: 30px;
}
.title {
    font-size: 20px;
    text-align: center;
    margin-bottom: 4px;
    font-weight: 600;
}

.el-table {
    :deep(tr) {
        &.show-span-row {
            td:nth-of-type(n + 3) {
                border-bottom: none;
            }
        }

        &.center-span {
            td {
                border-bottom: none;
            }
        }
    }
}
:deep(.el-input__wrapper) {
  width: 190px !important;
}
</style>
