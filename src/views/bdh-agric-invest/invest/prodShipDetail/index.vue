<template>
    <div class="app-container">
        <div ref="searchDom">
            <el-collapse-transition>
                <el-form class="form-line" :model="queryParams" ref="queryRef" :rules="rules"  v-show="showSearch" label-width="80px">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="年份" prop="year">
                                <el-select v-model="queryParams.year"
                                    clearable placeholder="请选择年份">
                                    <el-option
                                        v-for="dict in yearOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="收货单位" prop="orgCode">
                                <org-select ref="queryOrgRef" @handleOrgCode="handleOrgCodeQuery" />
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <b>
                                <el-form-item label="发运日期" prop="sendTime">
                                    <el-date-picker
                                        style="width:100%;"
                                        v-model="dataRange"
                                        @change="handleDataRange"
                                        type="daterange"
                                        value-format="YYYY-MM-DD"
                                        range-separator="至"
                                        start-placeholder="开始时间"
                                        end-placeholder="结束时间"
                                    />
                                </el-form-item>
                            </b>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="企业简称" prop="aiCompanyId">
                                <el-select v-model="queryParams.aiCompanyId" clearable placeholder="请选择企业简称">
                                    <el-option
                                        v-for="dict in companyAbbrOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="到货状态" prop="arriveType">
                                <el-select v-model="queryParams.arriveType"
                                    clearable placeholder="请选择到货状态">
                                    <el-option
                                        v-for="dict in arriveTypeOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="联系人" prop="contactName">
                                <el-input
                                    v-model="queryParams.contactName"
                                    placeholder="请输入联系人"
                                    clearable
                                    @keyup.enter="handleQuery"
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item  label="采购类型">
                              <el-select v-model="queryParams.fillType"
                                         clearable placeholder="请选择采购类型">
                                <el-option
                                    v-for="dict in fillTypes"
                                    :key="dict.code"
                                    :label="dict.name"
                                    :value="dict.code"
                                />
                              </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6" align="right">
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                        </el-col>
                    </el-row>
                </el-form>
            </el-collapse-transition>
        </div>


        <!-- 到货导出（编辑）、导出、到货情况 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    icon="Edit"
                    @click="handleUpdateBatch"
                    :disabled="selectNum===0 || hasArrive"
                    v-hasPermi="['prodshipinfoarrival:confirmArrive']"
                >到货确认
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    icon="Download"
                    @click="handleExport"
                    v-hasPermi="['prodshipinfoarrival:exportExcel']"
                >导出
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['prodshipinfoarrival:arriveInfo']"
                    icon="Search"
                    @click="handleArriveInfo"
                >到货情况
                </el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!-- 分页查询表格 -->
        <el-table border :height="tableHeight"  :data="prodShipInfoList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" fixed="left"/>
          <el-table-column label="发货单ID" align="center" prop="prodShipId"/>
            <el-table-column label="年份" width="80" align="center" prop="year"/>
            <el-table-column label="作业季节" align="center" prop="fertilizeSeason"
                           :formatter="fertilizeSeasonFmt"/>
            <el-table-column label="采购类型" align="center" prop="fillType" :formatter="fillTypeFmt" />
            <el-table-column label="企业简称" align="center" prop="companyAbbr" />
            <el-table-column label="运输方式" align="center" prop="arrivalType" :formatter="transTypeOptionFmt" />
            <el-table-column label="收货单位" align="center" prop="organizationName"/>
            <el-table-column label="发运时间" align="center" prop="sendTime" width="120">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.sendTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="本次到货量" align="center" prop="sendNumTotal">
                <template #default="scope">
                    <a style="color: dodgerblue" @click="clickSendNumTotal(scope.row)" >{{ scope.row.sendNumTotal }}</a>
                </template>
            </el-table-column>
            <el-table-column label="联系人" align="center" prop="contactName"/>
            <el-table-column label="联系电话" align="center" prop="contactPhone"/>
            <el-table-column label="到站" align="center" prop="arrivalName"/>
            <el-table-column label="专用线" align="center" prop="shipCarnum"/>
            <el-table-column label="到货状态" align="center" prop="arriveType" :formatter="arriveTypeFormat"/>
            <el-table-column label="操作" width="120" fixed="right" align="center" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        type="primary"
                        link
                        icon="Edit"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['prodshipinfoarrival:update']"
                        :disabled="scope.row.arriveType === 1"
                    >到货确认
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页查询底部换页栏 -->
        <pagination
            v-show="total>0"
            :total="total"
            v-model:page="queryParams.page"
            v-model:limit="queryParams.rows"
            @pagination="getList"
        />

        <!-- 到货确认对话框 -->
        <el-dialog :title="titleShip" v-model="openShip" width="90%" append-to-body :close-on-click-modal="false">
            <div class="app-container">
                <el-form ref="prodShipInfoRef" :model="shipForm" label-width="80px">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="年份" prop="year">
                                <el-input
                                    v-model="shipForm.year"
                                    disabled
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                          <el-form-item label="作业季节" prop="regionCode">
                            <el-select disabled v-model="shipForm.fertilizeSeason"
                                       clearable
                                       placeholder="请选择作业季节">
                              <el-option
                                  v-for="dict in fertilizes"
                                  :key="dict.code"
                                  :label="dict.name"
                                  :value="dict.code"
                              />
                            </el-select>
                          </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="运输方式" prop="arrivalType">
                                <el-input
                                    type="text"
                                    v-model="arrivalTypeName"
                                    disabled
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="收货单位" prop="organizationName">
                                <el-input
                                    v-model="shipForm.organizationName"
                                    disabled
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="联系人" prop="contactName">
                                <el-input
                                    v-model="shipForm.contactName"
                                    placeholder="请输入联系人"
                                    disabled
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="联系电话" prop="contactPhone">
                                <el-input
                                    v-model="shipForm.contactPhone"
                                    placeholder="请输入联系电话"
                                    disabled
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-row :gutter="20" class="mb8">
                <el-col :span="1.5">
                    <el-button type="primary" v-if="showArriveSubmit" @click="submitForm">确 定</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button @click="cancel">取 消</el-button>
                </el-col>
            </el-row>
            <el-table border height="470"  :data="shipList">
                <el-table-column label="企业简称" align="center" prop="companyAbbr"/>
                <el-table-column label="投入品分类" align="center" prop="aiTypeName"/>
                <el-table-column label="商品编码" align="center" prop="aiTypeSubCode"/>
                <el-table-column label="商品名称" align="center" prop="aiTypeSubName"/>
                <el-table-column label="规格" align="center" prop="aiTypeSubSpecs"/>
                <el-table-column label="发货数量" align="center" prop="sendNum"/>
                <el-table-column label="单位" align="center" prop="aiTypeReportUnit"/>
            </el-table>

            <!-- <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" v-if="showArriveSubmit" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template> -->
        </el-dialog >

        <!--确认到货手动选择库房-->
        <el-dialog title="选择库房" v-model="openWarehouse" width="450px" append-to-body :close-on-click-modal="false">
            <el-table border  height="470" :data="warehouseList" style="width: 400px;">
                <el-table-column label="库房列表" align="center" prop="warehouseName"/>
                <el-table-column label="操作" width="120" fixed="right" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button
                                type="primary"
                                link
                                icon="Edit"
                                @click="handleChooseWarehouse(scope.row)"
                        >选择库房
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>

        <el-dialog title="选择库房" v-model="openWarehouseBatch" width="450px" append-to-body :close-on-click-modal="false">
            <el-table border  height="470" :data="warehouseListBatch" style="width: 400px;">
                <el-table-column label="库房列表" align="center" prop="warehouseName"/>
                <el-table-column label="操作" width="120" fixed="right" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button
                                type="primary"
                                link
                                icon="Edit"
                                @click="handleChooseWarehouseBatch(scope.row)"
                        >选择库房
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </el-dialog>

        <!-- 到货情况对话框 -->
        <el-dialog :title="titleArrive" v-model="openArrive" width="90%" append-to-body :close-on-click-modal="false">
            <div class="el-dialog-div">
                <el-form ref="prodArriveInfoRef" :model="arriveQueryParams" label-width="auto">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="年份" prop="year">
                                <el-select
                                    v-model="arriveQueryParams.year"
                                    clearable
                                    placeholder="请选择年份">
                                    <el-option
                                        v-for="dict in yearOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="企业简称" prop="aiCompanyId">
                                <el-select v-model="arriveQueryParams.aiCompanyId"
                                            clearable placeholder="请选择企业简称">
                                    <el-option
                                        v-for="dict in companyAbbrOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6" :push="6" align="right">
                            <el-button icon="Refresh" @click="resetArriveQuery">重置</el-button>
                            <el-button type="primary" icon="Search" @click="handleArriveQuery">搜索</el-button>
                        </el-col>
                    </el-row>
                </el-form>
                <el-table border  height="400" :data="arriveList" style="width: 100%;">
                    <el-table-column label="企业简称" align="center" prop="companyAbbr"/>
                    <el-table-column label="投入品分类" align="center" prop="aiTypeName"/>
                    <el-table-column label="商品编码" align="center" prop="aiTypeSubCode"/>
                    <el-table-column label="商品名称" align="center" prop="aiTypeSubName"/>
                    <el-table-column label="规格" align="center" prop="aiTypeSubSpecs"/>
                    <el-table-column label="订购量" align="center" prop="currentNum"/>
                    <el-table-column label="发货总量" align="center" prop="alreadySendNum"/>
                    <el-table-column label="单位" align="center" prop="aiTypeReportUnit"/>
                </el-table>
            </div>
        </el-dialog>
    </div>
</template>

<script setup name="/invest/prodShipDetail/arrival/queryByPage">
import {
    detail,
    confirmShip,
    listProdShipInfo,
    getProdShipInfo,
    updateProdShipInfo,
    detailList, listFarmWarehouseList,
} from "@/api/bdh-agric-invest/invest/prodShipDetail";
import {computed, reactive, ref, toRefs} from "vue";
import {allCompanyInfo} from "@/api/bdh-agric-invest/invest/companyInfo";
import {getDicts} from "@/api/bdh-agric-invest/system/dict/data";
import {postForExcel} from "@/api/bdh-agric-invest/invest/excel";
import { queryAllOrgTree} from "@/api/bdh-agric-invest/system/dict/data";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import {delFarmWarehouse, delFarmWarehouses, listFarmWarehouse} from "@/api/bdh-agric-invest/invest/farmWarehouse";
const fertilizes = ref([]);
const fillTypes=ref([]);
const {proxy} = getCurrentInstance();
const arriveLoading = ref(true);
const prodShipInfoList = ref([]);
const openShip = ref(false);
const openArrive = ref(false);
const openWarehouse = ref(false);//到货确认打开库房列表
const warehouseList=ref([]);//库房列表
const openWarehouseBatch = ref(false)//批量到货时打开库房列表
const warehouseListBatch=ref([]);//批量到货时的库房列表
const loading = ref(true);
const detailLoading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const selectNum = ref(0);
const hasArrive = ref(false);
const total = ref(0);
const titleShip = ref("");
const titleArrive = ref("");
const yearOption = ref([]);
const companyAbbrOption = ref([]);
const arriveTypeOption = ref([]);
const transTypeOption = ref([]);
const dataRange = ref([]);
const queryOrgRef = ref()
const showArriveSubmit= ref(true)
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const data = reactive({
    //分页查询表单
    form: {},

    //确认到货对话框展示列表
    shipList: [],

    //确认到货对话框展示列表+公共数据
    shipForm: {},

    //查询参数
    queryParams: {
        rows: 10,
        page: 1,
        year: null,
        arriveType: null,
        orgCode: null,
        aiCompanyId: null,
        contactName: null,
        sendTimeStartTime: null,
        sendTimeEndTime:null,
        fillType:null
    },

    //到货情况对话框表单
    arriveList: [],

    //查询参数
    arriveQueryParams: {
        year: null,
        aiCompanyId: null,
    },

    rules: {
        contactName: [
            {max: 10, message: "最多10个字符", trigger: "blur"}
        ],
    }
});
const defaultOrgCode = ref('');

const {queryParams, shipList, shipForm, arriveList, form, arriveQueryParams, rules} = toRefs(data);

//表格 运输方式状态栏字典转换
const transTypeOptionFmt=(row)=>{
    let transTypeName=null
    transTypeOption.value.forEach(function(item){
        if(item.code===row.arrivalType+''){
            transTypeName =  item.name
        }
    })
    return transTypeName
}

onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});

function fertilizeSeasonFmt(row){
  for(let i=0;i<fertilizes.value.length;i++){
    if(fertilizes.value[i].code==row.fertilizeSeason){
      return fertilizes.value[i].name
    }
  }
}

function fillTypeFmt(row){
  for(let i=0;i<fillTypes.value.length;i++){
    if(fillTypes.value[i].code==row.fillType){
      return fillTypes.value[i].name
    }
  }
}

//表格 到货状态字典转换
function arriveTypeFormat(row) {
    let arriveType = null
    if (arriveTypeOption.value.length > 0) {
        arriveTypeOption.value.forEach((v, i) => {
            if (v.code == row.arriveType) {
                arriveType = v.name
            }
        })
    }
    return arriveType;
}

//到货确认对话框  运输方式字典转换 —— 计算属性实现
const arrivalTypeName = computed(()=>{
    let transTypeName=null
    transTypeOption.value.forEach(item => {
        if(item.code===shipForm.value.arrivalType+''){
            transTypeName =  item.name
        }
    })
    return transTypeName
})

/** 获取字典 */
const getDict = () => {
    //查询年份
    getDicts('year_cd').then(response => {
        yearOption.value = response.data.sort((a, b) => {
            return Number(b.code) - Number(a.code)
        })
        queryParams.value.year = yearOption.value[0].code
        queryAllOrgTree("3").then((res) => {
            var orgList = res.data;
            if (orgList && orgList.length && orgList.length > 0) {
                defaultOrgCode.value = orgList[0].orgCode;
                queryParams.value.orgCode = defaultOrgCode.value;
            }
            getList()
        })
    });

    //企业简称
    allCompanyInfo({}).then(response => {
        response.data.forEach(each => {
            companyAbbrOption.value.push({
                code: each.aiCompanyId,
                name: each.companyAbbr
            })
        })
    });

    //到货状态
    getDicts('arrive_type').then(response => {
        arriveTypeOption.value = response.data
        arriveTypeOption.value.unshift({code:"-1", name:"全部"})
        queryParams.value.arriveType = arriveTypeOption.value[0].code
    });

    //运输方式
    getDicts('trp_arrival_type').then(response => {
        transTypeOption.value = response.data
    });

    getDicts("fertilize_season").then((response) => {
      fertilizes.value = response.data
    });

    getDicts("invest_ship_fill_type").then(response=>{
      fillTypes.value=response.data
    });
}

/** 组织机构下拉 */
const handleOrgCodeQuery = (orgInfo) => {
    queryParams.value.orgCode = orgInfo.orgCode
}

/** 取出时间范围的起止时间 */
const handleDataRange = () =>{
    console.log(dataRange)
    queryParams.value.sendTimeStartTime = dataRange.value[0];
    queryParams.value.sendTimeEndTime = dataRange.value[1];
}

/** 查询发运信息列表 */
function getList() {
    loading.value = true;
    if(queryParams.value.arriveType === "-1") {
        queryParams.value.arriveType = null
    }
    listProdShipInfo(queryParams.value).then(response => {
        prodShipInfoList.value = response.data.records;
        total.value = response.data.total;
        loading.value = false;
    });
}

// 多选框选中数据
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.prodShipId);
    selectNum.value = selection.length;    //选中项个数
    hasArrive.value = false;
    selection.forEach(item => {     //是否已到货
        if(item.arriveType === 1){
            hasArrive.value = true;
            proxy.$modal.notifyWarning("已到货的无需确认到货！");
        }
    });
}

/** 确认到货按钮操作 */

//列表上方的确认到货按钮
function handleUpdateBatch(){
    if(selectNum.value > 1){//批量确认到货时，直接提交
        openShip.value = false;
        listFarmWarehouse({warehouseName:'',queryPageType:2}).then((response) => {
            warehouseListBatch.value = response.data.records;
            openShip.value = false;
            openWarehouseBatch.value=true
        });
        // const prodShipIds = ids.value
        // confirmShip(prodShipIds).then(response => {
        //     proxy.$modal.msgSuccess("确认收货成功");
        //     openShip.value = false;
        //     getList();
        // });
    }
    if(selectNum.value === 1){       //单个确认到货时，先查看信息再提交
        reset();
        detailLoading.value = true;
        openShip.value = false;
        const prodShipId = ids.value[0]
        detail({prodShipId : prodShipId}).then(response => {
            shipForm.value = response.data
            shipForm.value.arrivalType = shipForm.value.arrivalType+""
            shipList.value = response.data.aiProdShipDetailList
            detailLoading.value = false;
            openShip.value = true;
            titleShip.value = "确认到货";
        });
    }
}

//列表内的确认到货按钮
function handleUpdate(row) {
    if(row !== null){ //单个确认到货时，先查看信息再提交
        reset();
        detailLoading.value = true;
        openShip.value = false;
        const prodShipId = row.prodShipId
        detail({prodShipId : prodShipId}).then(response => {
            shipForm.value = response.data
            shipForm.value.arrivalType = shipForm.value.arrivalType+""
            shipList.value = response.data.aiProdShipDetailList
            detailLoading.value = false;
            openShip.value = true;
            titleShip.value = "确认到货";
        });
    }
}

/** 提交按钮 ————确认收货 */
//单条数据时弹出对应发货单信息
function submitForm() {
    proxy.$refs["prodShipInfoRef"].validate(valid => {
        if (valid) {
            if (shipForm.value.prodShipId != null) {
                listFarmWarehouseList({}).then((response) => {
                    warehouseList.value = response.data;
                    openWarehouse.value=true
                });
                // const id=new Array();
                // id.push(shipForm.value.prodShipId)  //由于接口是数组，即使一条id也要包为数组
                // confirmShip(id).then(response => {
                //     proxy.$modal.msgSuccess("确认收货成功");
                //     openShip.value = false;
                //     getList();
                // });
            }
        }
    });
}

// 选择库房
//选择单条数据时的选择库房
function handleChooseWarehouse(row){
   const warehouseId=row.warehouseId;
   const warehouseName=row.warehouseName;
        const id=new Array();
        id.push(shipForm.value.prodShipId)  //由于接口是数组，即使一条id也要包为数组
        proxy
            .$confirm("是否确认选择此库房?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
            .then(function () {
                return confirmShip({prodShipIdList:id,warehouseId:warehouseId,warehouseName:warehouseName})
            })
            .then(() => {
                proxy.$modal.msgSuccess("确认收货成功");
                openWarehouse.value=false;
                openShip.value = false;
                getList();
            });
}

//选择多条数据时的选择库房
function handleChooseWarehouseBatch(row){
    const warehouseId=row.warehouseId;
    const warehouseName=row.warehouseName;
    if(selectNum.value > 1){
        const prodShipIds = ids.value
        proxy
            .$confirm("是否确认选择此库房?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            })
            .then(function () {
                return confirmShip({prodShipIdList:prodShipIds,warehouseId:warehouseId,warehouseName:warehouseName})
            })
            .then(() => {
                proxy.$modal.msgSuccess("确认收货成功");
                openWarehouseBatch.value=false;
                openShip.value = false;
                getList();
            });
    }
}


//点击 到货情况
function handleArriveInfo() {

    titleArrive.value = "到货情况";
    let now= new Date();
    arriveQueryParams.value.year = now.getFullYear()+"";
    arriveQueryParams.value.aiCompanyId=null;
    getArriveList()
}

//查询 到货情况
function getArriveList(){
    arriveLoading.value = true;
    openArrive.value = true;
    detailList(arriveQueryParams.value).then( response=>{
        arriveList.value = response.data
        arriveLoading.value = false;
    })
}

// 取消按钮
function cancel() {
    openShip.value = false;
    reset();
    resetShipForm()
}

// 表单重置
function reset() {
    form.value = {
        year: null,
        aiCompanyId: null,
        arrivalType: null,
        orgCode: null,
        orgName: null,
        sendTime: null,
        regionCode: null,
        regionName: null,
        shipTitle: null,
        contactName: null,
        contactPhone: null,
        arrivalName: null,
        shipCarnum: null,
        arriveType: null,
        confirmStatus: null,
        createBy: null,
        updateBy: null,
        updateTime: null,
        statusCd: null,
        prodShipId: null,
        createTime: null
    };
    proxy.resetForm("prodShipInfoRef");
}

function resetShipForm(){
    shipForm.value={
        aiCompanyId: null,
        aiProdShipDetailList: [],
        arrivalName: null,
        arrivalType: null,
        arriveType: null,
        companyAbbr: null,
        companyName: null,
        confirmStatus: null,
        contactName: null,
        contactPhone: null,
        createBy: null,
        createTime: null,
        organizationName: null,
        organizationNo: null,
        prodShipId: null,
        regionCode: null,
        regionName: null,
        sendTime: null,
        shipCarnum: null,
        shipTitle: null,
        statusCd: null,
        updateBy: null,
        updateTime: null,
        year: null
    }
}

/** 搜索按钮操作 */
function handleQuery() {
    // queryParams.value.rows = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    queryOrgRef.value.clear();
    dataRange.value = [];
    queryParams.value.sendTimeStartTime = null
    queryParams.value.sendTimeEndTime = null
    queryParams.value.year = yearOption.value[0].code;
    queryParams.value.orgCode = defaultOrgCode.value;
    queryParams.value.fillType = null;
    queryOrgRef.value.setValue(defaultOrgCode.value, "");
    handleQuery();
}

/** 到货详情对话框，搜索按钮 */
function handleArriveQuery(){
    getArriveList()
}

/** 到货详情对话框，重置按钮 */
function resetArriveQuery(){
    proxy.resetForm("prodArriveInfoRef");
    let now= new Date();
    arriveQueryParams.value.year = now.getFullYear()+"";
    getArriveList()
}

/** 导出按钮操作 */
function handleExport() {
    if(queryParams.value.arriveType === "-1") {
        queryParams.value.arriveType = null
    }
    postForExcel('/bdh-agric-invest-api/invest/prodShipInfo/arrival/exportExcel', queryParams.value,'到货确认')
}

function clickSendNumTotal(row){
    //已到货的只能查看详情
    if(row.arriveType === 1){
      showArriveSubmit.value=false
    }else{
      showArriveSubmit.value=true
    }
    handleUpdate(row)
}

getDict();
</script>

<style lang="scss" scoped>
.el-dialog-div{
    width: 100%;
    height: 70vh;
    overflow: auto;
}

</style>
