<template>
    <div class="app-container">
        <div ref="searchDom">
            <el-collapse-transition>
                <el-form :model="queryParams" ref="queryRef" class="form-line" v-show="showSearch" label-width="80px">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="年份" prop="year">
                                <el-select v-model="queryParams.year" clearable placeholder="请选择年份">
                                    <el-option
                                        v-for="dict in yearOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="收货单位" prop="organizationNo" label-width="auto">
                                <org-select ref="queryOrgRef" @handleOrgCode="handleOrgCodeQuery" />
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="企业简称" prop="aiCompanyId" label-width="auto">
                                <el-select v-model="queryParams.aiCompanyId" clearable placeholder="请选择企业简称">
                                    <el-option
                                        v-for="dict in companyAbbrOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="到货状态" prop="arriveType" label-width="auto">
                                <el-select v-model="queryParams.arriveType" clearable placeholder="请选择到货状态">
                                    <el-option
                                        v-for="dict in arriveTypeOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                          <el-form-item label="作业季节" prop="fertilizeSeason">
                            <el-select
                                v-model="queryParams.fertilizeSeason"
                                clearable
                                placeholder="请选择作业季节">
                              <el-option
                                  v-for="dict in fertilizes"
                                  :key="dict.code"
                                  :label="dict.name"
                                  :value="dict.code"
                              />
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="6" :push="12" align="right">
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                        </el-col>
                    </el-row>
                </el-form>
            </el-collapse-transition>
        </div>

        <!-- 新增、修改、删除、导出 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    icon="Plus"
                    @click="handleAdd"
                    v-hasPermi="['prodShipInfo:insert']"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    icon="Edit"
                    :disabled="single"
                    @click="handleUpdate"
                    v-hasPermi="['prodShipInfo:update']"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    icon="Delete"
                    :disabled="multiple"
                    @click="handleDelete"
                    v-hasPermi="['prodShipInfo:logicDeleteByIds']"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    icon="Download"
                    @click="handleExport"
                    v-hasPermi="['prodShipInfo:exportExcel']"
                >导出
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    icon="Check"
                    :disabled="single"
                    @click="handleApprove"
                    v-hasPermi="['prodShipInfo:approve']"
                >审核
                </el-button>
            </el-col>
<!--          <el-col :span="1.5">
            <el-button
                type="success"
                plain
                icon="Check"
                :disabled="cancelShipFlag"
                @click="handleCancelReturn"
                v-hasPermi="['prodShipInfo:approve']"
            >撤回发货
            </el-button>
          </el-col>-->
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!--审核-->
        <el-dialog :title="title" v-model="openApproveView"  append-to-body :close-on-click-modal="false">
            <el-form ref="approveForm" :model="form" :rules="rulesApprove" label-width="100px">
                <el-form-item label="是否通过" prop="agree">
                    <el-radio v-model="form.agree" :label="true">同意</el-radio>
                    <el-radio v-model="form.agree" :label="false">不同意</el-radio>
                </el-form-item>
                <el-form-item label="审核意见" prop="opinion">
                    <el-input v-model="form.opinion" type="textarea"/>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitApproveForm">确 定</el-button>
                </div>
            </template>
        </el-dialog>

        <!-- 分页查询表格 -->
        <el-table border :height="tableHeight"  :data="prodShipInfoList" @selection-change="handleSelectionChange" ref="mainTable">
            <el-table-column type="selection" width="55" fixed="left" align="center"/>
            <el-table-column label="发货单ID" align="center" prop="prodShipId"/>
            <el-table-column label="年份" align="center" prop="year"/>
            <el-table-column label="作业季节" align="center" prop="fertilizeSeason"
                             :formatter="fertilizeSeasonFmt"/>
            <el-table-column label="企业简称" align="center" prop="companyAbbr" />
            <el-table-column label="运输方式" align="center" prop="arrivalType" :formatter="transTypeOptionFmt" />
            <el-table-column label="收货单位" align="center" :show-overflow-tooltip="true" prop="organizationName"/>
            <el-table-column label="发运时间" align="center" prop="sendTime" width="120">
                <template #default="scope">
                    <span>{{ parseTime(scope.row.sendTime, '{y}-{m}-{d}') }}</span>
                </template>
            </el-table-column>
            <el-table-column label="所在省市" align="center" prop="regionName"/>
            <el-table-column label="发货总量" align="center" prop="sendNumTotal">
                <template #default="scope">
                    <a style="color: dodgerblue" @click="handleUpdate(scope.row)" >{{ scope.row.sendNumTotal }}</a>
                </template>
            </el-table-column>
            <el-table-column label="联系人" align="center" prop="contactName"/>
            <el-table-column label="联系电话" align="center" prop="contactPhone"/>
            <el-table-column label="到站" align="center" :show-overflow-tooltip="true" prop="arrivalName"/>
            <el-table-column label="专用线" align="center" :show-overflow-tooltip="true" prop="shipCarnum"/>
            <el-table-column label="审核状态" align="center" prop="auditStatus" :formatter="auditStatuFormat"/>
            <el-table-column label="审核人" align="center" prop="auditPerson" :show-overflow-tooltip="true" :formatter="fmtAuditName"/>
            <el-table-column label="到货状态" align="center" prop="arriveType" :formatter="arriveTypeFormat"/>
            <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
            <el-table-column label="操作" align="center" width="150" fixed="right" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button
                        type="primary"
                        link
                        icon="Edit"
                        v-if="scope.row.auditStatus!=1&&scope.row.auditStatus!=4"
                        @click="handleUpdate(scope.row)"
                        v-hasPermi="['prodShipInfo:update']"
                    >修改
                    </el-button>
                    <el-button
                        type="primary"
                        link
                        icon="Delete"
                        @click="handleDelete(scope.row)"
                        v-hasPermi="['prodShipInfo:logicDeleteById']"
                    >删除
                    </el-button>
                    <br>
                  <el-button
                      v-if="scope.row.arriveType === 0 && scope.row.auditStatus === 0"
                      type="primary"
                      link
                      icon="RemoveFilled"
                      @click="handleCancelShip(scope.row)"
                  >撤回发货
                  </el-button>
                  <el-button
                      v-if="scope.row.auditStatus === 3"
                      type="primary"
                      link
                      icon="CircleCheck"
                      @click="handleStartFlow(scope.row)"
                  >发起流程
                  </el-button>
                    <el-link type="success" icon="User" @click="viewFlow(scope.row)">查看流程</el-link>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页查询底部换页栏 -->
        <pagination
            v-show="total>0"
            :total="total"
            v-model:page="queryParams.page"
            v-model:limit="queryParams.rows"
            @pagination="getList"
        />

        <!-- 查看流程对话框 -->
        <el-dialog
            v-model="processVisible"
            title="查看流程"
            width="800px"
            :close-on-click-modal="false"
        >
            <FlowDetail v-model="form.instId" v-if="flowDetailTag" ></FlowDetail>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="processVisible = false">关闭</el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 添加或修改发运信息对话框 -->
        <el-dialog :title="title" v-model="open" :close-on-click-modal="false" width="90%" append-to-body>
            <div class="app-container">
                <el-form ref="prodShipInfoRef" :disabled="readOnly" :model="shipForm" :rules="rulesShip" label-width="100px">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="年份" prop="year">
                                <el-select
                                    v-model="shipQueryParams.year"
                                    @change="handleQueryDetail"
                                    clearable
                                    :disabled="insertOrEdit"
                                    placeholder="请选择年份">
                                    <el-option
                                        v-for="dict in yearOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                          <el-form-item label="作业季节" prop="regionCode">
                            <el-select :disabled="insertOrEdit"
                                v-model="shipQueryParams.fertilizeSeason"
                                @change="handleQueryDetail"
                                clearable
                                placeholder="请选择作业季节">
                              <el-option
                                  v-for="dict in fertilizes"
                                  :key="dict.code"
                                  :label="dict.name"
                                  :value="dict.code"
                              />
                            </el-select>
                          </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="收货单位" prop="organizationNo">
                                <org-select :disabled="insertOrEdit" v-if="orgFlag" ref="shipOrgRef" :defaultOrgCode="shipQueryParams.organizationNo"
                                            @handleOrgCode="handleOrgCodeQueryDetail"/>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="运输方式" prop="arrivalType">
                                <el-select v-model="shipQueryParams.arrivalType"
                                           @change="handleQueryDetail"
                                           placeholder="请选择运输方式"
                                           :disabled="insertOrEdit"
                                           clearable>
                                    <el-option
                                        v-for="dict in transTypeOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>


                    </el-row>

                    <el-row :gutter="20">
                      <el-col :span="6">
                        <el-form-item label="专用线" prop="shipCarnum">
                          <el-input
                              v-model="shipForm.shipCarnum"
                              placeholder="请输入专用线"
                              disabled
                          />
                        </el-form-item>
                      </el-col>
                        <el-col :span="6">
                            <el-form-item label="联系人" prop="contactName">
                                <el-input
                                    v-model="shipForm.contactName"
                                    placeholder="请输入联系人"
                                    disabled
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="联系电话" prop="contactPhone">
                                <el-input
                                    v-model="shipForm.contactPhone"
                                    placeholder="请输入联系电话"
                                    disabled
                                />
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="所在省市" prop="regionCode">
                                <el-cascader
                                    ref="regionCascader"
                                    v-model="shipForm.regionCode"
                                    @change="handleRegionSelect"
                                    :props="regCascProps"
                                    :options="regionOrgOption"
                                    disabled
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row  :gutter="20">
                        <el-col :span="6">
                        <el-form-item label="发运时间" prop="sendTime">
                            <el-date-picker clearable
                                            v-model="shipForm.sendTime"
                                            ref="picker"
                                            @focus="focus"
                                            type="date"
                                            value-format="YYYY-MM-DD"
                                            placeholder="请选择发运时间">
                            </el-date-picker>
                        </el-form-item>
                        </el-col>
                        <el-col :span="18">
                            <el-form-item label="备注" prop="remark">
                                <el-input
                                    maxlength="500"
                                    v-model="shipForm.remark"
                                    placeholder="请输入备注"
                                    clearable
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>

            <el-table height="450" border  :data="planList">
                <el-table-column label="企业简称" align="center" prop="companyAbbr"/>
                <el-table-column label="投入品分类" align="center" prop="aiTypeName"/>
                <el-table-column label="商品编码" align="center" prop="aiTypeSubCode"/>
                <el-table-column label="商品名称" align="center" prop="aiTypeSubName"/>
                <el-table-column label="规格" align="center" prop="aiTypeSubSpecs"/>
                <el-table-column label="商品重量" align="center" prop="aiTypeSubWeight">
                  <template #default="scope">
                    {{scope.row.aiTypeSubWeight}}{{scope.row.aiTypeSubUnit}}
                  </template>
                </el-table-column>
                <el-table-column label="订购量" align="center" v-if="!readOnly" prop="currentNum"/>
                <el-table-column label="已发货量" align="center" v-if="!readOnly" prop="alreadySendNum"/>
                <el-table-column label="发货待审核量" align="center" v-if="!readOnly" prop="alreadySendButNotPassNumber"/>
                <el-table-column label="本次发货量" align="center" prop="sendNum" width="250">
                    <template #default="scope">
                      <el-input width="200" :disabled="readOnly"
                                v-model="scope.row.sendNum"
                                @change="handleSendNum(scope.row)"
                                @input="checkNumber($event,scope.row,'sendNum')"
                                placeholder="请输入发货量"
                                clearable
                                maxlength="10"
                      >
                        <template #append>{{ scope.row.aiTypeReportUnit }}</template>
                      </el-input>
                    </template>
                </el-table-column>
                <el-table-column label="预计发货总量" v-if="!readOnly" align="center" prop="estimateSendTotal"/>
                <el-table-column label="单位" align="center" prop="aiTypeReportUnit"/>
            </el-table>

            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" :loading="isloading" v-if="!readOnly" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>



    </div>
</template>

<script setup name="/invest/prodShipInfo/send/queryByPage">
import {
  listProdShipInfo,
  getProdShipInfo,
  delProdShipInfo,
  addProdShipInfo,
  updateProdShipInfo,
  getPropertyFromAiConsigneeInfo,
  getProdReportPlanConfigList,
  approve, cancelShip,startFlow
} from "@/api/bdh-agric-invest/invest/prodShipInfo";
import {reactive, ref, toRefs} from "vue";
import {allCompanyInfo} from "@/api/bdh-agric-invest/invest/companyInfo";
import {getDicts} from "@/api/bdh-agric-invest/system/dict/data";
import {queryRegionOrg} from "@/api/bdh-agric-invest/invest/consigneeInfo";
import {postForExcel} from "@/api/bdh-agric-invest/invest/excel";
import { queryAllOrgTree} from "@/api/bdh-agric-invest/system/dict/data";
import {checkNumber} from "@/views/bdh-agric-invest/utils/validate";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import FlowDetail from "@/views/bdh-agric-invest/components/FlowDetail/index.vue";
const {proxy} = getCurrentInstance();
const fertilizes = ref([]);
const prodShipInfoList = ref([]);
const open = ref(false);
const readOnly = ref(false);
const loading = ref(true);
const detailLoading = ref(false);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const cancelShipFlag = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const yearOption = ref([]);
const companyAbbrOption = ref([]);
const arriveTypeOption = ref([]);
const transTypeOption = ref([]);
const auditStatusOption = ref([]);
const regionOrgOption = ref([]);
const instId = ref(null);
const openApproveView = ref(false);
const canSubmit = ref(true);
const insertOrEdit = ref(true);
const isloading = ref(false)
const orgFlag = ref(false)
const flowDetailTag=ref(false);
const processVisible = ref(false);
const queryOrgRef = ref();
const shipOrgRef = ref();
const canCancelShipIdsArr = ref([]);
const picker = ref();
const regCascProps = {
    emitPath: false,
    value: "regionCode",
    label: "regionName"
}
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const data = reactive({
    //审核表单
    form: {},

    //新增修改对话框返回表单
    planList: [],

    //TODO  organizationNo不能默认死数据，应该为列表第一条
    shipForm: {
        year: null,
        arrivalType: null,
        organizationNo: null,
        fertilizeSeason:null
    },

    //新增页面查询参数
    shipQueryParams: {
        year: null,
        arrivalType: null,
        organizationNo: null,
        fertilizeSeason:null,
    },

    //查询参数
    queryParams: {
        rows: 10,
        page: 1,
        year: null,
        arriveType: null,
        organizationNo: null,
    },

    rulesShip: {
        year: [
            {required: true, message: "请输入年份", trigger: "change"}
        ],
        fertilizeSeason: [
          {required: true, message: "请选择作业季节", trigger: "change"}
        ],
        organizationNo: [
            {required: true, message: "请输入收货单位", trigger: "change"}
        ],
        arrivalType: [
            {required: true, message: "请输入运输方式", trigger: "change"}
        ],
        sendTime: [
            {required: true, message: "请输入发运时间", trigger: "change"}
        ],
        contactName: [
            {required: true, message: "当前收货单位下无联系人", trigger: "change"}
        ]
    },

    rulesApprove: {
        agree: [
            {required: true, message: "请选择是否同意", trigger: "change"},
        ],
        opinion: [
            {required: true, message: "请输入审核意见", trigger: "blur"},
            {max: 100, message: "最多100个字符", trigger: "blur"}
        ],
    }

});
const defaultOrgCode = ref('');

const {queryParams, shipForm, shipQueryParams, planList, form, rulesShip, rulesApprove} = toRefs(data);
onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
//表格 运输方式状态栏字典转换
const transTypeOptionFmt=(row)=>{
    let transTypeName=null
    transTypeOption.value.forEach(function(item){
        if(item.code===row.arrivalType+''){
            transTypeName =  item.name
        }
    })
    return transTypeName
}

function fertilizeSeasonFmt(row){
  for(let i=0;i<fertilizes.value.length;i++){
    if(fertilizes.value[i].code==row.fertilizeSeason){
      return fertilizes.value[i].name
    }
  }
}

function focus() {
  picker.value.handleOpen();
}

/** 批量撤回发货 */
function handleCancelReturn(){
  const ids = canCancelShipIdsArr.value;
  proxy.$modal.confirm('是否确认批量撤回发货？').then(function () {
    return cancelShip(ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("批量撤回发货成功");
  }).catch(() => {
  });
}

//撤回发货
function handleCancelShip(data){
    const arr = [];
    arr.push(data.prodShipId);
  cancelShipByIds(arr);
}


function handleStartFlow(data){

  proxy.$modal.confirm('确认发起流程么？').then(function () {
    return startFlow(data);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("发起流程成功");
  }).catch(() => {
  });
}

function cancelShipByIds(ids){
  proxy.$modal.confirm('是否确认撤回发货？').then(function () {
    return cancelShip(ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("撤回发货成功");
  }).catch(() => {
  });
}

//表格 审核状态栏字典转换
function auditStatuFormat(row) {
    if (row.auditStatus == null){
      return '未审核'
    }
    let auditStatus = null
    if (auditStatusOption.value.length > 0) {
        auditStatusOption.value.forEach((v, i) => {
            if (v.code == row.auditStatus) {
                auditStatus = v.name
            }
        })
    }
    return auditStatus;
}

//表格 到货状态字典转换
function arriveTypeFormat(row) {
  if (row.arriveType == null){
    return '已撤回'
  }
    let arriveType = null
    if (arriveTypeOption.value.length > 0) {
        arriveTypeOption.value.forEach((v, i) => {
            if (v.code == row.arriveType) {
                arriveType = v.name
            }
        })
    }
    return arriveType;
}

//表格 审核人字典转换
function fmtAuditName(row,column){
    if(row.flowMsg&&JSON.parse(row.flowMsg).auditNames){
        return JSON.parse(row.flowMsg).auditNames
    }
    return "无";
}

/** 获取字典 */
const getDict = () => {
    //查询年份
    getDicts('year_cd').then(response => {
        yearOption.value = response.data.sort((a, b) => {
            return Number(b.code) - Number(a.code)
        })
        queryParams.value.year = yearOption.value[0].code
        queryAllOrgTree("1").then((res) => {
            var orgList = res.data;
            if (orgList && orgList.length && orgList.length > 0) {
                defaultOrgCode.value = orgList[0].orgCode;
                queryParams.value.organizationNo = defaultOrgCode.value;
            }
            getList()
        })
    });

    //企业简称
    allCompanyInfo({}).then(response => {
        response.data.forEach(each => {
            companyAbbrOption.value.push({
                code: each.aiCompanyId,
                name: each.companyAbbr
            })
        })
    });

    //到货状态
    getDicts('arrive_type').then(response => {
        arriveTypeOption.value = response.data
        arriveTypeOption.value.unshift({code:"-1", name:"全部"})
        queryParams.value.arriveType = arriveTypeOption.value[0].code
    });

    //运输方式
    getDicts('trp_arrival_type').then(response => {
        transTypeOption.value = response.data
    });

    //审核状态
    getDicts('agric_invest_audit_status').then(response => {
        auditStatusOption.value = response.data
    });


    getDicts("fertilize_season").then((response) => {
      fertilizes.value = response.data
    });

    //查询省市
    queryRegionOrg().then(response => {
        regionOrgOption.value = response.data
    })
}

/** 选择省市后将省市名称也加入form */
const handleRegionSelect = () => {
    let nodesInfo = proxy.$refs.regionCascader.getCheckedNodes()
    regionOrgOption.value[0].children.forEach(each => {
        if (each.regionCode === nodesInfo[0].data.regionCode) {
            shipForm.value.regionName = nodesInfo[0].data.regionName
        }
    })
}

/** 组织机构下拉 */
const handleOrgCodeQuery = (orgInfo) => {
    if(!orgInfo.orgCode) queryOrgRef.value.clear()
    queryParams.value.organizationNo = orgInfo.orgCode
}

/** 新增修改对话框 —— 组织机构下拉 */
const handleOrgCodeQueryDetail = (orgInfo) => {
    if(!orgInfo.orgCode) shipOrgRef.value.clear()
    shipQueryParams.value.organizationNo = orgInfo.orgCode
    handleQueryDetail()
}

/** 触发查询  投入品计划、联系人信息 */
function handleQueryDetail () {
    //每次修改年份、所在单位、到达状态三个参数，重置其他
    resetShipForm()
    console.log(shipQueryParams.value)
    //将查询参数shipQueryParams填充入shipForm中，因为rules校验的是shipForm
    shipForm.value.year = shipQueryParams.value.year;
    shipForm.value.organizationNo = shipQueryParams.value.organizationNo;
    shipForm.value.arrivalType = shipQueryParams.value.arrivalType;
    shipForm.value.fertilizeSeason = shipQueryParams.value.fertilizeSeason;
    if(shipQueryParams.value.year && shipQueryParams.value.organizationNo&&shipQueryParams.value.fertilizeSeason){
        if(!shipQueryParams.value.arrivalType){
            getPlanInfo()   //两个参数时查询计划表信息
        }
        else {
            getConsigneeInfo()  //三个参数时既查询计划表信息，也要查询收货人信息
            getPlanInfo()
        }
    }
}

/** 查询发运信息列表 */
function getList() {
    loading.value = true;
    if(queryParams.value.arriveType === "-1") {
        queryParams.value.arriveType = null
    }
    listProdShipInfo(queryParams.value).then(response => {
        prodShipInfoList.value = response.data.records;
        total.value = response.data.total;
        loading.value = false;
    });
}

/** 新增修改对话框 —— 新增页投入品计划列表 */
function getPlanInfo() {
    console.info('detailLoading.value:',detailLoading.value);
    detailLoading.value = true;
    getProdReportPlanConfigList({
        year : shipQueryParams.value.year,
        orgCode : shipQueryParams.value.organizationNo,
        fertilizeSeason: shipQueryParams.value.fertilizeSeason
    }).then(response => {
        //将企业id加入shipForm
        if(response.data&&response.data[0]){
          shipForm.value.aiCompanyId = response.data[0].aiCompanyId
          planList.value = response.data;
          initialSendNum() //点开新增页面时初始化预计发货总量
        }
        detailLoading.value = false;
      console.info('detailLoading.value:',detailLoading.value);
    }, error => {
        detailLoading.value = false;
        console.log('请求投入品计划列表失败 ', error.message)
    })
}

/** 新增修改对话框 —— 查询联系人信息 */
function getConsigneeInfo() {
  console.info('detailLoading.value:',detailLoading.value);
    detailLoading.value = true;
    getPropertyFromAiConsigneeInfo({
        year : shipQueryParams.value.year,
        organizationNo : shipQueryParams.value.organizationNo,
        arrivalType : shipQueryParams.value.arrivalType
    }).then(response => {
        shipForm.value.contactName = response.data.contactName;
        shipForm.value.contactPhone = response.data.contactPhone;
        shipForm.value.regionCode = response.data.regionCode;
        shipForm.value.regionName = response.data.regionName;
        shipForm.value.shipCarnum = response.data.shipCarnum;
        shipForm.value.arrivalName = response.data.arrivalName;
        shipForm.value.aiProdShipDetailList = []

        detailLoading.value = false;
      console.info('detailLoading.value:',detailLoading.value);
    }, error => {
        detailLoading.value = false;
        console.log('请求联系人信息失败 ', error.message)
      console.info('detailLoading.value:',detailLoading.value);
    })
}

/** 提交按钮 */
function submitForm() {

    var stop=false;
    planList.value.forEach(item=>{
      if(item.sendNum&&item.sendNum>0){
        if(!chkCurrentNum(item)){
          console.info('1111,',item);
          stop=true;
          return;
        }

        const add = Number(item.sendNum) + Number(item.alreadySendNum) + Number(item.alreadySendButNotPassNumber)
        if(Number(add) > Number(item.currentNum)){
          stop=true;
          proxy.$modal.msgError("商品["+item.aiTypeSubName+"]发货数量不能超过订购总量");
          return ;
        }
      }
    })

    if(stop){
      return;
    }


    //先重置（避免校验不通过还重复将表格数组加入aiProdShipDetailList）
    //再将planList存入shipForm里的aiProdShipDetailList数组中
    shipForm.value.aiProdShipDetailList = []
    let isNull = false
    let count = 0
    planList.value.forEach( each => {
        if(each.sendNum>0){
          shipForm.value.aiProdShipDetailList.push({
            aiCompanyId : each.aiCompanyId,
            aiTypeSubId : each.aiTypeSubId,
            sendNum : each.sendNum,
            orgCode : each.orgCode,
            aiTypeId : each.aiTypeId,
            year: each.year,
            prodShipDetailId : each.prodShipDetailId,
            sendNumOldForUpdate : each.sendNumOldForUpdate,
          })
          count += 1
        }
        if(each.sendNum === null){
            isNull = true;
        }
    });

    if(isNull) proxy.$modal.alertError("请输入发运量！");
    if(!isNull && count === 0) proxy.$modal.alertError("订单的总发运量不能为零！");
    if(!canSubmit.value) proxy.$modal.alertError("发货总量超出订购量！");

    proxy.$refs["prodShipInfoRef"].validate(valid => {
        if (valid && canSubmit.value && !isNull && count > 0) {
            isloading.value = true
            if (shipForm.value.prodShipId != null) {
                open.value = false;
                updateProdShipInfo(shipForm.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                    isloading.value = false
                },error => {
                    isloading.value = false
                    console.log('提交失败 ', error.message)
                });
            } else {open.value = false;
                addProdShipInfo(shipForm.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                    isloading.value = false
                })
            }
        }
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    resetShipQueryParams();
    resetShipForm();
}

/** 初始化新增修改页面时计算出预计发货总量 */
const initialSendNum = () =>{
    planList.value.forEach(each => {
        if(!each.sendNum){      //如果是新增，传来的sendNum为空，要赋为0，如果是修改，使用后端传来的sendNum
            //each.sendNum = Number(each.currentNum) - Number(each.alreadySendButNotPassNumber) - Number(each.alreadySendNum)
          each.sendNum=0
        }
        each.estimateSendTotal = Number(each.sendNum) + Number(each.alreadySendNum) + Number(each.alreadySendButNotPassNumber)
        each.estimateSendTotal.toFixed(3)
        if( Number(each.estimateSendTotal) > Number(each.currentNum)){
            canSubmit.value = false
        }
    })
    //if(!canSubmit.value) proxy.$modal.alertError("发货总量超出订购量!");
}

/** 当输入发货量时计算出预计发货总量 */
const handleSendNum = (row) =>{

    if(!chkCurrentNum(row)){
      return false;
    }

    const add = Number(row.sendNum) + Number(row.alreadySendNum) + Number(row.alreadySendButNotPassNumber)
    if(Number(add) <= Number(row.currentNum)){
        row.estimateSendTotal = add.toFixed(3)
    }
    else{   //如果预计发货总量超出订购量 弹窗报错
        console.log(row.sendNum)
        proxy.$modal.alertError("发货总量超出订购量!");
    }
}

import {bignumber, evaluate, multiply} from 'mathjs'
function isInteger(obj) {
  return typeof obj === 'number' && obj%1 === 0
}
function chkCurrentNum(row){
  console.info('row:',row);
  console.info('row-transNum:',row.transNum);
  //按规格上报，校验currentNum 必须是整数
  if(row.sendNum&&row.transNum&&row.transNum==-1&&!isInteger(Number(row.sendNum))){
    proxy.$modal.msgError("商品："+row.aiTypeSubName+"发货数量必须是整数");
    return false;
  }
  if(row.sendNum&&row.aiTypeSubWeight&&row.transNum&&row.transNum!=-1){

    var a=multiply(bignumber(row.sendNum),bignumber(row.transNum))
    var b=bignumber(row.aiTypeSubWeight)
    //console.info('a:'+a+'b:'+b);
    //console.info('a mod b: isZero',a.mod(b).isZero());
    if(!a.mod(b).isZero()){
      proxy.$modal.msgError("商品："+row.aiTypeSubName+"发货数量必须是，商品重量的整数倍,请仔细检查数据");
      return false;
    }
  }
  return true;
}

// 表单重置
function reset() {
    form.value = {
        year: null,
        aiCompanyId: null,
        arrivalType: null,
        orgCode: null,
        orgName: null,
        sendTime: null,
        regionCode: null,
        regionName: null,
        shipTitle: null,
        contactName: null,
        contactPhone: null,
        arrivalName: null,
        shipCarnum: null,
        arriveType: null,
        createBy: null,
        prodShipId: null,
    };
    proxy.resetForm("approveForm");
}

function resetShipQueryParams() {
    shipQueryParams.value.year = null
    shipQueryParams.value.arrivalType = null
    shipQueryParams.value.organizationNo = null
}

function resetShipForm(){
    shipForm.value={
        aiCompanyId: null,
        aiProdShipDetailList: [],
        arrivalName: null,
        arrivalType: null,
        arriveType: null,
        companyAbbr: null,
        companyName: null,
        contactName: null,
        contactPhone: null,
        organizationName: null,
        organizationNo: null,
        prodShipId: null,
        regionCode: null,
        regionName: null,
        sendTime: null,
        shipCarnum: null,
        shipTitle: null,
        year: null
    }
    planList.value=[]
    proxy.resetForm("prodShipInfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    // queryParams.value.rows = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    queryOrgRef.value.clear()
    queryParams.value.year = yearOption.value[0].code;
    queryParams.value.organizationNo = defaultOrgCode.value;
    queryOrgRef.value.setValue(defaultOrgCode.value, "");
    handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {

    //激活撤回发货按钮
    const canCancelShip = selection.filter(rows=>rows.auditStatus === 0 && rows.arriveType === 0);
    if(canCancelShip.length > 0 && canCancelShip.length === selection.length){
      cancelShipFlag.value = false
      canCancelShipIdsArr.value = canCancelShip.map(item => item.prodShipId);
    }else {
      cancelShipFlag.value = true
    }

    ids.value = selection.map(item => item.prodShipId);
    single.value = selection.length != 1;

    if(selection.length != 1){
        single.value=true;
    }else{
        //auditStatus==0
        let canAudit=[]
        canAudit=selection.filter(item=>item.auditStatus==0)

        single.value=!(canAudit.length==1)
        if(!single.value){
            instId.value=selection.map(item => item.instId)[0]
        }
    }
    //审批拒绝的，撤销的，可以删除
    let canRemove=[];

    if(selection&&selection.length){
        //如果选择了，就判断一下选择的数据里 是否有不允许删除的变量
        canRemove=selection.filter(item=>item.auditStatus==3||item.auditStatus==4)
        multiple.value = !(canRemove&&canRemove.length==selection.length)
    }else{
        multiple.value = true
    }
}

/** 审核 */
function handleApprove(){
    title.value="审核"
    if(ids.value){
        form.value.prodShipId=ids.value[0]
        form.value.instId=instId;
        form.value.agree=true
        form.value.opinion=null
        openApproveView.value=true;
    }
    console.log(form)
}

/** 审核提交 */
function submitApproveForm(){
    proxy.$refs["approveForm"].validate(valid => {
        if (valid) {
            let apprveForm={
                    prodShipId:form.value.prodShipId,
                    agree:form.value.agree,
                    instId:form.value.instId,
                    opinion:form.value.opinion
            }
            approve(apprveForm).then(response=>{
                if(response.success){
                    openApproveView.value=false;
                    proxy.$modal.msgSuccess("成功!");
                    getList();
                }
            })
        }
    })
}

/** 新增按钮操作 */
function handleAdd() {
    readOnly.value=false;
    detailLoading.value=false;
    resetShipQueryParams();
    resetShipForm();
    orgFlag.value = false;
    nextTick(() => {
        orgFlag.value = true;
    });
    insertOrEdit.value = false;     //新增时，年份和收货单位组件是可用的
    open.value = true;
    title.value = "添加发运信息";

    /*let now= new Date();
    shipQueryParams.value.year = now.getFullYear()+"";
    shipQueryParams.value.arrivalType = transTypeOption.value[0].code*/

    //查询 投入品计划、联系人信息
    handleQueryDetail();
}

/** 修改按钮操作 */
function handleUpdate(row) {
    resetShipQueryParams();
    resetShipForm();
    orgFlag.value = false;
    nextTick(() => {
        orgFlag.value = true;
    });
    detailLoading.value = true;
    open.value = false;
    const prodShipId = row.prodShipId || ids.value
    getProdShipInfo(prodShipId).then(response => {
        if(response.data.auditStatus === 1 || response.data.auditStatus === 4){
          readOnly.value=true;
          title.value = "查看发运信息";
          shipForm.value = response.data
          planList.value = response.data.aiProdShipDetailList
          shipQueryParams.value.year = shipForm.value.year+""
          shipQueryParams.value.organizationNo = shipForm.value.organizationNo+""
          shipQueryParams.value.arrivalType = shipForm.value.arrivalType+""
          shipQueryParams.value.fertilizeSeason = shipForm.value.fertilizeSeason+""
          insertOrEdit.value = true;
          detailLoading.value = false;
          open.value = true;
        }
        else{
            readOnly.value=false;
            title.value = "修改发运信息";
            shipForm.value = response.data
            planList.value = response.data.aiProdShipDetailList
            planList.value.forEach(each => {
                each.sendNumOldForUpdate = each.sendNum
            })
            //console.log('planlist1:',planList.value)
            shipQueryParams.value.year = shipForm.value.year+""
            shipQueryParams.value.organizationNo = shipForm.value.organizationNo+""
            shipQueryParams.value.arrivalType = shipForm.value.arrivalType+""
            shipQueryParams.value.fertilizeSeason = shipForm.value.fertilizeSeason+""
            initialSendNum() //点开修改页面时初始化预计发货总量
            //console.log('planlist2:',planList.value)

            insertOrEdit.value = true;  //修改时，年份和收货单位组件是不可用的
            detailLoading.value = false;
            open.value = true;

        }
    });
}

/** 删除按钮操作 */
function handleDelete(row) {
    const prodShipIds = row.prodShipId || ids.value;
    proxy.$modal.confirm('是否确认删除所选数据？').then(function () {
        return delProdShipInfo(prodShipIds);
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
    });
}

//查看流程按钮
function viewFlow(row){
    form.value=row
    flowDetailTag.value = false
    nextTick(() => {
        flowDetailTag.value=true;
    });
    processVisible.value=true
}

/** 导出按钮操作 */
function handleExport() {
    postForExcel('/bdh-agric-invest-api/invest/prodShipInfo/send/exportExcel', queryParams.value,'发运管理')
}

getDict();
</script>
