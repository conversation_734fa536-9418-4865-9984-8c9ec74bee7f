<!--
@name: 收货人管理
@description:
@author: donglu
@time: 2022-10-06 11:01:59
-->
<template>
    <div class="app-container">
        <!-- 筛选条件及搜索栏 -->
        <div ref="searchDom">
            <el-collapse-transition>
                <el-form v-show="showSearch" ref="queryRef" class="form-line"  :model="queryParams" label-width="80px">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="年份" prop="year">
                                <el-select v-model="queryParams.year" clearable placeholder="请选择年份">
                                    <el-option
                                        v-for="dict in yearOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="所在单位" prop="orgCode">
                                <org-select
                                    ref="queryOrgRef"
                                    @handleOrgCode="handleOrgCodeQuery" :defaultOrgCode="queryParams.orgCode" :level="3" :checkStrictly="false" />
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item label="企业简称" prop="aiCompanyId">
                                <el-select v-model="queryParams.aiCompanyId" clearable placeholder="请选择企业简称">
                                    <el-option
                                        v-for="dict in companyAbbrOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6" align="right">
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                            <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
                        </el-col>
                    </el-row>
                </el-form>
            </el-collapse-transition>
        </div>
        <!-- 新增、修改、删除、导出 -->
        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['consigneeInfo:insert']"
                    icon="Plus"
                    type="primary"
                    @click="handleAdd"
                >新增
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['consigneeInfo:update']"
                    :disabled="single"
                    icon="Edit"
                    @click="handleUpdate"
                >修改
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['consigneeInfo:logicDeleteByIds']"
                    :disabled="multiple"
                    icon="Delete"
                    @click="handleDelete"
                >删除
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <el-button
                    v-hasPermi="['consigneeInfo:export']"
                    icon="Download"
                    @click="handleExport"
                >导出
                </el-button>
            </el-col>
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!-- 分页查询表格 -->
        <el-table border :height="tableHeight"  :data="consigneeInfoList" @selection-change="handleSelectionChange">
            <el-table-column align="center" type="selection" width="55" fixed="left"/>
            <el-table-column align="left" label="年份" prop="year" width="80"/>
            <el-table-column align="left" label="所在单位" prop="orgName"/>
            <el-table-column align="left" label="企业简称" prop="companyAbbr" :formatter="companyIdFormat"/>
            <el-table-column align="left" label="运输方式" prop="transportType" :formatter="transTypeOptionFmt"
                             width="80"/>
            <el-table-column align="left" label="所在省市" prop="regionName"/>
            <el-table-column align="left" label="收货单位" prop="acceptOrgName"/>
            <el-table-column align="left" label="接货机构" prop="acceptOrgFullname"/>
            <el-table-column align="left" label="联系人" prop="contractPerson"/>
            <el-table-column align="left" label="联系电话" prop="contractPhone" width="150px"/>
            <el-table-column align="left" label="到站" prop="arriveStation"/>
            <el-table-column align="left" label="专用线" prop="specialLine" width="150px"/>
            <el-table-column align="center" width="150" fixed="right" class-name="small-padding fixed-width" label="操作">
                <template #default="scope">
                    <el-link
                        v-hasPermi="['consigneeInfo:update']"
                        type="primary"
                        class="mr10"
                        @click="handleUpdate(scope.row)"
                    >修改
                    </el-link>
                    <el-link
                        v-hasPermi="['consigneeInfo:logicDeleteByIds']"
                        type="danger"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-link>
                </template>
            </el-table-column>
        </el-table>

        <!-- 分页查询底部选页栏 -->
        <pagination
            v-show="total>0"
            v-model:limit="queryParams.rows"
            v-model:page="queryParams.page"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改收货人对话框 -->
        <el-dialog v-model="open" :title="title" append-to-body style="width:800px"  label-width="auto" :close-on-click-modal="false">
            <el-form ref="consigneeInfoRef" label-position="top" :model="form" :rules="rules" label-width="auto">
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="年份" prop="year">
                            <el-select  v-model="form.year" placeholder="请选择年份" clearable>
                                <el-option
                                    v-for="dict in yearOption"
                                    :key="dict.code"
                                    :label="dict.name"
                                    :value="dict.code"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="所在单位" prop="orgCode">
                            <org-select v-if="orgFlag" style="width:214px"
                                        :defaultOrgCode="form.orgCode === null ? '' : form.orgCode"
                                        @handleOrgCode="handleOrgCodeDetail" :level="3" :checkStrictly="false"/>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item label="企业简称" prop="aiCompanyId">
                            <el-select style="width:214px"  v-model="form.aiCompanyId" clearable placeholder="请选择企业简称">
                                <el-option
                                    v-for="dict in companyAbbrOption"
                                    :key="dict.code"
                                    :label="dict.name"
                                    :value="dict.code"
                                />
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item label="收货单位" prop="acceptOrgCode">
                            <org-select v-if="orgFlag"
                                        :defaultOrgCode="form.acceptOrgCode === null ? '' : form.acceptOrgCode"
                                        @handleOrgCode="handleAcceptOrgCodeDetail"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="联系人" prop="contractPerson">
                            <el-input v-model="form.contractPerson" placeholder="请输入联系人"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item label="联系电话" prop="contractPhone">
                            <el-input style="width:214px" v-model="form.contractPhone" placeholder="请输入联系电话"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="运输方式" prop="transportType">
                      <el-select v-model="form.transportType" placeholder="请选择运输方式" clearable>
                        <el-option
                            v-for="dict in transTypeOption"
                            :key="dict.code"
                            :label="dict.name"
                            :value="dict.code"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="所在省市" prop="regionCode">
                      <el-cascader style="width:100%"
                                   ref="regionCascader"
                                   v-model="form.regionCode"
                                   @change="handleRegionSelect"
                                   :props="regCascProps"
                                   :options="regionOrgOption"
                                   clearable/>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="接货机构" prop="acceptOrgFullname">
                      <el-input style="width:214px" v-model="form.acceptOrgFullname" placeholder="请输入接货机构"/>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                    <el-col :span="8">
                        <el-form-item v-if="form.transportType === '1'" label="到站名称" prop="arriveStation">
                            <el-input v-model="form.arriveStation" placeholder="请输入到站"/>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item v-if="form.transportType === '1'" label="专用线名称" prop="specialLine">
                            <el-input v-model="form.specialLine" placeholder="请输入专用线"/>
                        </el-form-item>
                    </el-col>
                </el-row>
                <el-row >
                    <el-col :span="24">
                        <el-form-item v-if="form.transportType === '1'" label="营业执照">
                            <imageUpload urlApi="bdh-agric-invest-api" @fileUploadChange="fileUploadChange" :limit="1" v-model="form.licensePicUrl"></imageUpload>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>

            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script name="/invest/consigneeInfo/queryByPage" setup>
import {
    addConsigneeInfo,
    delConsigneeInfo,
    getConsigneeInfo,
    listConsigneeInfo,
    updateConsigneeInfo,
    queryRegionOrg
} from "@/api/bdh-agric-invest/invest/consigneeInfo";

import {allCompanyInfo} from '@/api/bdh-agric-invest/invest/companyInfo'
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import {getDicts} from '@/api/bdh-agric-invest/system/dict/data'
import {postForExcel} from "@/api/bdh-agric-invest/invest/excel";
import {delConsigneeInfos} from "@/api/bdh-agric-invest/invest/consigneeInfo";
import { queryAllOrgTree} from "@/api/bdh-agric-invest/system/dict/data";
import {ref} from "vue";

const {proxy} = getCurrentInstance();

const consigneeInfoList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const yearOption = ref([]);
const companyAbbrOption = ref([]);
const regionOrgOption = ref([]);
const transTypeOption = ref([]);
const orgFlag = ref(false);
const queryOrgRef = ref()
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const defaultOrgCode = ref('');

const regCascProps = {
    emitPath: false,
    value: "regionCode",
    label: "regionName"
}

const data = reactive({
    form: {},
    queryParams: {
        rows: 10,
        page: 1,
        orgName: null,
        orgCode: null,
        aiCompanyId: null,
        year: null,
    },
    rules: {
        transportType: [
            {required: true, message: "请输入运输方式", trigger: "change"}
        ],
        aiCompanyId: [
            {required: true, message: "请输入企业简称", trigger: "change"}
        ],
        regionCode: [
            {required: true, message: "请输入所在省市", trigger: "change"}
        ],
        orgCode: [
            {required: true, message: "请输入所在单位", trigger: "change"}
        ],
        acceptOrgCode: [
            {required: true, message: "请输入收货单位", trigger: "change"}
        ],
        year: [
            {required: true, message: "请输入年份", trigger: "change"}
        ],
        contractPerson: [
            {required: true, message: "请输入联系人", trigger: "blur"},
            {max: 10, message: "最多10个字符", trigger: "change"}
        ],
        contractPhone: [
            {required: true, message: "请输入联系电话", trigger: "blur"},
            {len: 11, message: "请输入11位电话号码", trigger: "blur"}
        ],
        arriveStation: [
            {required: true, message: "请输入到站名称", trigger: "blur"},
            {max: 30, message: "最多30个字符", trigger: "blur"}
        ],
        specialLine: [
            {required: true, message: "请输入专用线名称", trigger: "blur"},
            {max: 30, message: "最多30个字符", trigger: "blur"}
        ],
    }
});

const {queryParams, form, rules} = toRefs(data);

onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});

/** 获取字典 */
const getDict = () => {
    //查询年份
    getDicts('year_cd').then(response => {
        yearOption.value = response.data.sort((a, b) => {
            return Number(b.code) - Number(a.code)
        })
        queryParams.value.year = yearOption.value[0].code
        queryAllOrgTree("3").then((res) => {
            var orgList = res.data;
            if (orgList && orgList.length && orgList.length > 0) {
                defaultOrgCode.value = orgList[0].orgCode;
                queryParams.value.orgCode = defaultOrgCode.value;
            }
            getList()
        })
    });

    //企业简称
    allCompanyInfo({}).then(response => {
        response.data.forEach(each => {
            companyAbbrOption.value.push({
                code: each.aiCompanyId,
                name: each.companyAbbr
            })
        })
    });

    //查询省市
    queryRegionOrg().then(response => {
        regionOrgOption.value = response.data
    })

    //运输方式
    getDicts('trp_arrival_type').then(response => {
        transTypeOption.value = response.data
    });
}

const companyIdFormat = (row) => {
    let comanyAbbr = null;
    companyAbbrOption.value.forEach(function (item) {
        if (item.code === row.aiCompanyId) {
            comanyAbbr = item.name
        }
    })
    return comanyAbbr;
}

const transTypeOptionFmt = (row) => {
    let transTypeName = null
    transTypeOption.value.forEach(function (item) {
        if (item.code === row.transportType + '') {
            transTypeName = item.name
        }
    })
    return transTypeName
}


/** 查询页面的所在单位 */
const handleOrgCodeQuery = (orgInfo) => {
    queryParams.value.orgCode = orgInfo.orgCode
    // queryParams.value.orgName = orgInfo.orgName
}

/** 新增页面的所在单位 */
const handleOrgCodeDetail = (orgInfo) => {
    form.value.orgCode = orgInfo.orgCode
    form.value.orgName = orgInfo.orgName
}

/** 新增页面的收货单位 */
const handleAcceptOrgCodeDetail = (orgInfo) => {
    form.value.acceptOrgCode = orgInfo.orgCode
    form.value.acceptOrgName = orgInfo.orgName
}

/** 选择省市后将省市名称也加入form */
const handleRegionSelect = () => {
    let nodesInfo = proxy.$refs.regionCascader.getCheckedNodes()
    regionOrgOption.value[0].children.forEach(each => {
        if (each.regionCode === nodesInfo[0].data.regionCode) {
            form.value.regionName = nodesInfo[0].data.regionName
        }
    })
}

/** 查询收货人列表 */
function getList() {
    loading.value = true;
    var obj = Object.assign({}, queryParams.value);
    listConsigneeInfo(obj).then(response => {
        consigneeInfoList.value = response.data.records;
        total.value = response.data.total;
        loading.value = false;
    });
}

// 取消按钮
function cancel() {
    open.value = false;
    reset();
}

// 表单重置
function reset() {
    form.value = {
        orgCode: null,
        orgName: null,
        transportType: null,
        regionCode: null,
        regionName: null,
        acceptOrgCode: null,
        acceptOrgName: null,
        aiConsigneeId: null,
        year: null,
        contractPerson: null,
        contractPhone: null,
        arriveStation: null,
        specialLine: null,
        remark: null,
        licensePicUrl: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        statusCd: null
    };
    proxy.resetForm("consigneeInfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
    // queryParams.value.rows = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    queryOrgRef.value.clear();
    queryParams.value.year = yearOption.value[0].code;
    queryParams.value.orgCode = defaultOrgCode.value;
    queryOrgRef.value.setValue(defaultOrgCode.value, "");
    handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.aiConsigneeId);
    console.log("选中：", ids)
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
    reset();
    let now= new Date();
    form.value.year = now.getFullYear()+"";
    orgFlag.value = false;
    nextTick(() => {
        orgFlag.value = true;
    });
    open.value = true;
    title.value = "添加收货人";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    orgFlag.value = false;
    nextTick(() => {
        orgFlag.value = true;
    });
    const aiConsigneeId = row.aiConsigneeId || ids.value
    getConsigneeInfo(aiConsigneeId).then(response => {
        form.value = response.data;
        console.info('handleUpdate:', form.value)
        form.value.transportType = form.value.transportType + ''
        open.value = true;
        title.value = "修改收货人";
    });
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["consigneeInfoRef"].validate(valid => {
        if (valid) {
            if (form.value.aiConsigneeId != null) {
                updateConsigneeInfo(form.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            } else {
                addConsigneeInfo(form.value).then(response => {
                    console.info('form.value', form.value)
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                });
            }
        }
    });
}
let fileUploadChange = (value) => {
  if (Array.isArray(value) && value.length > 0) {
    const urls = value.map(item => item.url || item).join(',');
    form.value.licensePicUrl = urls;
    proxy.$refs.consigneeInfoRef.clearValidate('licensePicUrl');
  } else {
    form.value.licensePicUrl = '';
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
    const aiConsigneeIds = row.aiConsigneeId || ids.value;
    console.log("删除：", ids.value)
    proxy.$modal.confirm('确认删除选择的数据项？').then(function () {
        if (ids.value.length > 1) {
            return delConsigneeInfos(aiConsigneeIds);
        } else {
            return delConsigneeInfo(aiConsigneeIds);
        }
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {
    });
}

/** 导出按钮操作 */
function handleExport() {
    postForExcel('/bdh-agric-invest-api/invest/consigneeInfo/postForExcel', queryParams.value, '收货人管理');
}

getDict()
</script>
<style scoped>
.el-link {
    margin-right: 8px;
}

.el-link .el-icon--right.el-icon {
    vertical-align: text-bottom;
}
</style>
