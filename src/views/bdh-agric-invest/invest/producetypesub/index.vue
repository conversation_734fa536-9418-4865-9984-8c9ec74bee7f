
<template>
  <div class="app-container">
<!--
@name: 商品库管理-商品库
@description:
@author: liuying
@time: 2022-10-14 15:54:36
-->
    <el-container>
      <el-aside width="200px">
        <el-scrollbar>
          <el-form :model="formCompany">
            <el-form-item style="width:152px" prop="companyId">
              <el-select v-model="formCompany.companyId" placeholder="请选择企业简称" @change="getAiTypeSort">
                <el-option
                    v-for="dict in companyAbbrOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-form>
          <el-tree
              :data="tree"
              :props="defaultProps"
              accordion
              @node-click="handleNodeClick"
          />
        </el-scrollbar>
      </el-aside>

      <el-main>
        <div ref="searchDom">
          <el-collapse-transition>
            <el-form
                v-show="showSearch"
                ref="queryRef"
                :model="queryParams"
                label-width="80px"
                class="form-line"
            >
              <el-row :gutter="20">
                <el-col :span="6">
                  <el-form-item label="商品名称" prop="aiTypeSubName">
                    <el-input
                        v-model="queryParams.aiTypeSubName"
                        clearable
                        placeholder="请输入商品名称"
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="商品规格" prop="aiTypeSubSpecs">
                    <el-input
                        v-model="queryParams.aiTypeSubSpecs"
                        clearable
                        placeholder="请输入商品规格"
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="是否显示" prop="showFlag">
                    <el-select
                        v-model="queryParams.showFlag"
                        placeholder="请选择是否显示"
                        clearable
                    >
                      <el-option
                          v-for="dict in showFlagOptions"
                          :key="dict.code"
                          :label="dict.name"
                          :value="dict.code"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="是否启用" prop="useFlag">
                    <el-select
                        v-model="queryParams.useFlag"
                        placeholder="请选择是否启用"
                        clearable
                    >
                      <el-option
                          v-for="dict in showFlagOptions"
                          :key="dict.code"
                          :label="dict.name"
                          :value="dict.code"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-collapse-transition>
        </div>
        <el-row :gutter="10" class="mb8" style="min-height: 32px;">
          <el-col :span="1.5">
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                v-hasPermi="['producetypesub:insert']"
                type="primary"
                @click="handleAdd"
                icon="Plus"
            >新增
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                v-hasPermi="['producetypesub:logicDeleteByIds']"
                :disabled="multiple"
                @click="handleDelete"
                icon="Delete"
            >删除
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                v-hasPermi="['producetypesub:batchUseFlagByIds']"
                :disabled="multiple"
                @click="batchEnable(1, null)"
            >批量启用
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                v-hasPermi="['producetypesub:batchUseFlagByIds']"
                :disabled="multiple"
                @click="batchEnable(0, null)"
            >批量禁用
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
                v-hasPermi="['producetypesub:exportExcel']"
                @click="handleExport"
                icon="Download"
            >导出
            </el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
            v-hasPermi="['series:saveOrUpdate']"
            @click="handleOne"
            >设为同一系列商品
            </el-button>
          </el-col>
          <right-toolbar
              v-model:showSearch="showSearch"
              @queryTable="getList"
          ></right-toolbar>
        </el-row>

        <el-table border
                  :data="producetypesubList"
                  @selection-change="handleSelectionChange"
                  @row-click="openUpdate"
                  :height="tableHeight"
                  row-key="aiTypeSubId"
                  ref="tableRef"
        >
          <el-table-column align="center" type="selection" width="55" reserve-selection />
          <el-table-column align="center" label="商品图片" prop="aiTypeSubUrl1List">
            <template #default="scope">
              <el-image
                  v-if="scope.row.aiTypeSubUrl1List != null"
                  :src="scope.row.aiTypeSubUrl1List[0]"
                  style="width: 40px; height: 40px"
                  :preview-src-list="scope.row.aiTypeSubUrl1List"
                  :preview-teleported="true"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="商品编号" prop="aiTypeSubCode"/>
          <el-table-column align="center" label="企业简称" prop="companyAbbr"/>
          <el-table-column align="center" label="分类名称" prop="aiTypeName"/>
          <el-table-column align="center" label="商品系列" prop="produceSeriesName"/>
          <el-table-column align="center" label="商品名称" prop="aiTypeSubName">
            <template #default="scope">
              <span style="color: #00bcd4;cursor: pointer;">{{scope.row.aiTypeSubName}}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="规格" prop="aiTypeSubSpecs"/>
          <el-table-column align="center" label="重量" prop="aiTypeSubWeight">
            <template #default="scope">
              {{scope.row.aiTypeSubWeight}}{{scope.row.aiTypeSubUnit}}
            </template>
          </el-table-column>
          <el-table-column align="center" label="指导价格(元)" prop="aiGuidingPrice" width="100px">
            <template #default="scope">
              <span style="color: #00bcd4;" v-if="scope.row.guidingPriceRange">{{scope.row.guidingPriceRange}}</span>
              <span style="color: #00bcd4;" v-else></span>
            </template>
          </el-table-column>

          <el-table-column align="center" label="外部编码" prop="outsideCode"/>
          <!--                    <el-table-column align="center" label="是否显示" prop="showFlag">
                                  <template #default="scope">
                                      <el-switch
                                          v-model="scope.row.showFlag"
                                          :active-value="1"
                                          :inactive-value="0"
                                          @click="handleDisplayShow(scope.row)"
                                      ></el-switch>
                                  </template>
                              </el-table-column>-->
          <el-table-column align="center" label="生产企业" prop="produceCompanyName" width="150"/>
          <el-table-column align="center" label="排序" prop="aiTypeSubOrder"/>
          <el-table-column align="center" label="是否启用" prop="useFlag">
            <template #default="scope">
              <el-switch
                  v-model="scope.row.useFlag"
                  :active-value="1"
                  :inactive-value="0"
                  @click="batchEnable(3, scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              label="操作"
              width="150px"
          >
            <template #default="scope">
              <el-button
                  v-hasPermi="['producetypesub:update']"
                  link
                  size="small"
                  type="primary"
                  @click="handleUpdate(scope.row,'update')"
              >修改
              </el-button>
              <el-button
                  v-hasPermi="['producetypesub:logicDeleteById']"
                  link
                  size="small"
                  type="primary"
                  @click="handleDelete(scope.row)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:limit="queryParams.rows"
            v-model:page="queryParams.page"
            :total="total"
            @pagination="getList"
        />

        <!-- 添加或修改商品信息对话框 -->
        <el-dialog v-model="open" :title="title" append-to-body width="1200px" :destroy-on-close="true">
          <el-form
              ref="producetypesubRef"
              :model="form"
              :rules="rules"
              :disabled="updateType=='view'"
              label-width="120px"
          >
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="企业简称" prop="aiCompanyId">
                  <el-select  style="width: 100%;" disabled v-model="form.aiCompanyId" clearable placeholder="请选择企业简称" @change="getAiTypeSort">
                    <el-option
                        v-for="dict in companyAbbrOption"
                        :key="dict.code"
                        :label="dict.name"
                        :value="dict.code"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="商品分类" prop="aiTypeId">
                  <el-cascader :disabled="updateType=='update'"
                  style="width: 100%;"
                               placeholder="请选择商品分类"
                               ref="cascaderArr"
                               v-model="form.aiTypeId"
                               :props="produceTypeProps"
                               :options="sortOption"
                               :show-all-levels="false"
                               clearable
                               @change="handleChange"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="商品名称" prop="aiTypeSubName">
                  <el-input  style="width: 100%;" :maxlength="30" v-model="form.aiTypeSubName" placeholder="请输入商品名称"
                            clearable/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="商品重量" prop="aiTypeSubWeight" >
                  <el-input   style="width: 100%;" @change="refreshSpecs()"
                             v-model="form.aiTypeSubWeight"
                             placeholder="请输入重量"
                             class="input-with-select"
                             clearable>
                    <template #append>
                      <el-select v-model="form.aiTypeSubUnit"  @change="reportUnitOpt(null);refreshSpecs()" placeholder="单位"  style="width: 100px">
                        <el-option
                            v-for="dict in specUnitOptions"
                            :key="dict.srcUnit"
                            :label="dict.srcUnit"
                            :value="dict.srcUnit"
                        />
                      </el-select>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="商品规格" prop="aiTypeSubSpecs">
                  <el-input  style="width: 100%;" :maxlength="30" v-model="form.aiTypeSubSpecs" placeholder="请输入商品规格"
                            clearable/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="上报单位" prop="aiTypeReportUnit">
                  <el-select v-model="form.aiTypeReportUnit"  placeholder="请选择上报单位"  style="width: 100%;">
                    <el-option
                        v-for="dict in reportUnitOptions"
                        :key="dict.destUnit"
                        :label="dict.destUnit"
                        :value="dict.destUnit"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="外部编码" prop="outsideCode">
                  <el-input  style="width: 100%;" :maxlength="30" v-model="form.outsideCode" placeholder="请输入外部编码"
                            clearable/>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="生产企业" prop="produceCompanyName">
                  <el-input  style="width: 100%;" :maxlength="50" v-model="form.produceCompanyName" placeholder="请输入生产企业"
                             clearable/>
                </el-form-item>
              </el-col>
              <el-col :span="24" style="display: flex;margin-bottom: 10px;padding-left: 20px;" v-if="updateType=='update'||updateType=='view'">
                <span style="padding-right: 20px;font-weight: 600;">定价策略</span>
                      <el-button
                        link
                        v-hasPermi="['prodsell:addOrUpdate']"
                        type="primary"
                        @click="addDirections"
                    >添加销售去向
                    </el-button>
                    <el-button
                        link
                        type="primary"
                        @click="addStoreOne"
                    >从同一库点出库
                    </el-button>
                </el-col>
                <el-col :span="24" v-if="updateType=='update'||updateType=='view'">
                  <el-table border
                  :span-method="objectSpanMethod"
                  @selection-change="handleSelectionStoreChange"
                  :data="form.prodSells"
                  :height="300"
        >
          <el-table-column align="center" type="selection" width="55" />
          <el-table-column align="center" label="销售去向" :formatter="orgNameFmt"></el-table-column>
          <el-table-column align="center" label="商品指导价格" prop="guidePrice">
            <template #header>
              商品指导价格(元) <span style="color: red;">*</span>
            </template>
            <template #default="scope">
              <el-form-item label-width="0px"  :prop="`prodSells.${scope.$index}.guidePrice`"  :rules='rules.aiGuidingPrice'>
                <el-input v-model="scope.row.guidePrice" placeholder="请输入商品指导价格" clearable />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column align="center" label="库点名称" prop="storehouseName"  width="240">
            <template #header>
              库点名称 <span style="color: red;">*</span>
            </template>
            <template #default="scope">
              <el-button
              v-show="!scope.row.storehouseId"
                        link
                        type="primary"
                        @click="addStore(scope.row)"
                    >选择库点
                    </el-button>
                    <el-button
              v-show="scope.row.storehouseId"
                        link
                        type="primary"
                        @click="addStore(scope.row)"
                    >{{ scope.row.storehouseName }}
                    </el-button>
                    <el-form-item label-width="80px"  :prop="`prodSells.${scope.$index}.storehouseName`"  :rules='rules.storehouseName'>

                    </el-form-item>
            </template>
          </el-table-column>
          <el-table-column align="center" label="现库存" prop="curInStock">
          </el-table-column>
          <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              label="操作"
              width="150px"
          >
            <template #default="scope">
              <span class="link"  v-if="scope.row.storehouseId&&scope.row.curInStock>0 && (updateType=='view')" @click="handleStoreOut(scope.row)"  v-hasPermi="['storehouseProdInfo:manualStoreOutput']">出库</span>
              <span class="link"  v-if="scope.row.storehouseId&&(updateType=='view')"  @click="handleStoreIn(scope.row)"  v-hasPermi="['storehouseProdInfo:manualStoreInput']">入库</span>

              <!-- <el-button
                  v-if="scope.row.storehouseId&&scope.row.curInStock>0 && (updateType=='update')"
                  link
                  size="small"
                  type="primary"
                  @click="handleStoreOut(scope.row)"
              >出库
              </el-button>
              <el-button
                  v-if="scope.row.storehouseId&&(updateType=='update')"
                  link
                  size="small"
                  type="primary"
                  @click="handleStoreIn(scope.row)"
              >入库
              </el-button> -->
              <el-button
                  link
                  size="small"
                  type="primary"
                  @click="handleStoreDelete(scope.row)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
                </el-col>
              <el-col :span="24" style="margin-top: 10px;">
                <el-form-item label="商品简介" prop="aiBriefDesc" >
                  <Editor urlApi="bdh-agric-invest-api" v-model="form.aiBriefDesc" :height="400"  />
                </el-form-item>
              </el-col>
              <!--                            <el-col :span="8">
                                              <el-form-item label="是否显示" prop="showFlag">
                                                  <template #default="scope">
                                                      <el-switch v-model="form.showFlag"
                                                                 :active-value="1"
                                                                 :inactive-value="0"/>
                                                  </template>
                                              </el-form-item>
                                          </el-col>-->
              <el-col :span="8">
                <el-form-item label="是否启用" prop="useFlag">
                  <template #default="scope">
                    <el-switch v-model="form.useFlag"
                               :active-value="1"
                               :inactive-value="0"/>
                  </template>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="排序" prop="aiTypeSubOrder">
                  <el-input  style="width: 100%;" maxlength="6" v-model="form.aiTypeSubOrder" placeholder="请输入排序"
                            oninput="value=value.replace(/[^\d]/g,'')"
                            clearable/>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="商品图片" prop="aiTypeSubUrl1">
                  <imageUpload
                      urlApi="bdh-agric-invest-api"
                      :limit="5"
                      v-model="form.aiTypeSubUrl1"
                      @fileUploadChange="fileUploadAiTypeSubUrl1"
                  ></imageUpload>
                </el-form-item>
              </el-col>
            </el-row>
          <el-row style="padding-left: 60px;" v-if="updateType=='view'">
            <el-col :span="24" style="margin-bottom: 10px; display: flex;align-items: center;" class="ellipsis">
              <span> {{ form.aiTypeSubName}} 系列其他商品  </span>
               <!-- <el-button
                  v-hasPermi="['series:saveOrUpdate']"
                  link
                  type="primary"
                  @click="handleOneEdit()"
              >设置
              </el-button> -->
                <span class="link"  style="font-size: 14px;margin-left: 4px;" @click="handleOneEdit()" v-hasPermi="['series:saveOrUpdate']"> 设置</span>
              </el-col>
            <el-col v-if="form.produceSeriesSubs" v-for="item in form.produceSeriesSubs" :span="12">
              <el-row>
                <el-col :span="6" style="height: 140px;">
                  <el-image
                    v-if="item.aiTypeSubUrl1List != null"
                    :src="item.aiTypeSubUrl1List[0]"
                    style="width: 140px; height: 140px"
                    :preview-src-list="item.aiTypeSubUrl1List"
                    :preview-teleported="true"
                  />
              </el-col>
              <el-col :span="12" style="display: flex;
    flex-direction: column;
    justify-content: space-around;margin-left: 10px;">
                  <div class="ellipsis" style="color: #409eff;" :title="item.aiTypeSubName">{{ item.aiTypeSubName }}</div>
                  <div class="ellipsis" :title="'规格：'+item.aiTypeSubName">规格：{{ item.aiTypeSubSpecs }}</div>
                  <div class="ellipsis" :title="'商品编号：'+item.aiTypeSubName">商品编号：{{ item.aiTypeSubCode }}</div>
                </el-col>
              </el-row>
            </el-col>
          </el-row>
        </el-form>

          <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" v-if="updateType=='update'||updateType=='add'" @click="submitForm">确 定</el-button>
              <el-button @click="cancel" v-if="updateType=='update'||updateType=='add'" >取 消</el-button>
            </div>
          </template>
        </el-dialog>
        <!-- 设为同一系列商品 -->
        <el-dialog v-model="openOne" title="设为同一系列商品" append-to-body width="1200px" :destroy-on-close="true" @closed="cancelOne">
          <el-row :gutter="10" style="margin-bottom: 10px;">
              <el-col :span="1.5"><span style="font-weight: 600;">商品系列管理</span> </el-col>
              <el-col :span="18">设置商品系列可为指定商品设置相同的商品简介，同一系列商品在前台展示时做关联显示</el-col>
            </el-row>
          <el-form
              ref="formOneRef"
              :model="formOne"
              :rules="rules"
              label-width="80px"
          >
            <el-row>
              <el-col :span="8">
                <el-form-item label="系列名称" prop="produceSeriesName">
                  <el-input style="width: 220px" :maxlength="100" v-model="formOne.produceSeriesName" placeholder="请输入系列名称"
                            clearable/>
                </el-form-item>
              </el-col>
              <el-col :span="12"  style="padding-top: 5px;">提示：默认取第一个关联的商品名称，可修改。不能为空！</el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item label="关联商品" >
                  <el-button
                    @click="selectOne"
                    >重新选择商品
                  </el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
            <el-table border
                  ref="tableOneRef"
                  :data="formOne.aiTypeSubIds"
                  :height="300"
                  :highlight-current-row="formOne.isTemp==1"
                  @current-change="handleCurrentChange"
        >
          <el-table-column align="center" label="商品图片" prop="aiTypeSubUrl1List">
            <template #default="scope">
              <el-image
                  v-if="scope.row.aiTypeSubUrl1List != null"
                  :src="scope.row.aiTypeSubUrl1List[0]"
                  style="width: 40px; height: 40px"
                  :preview-src-list="scope.row.aiTypeSubUrl1List"
                  :preview-teleported="true"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="商品编号" prop="aiTypeSubCode"/>
          <el-table-column align="center" label="企业简称" prop="companyAbbr"/>
          <el-table-column align="center" label="分类名称" prop="aiTypeName"/>
          <el-table-column align="center" label="商品名称" prop="aiTypeSubName">
            <template #default="scope">
              <span>{{scope.row.aiTypeSubName}}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="规格" prop="aiTypeSubSpecs"/>
          <el-table-column align="center" label="指导价格(元)" prop="aiGuidingPrice" width="100px">
            <template #default="scope">
              <span style="color: #00bcd4;" v-if="scope.row.guidingPriceRange">{{scope.row.guidingPriceRange}}</span>
              <span style="color: #00bcd4;" v-else></span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="排序" prop="aiTypeSubOrder"/>
          <el-table-column align="center" label="是否启用" prop="useFlag">
            <template #default="scope">
              <span v-if="scope.row.useFlag==1" style="color: #00bcd4;">启用</span>
              <span v-if="scope.row.useFlag==0" style="color: red;">禁用</span>
            </template>
          </el-table-column>
          <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              label="操作"
              width="150px"
          >
            <template #default="scope">
              <el-button
                  link
                  size="small"
                  type="primary"
                  @click="handleOneDelete(scope.$index)"
              >从系列中删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-row>
              <el-col :span="24">
                <el-checkbox v-model="formOne.isTemp" true-label="1" false-label="0" label="设置为相同的商品简介" size="large" />
                <span class="aiTypeSubName">{{  formOne.isTemp==1 ? aiTypeSubName:'' }}</span>
              </el-col>
            </el-row>
          <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitFormOne">提 交</el-button>
              <el-button @click="cancelOne"  >取 消</el-button>
            </div>
          </template>
        </el-dialog>
         <!-- 选择商品 -->
         <el-dialog v-model="openOneEdit" title="选择商品" append-to-body width="1200px" :destroy-on-close="true" @closed="cancelOneEdit" >
          <el-form
                ref="queryOneRef"
                :model="queryParamsOne"
                label-width="80px"
            >
              <el-row :gutter="20">
                <el-col :span="8">
                <el-form-item label="商品分类" prop="aiTypeId">
                  <el-cascader
                  style="width: 100%;"
                               placeholder="请选择商品分类"
                               ref="cascaderArrOne"
                               v-model="queryParamsOne.aiTypeId"
                               :props="produceTypeProps"
                               :options="sortOption"
                               :show-all-levels="false"
                               clearable
                               @change="handleChangeOne"
                  />
                </el-form-item>
              </el-col>
                <el-col :span="8">
                  <el-form-item label="商品名称" prop="aiTypeSubName">
                    <el-input
                        v-model="queryParamsOne.aiTypeSubName"
                        clearable
                        placeholder="请输入商品名称"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="商品规格" prop="aiTypeSubSpecs">
                    <el-input
                        v-model="queryParamsOne.aiTypeSubSpecs"
                        clearable
                        placeholder="请输入商品规格"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="是否显示" prop="showFlag">
                    <el-select
                        style="width: 100%;"
                        v-model="queryParamsOne.showFlag"
                        placeholder="请选择是否显示"
                        clearable
                    >
                      <el-option
                          v-for="dict in showFlagOptions"
                          :key="dict.code"
                          :label="dict.name"
                          :value="dict.code"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="是否启用" prop="useFlag">
                    <el-select
                        style="width: 100%;"
                        v-model="queryParamsOne.useFlag"
                        placeholder="请选择是否启用"
                        clearable
                    >
                      <el-option
                          v-for="dict in showFlagOptions"
                          :key="dict.code"
                          :label="dict.name"
                          :value="dict.code"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8" style="text-align: right;">
                  <el-button icon="Refresh" @click="resetQueryOne">重置</el-button>
                  <el-button icon="Search" type="primary" @click="handleQueryOne">搜索</el-button>
                </el-col>
              </el-row>
            </el-form>
            <el-table border
                  :data="producetypesubListOne"
                  @selection-change="handleSelectionChangeOne"
                  :height="300"
                  row-key="aiTypeSubId"
                  ref="tableRefOne"
        >
          <el-table-column align="center" type="selection" width="55" reserve-selection />
          <el-table-column align="center" label="商品图片" prop="aiTypeSubUrl1List">
            <template #default="scope">
              <el-image
                  v-if="scope.row.aiTypeSubUrl1List != null"
                  :src="scope.row.aiTypeSubUrl1List[0]"
                  style="width: 40px; height: 40px"
                  :preview-src-list="scope.row.aiTypeSubUrl1List"
                  :preview-teleported="true"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="商品编号" prop="aiTypeSubCode"/>
          <el-table-column align="center" label="企业简称" prop="companyAbbr"/>
          <el-table-column align="center" label="分类名称" prop="aiTypeName"/>
          <el-table-column align="center" label="商品名称" prop="aiTypeSubName">
            <template #default="scope">
              <span style="color: #00bcd4;cursor: pointer;">{{scope.row.aiTypeSubName}}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="规格" prop="aiTypeSubSpecs"/>
          <el-table-column align="center" label="重量" prop="aiTypeSubWeight">
            <template #default="scope">
              {{scope.row.aiTypeSubWeight}}{{scope.row.aiTypeSubUnit}}
            </template>
          </el-table-column>
          <el-table-column align="center" label="指导价格(元)" prop="aiGuidingPrice" width="100px">
            <template #default="scope">
              <span style="color: #00bcd4;" v-if="scope.row.guidingPriceRange">{{scope.row.guidingPriceRange}}</span>
              <span style="color: #00bcd4;" v-else></span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="外部编码" prop="outsideCode"/>
          <el-table-column align="center" label="生产企业" prop="produceCompanyName"/>
          <el-table-column align="center" label="排序" prop="aiTypeSubOrder"/>
          <el-table-column align="center" label="是否启用" prop="useFlag">
            <template #default="scope">
              <span v-if="scope.row.useFlag==1" style="color: #00bcd4;">启用</span>
              <span v-if="scope.row.useFlag==0" style="color: red;">禁用</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
            v-show="totalOne > 0"
            v-model:limit="queryParamsOne.rows"
            v-model:page="queryParamsOne.page"
            :total="totalOne"
            @pagination="getListOne"
        />
          <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitFormOneEdit">提 交</el-button>
              <el-button @click="cancelOneEdit"  >取 消</el-button>
            </div>
          </template>
        </el-dialog>
         <!-- 销售去向选择 -->
         <el-dialog v-model="openDirection" title="添加销售去向" append-to-body width="1200px" :destroy-on-close="true" @closed="cancelDirection" >
            <el-tree :data="orgList" :props="orgTreeProp" show-checkbox check-strictly @check="check" ref="orgRef" node-key="orgCode"/>
          <template #footer>
            <div class="dialog-footer">
              <el-button type="primary" @click="submitDirection">提 交</el-button>
              <el-button @click="cancelDirection"  >取 消</el-button>
            </div>
          </template>
        </el-dialog>
        <!-- 库点列表选择 -->
        <el-dialog v-model="openStore" title="选择库点" append-to-body width="1200px" :destroy-on-close="true" @closed="cancelStore">
            <el-form
                ref="formStoreRef"
                :model="queryParamsStore"
                label-width="80px"
            >
            <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="仓库名称" prop="storehouseName">
                      <el-input
                          v-model="queryParamsStore.storehouseName"
                          clearable
                          placeholder="请输入仓库名称"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                  <el-form-item label="企业简称" prop="aiCompanyId">
                    <el-select style="width: 100%" disabled v-model="queryParamsStore.aiCompanyId" clearable placeholder="请选择企业简称">
                      <el-option
                          v-for="dict in companyAbbrOption"
                          :key="dict.code"
                          :label="dict.name"
                          :value="dict.code"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="queryOrgRef">
                              <el-form-item label="服务范围" prop="orgCode" style="width: 100%;">
                                  <org-select
                                  style="width: 100%;"
                                      ref="queryOrgRef"
                                      clearable
                                      multiple
                                      placeholderText="请选择服务范围"
                                      :defaultOrgCode="true"
                                      @handleOrgCode="handleOrgCodeQuery"
                                      :level="3"
                                  />
                              </el-form-item>
                          </el-col>
                  <el-col :span="24" style="text-align: right;margin-bottom: 8px;">
                    <el-button icon="Refresh" @click="resetQueryStore">重置</el-button>
                    <el-button icon="Search" type="primary" @click="handleQueryStore">搜索</el-button>
                  </el-col>
                </el-row>
            </el-form>
              <el-table border
                    :data="storeData"
                    :height="300"
          >
            <el-table-column type="index" width="80" label="序号"/>
            <el-table-column align="center" label="库点名称" prop="storehouseName"/>
            <el-table-column align="center" label="所属机构" prop="companyName"/>
            <el-table-column align="center" label="服务范围" prop="scopLst" :formatter="scopLstFmt"/>
            <el-table-column
                align="center"
                class-name="small-padding fixed-width"
                label="操作"
                width="150px"
            >
              <template #default="scope">
                <el-button
                    link
                    size="small"
                    type="primary"
                    @click="handleSelectStore(scope.row)"
                >选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <pagination
              v-show="totalStore > 0"
              v-model:limit="queryParamsStore.rows"
              v-model:page="queryParamsStore.page"
              :total="totalStore"
              @pagination="getListStore"
          />
            <template #footer>
              <div class="dialog-footer">
                <el-button @click="cancelStore"  >取 消</el-button>
              </div>
            </template>
          </el-dialog>
          <!-- 出库弹窗 -->
          <el-dialog v-model="openOut" title="出库" append-to-body width="514px" :destroy-on-close="true" @closed="cancelOut">
              <el-form
                  ref="formOutRef"
                  :model="formOut"
                  :rules="rules"
                  label-width="80px"
                  label-position="top"
              >
              <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form-item label="原库存" prop="curInStock">
                        <el-input
                            disabled
                            v-model="formOut.curInStock"
                            clearable
                            placeholder=""
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="出库数量" prop="count">
                        <el-input
                            v-model="formOut.count"
                            clearable
                            @input="formOutCountChange"
                            placeholder="请输入出库数量"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="出库后" prop="new">
                        <el-input
                            disabled
                            v-model="formOut.new"
                            clearable
                            placeholder=""
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
              </el-form>
              <template #footer>
                <div class="dialog-footer">
                  <el-button type="primary" @click="submitOut">提 交</el-button>
                  <el-button @click="cancelOut"  >取 消</el-button>
                </div>
              </template>
          </el-dialog>
          <el-dialog v-model="openIn" title="入库" append-to-body width="514px" :destroy-on-close="true" @closed="cancelIn">
              <el-form
                  ref="formOutRef"
                  :model="formOut"
                  :rules="rules"
                  label-width="80px"
                  label-position="top"
              >
              <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form-item label="原库存" prop="curInStock">
                        <el-input
                            disabled
                            v-model="formOut.curInStock"
                            clearable
                            placeholder=""
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="入库数量" prop="count">
                        <el-input
                            v-model="formOut.count"
                            clearable
                            @input="formInCountChange"
                            placeholder="请输入入库数量"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="24">
                      <el-form-item label="入库后" prop="new">
                        <el-input
                            disabled
                            v-model="formOut.new"
                            clearable
                            placeholder=""
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
              </el-form>
              <template #footer>
                <div class="dialog-footer">
                  <el-button type="primary" @click="submitIn">提 交</el-button>
                  <el-button @click="cancelIn"  >取 消</el-button>
                </div>
              </template>
          </el-dialog>
      </el-main>
    </el-container>
  </div>
</template>

<script name="/invest/producetypesub/queryByPage" setup>
import {ref, nextTick, toRefs, reactive, onMounted} from "vue";
import {
  addproducetypesub,
  delproducetypesub,
  getproducetypesub,
  listproducetypesub,
  updateproducetypesub,
  batchUseFlagByIds,
  getProduceInputType,
  delproducetypesubs,
  showFlagById,
  queryProduceTypeTreeWithCompanyId,
  saveOrUpdate,
  seriesInfo
} from "@/api/bdh-agric-invest/invest/producetypesub";

import {
  storeQueryByPage,
  simplePage,
  manualStoreOutput,
  manualStoreInput
} from "@/api/bdh-agric-invest/invest/storehouseInfo";
import { queryAllOrgTree} from "@/api/bdh-agric-invest/system/dict/data";
import {postForExcel} from "@/api/bdh-agric-invest/invest/excel";
import {allCompanyInfo} from '@/api/bdh-agric-invest/invest/companyInfo'
import QuillEditor from "@/views/bdh-agric-invest/components/QuillEditor";
import {getDicts} from "@/api/bdh-agric-invest/system/dict/data";
import {getProduceType} from "@/api/bdh-agric-invest/invest/produceType";
import { specUnitList,reportUnitList } from "@/api/bdh-agric-invest/invest/unittrans";
import { alertEffects } from "element-plus";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
import Editor from "@/components/Editor/index.vue";
const { proxy } = getCurrentInstance();
const producetypesubList = ref([]);//商品信息列表
const producetypesubListOne = ref([]);//商品信息列表
const open = ref(false);//弹窗是否显示
const showSearch = ref(true);//搜索是否显示
const updateType=ref('update');
const ids = ref([]);//多选选中的id
const useFlags = ref([]);//是否启用
const names = ref([]);//多选选中的aiTypeSubName
const cascaderArr = ref()//商品分类
const multiple = ref(true);//非多选禁用
const total = ref(0);//列表条数
const totalOne = ref(0);//列表条数
const totalStore = ref(0);//列表条数

const title = ref("");//弹窗标题
const showFlagOptions = ref([]);//是、否、全部、字典
const tree = ref([]); //商品分类数据
const sortOption = ref([]);//所属分类字典
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const maxlength = ref(8);//输入框最大长度
const produceTypeProps = { //商品分类
  value: "aiTypeId",
  label: "aiTypeName",
  expandTrigger: 'hover',
};
const specUnitOptions=ref([])//规格单位
const reportUnitOptions=ref([])//上报单位
const orgList=ref([])
//企业简称字典
const companyAbbrOption = ref([]);
const orgTreeProp=ref({
  value: 'orgCode',
  label: 'orgName',
  children: 'children', // 子级字段名
  disabled:"disabled"
})
const data = reactive({
  formOut:{
    aiTypeSubId:null,
    storehouseId:null,
    curInStock:null,
    count:null,
    new:0,
  },
  openOut:false,
  openIn:false,
  plantMergeArr:[],
  mergeSpanArrIndex:0,
  ischeck:false,
  currentProduct:null,
  storeData:[],
  openStore:false,
  openDirection:false,
  aiTypeSubName:'',
  formOneDialog:[],
  formStoreDialog:[],
  openOneEdit:false,
  openOne:false,
  //企业简称表单
  formCompany: {
    companyId: null
  },
  //分类树的props
  defaultProps: {
    value: "aiTypeId",
    label: "aiTypeName",
  },
  formOne:{
    aiTypeSubIds:[],
    briefDescId:null,
    isTemp:0,
    produceSeriesName:null,
    produceSeriesId:null
  },
  //表单参数
  form: {
    //企业简称
    aiCompanyId: null,
    //商品id
    aiTypeId: null,
    //商品名称
    aiTypeSubName: null,
    //规格
    aiTypeSubSpecs: null,
    //商品指导价格
    aiGuidingPrice: null,
    //商品重量
    aiTypeSubWeight: null,
    //外部编码
    outsideCode: null,
    //生产企业
    produceCompanyName: null,
    //商品简介
    aiBriefDesc: null,
    //是否显示
    showFlag: null,
    //是否启用
    useFlag: null,
    //排序
    aiTypeSubOrder: null,
    //商品图片
    aiTypeSubUrl1: null,
    aiTypeSubUnit: null,
    aiTypeReportUnit: null,
    prodSells:[

    ],
    produceSeriesSubs:[]
  },
  queryParamsStore: {
    aiTypeSubId: null,
    orgCode: null,
    storehouseName: "",
    rows: 10,
    page: 1,
    params: {
      orgCodes: [

      ]
    }
  },
  //查询参数
  queryParams: {
    rows: 10,
    page: 1,
    aiTypeId: 0,
    aiTypeLvl: 0,
    aiTypeSubName: null,
    aiTypeSubSpecs: null,
    showFlag: null,
    useFlag: null,
  },
   //查询参数
   queryParamsOne: {
    rows: 10,
    page: 1,
    aiTypeId: 0,
    aiTypeLvl: 0,
    aiTypeSubName: null,
    aiTypeSubSpecs: null,
    showFlag: null,
    useFlag: null,
  },
  //表单校验
  rules: {
    storehouseName:[ { required: true, message: "请选择库点", trigger: ["blur"]}],
    guidePrice:[
      { required: true, message: "请输入商品指导价格", trigger: ["blur"]}
    ],
    produceSeriesName:[
      { required: true, message: "请输入系列名称", trigger: ["blur"]}
    ],
    aiCompanyId: [{ required: true, message: "请输入企业简称", trigger: "blur"}],
    aiTypeId: [{ required: true, message: "请选择商品分类", trigger: "change"}],
    aiTypeSubName: [{ required: true, message: "请输入商品名称", trigger: "change"}],
    aiTypeSubSpecs: [{ required: true, message: "请输入商品规格", trigger: "change"}],
    aiTypeSubWeight: [{ required: true, message: "请输入重量", trigger: ["blur"]},
      { pattern: true,validator:( rule, value, callback) => {
          var reg = /^([0-9]{1}|^[1-9]{1}\d{1,15})(\.\d{1,2})?$/
          if(value === null || value === '' || typeof(value) == 'undefined') {
            callback(new Error('重量必须输入数字'))
          } else if(reg.test(value) && value.toString().split('.')[0].length > 5) {
            callback(new Error('数字过大，请确认'))
          } else if(reg.test(value)) {
            callback()
          } else {
            callback(new Error('请输入数字(可带两位小数)'))
          }
        }
        ,trigger: ["blur"]}],
    useFlag: [{ required: true, message: "请选择是否启用", trigger: "change"}],
    showFlag: [{ required: true, message: "请选择是否显示", trigger: "blur"}],
    aiTypeReportUnit:[{ required: true, message: "请选择上报单位", trigger: "change"}],
    aiGuidingPrice: [{ required: true, message: "请输入商品指导价格", trigger: ["change","blur"]},
      { pattern: true,validator:( rule, value, callback) => {
        var reg = /^([0-9]{1}|^[1-9]{1}\d{1,15})(\.\d{1,2})?$/
        if(value === null || value === '' || typeof(value) == 'undefined') {
          callback(new Error('价格必须输入数字'))
        } else if(reg.test(value) && value.toString().split('.')[0].length > 5) {
          callback(new Error('数字过大，请确认'))
        } else if(reg.test(value)) {
          callback()
        } else {
          callback(new Error('请输入数字(可带两位小数)'))
        }
      }
      ,trigger: ["change","blur"]}],
      count: [{ required: true, message: "请输入数量", trigger: ["blur"]},
      { pattern: true,validator:( rule, value, callback) => {
          var reg = /^([1-9]{1}|^[1-9]{1}\d{1,15})$/
          if(value === null || value === '' || typeof(value) == 'undefined') {
            callback(new Error('数量必须输入数字'))
          } else if(reg.test(value) && value.toString().split('.')[0].length > 8) {
            callback(new Error('数字过大，请确认'))
          } else if(reg.test(value)) {
            callback()
          } else {
            callback(new Error('请输入正整数'))
          }
        }
        ,trigger: ["blur","change"]}],
  },
});

const {queryParams, form, rules, defaultProps, sortParams, formCompany, openOne, formOne, openOneEdit, queryParamsOne, formOneDialog, aiTypeSubName, openDirection, openStore ,queryParamsStore, storeData, currentProduct,formStoreDialog, ischeck,mergeSpanArrIndex,plantMergeArr,openOut,openIn,formOut } = toRefs(data);

onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
      ? window.innerHeight - searchHeight.value - 260
      : window.innerHeight - 260;
});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
      ? window.innerHeight - searchHeight.value - 260
      : window.innerHeight - 260;
});
// 格式化服务范围
function scopLstFmt(row){
  let arr=[]
  row.scopLst.forEach(item=>{
    arr.push(item.orgName)
  })
  return arr.join()
}
function setMergeArr(data) {
  mergeSpanArrIndex.value = 0
  plantMergeArr.value = []
  for (var i = 0; i < data.length; i++) {
    if (i === 0) {
      plantMergeArr.value.push(1)
      mergeSpanArrIndex.value = 0
    } else {
      // 判断当前元素与上一个元素是否相同 plantId是要合并的那一列的字段名
      if (data[i].storehouseId && data[i].storehouseId === data[i - 1].storehouseId) {
        plantMergeArr.value[mergeSpanArrIndex.value] += 1
        plantMergeArr.value.push(0)
      } else {
        plantMergeArr.value.push(1)
        mergeSpanArrIndex.value = i
      }
    }
  }
}

function objectSpanMethod({ row, column, rowIndex, columnIndex }) {
  if (form.value.prodSells.length > 1) {
    if (columnIndex === 3||columnIndex === 4) {
      const _row = plantMergeArr.value[rowIndex]
      const _col = _row > 0 ? 1 : 0
      return {
        rowspan: _row,
        colspan: _col
      }
    }
  }

}
function formOutCountChange(value) {
  var reg = /^([1-9]{1}|^[1-9]{1}\d{1,15})$/
  if (value === null || value === '' || typeof (value) == 'undefined') {
    formOut.value.new = formOut.value.curInStock
  } else if (reg.test(value) && value.toString().split('.')[0].length > 8) {
    formOut.value.new = formOut.value.curInStock
  } else if (reg.test(value)) {
    if(value > formOut.value.curInStock){
      formOut.value.count = formOut.value.curInStock
    }
    formOut.value.new = formOut.value.curInStock - formOut.value.count
  } else {
    formOut.value.new = formOut.value.curInStock
  }
}

function formInCountChange(value) {
  var reg = /^([1-9]{1}|^[1-9]{1}\d{1,15})$/
  if (value === null || value === '' || typeof (value) == 'undefined') {
    formOut.value.new = formOut.value.curInStock
  } else if (reg.test(value) && value.toString().split('.')[0].length > 8) {
    formOut.value.new = formOut.value.curInStock
  } else if (reg.test(value)) {
    formOut.value.new = Number(formOut.value.curInStock) + Number(formOut.value.count)
  } else {
    formOut.value.new = formOut.value.curInStock
  }
}




function orgNameFmt(row){
  let arr=[]
  row.sellPlaces.forEach(item=>{
    arr.push(item.orgName)
  })
  return arr.join()
}

/** 选择服务范围 */
const handleOrgCodeQuery = (orgInfo) => {
  let arr = []
  orgInfo.forEach(item => {
    arr.push(item.orgCode)
  })
  queryParamsStore.value.params.orgCodes = arr
  handleQueryStore();
}
// 记录哪一条数据是正在选择的
function handleSelectStore(row) {
  if (ischeck.value) {
    formStoreDialog.value.forEach(item => {
      item.storehouseId = row.storehouseId
      item.storehouseName = row.storehouseName
      item.curInStock = row.curInStock
    })
  } else {
    for (let index = 0; index < form.value.prodSells.length; index++) {
      const item = form.value.prodSells[index];
      if (item.storehouseId) {
        if (item.storehouseId == currentProduct.value.storehouseId) {
          item.storehouseId = row.storehouseId
          item.storehouseName = row.storehouseName
          item.curInStock = row.curInStock
        }
      }
      else {
        if (item.key == currentProduct.value.key) {
          item.storehouseId = row.storehouseId
          item.storehouseName = row.storehouseName
          item.curInStock = row.curInStock
        }
      }
    }
  }

  function sortBy(props) {
    return function (a, b) {
      return a[props] - b[props];
    }
  }
  let arr2 = Object.assign([], form.value.prodSells.sort(sortBy("storehouseId")))
  form.value.prodSells = arr2
  setMergeArr(form.value.prodSells)
  openStore.value = false
  setTimeout(() => {
    form.value.prodSells.forEach((item, index) => {
      if (item.storehouseName) {
        proxy.$refs['producetypesubRef'].validateField(`prodSells.${index}.storehouseName`);
      }
      proxy.$refs['producetypesubRef'].clearValidate(`prodSells.${index}.guidePrice`);
    })
  })
}
//销售去向树选择事件
function check(data, obj){
  let checkedKeys = obj.checkedKeys
  let bool = checkedKeys.indexOf(data.orgCode) > -1
  if(data.children){
    bindkey(data.children,bool)
    uncheck(data.children)
  }
}
function addStore(row){
  openStore.value = true
  ischeck.value = false
  currentProduct.value = Object.assign({},row)
  // getListStore()
  resetQueryStore()
}
function addDirections(){
  openDirection.value = true
  bindkey(orgList.value,false)
  // nextTick(()=>{
  //   form.value.prodSells.forEach(item=>{
  //     item.sellPlaces.forEach(it=>{
  //       proxy.$refs['orgRef'].setChecked(it.orgCode,true)
  //     })
  //   })
  // })
}
function cancelStore(){
  openStore.value = false
}
function cancelDirection(){
  openDirection.value = false
}
// 去向提交事件
function submitDirection() {
  let nodes = proxy.$refs['orgRef'].getCheckedNodes()
  if (nodes.length > 0) {
    let arr = []
    nodes.forEach(item => {
      arr.push({
        orgCode: item.orgCode,
        orgName: item.orgName,
      })
    })
    let key = new Date().getTime()
    form.value.prodSells.push({
      key:key,
      aiTypeSubId: form.value.aiTypeSubId,
      storehouseId: null,
      storehouseName: null,
      guidePrice: null,
      sellPlaces: arr
    })

    function sortBy(props) {
      return function (a, b) {
        return a[props] - b[props];
      }
    }
    let arr2 = Object.assign([], form.value.prodSells.sort(sortBy("storehouseId")))
    form.value.prodSells = arr2
    setMergeArr(form.value.prodSells)
    openDirection.value = false
    setTimeout(()=>{
      form.value.prodSells.forEach((item,index)=>{
        if(item.key && item.key == key){
          proxy.$refs['producetypesubRef'].clearValidate(`prodSells.${index}.guidePrice`);
        }
      })
    })
  } else {
    proxy.$modal.msgError("请选择销售去向");
  }
}

// 同一系列
function handleOne(){
  if(formOne.value.aiTypeSubIds.length>0){
    formOne.value.produceSeriesName = formOne.value.aiTypeSubIds[0].aiTypeSubName
    openOne.value = true
    nextTick(()=>{
      proxy.$refs['tableOneRef'].setCurrentRow(formOne.value.aiTypeSubIds[0])
    })
  }else{
    proxy.$modal.msgError("请选择商品");
  }
}

// 同一系列编辑页面
function handleOneEdit() {
  if (form.value.produceSeriesId) {
    seriesInfo(form.value.produceSeriesId).then(res => {
      formOne.value = res.data
      formOne.value.aiTypeSubIds = res.data.subs
      openOne.value = true
      nextTick(() => {
        if (res.data.isTemp == 1) {
          let name = ''
          res.data.subs.forEach(item => {
            if (item.aiTypeSubId == res.data.briefDescId) {
              name = item.aiTypeSubName
              proxy.$refs['tableOneRef'].setCurrentRow(item)
            }
          })
          aiTypeSubName.value = name
        }else{
          formOne.value.briefDescId = null
        }
      })
    })
  } else {
    openOne.value = true
    let obj = Object.assign({}, form.value)
    formOne.value.aiTypeSubIds = [obj]
    formOne.value.produceSeriesName = formOne.value.aiTypeSubIds[0].aiTypeSubName
  }
}
// 取消子级选中
function uncheck(element) {
  element.forEach((el) => {
    proxy.$refs['orgRef'].setChecked(el,false)
    if (el.children) {
      uncheck(el.children);
    }
  });
}
// 递归添加属性
function bindkey(element,bool=false,parent) {
  element.forEach((el) => {
    el.disabled = bool
    el.parent = parent
    if (el.children) {
      bindkey(el.children,bool,el);
    }
  });
}
// 获取去向树
const getOrgTree = () => {
  queryAllOrgTree('3').then((res) => {
    bindkey(res.data)
    orgList.value = res.data;
  });
};
function cancelOne(){
  proxy.$refs["tableRef"].clearSelection()
  openOne.value = false
  aiTypeSubName.value = ''
  formOne.value= {
    aiTypeSubIds:[],
    briefDescId:null,
    isTemp:0,
    produceSeriesName:null,
    produceSeriesId:null
  }
}

function cancelOneEdit(){
  openOneEdit.value = false
  formOneDialog.value = []
  formStoreDialog.value = []

}
// 商品选择
function submitFormOneEdit (){
  openOneEdit.value = false
  formOne.value.aiTypeSubIds = formOneDialog.value

}
// 系列重新选择商品
function selectOne() {
  openOneEdit.value = true
  nextTick(()=>{
    formOne.value.aiTypeSubIds.map(it => {
      proxy.$refs["tableRefOne"].toggleRowSelection(it, true)
    })
    resetQueryOne()
  })
}
function cancelOut(){
  openOut.value=false
}
function cancelIn(){
  openIn.value=false
}
function submitOut() {
  proxy.$refs["formOutRef"].validate((valid) => {
    if (valid) {
      let obj = {
        aiTypeSubId: formOut.value.aiTypeSubId,
        storehouseId: formOut.value.storehouseId,
        curInStock: formOut.value.count
      }
      manualStoreOutput(obj).then(res => {
        proxy.$modal.msgSuccess("出库成功");
        openOut.value = false
        form.value.prodSells.forEach(item => {
          if (item.storehouseId == formOut.value.storehouseId) {
            item.curInStock = item.curInStock - obj.curInStock
          }
        })
      })
    }
  });
}

function submitIn() {
  proxy.$refs["formOutRef"].validate((valid) => {
    if (valid) {
      let obj = {
          aiTypeSubId: formOut.value.aiTypeSubId,
          storehouseId: formOut.value.storehouseId,
          curInStock: formOut.value.count
        }
        manualStoreInput(obj).then(res => {
        proxy.$modal.msgSuccess("入库成功");
        openIn.value = false
        form.value.prodSells.forEach(item => {
          if (item.storehouseId == formOut.value.storehouseId) {
            item.curInStock = Number(item.curInStock) + Number(obj.curInStock)
          }
        })
      })
    }
  });
}


// 点击出库按钮
function handleStoreOut(row){
  formOut.value={
    aiTypeSubId:form.value.aiTypeSubId,
    storehouseId:row.storehouseId,
    curInStock:row.curInStock,
    count:null,
    new:row.curInStock,
  }
  openOut.value=true
}
// 点击入库按钮
function handleStoreIn(row){
  formOut.value={
    aiTypeSubId:form.value.aiTypeSubId,
    storehouseId:row.storehouseId,
    curInStock:row.curInStock,
    count:null,
    new:row.curInStock,
  }
  openIn.value=true
}
// 去向删除
function handleStoreDelete(row){
  proxy.$modal
    .confirm('请确认该销售去向下，已无此商品的销售计划确认删除？')
    .then(function () {
      let index = form.value.prodSells.findIndex(item => {
        return item == row
      })
      form.value.prodSells.splice(index, 1)
    })
    .catch(() => {
    });
}
// 系列删除
function handleOneDelete(index) {
  proxy.$modal
    .confirm('从系列中移除该商品后，该商品在前台展示时将不再与其他商品关联显示。确认移除？')
    .then(function () {
      formOne.value.aiTypeSubIds.splice(index, 1)
    })
    .catch(() => {
    });

}
function submitFormOne(){
  proxy.$refs["formOneRef"].validate((valid) => {
    if (valid) {
      if(formOne.value.isTemp==1){
        if(!formOne.value.briefDescId){
          proxy.$modal.msgError("请选择商品简介的模板商品");
          return
        }else{
          let id  = formOne.value.aiTypeSubIds.find(item=>{
            return item.aiTypeSubId == formOne.value.briefDescId
          })
          if(!id){
            proxy.$modal.msgError("请选择商品简介的模板商品");
            return
          }
        }
      }
      let obj = Object.assign({},formOne.value)
      let arrId=[]
      obj.aiTypeSubIds.forEach(item=>{
        arrId.push(item.aiTypeSubId)
      })
      obj.aiTypeSubIds = arrId
      if(obj.isTemp==0){
        obj.briefDescId = null
      }
      saveOrUpdate(obj).then((response) => {
          proxy.$modal.msgSuccess("设置成功");
          openOne.value = false;
          if(open.value == true){
            handleUpdate(form.value,'view')
          }
          getList()
      });
    }
  });
}
function handleCurrentChange(val){
  if(val){
    formOne.value.briefDescId = val.aiTypeSubId
    aiTypeSubName.value = val.aiTypeSubName
  }else{
    formOne.value.briefDescId = null
    aiTypeSubName.value = ''
  }
}

/** 查询商品信息列表 */
const getList = () => {
  console.info('formCompany.value.companyId:',formCompany.value.companyId);
  console.info('queryParams.value:',queryParams.value);
  if(!sortOption.value||sortOption.value.length==0){
    producetypesubList.value=[]
    total.value = 0;
  }else{
    if(formCompany.value.companyId){
      queryParams.value.aiCompanyId=formCompany.value.companyId;
    }
    let obj = Object.assign({}, queryParams.value)
    if (obj.showFlag == 3) {
      obj.showFlag = null
    }
    if (obj.useFlag == 3) {
      obj.useFlag = null
    }
    listproducetypesub(obj).then((response) => {
      producetypesubList.value = response.data.records;
      total.value = response.data.total;
    });
  }
}
/** 查询商品信息列表 */
const getListOne = () => {
  if (!sortOption.value || sortOption.value.length == 0) {
    producetypesubListOne.value = []
    totalOne.value = 0;
  } else {

    if (formCompany.value.companyId) {
      queryParamsOne.value.aiCompanyId = formCompany.value.companyId;
    }
    let obj = Object.assign({}, queryParamsOne.value)
    if (obj.showFlag == 3) {
      obj.showFlag = null
    }
    if (obj.useFlag == 3) {
      obj.useFlag = null
    }
    listproducetypesub(obj).then((response) => {
      producetypesubListOne.value = response.data.records;
      totalOne.value = response.data.total;
    });
  }
}

/** 查询商品信息列表 */
const getListStore = () => {
  queryParamsStore.value.aiTypeSubId = form.value.aiTypeSubId
    let obj = Object.assign({}, queryParamsStore.value)
    simplePage(obj).then((response) => {
      storeData.value = response.data.records;
      totalStore.value = response.data.total;
    });
}

// 取消按钮
const cancel = () => {
  open.value = false;
  reset();
}

// 表单重置
const reset = () => {
  form.value = {
    aiTypeName: null,
    aiCompanyId: null,
    aiTypeId: null,
    aiTypeSubName: null,
    aiTypeSubSpecs: null,
    aiGuidingPrice: null,
    aiTypeSubWeight: null,
    outsideCode: null,
    aiBriefDesc: null,
    showFlag: null,
    useFlag: null,
    aiTypeSubOrder: null,
    aiTypeSubUrl1: null,
    aiTypeSubUnit: null,
    aiTypeReportUnit: null,
    prodSells:[],
    produceSeriesSubs:[]
  };
  proxy.resetForm("producetypesubRef");
}

/** 搜索按钮操作 */
const handleQuery = () => {
  // if (queryParams.value.showFlag == 3) {
  //   queryParams.value.showFlag = null
  // }
  // if (queryParams.value.useFlag == 3) {
  //   queryParams.value.useFlag = null
  // }
  queryParams.value.page = 1;
  getList();
}

/** 搜索按钮操作 */
const handleQueryOne = () => {
  // if (queryParamsOne.value.showFlag == 3) {
  //   queryParamsOne.value.showFlag = null
  // }
  // if (queryParamsOne.value.useFlag == 3) {
  //   queryParamsOne.value.useFlag = null
  // }
  queryParamsOne.value.page = 1;
  getListOne();
}

/** 搜索按钮操作 */
const handleQueryStore = () => {
  // if (queryParamsOne.value.showFlag == 3) {
  //   queryParamsOne.value.showFlag = null
  // }
  // if (queryParamsOne.value.useFlag == 3) {
  //   queryParamsOne.value.useFlag = null
  // }
  queryParamsStore.value.page = 1;
  getListStore();
}

/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 重置按钮操作 */
const resetQueryOne = () => {
  queryParamsOne.value = {
    rows: 10,
    page: 1,
    aiTypeId: 0,
    aiTypeLvl: 0,
    aiTypeSubName: null,
    aiTypeSubSpecs: null,
    showFlag: null,
    useFlag: null,
  }
  handleQueryOne();
}

/** 重置按钮操作 */
const resetQueryStore = () => {
  queryParamsStore.value = {
    aiCompanyId: form.value.aiCompanyId,
    orgCode: null,
    storehouseName: "",
    rows: 10,
    page: 1,
    params: {
      orgCodes: [

      ]
    }
  }
  nextTick(()=>{
    proxy.$refs['queryOrgRef'].clear()
  })
  handleQueryStore();
}

// 多选框选中数据
const handleSelectionChange = (selection) => {
  formOne.value.aiTypeSubIds = selection
  ids.value = selection.map((item) => item.aiTypeSubId);
  names.value = selection.map((item) => item.aiTypeSubName);
  useFlags.value = selection.map((item) => item.useFlag);
  multiple.value = !selection.length;
  console.log(formOne.value.aiTypeSubIds);
}
// 多选框选中数据
const handleSelectionChangeOne = (selection) => {
  formOneDialog.value = selection
}

// 多选框选中数据
const handleSelectionStoreChange = (selection) => {
  formStoreDialog.value = selection
}
function addStoreOne() {
  if (formStoreDialog.value.length > 0) {
    openStore.value = true
    ischeck.value = true
    resetQueryStore()
  } else {
    proxy.$modal.msgError("请选择销售去向");
  }
}
/** 新增按钮操作 */
const handleAdd = () => {
  updateType.value='add'
  reset();
  specUnitOpt();
  form.value.aiCompanyId = formCompany.value.companyId;
  open.value = true;
  title.value = "添加商品信息";
}

/** 修改按钮操作 */
const handleUpdate = (row,type) => {
  updateType.value=type
  reset();
  const aiTypeSubId = row.aiTypeSubId || ids.value;
  getproducetypesub(aiTypeSubId).then((response) => {
    form.value = response.data;
    console.info('form.value',form.value);

    function sortBy(props) {
      return function (a, b) {
        return a[props] - b[props];
      }
    }
    let arr2 = Object.assign([], form.value.prodSells.sort(sortBy("storehouseId")))
    form.value.prodSells = arr2
    setMergeArr(form.value.prodSells)
    specUnitOpt('init');
    open.value = true;
    if(type=='edit'){
      title.value = "修改商品信息";
    }
    else if(type=='update'){
      title.value = "编辑商品信息";
    }else{
      title.value = "查看商品信息";
    }
  });
}

/** 提交按钮 */
const submitForm = () => {
  proxy.$refs["producetypesubRef"].validate((valid) => {
    if (valid) {
      if (form.value.aiTypeSubId != null) {
        form.value.showFlag=form.value.useFlag
        let obj = Object.assign({},form.value)
        let prodSells = Object.assign([],form.value.prodSells)
        prodSells.forEach(item=>{
          delete item.key
        })
        obj.prodSells = prodSells
        updateproducetypesub(obj).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        form.value.showFlag=form.value.useFlag
        addproducetypesub(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
const handleDelete = (row) => {
  const aiTypeSubIds = row.aiTypeSubId || ids.value;
  const aiTypeSubNames = row.aiTypeSubName || names.value;
  proxy.$modal
      .confirm('是否确认删除商品名称为"' + aiTypeSubNames + '"的数据项？')
      .then(function () {
        return Array.isArray(aiTypeSubIds) ? delproducetypesubs(aiTypeSubIds) : delproducetypesub(aiTypeSubIds);
      })
      .then(() => {
        proxy.$refs["tableRef"].clearSelection()
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
      .catch(() => {
      });
}

/** 批量启用/禁用按钮操作 */
const batchEnable = (val, row) => {
  let buttonType = null;
  let aiTypeSubIds = [];
  let aiTypeSubNames = null;
  if (val == 3) {
    buttonType = row.useFlag;
    aiTypeSubIds = row.aiTypeSubId.toString().split();
    aiTypeSubNames = row.aiTypeSubName;
  } else {
    buttonType = val;
    aiTypeSubIds = ids.value;
    aiTypeSubNames = names.value;
  }
  let text = buttonType == 0 ? "禁用" : "启用";
  if (buttonType == 1 || buttonType == 0) {
    proxy.$modal
        .confirm('确认要' + text + '选中的数据吗?')
        .then(function () {
          return batchUseFlagByIds({aiTypeSubIdList: aiTypeSubIds,useFlag: buttonType});
        })
        .then(() => {
          getList();
          proxy.$modal.msgSuccess('' + text + '成功');
        })
        .catch(function () {
          row.useFlag = row.useFlag === 0 ? 1 : 0;
        });
  }
}

/** 导出按钮操作 */
const handleExport = () => {
  postForExcel('/bdh-agric-invest-api/invest/producetypesub/exportExcel', queryParams.value,'商品库');
}

/** 获取商品分类 */
const getAiTypeSort = () => {
  queryProduceTypeTreeWithCompanyId(formCompany.value.companyId).then((response) => {
    sortOption.value = response.data;
    tree.value = [
      {
        aiTypeName: "全部",
        aiTypeId: 0,
        aiTypeLevel: 0,
        children: response.data,
      },
    ];
    getList();
  });

}

/** 按分类分页查询 */
const handleNodeClick = (data) => {
  if (data.aiTypeName != "全部") {
    queryParams.value.aiTypeId = data.aiTypeId;
    queryParams.value.aiTypeLvl = data.aiTypeLvl;
  } else {
    queryParams.value.aiTypeId = 0;
    queryParams.value.aiTypeLvl = 0;
  }
  // queryParams.value.aiTypeId = sortParams.value.aiTypeId;
  // queryParams.value.aiTypeLvl = sortParams.value.aiTypeLvl;
  if(!sortOption.value||sortOption.value.length==0){
    producetypesubList.value=[]
    total.value = 0;
  }else{
    listproducetypesub(queryParams.value).then((response) => {
      producetypesubList.value = response.data.records;
      total.value = response.data.total;
    });
  }
};

/** 点击商品图片和商品名称跳转修改页 */
const openUpdate = (row, column) => {
  if(column.label == '商品名称') {
    handleUpdate(row,'view')
  }
}

/** 字典 */
const getDisplay = () => {
  getDicts("pub_if").then((response) => {
    response.data.unshift({code: 3, name: "全部"});
    showFlagOptions.value = response.data;
  });
}

/** 是否显示 */
const handleDisplayShow = (row) => {
  let text = row.showFlag == 1 ? "显示" : "隐藏";
  proxy.$modal
      .confirm('确认要"' + text + '"该条信息吗?')
      .then(function () {
        return showFlagById({aiTypeSubId: row.aiTypeSubId, showFlag: row.showFlag});
      })
      .then(() => {
        proxy.$modal.msgSuccess(text + "成功");
      })
      .catch(function () {
        row.showFlag = row.showFlag === 0 ? 1 : 0;
      });
}

//获取企业简称
const getCompanyId = () => {
  allCompanyInfo({}).then(response => {
    response.data.forEach(each => {
      companyAbbrOption.value.push({
        code: each.aiCompanyId,
        name: each.companyAbbr
      })
    })
    formCompany.value.companyId = companyAbbrOption.value[0].code
    getAiTypeSort();
  });
}

/** 获取分类节点信息 */
function handleChange() {
  if (form.value.aiTypeId != null) {
    const checkedNode = proxy.$refs["cascaderArr"].getCheckedNodes();
    form.value.aiTypeId = checkedNode[0].data.aiTypeId;
    form.value.aiTypeName = checkedNode[0].data.aiTypeName;
  }
}
function handleChangeOne() {
    const checkedNode = proxy.$refs["cascaderArrOne"].getCheckedNodes();
    if(checkedNode.length>0){
      queryParamsOne.value.aiTypeId = checkedNode[0].data.aiTypeId;
    }else{
      queryParamsOne.value.aiTypeId = 0;

    }
}


const specUnitOpt = (init) => {
  specUnitList({}).then(response=>{
    specUnitOptions.value=response.data
    if(specUnitOptions.value&&specUnitOptions.value.length>0){
      if(!form.value.aiTypeSubUnit){
        form.value.aiTypeSubUnit=specUnitOptions.value[0].srcUnit;
      }
      reportUnitOpt(init);
    }
  })
}

const reportUnitOpt = (init) => {
  reportUnitList({'srcUnit':form.value.aiTypeSubUnit}).then(response=>{
    //init 代表刚进入编辑页面，刚进入编辑页面不清空。没有inti 代表页面切换，页面切换的时候就清空
    if(!init){
      form.value.aiTypeReportUnit=null
    }
    reportUnitOptions.value=response.data
  });

}

const refreshSpecs = () => {
  let  num=form.value.aiTypeSubWeight==null?'':form.value.aiTypeSubWeight
  let  unit=form.value.aiTypeSubUnit==null?'':form.value.aiTypeSubUnit
  form.value.aiTypeSubSpecs=num+unit;
}

const fileUploadAiTypeSubUrl1 = (value) => {
  if (Array.isArray(value) && value.length > 0) {
    const urls = value.map(item => item.url || item).join(',');
    form.value.aiTypeSubUrl1 = urls;
    proxy.$refs.producetypesubRef.clearValidate('aiTypeSubUrl1');
  } else {
    form.value.aiTypeSubUrl1 = '';
  }
}

getOrgTree()
getCompanyId();
getList();
getDisplay();
specUnitOpt();
</script>

<style lang="scss" scoped>
:deep(.el-checkbox__input.is-checked+.el-checkbox__label) {
  color: #000000;
}
.aiTypeSubName{
  color: #409eff;
  font-size: 16px;
  padding-left: 10px;
}
.queryOrgRef{
  :deep(.el-tooltip__trigger){
    display: flex;
    flex: 1;
  }
  :deep(.el-cascader){
    flex: 1;
  }
}
.ellipsis{
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.link{
  font-size: 12px;
  color: #409eff;
  padding-right: 10px;
  cursor: pointer;
}
.group__append{
  :deep(.el-input-group__append){
    padding: 0;
  }
}
</style>
