<template>
    <div class="app-container">
        <div ref="searchDom">
            <el-collapse-transition>
                <el-form
                    :rules="queryRules"
                    v-show="showSearch"
                    ref="queryRef"
                    :model="queryParams"
                    label-width="100px"
                    class="form-line"
                >
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="年份" prop="year">
                                <el-select
                                    v-model="queryParams.year"
                                    placeholder="请选择年份"
                                    @change="handleQuery"
                                    clearable
                                >
                                    <el-option
                                        v-for="dict in yearOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="所在单位" prop="orgCode">
                                <org-select
                                    ref="queryOrgRef"
                                    clearable
                                    :defaultOrgCode="queryParams.orgCode"
                                    @handleOrgCode="handleOrgCodeQuery"
                                    :level="3"
                                    :checkStrictly="false"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="企业名称" prop="aiCompanyId">
                                <el-select
                                    v-model="queryParams.aiCompanyId"
                                    placeholder="请选择企业名称"
                                    @change="handleQuery"
                                    clearable
                                >
                                    <el-option
                                        v-for="dict in companyAbbrOption"
                                        :key="dict.aiCompanyId"
                                        :label="dict.companyAbbr"
                                        :value="dict.aiCompanyId"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="作业季节" prop="fertilizeSeason">
                                <el-select
                                    v-model="queryParams.fertilizeSeason"
                                    placeholder="请选择作业季节"
                                    @change="handleQuery"
                                    clearable
                                >
                                    <el-option
                                        v-for="dict in fertilizesOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="计划状态" prop="planStatus">
                                <el-select
                                    v-model="queryParams.planStatus"
                                    placeholder="请选择计划状态"
                                    @change="handleQuery"
                                    clearable
                                >
                                    <el-option
                                        v-for="dict in planStatusOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="上架日期" prop="taskDateArr">
                                <el-date-picker
                                    v-model="taskDateArr"
                                    end-placeholder="结束日期"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    type="daterange"
                                    clearable
                                    value-format="YYYY-MM-DD"
                                    style="width: 220px"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6" :push="6" align="right">
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                            <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
                        </el-col>
                    </el-row>
                </el-form>
            </el-collapse-transition>
        </div>

        <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
                <el-button
                    type="primary"
                    icon="Plus"
                    @click="handleAdd"
                    v-hasPermi="['prodShelvesInfo:insertShelvesInfo']"
                >新增
                </el-button>
                <el-button
                    icon="Edit"
                    @click="handleUpdate"
                    :disabled="single"
                    v-hasPermi="['prodShelvesInfo:updateShelvesInfo']"
                >修改
                </el-button>
                <el-button
                    icon="Delete"
                    @click="handleDelete"
                    :disabled="multiple"
                    v-hasPermi="['prodShelvesInfo:delShelvesInfoByIds']"
                >删除
                </el-button>
            </el-col>
            <right-toolbar
                v-model:showSearch="showSearch"
                @queryTable="getList"
            ></right-toolbar>
        </el-row>

        <el-table
            border
            :height="tableHeight"
            :data="prodShelvesList"
            @selection-change="handleSelectionChange"
        >
            <el-table-column align="center" type="selection" width="55"/>
            <el-table-column align="center" label="年份" prop="year"/>
            <el-table-column align="center" label="所在单位" prop="orgName"/>
            <el-table-column align="center" label="企业名称" prop="aiCompanyName"/>
            <el-table-column align="center" label="作业季节" prop="fertilizeSeason" :formatter="fertilizeSeasonFormat"/>
            <el-table-column align="center" label="投入品订购计划标题" prop="planTitle"/>
            <el-table-column align="center" label="上架时间" prop="planBeginTimeStr" :formatter="timeFormat"/>
            <el-table-column align="center" label="状态" prop="planStatus" :formatter="planStatusFormat"/>
            <el-table-column align="center" label="操作" prop="year">
                <template #default="scope">
                    <el-button
                        v-hasPermi="['prodShelvesInfo:updateShelvesInfo']"
                        icon="Edit"
                        link
                        size="small"
                        type="primary"
                        @click="handleUpdate(scope.row)"
                    >修改
                    </el-button>
                    <el-button
                        v-hasPermi="['prodShelvesInfo:delShelvesInfoById']"
                        icon="Delete"
                        link
                        size="small"
                        type="primary"
                        @click="handleDelete(scope.row)"
                    >删除
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-show="total > 0"
            v-model:limit="queryParams.rows"
            v-model:page="queryParams.page"
            :total="total"
            @pagination="getList"
        />

<!--        添加或修改投入品上架及价格管理对话框-->
        <el-dialog v-model="open" :title="title" append-to-body width="1340px" :close-on-click-modal="false">
            <div class="app-container">
                <el-form
                    ref="prodShelvesInfoRef"
                    :model="form"
                    :rules="rules"
                    label-width="90px"
                >
                    <el-row :gutter="4">
                        <el-col :span="6">
                            <el-form-item label="年份" prop="year">
                                <el-select
                                    v-model="form.year"
                                    placeholder="请选择年份"
                                    clearable
                                    :disabled="isDisabled"
                                >
                                    <el-option
                                        v-for="dict in yearOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="所在单位" prop="orgCode">
                                <org-select v-if="rstAddOrg"
                                    :disabled="isDisabled"
                                    ref="queryFormOrgRef"
                                    clearable
                                    :defaultOrgCode="form.orgCode"
                                    @handleOrgCode="handleOrgCodeForm"
                                    :level="3"
                                    :checkStrictly="false"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="企业名称" prop="aiCompanyId">
                                <el-select
                                    :disabled="isDisabled"
                                    v-model="form.aiCompanyId"
                                    placeholder="请选择企业名称"
                                    @change="aiCompanyIdchange"
                                >
                                    <el-option
                                        v-for="dict in companyAbbrOption"
                                        :key="dict.aiCompanyId"
                                        :label="dict.companyAbbr"
                                        :value="dict.aiCompanyId"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="作业季节" prop="fertilizeSeason">
                                <el-select
                                    v-model="form.fertilizeSeason"
                                    placeholder="请选择作业季节"
                                    clearable
                                >
                                    <el-option
                                        v-for="dict in fertilizesOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="计划标题" prop="planTitle">
                                <el-input
                                    clearable
                                    v-model="form.planTitle"
                                    placeholder="请输入计划标题"
                                    @keyup.enter="handleQueryForm"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="状态" prop="planStatus">
                                <el-select
                                    v-model="form.planStatus"
                                    placeholder="请选择状态"
                                    clearable
                                >
                                    <el-option
                                        v-for="dict in planStatusOption"
                                        :key="dict.code"
                                        :label="dict.name"
                                        :value="dict.code"
                                    />
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="计划时间" prop="formDateArr">
                                <el-date-picker
                                    v-model="form.formDateArr"
                                    end-placeholder="结束日期"
                                    range-separator="至"
                                    start-placeholder="开始日期"
                                    type="daterange"
                                    clearable
                                    value-format="YYYY-MM-DD"
                                    />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6" v-if="isDisabled">
                                <el-form-item label="商品分类" prop="aiTypeId">
                                    <el-cascader
                                        placeholder="请选择商品分类"
                                        ref="cascaderArredit"
                                        v-model="queryform.aiTypeId"
                                        :props="produceTypeProps"
                                        :options="sortOption"
                                        :show-all-levels="false"
                                        clearable
                                        @change="handleChangeedit"
                                    />
                                </el-form-item>
                            </el-col>
                            <el-col :span="6" v-if="isDisabled">
                                <el-form-item label="商品名称"  prop="aiTypeSubName">
                                    <el-input
                                    clearable
                                    v-model="queryform.aiTypeSubName"
                                    placeholder="请输入商品名称"
                                />
                                </el-form-item>
                            </el-col>
                            <el-col :span="6" v-if="!isDisabled">
                              <el-form-item label="商品名称" prop="aiTypeSubName">
                                <el-input
                                    clearable
                                    v-model="form.aiTypeSubName"
                                    placeholder="请输入商品名称"
                                />
                              </el-form-item>
                            </el-col>
                            <el-col :span="6" v-if="!isDisabled">
                                <el-form-item label="商品分类" prop="aiTypeId">
                                    <el-cascader
                                        placeholder="请选择商品分类"
                                        ref="cascaderArr"
                                        v-model="form.aiTypeId"
                                        :props="produceTypeProps"
                                        :options="sortOption"
                                        :show-all-levels="false"
                                        @change="handleChange"
                                        clearable
                                    />
                                </el-form-item>
                            </el-col>

                        <el-col :span="18" align="right" v-if="isDisabled">
                            <el-button icon="Refresh"  @click="resetQueryFormedit">重置</el-button>
                            <el-button icon="Search" type="primary" @click="handleQueryFormedit">查询</el-button>
                        </el-col>
                        <el-col :span="18" align="right" v-else>
                            <el-button icon="Refresh" :disabled="isDisabled" @click="resetQueryForm">重置</el-button>
                            <el-button icon="Search" type="primary" :disabled="isDisabled" @click="handleQueryForm">查询</el-button>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button
                        type="primary"
                        icon="Plus"
                        @click="submitForm"
                        v-hasPermi="['prodShelvesInfo:insertShelvesInfo']"
                    >提交
                    </el-button>
                    <el-button icon="Refresh" @click="cancel">取消</el-button>
                    <el-button
                        icon="Edit"
                        @click="batchUp"
                        :disabled="multipleForm"
                    >批量上架
                    </el-button>
                    <el-button
                        icon="Delete"
                        @click="batchDown"
                        :disabled="multipleForm"
                    >批量下架
                    </el-button>
                    <el-button
                        icon="Edit"
                        @click="batchPub"
                        :disabled="multipleForm"
                    >批量发布
                    </el-button>
                    <el-button
                        icon="Delete"
                        @click="batchUnPub"
                        :disabled="multipleForm"
                    >批量取消发布
                    </el-button>
                </el-col>
            </el-row>
            <el-table
                border
                height="470"
                :data="prodCompanyList"
                @selection-change="handleSelectionChangeForm"
            >
                <el-table-column align="center" type="selection" width="55"/>
                <el-table-column align="center" label="分类名称" prop="aiTypeName"/>
                <el-table-column align="center" label="商品名称" prop="aiTypeSubName"/>
                <el-table-column align="center" label="规格" prop="aiTypeSubSpecs"/>
                <el-table-column align="center" label="重量" prop="aiTypeSubWeight">
                  <template #default="scope">
                    {{scope.row.aiTypeSubWeight}}{{scope.row.aiTypeSubUnit}}
                  </template>
                </el-table-column>
                <el-table-column align="center" label="指导价格" prop="aiGuidingPrice"/>
<!--                <el-table-column align="center" label="销售订金" prop="saleDeposit">
                    <template #default="scope">
                        <el-input
                            v-model="scope.row.saleDeposit"
                            placeholder="0"
                            clearable
                            oninput="value=value.replace(/^\D*(\d{0,9}(?:\.\d{0,2})?).*$/g,'$1')"
                            :disabled="scope.row.selectFlag == true ? false : true"
                        />
                    </template>
                </el-table-column>-->
<!--                <el-table-column align="center" label="销售金额" prop="saleAmount">
                    <template #default="scope">
                        <el-input
                            v-model="scope.row.saleAmount"
                            placeholder="0"
                            clearable
                            :disabled="scope.row.selectFlag == true ? false : true"
                            oninput="value=value.replace(/^\D*(\d{0,9}(?:\.\d{0,2})?).*$/g,'$1')"
                        />
                    </template>
                </el-table-column>-->
<!--                <el-table-column align="center" label="销售金额" prop="aiGuidingPrice"/>-->
                <el-table-column align="center" label="是否上架" prop="selectFlag">
                    <template #default="scope">
                        <!-- <el-switch v-model="scope.row.selectFlag" @change="scope.row.saleAmount=0;scope.row.saleDeposit=0" :active-value=true :inactive-value=false></el-switch> -->
                        <el-switch v-model="scope.row.selectFlag" @change="selectFlagChange($event,scope.row)" :active-value=true :inactive-value=false></el-switch>
                    </template>
                </el-table-column>
                <el-table-column align="center" label="是否面向农户发布" prop="selectFlag">
                    <template #default="scope">
                        <!-- <el-switch v-model="scope.row.selectFlag" @change="scope.row.saleAmount=0;scope.row.saleDeposit=0" :active-value=true :inactive-value=false></el-switch> -->
                        <el-switch v-model="scope.row.isPub" active-value='02' inactive-value='01'></el-switch>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
            v-show="isDisabled==true && totaldia > 0"
            v-model:limit="queryform.rows"
            v-model:page="queryform.page"
            :total="totaldia"
            @pagination="getShelvesInfoByIdlist"
        />
        <pagination
            v-show="isDisabled==false && totaldia > 0"
            v-model:limit="form.rows"
            v-model:page="form.page"
            :total="totaldia"
            @pagination="getFormList"
        />
        </el-dialog>
    </div>

</template>

<script name="/invest/prodShelvesInfo/queryByPage" setup>
import {
    queryByPageList,
    queryProByCompanyList,
    insertShelvesInfo,
    getShelvesInfoById,
    delShelvesInfoById,
    updateShelvesInfo,
    delShelvesInfoByIds
} from "@/api/bdh-agric-invest/invest/prodShelvesInfo"
import {getDicts} from "@/api/bdh-agric-invest/system/dict/data";
import {allCompanyInfo} from "@/api/bdh-agric-invest/invest/companyInfo";
import { queryAllOrgTree} from "@/api/bdh-agric-invest/system/dict/data";
import {nextTick, reactive, ref, toRefs} from "vue";
import {addproducetypesub, updateproducetypesub ,queryProduceTypeTreeWithCompanyId } from "@/api/bdh-agric-invest/invest/producetypesub";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
const { proxy } = getCurrentInstance();
const isDisabled = ref(false);
const single = ref(true);
const multiple = ref(true);
const multipleForm = ref(true);
const ids = ref([]);
const idsForm = ref([]);
const selects = ref([]);
const unselects = ref([]);
const showSearch = ref(true);//搜索是否显示
const yearOption = ref([]);//年份字典
const companyAbbrOption = ref([]);//企业名称字典
const fertilizesOption = ref([]);//施肥季节字典
const taskDateArr = ref([]);//上架日期
const queryRef = ref();//查询ref
const queryOrgRef = ref();//组织机构ref
const queryFormOrgRef = ref();//form表单中组织机构ref
const prodShelvesInfoRef = ref();//Form表单ref
const total = ref(0);//列表条数
const totaldia = ref(0);//列表条数
const title = ref("");//弹窗标题
const open = ref(false);//弹窗是否显示
const rstAddOrg = ref(false);
const prodShelvesList = ref([]);//投入品上架及价格管理表格数据
const prodCompanyList = ref([]);//对话框中表格数据
const sortOption = ref([]);//所属分类字典
const planStatusOption = ref([
    {
        code: 0,
        name: '启用'
    },
    {
        code: 1,
        name: '禁用'
    }])
const produceTypeProps = { //商品分类
    value: "aiTypeId",
    label: "aiTypeName",
    expandTrigger: 'hover',
};
const data = reactive({
    //查询参数
    queryParams: {
        rows: 10,
        page: 1,
        year: null,
        orgCode: null,
        aiCompanyId: null,
        fertilizeSeason: null,
        planStatus: null,
        planBeginTime: null,
        planEndTime: null,
    },
    //查询参数校验
    queryRules: {
        aiCompanyId: [{ required: true, message: "请选择企业名称", trigger: "blur"}],
    },
    //表单参数
    form: {
        rows: 10,
        page: 1,
        aiTypeId:null,
        aiTypeName:'',
        aiTypeSubName:'',
        aiProdShelvesDetailLst: []
    },
    queryform:{
        rows: 10,
        page: 1,
        aiTypeId:null,
        aiTypeName:'',
        aiTypeSubName:'',
        prodShelvesId:null,
    },
    //表单校验
    rules: {
        year: [{ required: true, message: "请选择年份", trigger: "change"}],
        orgCode: [{ required: true, message: "请选择所在单位", trigger: "change"}],
        aiCompanyId: [{ required: true, message: "请选择企业名称", trigger: "change"}],
        planTitle: [{ required: true, message: "请输入计划标题", trigger: "blur"}],
        planStatus: [{ required: true, message: "请选择计划状态", trigger: "change"}],
        fertilizeSeason: [{ required: true, message: "请选择作业季节", trigger: "change"}],
        formDateArr: [{ required: true, message: "请选择计划时间", trigger: "change"}],
    }
})

const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const defaultOrgCode = ref('');
const {queryParams, queryRules, form, rules, queryform} = toRefs(data);

onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
/** 获取字典 */
const getDict = () => {
    //获取企业简称字典
    allCompanyInfo({}).then((response) => {
        companyAbbrOption.value = response.data;
        queryParams.value.aiCompanyId = companyAbbrOption.value[0].aiCompanyId;
        //获取年份字典
        getDicts("year_cd").then((response) => {
            yearOption.value = response.data.sort((a, b) => {
                return Number(b.code) - Number(a.code);
            });
            queryParams.value.year = yearOption.value[0].code
            queryAllOrgTree("3").then((res) => {
                var orgList = res.data;
                if (orgList && orgList.length && orgList.length > 0) {
                    defaultOrgCode.value = orgList[0].orgCode;
                    queryParams.value.orgCode = defaultOrgCode.value;
                }
                getList();
            })
        });
    })
    //获取季节字典
    getDicts("fertilize_season").then((response) => {
        fertilizesOption.value = response.data;
    });
}
/** 获取商品分类 */
const getAiTypeSort = () => {
    queryProduceTypeTreeWithCompanyId(form.value.aiCompanyId).then((response) => {
        sortOption.value = response.data;
    });
}
function selectFlagChange(value,row){
    row.saleAmount=0;
    row.saleDeposit=0
    console.log(value)
    if(value){
        var index = selects.value.findIndex(item=>{
            return item.aiTypeSubId==row.aiTypeSubId
        })
        if(index==-1){
            selects.value.push(row)
        }
        var index2 = unselects.value.findIndex(item=>{
            return item.aiTypeSubId==row.aiTypeSubId
        })
        if(index2!=-1){
            unselects.value.splice(index2,1)
        }
    }else{
        row.isPub='01'
        var index = selects.value.findIndex(item=>{
            return item.aiTypeSubId==row.aiTypeSubId
        })
        if(index!=-1){
            selects.value.splice(index,1)
        }
        var index2 = unselects.value.findIndex(item=>{
            return item.aiTypeSubId==row.aiTypeSubId
        })
        if(index2==-1){
            unselects.value.push(row)
        }
    }
    console.log('selects',selects);
    console.log('unselects',unselects);
}
/** 选择所在单位 */
const handleOrgCodeQuery = (orgInfo) => {
    queryParams.value.orgCode = orgInfo.orgCode
    // handleQuery();
}
/** 获取分类节点信息 */
function handleChangeedit() {
    if (queryform.value.aiTypeId != null) {
        const checkedNode = proxy.$refs["cascaderArredit"].getCheckedNodes();
        queryform.value.aiTypeName = checkedNode[0].data.aiTypeName;
    }else{
        queryform.value.aiTypeName = ''
    }
}
/** 获取分类节点信息 */
function handleChange() {
    if (form.value.aiTypeId != null) {
        const checkedNode = proxy.$refs["cascaderArr"].getCheckedNodes();
        form.value.aiTypeName = checkedNode[0].data.aiTypeName;
    }else{
        form.value.aiTypeName = ''
    }
}
/** 选择表单中所在单位 */
const handleOrgCodeForm = (orgInfo) => {
    console.info('form.value.orgCode:',orgInfo.orgCode);
    form.value.orgCode = orgInfo.orgCode
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryRef.value.validate(valid => {
    if (valid) {
      getList();
    }
  })
}

/** 获取投入品上架及价格管理数据 */
const getList = () => {
    if (taskDateArr.value.length != 0) {
        queryParams.value.planBeginTime = taskDateArr.value[0];
        queryParams.value.planEndTime = taskDateArr.value[1];
    }else{
      queryParams.value.planBeginTime = null;
      queryParams.value.planEndTime = null;
    }
    queryByPageList(queryParams.value).then((response) => {
        prodShelvesList.value = response.data.records;
        total.value = response.data.total;
    })
}

/** 重置按钮操作 */
const resetQuery = () => {
    proxy.resetForm("queryRef");
    queryOrgRef.value.clear();
    queryParams.value.aiCompanyId = companyAbbrOption.value[0].aiCompanyId;
    queryParams.value.year = yearOption.value[0].code;
    queryParams.value.orgCode = defaultOrgCode.value;
    taskDateArr.value=[];
    queryOrgRef.value.setValue(defaultOrgCode.value, "");
    handleQuery();
}

/** 表单重置 */
const resetQueryForm = () => {
    proxy.resetForm("prodShelvesInfoRef");
    queryFormOrgRef.value.clear();
    form.value.aiTypeId=null
    form.value.aiTypeName=''
    form.value.aiTypeSubName=''
}
function resetQueryFormedit (){
    queryform.value.aiTypeId=null
    queryform.value.aiTypeName=''
    queryform.value.aiTypeSubName=''
}
// 多选框选中数据
const handleSelectionChange = (selection) => {
    ids.value = selection.map((item) => item.prodShelvesId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}

/** 弹窗中多选 */
const handleSelectionChangeForm = (selection) => {
    // idsForm.value.push(selection)
    idsForm.value = selection.map((item) => item.aiTypeSubId);
    multipleForm.value = !selection.length;
}

// 表单重置
const reset = () => {
    form.value = {
        year: null,
        orgCode: null,
        aiCompanyId: null,
        planTitle: null,
        planStatus: null,
        formDateArr: [],
        fertilizeSeason: null,
        planEndTime: null,
        planBeginTime: null,
        rows: 10,
        page: 1,
        aiTypeId:null,
        aiTypeName:'',
        aiTypeSubName:'',
    };
    proxy.resetForm("prodShelvesInfoRef");
}

/** 新增按钮操作 */
const handleAdd = () => {
    reset();
    isDisabled.value = false;
    open.value = true;
    title.value = "添加商品上架计划";
    prodCompanyList.value = [];
    selects.value=[]
    unselects.value=[]
    form.value.year = yearOption.value[0].code
    //aiCompanyId
    form.value.aiCompanyId=companyAbbrOption.value[0].aiCompanyId;
    getAiTypeSort()
    rstAddOrg.value=false;
    nextTick(() => {
      rstAddOrg.value = true
    })
    getFormList();
}

/** 修改按钮操作 */
const handleUpdate = (row) => {
    reset();
    queryform.value={
        rows: 10,
        page: 1,
        aiTypeId:null,
        aiTypeName:'',
        aiTypeSubName:'',
        prodShelvesId:null,
    }
    selects.value=[]
    unselects.value=[]
    const prodShelvesIds = row.prodShelvesId || ids.value[0];
    queryform.value.prodShelvesId = prodShelvesIds
    getShelvesInfoByIdlist()
}
function getShelvesInfoByIdlist(){
    var obj = Object.assign({},queryform.value)
    delete obj.aiTypeId
    getShelvesInfoById(obj).then((response) => {
        form.value = response.data;
        totaldia.value=response.data.total
        var arr=[]
        response.data.aiProduceInputTypeSubLst.forEach((row,index)=>{
                var obj = selects.value.find(item=>{
                    return item.aiTypeSubId==row.aiTypeSubId
                })
                var obj2 = unselects.value.find(item=>{
                    return item.aiTypeSubId==row.aiTypeSubId
                })
                if(obj){
                    arr.push(obj)
                }else if(obj2){
                    arr.push(obj2)
                }else{
                    arr.push(row)
                }
        })
        prodCompanyList.value = arr
        prodCompanyList.value.forEach(item=>{
            var index = selects.value.findIndex(it=>{
                return item.aiTypeSubId==it.aiTypeSubId
            })
            if(item.selectFlag&&index==-1){
                selects.value.push(item)
            }
            var index2 = unselects.value.findIndex(it=>{
                return item.aiTypeSubId==it.aiTypeSubId
            })
            if(item.selectFlag!=true&&index2==-1){
                unselects.value.push(item)
            }
        })
        // console.log(selects);
        form.value.formDateArr = [response.data.planBeginTimeStr, response.data.planEndTimeStr]
        open.value = true;
        getAiTypeSort()
        title.value = "修改商品上架计划";
        isDisabled.value = true;
        rstAddOrg.value=false;
        nextTick(() => {
          rstAddOrg.value = true
        })
    });
}

/** 删除按钮操作 */
const handleDelete = (row) => {
    const prodShelvesIds = row.prodShelvesId || ids.value;
    proxy.$modal
        .confirm('是否确认删除选中的数据项？')
        .then(function () {
            return Array.isArray(prodShelvesIds) ? delShelvesInfoByIds(prodShelvesIds) : delShelvesInfoById({prodShelvesId: prodShelvesIds});
        })
        .then(() => {
            getList();
            proxy.$modal.msgSuccess("删除成功");
        })
        .catch(() => {
        });
}

/** 作业季节字典翻译 */
const fertilizeSeasonFormat = (row) => {
    for(let i=0;i<fertilizesOption.value.length;i++){
        if(fertilizesOption.value[i].code==row.fertilizeSeason){
            return fertilizesOption.value[i].name
        }
    }
}

/** 状态字典翻译 */
const planStatusFormat = (row) => {
    for(let i=0;i<planStatusOption.value.length;i++){
        if(planStatusOption.value[i].code==row.planStatus){
            return planStatusOption.value[i].name
        }
    }
}

/** 上架时间字典翻译 */
const timeFormat = (row) => {
    return row.planBeginTimeStr + '至' + row.planEndTimeStr
}

/** 对话框中查询 */
const handleQueryForm = () => {
    form.value.page=1
    getFormList();
}
/** 对话框中查询 */
const handleQueryFormedit = () => {
    queryform.value.page=1
    getShelvesInfoByIdlist()
}
function aiCompanyIdchange(){
    selects.value=[]
    unselects.value=[]
    form.value.page=1
    getAiTypeSort()
    getFormList()
}
/** 获取对话框中表格 */
const getFormList = () => {
    var obj ={
        aiCompanyId: form.value.aiCompanyId,
        rows: form.value.rows,
        page: form.value.page,
        aiTypeName:form.value.aiTypeName,
        aiTypeSubName:form.value.aiTypeSubName,
    }
    queryProByCompanyList(obj).then((response) => {
        var arr=[]
        response.data.records.forEach((row,index)=>{
                var obj = selects.value.find(item=>{
                    return item.aiTypeSubId==row.aiTypeSubId
                })
                var obj2 = unselects.value.find(item=>{
                    return item.aiTypeSubId==row.aiTypeSubId
                })
                if(obj){
                    arr.push(obj)
                }else if(obj2){
                    arr.push(obj2)
                }else{
                    arr.push(row)
                }
        })
        prodCompanyList.value = arr;
        prodCompanyList.value.forEach(item=>{
            var index = selects.value.findIndex(it=>{
                return item.aiTypeSubId==it.aiTypeSubId
            })
            if(item.selectFlag&&index==-1){
                selects.value.push(item)
            }
            var index2 = unselects.value.findIndex(it=>{
                return item.aiTypeSubId==it.aiTypeSubId
            })
            if(item.selectFlag!=true&&index2==-1){
                unselects.value.push(item)
            }
        })
        console.log('selects',selects);
        console.log('unselects',unselects);
        totaldia.value = response.data.total
    })
}

/** 弹窗取消按钮 */
const cancel = () => {
    open.value = false;
    resetQueryForm();

}

/** 批量上架按钮 */
const batchUp = () => {
    prodCompanyList.value.forEach(v => {
        idsForm.value.forEach((item => {
            if (v.aiTypeSubId == item) {
                var index = selects.value.findIndex(it=>{
                    return it.aiTypeSubId==v.aiTypeSubId
                })
                if(index==-1){
                    selects.value.push(v)
                }
                var index2 = unselects.value.findIndex(it=>{
                    return it.aiTypeSubId==v.aiTypeSubId
                })
                if(index2!=-1){
                    unselects.value.splice(index2,1)
                }
                v.selectFlag = true
            }
        }))
    })
    console.log('selects',selects);
    console.log('unselects',unselects);
}

/** 批量下架按钮 */
const batchDown = () => {
    prodCompanyList.value.forEach(v => {
        idsForm.value.forEach((item => {
            if (v.aiTypeSubId == item) {
                var index = selects.value.findIndex(it=>{
                    return it.aiTypeSubId==v.aiTypeSubId
                })
                if(index!=-1){
                    selects.value.splice(index,1)
                }
                var index2 = unselects.value.findIndex(it=>{
                    return it.aiTypeSubId==v.aiTypeSubId
                })
                if(index2==-1){
                    unselects.value.push(v)
                }
                v.selectFlag = false
                v.saleAmount = 0
                v.saleDeposit = 0
            }
        }))
    })
    console.log('selects',selects);
    console.log('unselects',unselects);
}


//批量发布
const batchPub = () => {
  if(selects.value==null||selects.value.length==0){
    proxy.$modal.msgError("请先上架商品!");
    return;
  }

  selects.value.forEach(v => {
    idsForm.value.forEach((item => {
      if (v.aiTypeSubId == item) {
        v.isPub="02"
      }
    }))
  })
}

//批量不发布
const batchUnPub = () =>{
  if(selects.value==null||selects.value.length==0){
    proxy.$modal.msgError("请先上架商品!");
    return;
  }

  selects.value.forEach(v => {
    idsForm.value.forEach((item => {
      if (v.aiTypeSubId == item) {
        v.isPub="01"
      }
    }))
  })
}




/** 提交按钮操作 */
const submitForm = () => {
    prodShelvesInfoRef.value.validate(valid => {
        if (valid) {
            //checkDetail
            var chkSaleAmount=false;
            let errorList=[]
            /*selects.value.forEach(item => {
              if(item.selectFlag&&item.isPub=='02'){//面相用户 验证数据为0
                let amountEmpty=(item.saleAmount == null || item.saleAmount == 0)
                let epositdEmpty=(item.saleDeposit==null || item.saleDeposit==0)
                if(amountEmpty&&epositdEmpty){
                  errorList.push(item)
                }
              }
            })

            if(errorList.length>0){
              proxy.$modal.msgError("销售定金、销售金额不能同时为0,请仔细核查上架商品列表!！");
              return;
            }*/

            if (form.value.formDateArr && form.value.formDateArr.length == 2) {
                    form.value.planBeginTime = form.value.formDateArr[0]
                    form.value.planEndTime = form.value.formDateArr[1]
                } else {
                    form.value.planBeginTime = null
                    form.value.planEndTime = null
                }
            delete form.value.formDateArr;
            let list = []
            let aiProdShelvesDetailLstCancel  = []
            unselects.value.forEach(item => {
                aiProdShelvesDetailLstCancel.push({
                            saleAmount: item.saleAmount,
                            saleDeposit: item.saleDeposit,
                            aiTypeSubId: item.aiTypeSubId,
                            isPub:item.isPub
                })
            })
            form.value.aiProdShelvesDetailLstCancel = aiProdShelvesDetailLstCancel;
            if (form.value.prodShelvesId != null) {
                selects.value.forEach(item => {
                    /*if(item.isPub!='02'){//不面相用户 不验证数据为0
                        list.push({
                            saleAmount: item.saleAmount,
                            saleDeposit: item.saleDeposit,
                            aiTypeSubId: item.aiTypeSubId,
                            isPub:item.isPub
                        })
                    }else if (item.saleAmount !== null && item.saleAmount > 0 || item.saleDeposit !== null && item.saleDeposit > 0) {
                        list.push({
                            saleAmount: item.saleAmount,
                            saleDeposit: item.saleDeposit,
                            aiTypeSubId: item.aiTypeSubId,
                            isPub:item.isPub
                        })
                    }*/
                  list.push({
                    saleAmount: item.saleAmount,
                    saleDeposit: item.saleDeposit,
                    aiTypeSubId: item.aiTypeSubId,
                    isPub:item.isPub
                  })
                })
                if(list.length==0){
                  proxy.$modal.msgError("上架列表不能为空，请仔细核查上架商品列表!");
                  return;
                }
                form.value.aiProdShelvesDetailLst = list;
                var obj = Object.assign({},form.value)
                delete obj.aiTypeId
                updateShelvesInfo(obj).then((response) => {
                    proxy.$modal.msgSuccess("修改成功");
                    open.value = false;
                    getList();
                });
            } else {
                selects.value.forEach(item => {
                    /*if(item.isPub!='02'){//不面相用户 不验证数据为0
                        list.push({
                            saleAmount: item.saleAmount,
                            saleDeposit: item.saleDeposit,
                            aiTypeSubId: item.aiTypeSubId,
                            isPub:item.isPub
                        })
                    }else if (item.saleAmount !== null && item.saleAmount > 0 || item.saleDeposit !== null && item.saleDeposit > 0) {
                        list.push({
                            saleAmount: item.saleAmount,
                            saleDeposit: item.saleDeposit,
                            aiTypeSubId: item.aiTypeSubId,
                            isPub:item.isPub
                        })
                    }*/
                  list.push({
                    saleAmount: item.saleAmount,
                    saleDeposit: item.saleDeposit,
                    aiTypeSubId: item.aiTypeSubId,
                    isPub:item.isPub
                  })
                })
                if(list.length==0){
                  proxy.$modal.msgError("上架列表不能为空，请仔细核查上架商品列表!");
                  return;
                }
                form.value.aiProdShelvesDetailLst = list;
                var obj = Object.assign({},form.value)
                delete obj.aiTypeId
                insertShelvesInfo(obj).then((response) => {
                    proxy.$modal.msgSuccess("新增成功");
                    open.value = false;
                    getList();
                });
            }
        }
    })
}

getDict();
</script>

<style scoped lang="scss">
:deep(.el-form-item--default .el-form-item__label) {
    font-weight: 700;
}
//:deep(.el-input__wrapper) {
//    width: 190px !important;
//}
</style>
