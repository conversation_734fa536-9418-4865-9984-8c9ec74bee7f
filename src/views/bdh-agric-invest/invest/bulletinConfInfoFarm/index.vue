<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form :model="queryParams" ref="queryRef" v-show="showSearch" label-width="80px" class="form-line">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年度" prop="year">
                  <el-select v-model="queryParams.year" clearable placeholder="请选择年度">
                      <el-option
                          v-for="dict in yearOption"
                          :key="dict.code"
                          :label="dict.name"
                          :value="dict.code"
                      />
                  </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="公示标题" prop="bulletinTitle">
                <el-input
                    v-model="queryParams.bulletinTitle"
                    maxlength="50"
                    show-word-limit
                    placeholder="请输入公示标题"
                    clearable
                    @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="公示日期">
                <el-date-picker style="width:230px"
                    v-model="queryDateRange"
                    type="daterange"
                    value-format="YYYY-MM-DD"
                    range-separator="~"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6" align="right">
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>
    <el-row :gutter="10" class="mb8">
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>
    <el-table border :height="tableHeight"  :data="bulletinConfInfoList"  @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <el-table-column label="年度" align="center" prop="year" >
      </el-table-column>
      <el-table-column label="公示标题" align="center" prop="bulletinTitle" >
        <template #default="scope">
          <a style="color: dodgerblue" @click="handleTitleClick(scope.row)" >{{scope.row.bulletinTitle}}</a>
        </template>
      </el-table-column>
      <el-table-column label="公示时间" align="center" width="220" >
        <template #default="scope">
          {{geBulletinDateRange(scope.row)}}
        </template>
      </el-table-column>
      <el-table-column label="发布状态" align="center" prop="isPublish">
        <template #default="scope">
          {{scope.row.isPublish === '1' ? '已发布' : '未发布'}}
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.rows"
        @pagination="getList"
    />

    <!-- 添加或修改公示配置管理表对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <div class="form-wrapper bulletinConfInfoCompany">
        <el-form ref="bulletinConfInfoRef" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="年度" prop="year">
            <el-select disabled v-model="form.year" style="width:100%" clearable placeholder="请选择年度">
              <el-option
                  v-for="dict in yearOption"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="公示标题" prop="bulletinTitle">
            <el-input v-model="form.bulletinTitle"   disabled
                      show-word-limit placeholder="请输入公示标题"/>
          </el-form-item>
          <el-form-item label="公示日期" prop="formDateRange" class="item">
            <el-date-picker
                disabled
                v-model="form.formDateRange"
                type="daterange"
                value-format="YYYY-MM-DD"
                range-separator="~"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
            />
          </el-form-item>
          <el-form-item label="公示对象" prop="bulletinObject" class="item">
            <el-input v-model="form.orgNameList" disabled></el-input>
          </el-form-item>
          <el-form-item label="是否发布" prop="isPublish">
            <el-switch
                disabled
                v-model="form.isPublish"
                active-value="1"
                inactive-value="0"
            />
          </el-form-item>
          <el-form-item label="公示附件">
            <fileUpload urlApi="bdh-agric-invest-api" :show="false"  v-model:fileType="fileType" v-model="form.annexUrlArray"></fileUpload>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
<!--          <el-button type="primary" @click="submitForm">确 定</el-button>-->
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="/invest/bulletinConfInfoFarm">
import {
  listBulletinConfInfo,
  getBulletinConfInfo,
  delBulletinConfInfo,
  addBulletinConfInfo,
  updateBulletinConfInfo
} from "@/api/bdh-agric-invest/invest/bulletinConfInfoFarm";
import {reactive, ref, toRefs} from "vue";
import {getDicts} from "@/api/bdh-agric-invest/system/dict/data";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";

const {proxy} = getCurrentInstance();

const bulletinConfInfoList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const yearOption = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom

const data = reactive({
  form: {formDateRange: []},
  queryParams: {
    rows: 10,
    page: 1,
    year: null,
    bulletinTitle: null,
    beginTime: null,
    endTime: null,
    bulletinObject: null,
    orgCode: null,
    orgName: null,
    isPublish: null,
    annexUrl: null,
    statusCd: null,
    bulletin: null,
  },
  // rules: {
  //   bulletinTitle: [{required: true, message: "请输入公示标题", trigger: "blur"},
  //     {length: 30, message: "最多30个字符", trigger: "blur"}],
  //   year: [{required: true, message: "请选择年度", trigger: "blur"}],
  //   beginTime: [{required: true, message: "请选择公示开始时间", trigger: "blur"}],
  //   endTime: [{required: true, message: "请选择公示结束时间", trigger: "blur"}],
  //   bulletinObject: [{type: 'array',required: true, message: "请选择公示对象", trigger: "change"}],
  //   formDateRange: [{required: true, message: "请选择公示日期", trigger: "blur"}],
  // }
});

const fileType = ['pdf'];
const queryDateRange = ref([]);
// const formDateRange = ref([]);
//发布状态 已发布1 未发布0
const isPublish = [
  {
    value: 0,
    label: '未发布',
  },
  {
    value: 1,
    label: '已发布',
  }];

const bulletinObject = ref([]);

const {queryParams, form, rules} = toRefs(data);

onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});

/** 查询公示配置管理表列表 */
function getList() {
  loading.value = true;
  if(queryDateRange.value&&queryDateRange.value.length>0){
    queryParams.value.beginTime = queryDateRange.value[0];
    queryParams.value.endTime = queryDateRange.value[1];
  }
  listBulletinConfInfo(queryParams.value).then(response => {
    bulletinConfInfoList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
    queryParams.value.beginTime = '';
    queryParams.value.endTime =  '';
  });
}

function handleTitleClick(row){
  title.value="查看公示"
  console.log(row);
  handleUpdate(row);
}


// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    year: null,
    bulletinTitle: null,
    beginTime: null,
    endTime: null,
    bulletinObject: [],
    orgCode: null,
    orgName: null,
    isPublish: null,
    annexUrl: null,
    remark: null,
    createBy: null,
    updateBy: null,
    updateTime: null,
    bulletinConfId: null,
    createTime: null,
    statusCd: null,
    bulletin: '1',
    formDateRange: []
  };
  proxy.resetForm("bulletinConfInfoRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  // queryParams.value.rows = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryDateRange.value=null
  proxy.resetForm("queryRef");
  queryParams.value.year = yearOption.value[0].code
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.bulletinConfId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加公示配置管理表";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const bulletinConfId = row.bulletinConfId || ids.value

  getBulletinConfInfo(bulletinConfId).then(response => {
    form.value = response.data;
    let begin = new Date(form.value.beginTime).toISOString().split('T')[0];
    let end = new Date(form.value.endTime).toISOString().split('T')[0];
    let arr = [begin,end];
    form.value.formDateRange = arr;
    // let bulletinObject = response.data.bulletinObject.split(',');
    // form.value.bulletinObject = bulletinObject;
    open.value = true;
    title.value = "查看公示";
  });
}

/** 提交按钮 */
function submitForm() {
  form.value.beginTime = form.value.formDateRange[0];
  form.value.endTime = form.value.formDateRange[1];
  form.value.bulletin = '1';
  proxy.$refs["bulletinConfInfoRef"].validate(valid => {
    if (valid) {
      if (form.value.bulletinConfId != null) {
        updateBulletinConfInfo(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addBulletinConfInfo(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });

}

/** 删除按钮操作 */
function handleDelete(row) {
  const bulletinConfIds = row.bulletinConfId || ids.value;
  proxy.$modal.confirm('是否确认删除公示配置管理表编号为"' + bulletinConfIds + '"的数据项？').then(function () {
    return delBulletinConfInfo(bulletinConfIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('invest/bulletinConfInfo/export', {
    ...queryParams.value
  }, `bulletinConfInfo_${new Date().getTime()}.xlsx`)
}


function geBulletinDateRange(obj){
  const beginTime = new Date(obj.beginTime);
  const endTime = new Date(obj.endTime);
  return  beginTime.toISOString().split('T')[0]
          + '至' +
          endTime.toISOString().split('T')[0];
}

function getBulletinObjectFromDict(){
  getDicts('aiInvest:bulletinObject').then(response=>{
    console.log(response.data);
    bulletinObject.value = response.data;
  })
}

/** 获取字典 */
const getDict = () => {
    //查询年份
    getDicts('year_cd').then(response => {
        yearOption.value = response.data.sort((a, b) => {
            return Number(b.code) - Number(a.code)
        })
        queryParams.value.year = yearOption.value[0].code
        getList();
    });
}

getDict();
getBulletinObjectFromDict();
</script>
<style scoped lang="scss">
.el-form-item_label{
    font-weight: 700;
  }
.upload-file{
  width: 100%;
}
.bulletinConfInfoCompany{
  :deep(.el-form-item--default){
    margin-bottom: 18px !important;
  }
}
</style>
