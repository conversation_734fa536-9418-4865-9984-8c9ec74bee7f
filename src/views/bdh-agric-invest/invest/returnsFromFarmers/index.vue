<!--
@name: 农户退货
@description:
@author: manchunyu
@time: 2022-12-02
-->
<template>
  <div class="app-container">
    <!-- 筛选条件及搜索栏 -->
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form
          v-show="showSearch"
          ref="queryRef"
          :model="_this.queryParams"
          label-width="80px"
          class="form-line"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年份" prop="year">
                <el-select
                  v-model="_this.queryParams.year"
                  placeholder="请选择年份"
                  @change="handleQuery"
                >
                  <el-option
                    v-for="dict in _this.yearOption"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所在单位" prop="orgCode">
                <org-select
                  ref="queryOrgRef"
                  :defaultOrgCode="true"
                  @handleOrgCode="handleOrgCodeQuery"
                />
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="农户姓名" prop="farmerName">
                <el-input
                  v-model="_this.queryParams.farmerName"
                  placeholder="请输入农户姓名"
                  maxlength="100"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="身份证号" prop="idIccid">
                <el-input
                  v-model="_this.queryParams.idIccid"
                  placeholder="请输入农户身份证号"
                  maxlength="100"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业名称" prop="aiCompanyId">
                <el-select
                  v-model="_this.queryParams.aiCompanyId"
                  placeholder="请选择企业名称"
                  @change="chgCompany"
                >
                  <el-option
                    v-for="dict in _this.companyAbbrOption"
                    :key="dict.aiCompanyId"
                    :label="dict.companyAbbr"
                    :value="dict.aiCompanyId"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="作业季节" prop="season">
                <el-select
                  v-model="_this.queryParams.season"
                  placeholder="请选择作业季节"
                  @change="handleQuery"
                >
                  <el-option
                    v-for="(val, key) in _this.fertilizes"
                    :key="key"
                    :label="val"
                    :value="key"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="商品分类" prop="aiTypeId">
                <ProduceTypeDynamic
                  :companyId="_this.queryParams.aiCompanyId"
                  @handleTypeId="handleTypeId"
                  ref="produceTypeRef"
                ></ProduceTypeDynamic>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="商品名称" prop="aiTypeSubName">
                <el-input
                  v-model="_this.queryParams.aiTypeSubName"
                  placeholder="请输入商品名称"
                  maxlength="100"
                  clearable
                  @keyup.enter="handleQuery"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="审批状态" prop="auditStatus">
                <el-select
                    v-model="_this.queryParams.auditStatus"
                    clearable
                    placeholder="全部"
                >
                  <el-option
                      v-for="(val, key) in _this.auditStatusOption"
                      :key="key"
                      :label="val"
                      :value="key"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="18"  align="right">
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              <el-button icon="Search" type="primary" @click="handleQuery">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" icon="Delete" @click="handleAdd"
            v-hasPermi="['farmRefund:insert']"
          >退货
        </el-button>
        <el-button icon="Download" @click="handleExport"
          >导出
        </el-button>
        <el-button
          icon="Check"
          @click="handleApprove(false)"
          :disabled="_this.selection.length != 1"
          >审核
        </el-button>
<!--        <el-button
          type="success"
          plain
          icon="Check"
          @click="handleAudit(false)"
          :disabled="_this.selection.length != 1"
          >启动审核
        </el-button>-->
      </el-col>
      <right-toolbar
        v-model:showSearch="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <!-- 查询表格 -->
    <div v-text="_this.title" class="title"></div>
    <el-table
      border
      :height="tableHeight"
			:key="_this.tablekey"

      :data="_this.consigneeInfoList"
      row-key="farmRefundId"
      lazy
      :load="load"
      :tree-props="{ hasChildren: 'hasChildren' }"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <ColumnItem :columns="_this.columns" />
      <el-table-column
        label="操作"
        align="center"
        width="150"
        fixed="right"
        class-name="small-padding fixed-width"
      >
        <template #default="scope">
          <el-button
            type="primary"
            link
            icon="Edit"
            v-if="scope.row.hasChildren&&scope.row.auditStatus=='待发起'"
            @click="handleEdit(scope.row)"
            v-hasPermi="['farmRefund:insert']"
            >编辑
          </el-button>
          <el-button
              type="primary"
              link
              icon="Edit"
              v-if="scope.row.hasChildren&&(scope.row.auditStatus=='待发起'||scope.row.auditStatus=='审批拒绝'
                  ||scope.row.auditStatus=='已撤销')"
              @click="handleDelete(scope.row)"
              v-hasPermi="['farmRefund:logicDeleteByIds']"
          >删除
          </el-button>
          <el-button
              type="primary"
              link
              icon="CircleCheck"
              v-if="scope.row.hasChildren&&scope.row.auditStatus=='待发起'"
              @click="startAudit(scope.row)"
              v-hasPermi="['farmRefund:insert']"
          >发起流程
          </el-button>
          <el-link
            type="success"
            icon="User"
            @click="viewFlow(scope.row)"
            v-if="scope.row.hasChildren && scope.row.instId"
            >查看流程</el-link
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页查询底部选页栏 -->
    <pagination
      v-show="_this.total > 0"
      v-model:limit="_this.queryParams.rows"
      v-model:page="_this.queryParams.page"
      :total="_this.total"
      @pagination="getList"
    />
    <!-- 查看流程对话框 -->
    <el-dialog v-model="_this.processVisible" title="查看流程" width="800px" :close-on-click-modal="false">
      <FlowDetail
        v-model="_this.flow.instId"
        v-if="_this.processVisible"
      ></FlowDetail>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="_this.processVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 添加或修改公示配置管理表对话框 -->
    <add
      :dialogTitle="_this.dialogTitle"
      @success="getList"
      :isAdd="_this.isAdd"
      :currentId="_this.currentId"
      v-model:dialogOpen="_this.dialogOpen"
      v-if="_this.dialogOpen"
    />
    <!--审核-->
    <el-dialog title="审核" v-model="_this.openApproveView" append-to-body :close-on-click-modal="false">
      <el-form
        ref="approveForm"
        :model="_this.form"
        :rules="_this.rulesApprove"
        label-width="100px"
      >
        <el-form-item label="是否通过" prop="agree">
          <el-radio v-model="_this.form.agree" :label="true">同意</el-radio>
          <el-radio v-model="_this.form.agree" :label="false">不同意</el-radio>
        </el-form-item>
        <el-form-item label="审核意见" prop="option">
          <el-input v-model="_this.form.option" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitApproveForm">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script name="/invest/farmRefund/queryByPage" setup>
import { getToken } from "@/utils/auth";
import { config } from "@/api/bdh-agric-invest/invest/aiProdAgentInfo";
import {
  queryPage,
  detailList,
  approve,
  startFlow,
  logicDeleteById
} from "@/api/bdh-agric-invest/invest/farmRefund";
import ProduceTypeDynamic from '@/views/bdh-agric-invest/components/ProduceTypeDynamic'
import { allCompanyInfo } from "@/api/bdh-agric-invest/invest/companyInfo";
import { getDicts } from "@/api/bdh-agric-invest/system/dict/data";
import { postForExcel } from "@/api/bdh-agric-invest/invest/excel";
import { ref } from "vue";
import ColumnItem from "@/views/bdh-agric-invest/components/columnItem";
import add from "./add";
import FlowDetail from "@/views/bdh-agric-invest/components/FlowDetail/index.vue";
import orgSelect from "@/views/bdh-agric-invest/components/OrgSelect/index.vue";
const { proxy } = getCurrentInstance();
//所在单位
const queryOrgRef = ref();
//商品分类
const produceTypeRef = ref();
//审核表单
const approveForm = ref();
//页面数据
const _this = reactive({
  processVisible: false,
  //表格数据
  consigneeInfoList: [],
  //加载状态
  loading: true,
  //年份
  yearOption: [],
  //季节数据
  fertilizes: [],
  //企业数据
  companyAbbrOption: [],
  //动态列头
  columns: [
    {
      label: "年份",
      prop: "year",
			width:"200px"
    },
    {
      label: "所在单位",
      prop: "orgName",
    },
    {
      label: "农户姓名",
      prop: "farmerName",
    },
    {
      label: "身份证",
      prop: "idIccid",
    },
    {
      label: "企业简称",
      prop: "companyAbbr",
    },
    {
      label: "作业季节",
      prop: "season",
      formatter:seasonFmt
    },
    {
      label: "投入品分类",
      prop: "aiTypeName",
    },
    {
      label: "商品名称",
      prop: "aiTypeSubName",
    },
    {
      label: "原订购数量",
      prop: "origSubscribeNum",
    },
    {
      label: "调整订购数量",
      prop: "chgSubscribeNum",
    },
    {
      label: "调整日期",
      prop: "changeDate",
    },
    {
      label: "审核状态",
      prop: "auditStatus",
    },
    {
      label: "当前审核人",
      prop: "auditPerson",
      formatter:fmtAuditName
    },
  ],
  total: 0,
  queryParams: {
    rows: 10,
    page: 1,
    year: "", //年份
    orgCode: "", //组织机构
    farmerName: "", //农户姓名
    idIccid: "", //身份证
    aiCompanyId: null, //企业
    season: null, //季节
    aiTypeId: null, //分类
    aiTypeSubName: "", //商品名称
    auditStatus: "",
  },
  //表格title
  title: "",
  dialogTitle: "农户订购信息",
  dialogOpen: false,
  dialogEditTitle: "农户订购信息",
  dialogEditOpen: false,
  flow: {},
  selection: [],
  isAdd: true,
  currentId: null,
  form: {
    agree: true,
    option: "",
  },
  openApproveView: false,
  rulesApprove: {
    agree: [{ required: true, message: "请选择是否同意", trigger: "change" }],
    option: [
      { required: true, message: "请输入审核意见", trigger: "change" },
      { max: 100, message: "最多100个字符", trigger: "blur" },
    ],
  },
	tablekey:0
});
const showSearch = ref(true);//搜索是否显示
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
onMounted(() => {
    searchHeight.value = searchDom.value?.clientHeight;
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
watch(showSearch, (value) => {
    tableHeight.value = showSearch.value
        ? window.innerHeight - searchHeight.value - 220
        : window.innerHeight - 220;
});
function seasonFmt(row,value){
  return _this.fertilizes[row.season]
}
function load(row, treeNode, resolve) {
  var obj = {
    parentFarmRefundId: row.farmRefundId,
  };
  detailList(obj)
    .then((response) => {
      resolve(response.data);
    })
    .catch((err) => {});
}

/** 启动启动审核 */
function handleAudit() {
  var id = _this.selection[0].farmRefundId;
  proxy.$modal
    .confirm("是否确认启动审核？")
    .then(function () {
      return startFlow({ farmRefundId: id });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("启动成功");
    })
    .catch(() => {});
}

function handleDelete(row){
  var id = row.farmRefundId;
  proxy.$modal
      .confirm("是否确认删除？")
      .then(function () {
        return logicDeleteById(id);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
      .catch(() => {});
}

function startAudit(row) {
  var id = row.farmRefundId;
  proxy.$modal
      .confirm("是否确认启动审核？")
      .then(function () {
        return startFlow({ farmRefundId: id });
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("启动成功");
      })
      .catch(() => {});
}

//审核按钮
function handleApprove(row) {
  // if (_this.selection[0].hasChildren) {
  // }
  _this.openApproveView = true;
  _this.form.option = "";
}

function fmtAuditName(row,column){
  if(row.flowMsg&&JSON.parse(row.flowMsg).auditNames){
    return JSON.parse(row.flowMsg).auditNames
  }
  return "无";
}

function fmtAuditNode(row,column){
  if(row.flowMsg&&JSON.parse(row.flowMsg).node){
    return JSON.parse(row.flowMsg).node
  }
  return "无";
}

//审核提交
function submitApproveForm() {
  approveForm.value.validate((valid) => {
    if (valid) {
      var obj = {};
      obj.instId = _this.selection[0].instId;
      obj.opinion = _this.form.option;
      obj.agree = _this.form.agree;
      approve(obj).then((response) => {
        if (response.success) {
          _this.openApproveView = false;
          proxy.$modal.msgSuccess("成功!");
          getList();
        }
      });
    }
  });
}

// 多选框选中数据
function handleSelectionChange(selection) {
  _this.selection = selection;
}
//查看流程按钮
function viewFlow(row) {
  _this.flow = row;
  _this.processVisible = true;
}
//编辑按钮
function handleEdit(row) {
  if (row) {
    _this.currentId = row.farmRefundId;
    _this.dialogOpen = true;
    _this.isAdd = false;
  }
}
//新增
function handleAdd() {
  _this.isAdd = true;
  _this.dialogOpen = true;
}
function chgCompany() {
  _this.queryParams.aiTypeId = null;
  handleQuery();
}
function handleTypeId(type) {
  _this.queryParams.aiTypeId = type.aiTypeId;
}

/** 获取字典 */
const getDict = () => {
  //作业季节 确认状态 审批状态
  config().then((response) => {
    _this.fertilizes = response.data.fertilizeSeason; //作业季节
    _this.auditStatusOption = response.data.auditStatus; //审批状态
    console.info('_this.auditStatusOption',_this.auditStatusOption);
  });
  allCompanyInfo({}).then((response) => {
    _this.companyAbbrOption = response.data;
    //查询年份
    getDicts("year_cd").then((response) => {
      _this.yearOption = response.data.sort((a, b) => {
        return Number(b.code) - Number(a.code);
      });
      initSearch();
      getList();
    });
  });
};
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryOrgRef.value.clear();
  produceTypeRef.value.clear();
  initSearch();
  handleQuery();
}
//默认值赋值
function initSearch() {
  if (_this.fertilizes) {
    for (let key in _this.fertilizes) {
      _this.queryParams.season = key;
      break;
    }
  } else {
    _this.queryParams.season = null;
  }
  if (_this.companyAbbrOption.length > 0) {
    _this.queryParams.aiCompanyId = _this.companyAbbrOption[0].aiCompanyId;
  } else {
    _this.queryParams.aiCompanyId = null;
  }
  if (_this.yearOption.length > 0) {
    _this.queryParams.year = _this.yearOption[0].code;
  } else {
    _this.queryParams.year = null;
  }
  _this.queryParams.orgCode = null;
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
//所在单位更改
const handleOrgCodeQuery = (orgInfo) => {
  if (!orgInfo.orgCode) queryOrgRef.value.clear();
  _this.queryParams.orgCode = orgInfo.orgCode;
};

/** 查询收货人列表 */
function getList() {
  _this.loading = true;
  var obj = Object.assign({}, _this.queryParams);
  queryPage(obj)
    .then((response) => {
      var data = response.data;
      data.records.forEach((item) => {
        for (let key in _this.auditStatusOption) {
          if (key == item.auditStatus) {
            item.auditStatus = _this.auditStatusOption[key];
          }
        }
        item.hasChildren = true;
        item.updateTime = proxy.parseTime(
          item.updateTime,
          "{y}-{m}-{d} {h}:{i}:{s}"
        );
      });
      _this.consigneeInfoList = data.records;
      _this.loading = false;
      _this.total = data.total;
			_this.tablekey += 1
    })
    .catch((err) => {
      _this.loading = false;
      _this.consigneeInfoList = [];
    });
}

/** 导出按钮操作 */
function handleExport() {
  postForExcel(
    "/bdh-agric-invest-api/invest/farmRefund/export",
    _this.queryParams,
    "农户退换货"
  );
}
getDict();
</script>
<style scoped>

.el-link {
  margin-right: 8px;
}

.el-link .el-icon--right.el-icon {
  vertical-align: text-bottom;
}
.mb8 {
  min-height: 30px;
}
.title {
  font-size: 20px;
  text-align: center;
  margin-bottom: 4px;
  font-weight: 600;
}
</style>
