<template>
    <div class="history-map-container">
        <div id="history-map" />
        <div class="fiex_right" v-if=true>
            <div class="title" style="justify-content: space-between;">
                <div>
                    <el-icon>
                        <CaretRight />
                    </el-icon>&nbsp;&nbsp;作业标准
                </div>
                <div>
                    <el-button @click="cancel">取消</el-button>
                </div>
            </div>
            <div class="title" style="cursor: pointer;" @click="toggle">
                作业标准名称:{{ standard.standardName }}
                &nbsp;&nbsp;
                <el-icon>
                    <Bottom v-show="toggleshow" />
                    <Top v-show="!toggleshow" />
                </el-icon>
            </div>
            <el-collapse-transition>
                <div v-show="toggleshow" style="border-bottom: 1px solid #dfdfdf;margin-left: 20px;">
                    <el-descriptions title="" :column="2" style="width: 100%;">
                        <el-descriptions-item label="作业速度">{{ standard.workSpeed }}千米/时</el-descriptions-item>
                        <el-descriptions-item label="允许偏差">{{ standard.speedAffectMin }}千米/时-{{ standard.speedAffectMax
                        }}千米/时</el-descriptions-item>
                        <el-descriptions-item label="流量">{{ standard.workFlow }}升/分</el-descriptions-item>
                        <el-descriptions-item label="允许偏差">{{ standard.flowAffectMin }}升/分-{{ standard.flowAffectMax
                        }}升/分</el-descriptions-item>
                        <el-descriptions-item label="相对冠层高度">{{ standard.workAltitude }}米</el-descriptions-item>
                        <el-descriptions-item label="允许偏差">{{ standard.altitudeAffectMin }}米-{{ standard.altitudeAffectMax
                        }}米</el-descriptions-item>
                    </el-descriptions>
                </div>
            </el-collapse-transition>
            <div class="title">
                <el-icon>
                    <CaretRight />
                </el-icon>&nbsp;&nbsp;作业航空器
            </div>
            <el-table :data="tableDataAIR" id="tableDataAIR" style="width: 100%;" @select="selectChange"
                @selection-change="handleSelectionChange" ref="multipleTable">
                <el-table-column type="selection" label="选择查看" :selectable="selectable">
                </el-table-column>
                <el-table-column prop="typeName" label="型号">
                </el-table-column>
                <el-table-column prop="regCode" label="注册号">
                </el-table-column>
                <el-table-column prop="aerobatType" label="航空器分类">
                </el-table-column>
                <el-table-column prop="droneType" label="有人机分类">
                </el-table-column>
                <el-table-column prop="code" label="设备编码(作业监测)">
                </el-table-column>
                <el-table-column prop="actWorkArea" label="实际作业面积(智能计算)" width="140">
                    <!-- <template #default="scope">
                        <el-input-number style="width: 100px;" v-model="scope.row.actWorkArea" :precision="2"
                            :controls="false" :min="0" :max="999999" label=""></el-input-number>
                    </template> -->
                </el-table-column>
            </el-table>
            <el-row style="margin-bottom: 10px;margin-top: -2px;">
                <el-descriptions title="" direction="vertical" :column="3" border style="width: 100%;">
                    <el-descriptions-item label="任务计划面积">{{ standard.jobArea }}亩</el-descriptions-item>
                    <el-descriptions-item label="实际作业面积总和">{{ allmianjiplane }}亩</el-descriptions-item>
                    <el-descriptions-item label="作业覆盖率">{{ allmianjiplanerate
                    }}%</el-descriptions-item>
                </el-descriptions>
            </el-row>
            <el-table :data="tableDataUAV" id="tableDataUAV" style="width: 100%" @select="selectChange2"
                @selection-change="handleSelectionChange2" ref="multipleTableUAV">
                <el-table-column type="selection" label="选择查看" :selectable="selectable">
                </el-table-column>
                <el-table-column prop="typeName" label="型号">
                </el-table-column>
                <el-table-column prop="produceCode" label="出厂编号">
                </el-table-column>
                <el-table-column prop="aerobatType" label="航空器分类">
                </el-table-column>
                <el-table-column prop="droneType" label="无人机分类">
                </el-table-column>
                <el-table-column prop="code" label="设备编码(作业监测)">
                </el-table-column>
                <el-table-column prop="actWorkArea" label="实际作业面积(智能计算)" width="140">
                    <!-- <template #default="scope">
                        <el-input-number style="width: 100px;" v-model="scope.row.actWorkArea" :precision="2"
                            :controls="false" :min="0" :max="999999" label=""></el-input-number>
                    </template> -->
                </el-table-column>
            </el-table>
            <el-row style="margin-bottom: 10px;margin-top: -2px;">
                <el-descriptions title="" direction="vertical" :column="3" border style="width: 100%;">
                    <el-descriptions-item label="任务计划面积">{{ standard.jobArea }}亩</el-descriptions-item>
                    <el-descriptions-item label="实际作业面积总和">{{ allmianjidrone }}亩</el-descriptions-item>
                    <el-descriptions-item label="作业覆盖率">{{ allmianjidronerate
                    }}%</el-descriptions-item>
                </el-descriptions>
            </el-row>
            <!-- <div class="title">
                <el-icon>
                    <CaretRight />
                </el-icon>&nbsp;&nbsp;作业合格率
            </div>
            <div class="title">
                总体作业合格率：{{ standard.qualRate }}%
            </div>
            <el-table :data="tableData3" style="width: 100%">
                <el-table-column prop="text" label="监测作业参数">
                </el-table-column>
                <el-table-column prop="rate" label="合格率">
                </el-table-column>
                <el-table-column prop="workSpeed" label="智能分析">
                </el-table-column>
            </el-table> -->
            <!-- <el-row style="margin-top: 10px;">
                <el-descriptions title="" direction="horizontal" :column="3" border style="width: 100%;">
                    <el-descriptions-item label="总体评价">
                        <div v-html="standardMessage"></div>
                    </el-descriptions-item>
                </el-descriptions>
            </el-row> -->
            <!-- <div class="lr">
                <div class="title">
                    <el-icon>
                        <CaretRight />
                    </el-icon>&nbsp;&nbsp;作业不合格详情
                </div>
                <span>总计{{ errorpoints.length }}个作业点</span>
            </div>
            <el-table :data="errorpoints" style="width: 100%" :height="500">
                <el-table-column type="index" width="50" label="序号">
                </el-table-column>
                <el-table-column prop="text" label="定位">
                    <template #default="scope">
                        <span style="color:#409eff;cursor: pointer;" @click="goPosition(scope.row)">地图定位</span>
                    </template>
                </el-table-column>
                <el-table-column prop="workDate" label="作业时间" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.workDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="workFlag" label="状态" width="60">
                </el-table-column>
                <el-table-column prop="altitudeUnqual" label="相对冠层高度" width="160">
                    <template #default="scope">
                        <span>{{ scope.row.altitudeUnqual.split("#")[0] }}</span>米&nbsp;
                        (<span style="color:red">{{ scope.row.altitudeUnqual.split("#")[1] }}</span>)
                    </template>
                </el-table-column>
                <el-table-column prop="speedUnqual" label="速度" width="160">
                    <template #default="scope">
                        <span>{{ scope.row.speedUnqual.split("#")[0] }}千米/时</span>&nbsp;
                        (<span style="color:red">{{ scope.row.speedUnqual.split("#")[1] }}</span>)
                    </template>
                </el-table-column>
                <el-table-column prop="flowUnqual" label="流量" width="160">
                    <template #default="scope">
                        <span>{{ scope.row.flowUnqual.split("#")[0] }}</span>升/分&nbsp;
                        (<span style="color:red">{{ scope.row.flowUnqual.split("#")[1] }}</span>)
                    </template>
                </el-table-column>
            </el-table> -->
        </div>

        <div class="extend-control">
            <el-tooltip class="item" effect="dark" content="视角自适应" placement="left">
                <div @click="zoomToFeatures()">
                    <svg-icon icon-class="radio" />
                </div>
            </el-tooltip>
            <el-tooltip class="item" effect="dark" content="测距" placement="left">
                <div @click="spaceMeasureFun('LineString')">
                    <svg-icon icon-class="测距" />
                </div>
            </el-tooltip>
        </div>
        <div class="drawinfo_box">
            <!-- <div class="drawinfo" @click="tgsucess">
                <div class="line success" :class="[!showSucess ? 'activeClass' : '']"></div>
                <div class="text" :class="[!showSucess ? 'activeColor' : '']">作业轨迹</div>
            </div> -->
            <div class="drawinfo" >
                <div class="line success" ></div>
                <div class="text" >作业轨迹</div>
            </div>
            <!-- <div class="drawinfo" @click="tgerror">
                <div class="line" :class="[!showError ? 'activeClass' : '']"></div>
                <div class="text" :class="[!showError ? 'activeColor' : '']">不合格轨迹</div>
            </div> -->
        </div>
        <div style="display:none">
            <div :id="item.p.code" class="tooltip" v-for="item in trajectory" :key="item.p.code">
                <div class="close" @click="closeToolTip(item.p.code)">
                    <el-icon>
                        <Close />
                    </el-icon>
                </div>
                <div>
                    {{ item.p.droneType }}{{ item.p.aerobatType }}
                </div>
                <div>
                    型号：{{ item.p.typeName }}
                </div>
                <div v-if="item.p.aerobatType == '有人机'">
                    注册号：{{ item.p.regCode }}
                </div>
                <div v-if="item.p.aerobatType == '无人机'">
                    出厂编号：{{ item.p.produceCode }}
                </div>
                <div class="angle" />
            </div>
        </div>
    </div>
</template>

<script>
import 'ol/ol.css';
import Map from "ol/Map";
import { TianDiTuKey } from "@/views/bdh-aviation-mission/script/Config/GlobalVariable";
import View from "ol/View";
import Feature from 'ol/Feature';
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, download, handleTree } from "@/utils/cop";
// import Polyline from 'ol/format/Polyline';
import VectorSource from 'ol/source/Vector';
import XYZ from 'ol/source/XYZ';
import {
    Fill,
    // Circle as CircleStyle,
    // Fill,
    Icon,
    Stroke,
    Style,
} from 'ol/style';
import { Tile as TileLayer, Vector as VectorLayer } from 'ol/layer';
import { getVectorContext } from 'ol/render';
import { containsCoordinate, extend } from "ol/extent";
import GeoJSON from "ol/format/GeoJSON";
import TileWMS from "ol/source/TileWMS";
import WKT from "ol/format/WKT";
import { Text, Circle, RegularShape } from "ol/style";
import Overlay from 'ol/Overlay'
import { getGeoWfs, getUserOrgInfoGps } from "@/api/bdh-aviation-mission/gis";
import {
    Point,
    LineString,
    LinearRing,
    Polygon,
    MultiPoint,
    MultiLineString,
    MultiPolygon,
} from "ol/geom.js";
import { composeCssTransform } from 'ol/transform';
import { toLonLat, transformExtent, transform } from 'ol/proj';
import { defaults, Interaction, Select } from 'ol/interaction';
import corsImg from '@/views/bdh-aviation-mission/assets/images/gis/DX.png';
import corsImg2 from '@/views/bdh-aviation-mission/assets/images/gis/YD.png';
import corsImg3 from '@/views/bdh-aviation-mission/assets/images/gis/LT.png';
import { fixErrorDis, getMiddle } from "@/views/bdh-aviation-mission/script/Utils/Trajectory/TrajectoryCommon";
import rangingMixin from '@/views/bdh-aviation-mission/script/mixins/map/rangingMixin'
// const jsts = require("jsts/dist/jsts");
import { unByKey } from "ol/Observable";
const fix = fixErrorDis;
/**
 * 组件说明: 历史轨迹地图
 *
 */
export default {
    name: "TrajectoryHistoryMapComponent",
    mixins: [rangingMixin],
    props: {
        timedata: {
            type: Array,
            required: true
        },
        time: {
            type: Number,
            required: true
        },
        standard: {
            type: Object,
            required: true
        },
        plotListData: {
            type: Array,
            required: true
        },
        errorpoints: {
            type: Array,
            required: true
        },
        list: {
            type: Array,
            required: true
        }
    },
    data() {
        return {
            count: 1,
            drawer: true,
            page: 0,
            rows: 10,
            checked: true,
            plotList: [],
            data: [],
            showData: true,
            showToggle: true,
            map: null,
            percent: 0,
            trackData: [],
            trajectory: [],
            first: true,
            // 保存当前轨迹走到的index
            index: 0,

            vectorLayer: null,

            bolInit: false, //记录地图初始化状态
            vLayerBase: {},//基础切片服务图层
            vLayerTdtImg: {},//天地图在线影像服务图层
            vLayerTdt: {},//天地图在线矢量服务图层
            vLayerTdtLable: {},//天地圖標註服務圖層

            center: [14117582.2457, 6127780.0027],
            //单位坐标集合
            centerGeo: "",
            current: '',
            zoom: 7,
            nowZoom: 7,
            minZoom: 3,
            maxZoom: 19,
            vLayerArea: {},

            viewParentOrg: { id: '86', name: '北大荒农垦集团总公司' },//记录当前用户最高级别org_code和org_name,用于全图后的数据交互
            vLayerCompany: {},//分公司矢量图层

            zoomCompanyMin: 5,
            zoomCompanyMax: 8,

            vLayerFarm: {},//农场矢量图层
            zoomFarmMin: 9,
            zoomFarmMax: 11,

            vLayerLineFarm: null,//农场边界线
            zoomFarmLineMin: 12,
            zoomFarmLineMax: 19,

            vLayerManage: {},//管理区矢量图层
            zoomManageMin: 12,
            zoomManageMax: 13,

            vLayerLineManage: {},//管理区矢量图层
            zoomManageLineMin: 14,
            zoomManageLineMax: 19,


            vLayerStation: {},//作业站图层
            zoomStationMin: 14,
            zoomStationMax: 16,

            // zoomLandMin:14,
            zoomLandMin: 12,
            zoomLandMax: 19,

            zoomLandLableMin: 14,
            zoomLandLableMax: 19,
            bolZoomLandLable: false,

            viewColls: [],//记录屏幕范围
            viewCollsIndex: -1,//记录当前视图所在索引位置
            viewCollsBol: true,//记录当前操作是否需要并入记录范围

            listHasFarms: [],

            //////////////////////////////地块边界
            bolCompany: true,//分公司
            bolFarm: true,//农场
            bolManage: true,//管理区
            bolStation: true,//作业站
            bolPolygon: true,//整体地块 显隐开关的控制判断
            bolArea: true,//大边界
            ////////////////////////////种植作物
            bolCorn: true,//玉米
            bolSoybean: true,//大豆
            bolRice: true,//水稻
            bolOtherCrop: true,//其他
            ////////////////////////////土地类型
            bolTypeWater: false,//水田
            bolTypeDry: false,//旱田
            bolTypeOther: false,//其他
            ////////////////////////////缴费情况
            bolPaid: false,//已缴费
            bolUnpaid: false,//未缴费
            bolPartPaid: false,//部分缴费
            ////////////////////////////认证类型
            bolOrganic: false,//有机
            bolGreen: false,//绿色
            bolGeography: false,//农产品地理标志
            bolOrgOther: false,//其他
            ////////////////////////////耕地级别
            bolLevel1: false,//一等地
            bolLevel2: false,//二等地
            bolLevel3: false,//三等地
            bolLevelOther: false,//其他
            ////////////////////////////自然资源
            bolCanal: false,//水渠
            bolForest: false,//森林
            bolGrass: false,//草原
            bolRivers: false,//河流
            //////////////////////////基站
            bolMobile: false, //电信基站
            bolMobileYD: false, //移动基站
            bolMobileLT: false, //联通基站

            vLayerLandCrop: {},//种植作物图层（玉米、大豆、水稻、其他）
            vLayerLandType: {},//土地类型（水田、旱田、其他）
            vLayerLandPay: {},//缴费情况
            vLayerAuthType: {},//认证类型（有机、绿色、农产品地理标志）
            vLayerLandLevel: {},//耕地级别（一等地、二等地、三等地、其他）
            vLayerResources: {},//自然资源（水渠、森林、草原、河流）

            zoomResourcesLinesMin: 11,
            zoomResourcesLinesMax: 19,
            vLayerResourcesLines: {},
            vLayerResourcesPoints: {},

            vLayerResourcesLinesIds: [],


            lastLineFeat: null,

            vLayerHighLight: {},
            vLayerCompanyHighLight: {},

            vLayerMobileStation: {},
            vLayerMobileStation_yd: {},
            vLayerMobileStation_lt: {},

            offsets: [],
            measureDrawAction: null,
            drawGeolistener: null,
            measureTooltip: null,
            measureTooltips: [],
            measureTpElement: null,
            measureHelpTooltip: null,
            measureHelpTpElement: null,
            measureSketch: null,
            chooseYear: new Date().getFullYear(), // 默认年份
            showIndex: 0,
            vLayerPoint: null,
            showSucess: true,
            showError: true,
            toolTip: false,
            tableDataAIR: [],
            tableDataUAV: [],
            tableData3: [
            ],
            feature: {
                code: '',
                companyName: '',
                name: 1,
                id: null
            },
            toggleshow: false,
            multipleSelection: [],
            standardMessage: '',
            needRender: true,
            selection:[],
            selection2:[]
        }
    },
    computed: {
        allmianjiplane() {
            var num = 0
            this.tableDataAIR.map(item => {
                num += item.actWorkArea
            })
            return num.toFixed(2)
        },
        allmianjiplanerate() {
            var num = 0
            if (this.allmianjiplane == 0) {
                return '0.00'
            } else {
                num = (this.allmianjiplane / this.standard.jobArea)
                if (isNaN(num)) {
                    return '0.00'
                } else {
                    return (num * 100).toFixed(2)
                }
            }
        },
        allmianjidrone() {
            var num = 0
            this.tableDataUAV.map(item => {
                num += item.actWorkArea
            })
            return num
        },
        allmianjidronerate() {
            var num = 0
            if (this.allmianjidrone == 0) {
                return '0.00'
            } else {
                num = (this.allmianjidrone / this.standard.jobArea)
                if (isNaN(num)) {
                    return '0.00'
                } else {
                    return (num * 100).toFixed(2)
                }
            }
        },
    },
    watch: {
        plotListData() {
            this.plotList = this.plotListData;
            this.plotList.forEach((item, index) => {
                if (item.aerobatType == "有人机") {
                    if (item.actWorkArea == null) {
                        item.actWorkArea = 0
                    }

                    this.tableDataAIR.push(item);
                }
                if (item.aerobatType == "无人机") {
                    if (item.actWorkArea == null) {
                        item.actWorkArea = 0
                    }
                    this.tableDataUAV.push(item);
                }
            })
            this.tableDataAIR.forEach(item => {
                    this.$refs.multipleTable.toggleRowSelection(item, true);
                    this.selection.push(item)
            })
            this.tableDataUAV.forEach(item => {
                    this.$refs.multipleTableUAV.toggleRowSelection(item, true);
                    this.selection2.push(item)

            })

        },
        standard() {
            this.tableData3 = [
                {
                    text: "相对冠层高度(米)",
                    rate: this.standard.altitudeQualRate + "%",
                    workSpeed: `${this.standard.altitudeOvertop == 0 ? '' : this.standard.altitudeOvertop + '个作业点相对冠层高度过高,'}${this.standard.altitudeLow == 0 ? '' : this.standard.altitudeLow + '个作业点相对冠层高度过低'}`
                },
                {
                    text: "速度(千米/时)",
                    rate: this.standard.speedQualRate + "%",
                    workSpeed: `${this.standard.speedOvertop == 0 ? '' : this.standard.speedOvertop + '个作业点速度过高,'}${this.standard.speedLow == 0 ? '' : this.standard.speedLow + '个作业点速度过低'}`

                },
                {
                    text: "流量(升/分)",
                    rate: this.standard.flowQualRate + "%",
                    workSpeed: `${this.standard.flowOvertop == 0 ? '' : this.standard.flowOvertop + '个作业点流量过高,'}${this.standard.flowLow == 0 ? '' : this.standard.flowLow + '个作业点流量过低'}`
                }
            ]
            var str = ''
            var strformat = () => {
                str += `本次作业共计${this.standard.workPoint}个作业点，作业分析如下：<br>`
                if (this.standard.altitudeOvertop == 0 && this.standard.altitudeLow == 0) {
                    str += `1、相对冠层高度全部合格`
                } else if (this.standard.altitudeOvertop != 0 && this.standard.altitudeLow != 0) {
                    str += `1、相对冠层高度不合格的作业点共计个${this.standard.altitudeOvertop + this.standard.altitudeLow}，包含：${this.standard.altitudeOvertop}个过高，${this.standard.altitudeLow}个过低，`
                } else if (this.standard.altitudeOvertop == 0) {
                    str += `1、相对冠层高度不合格的作业点共计个${this.standard.altitudeLow}，均为：过低，`
                } else {
                    str += `1、相对冠层高度不合格的作业点共计个${this.standard.altitudeOvertop}，均为：过高，`
                }
                str += `合格率${this.standard.altitudeQualRate}%；<br>`

                if (this.standard.speedOvertop == 0 && this.standard.speedLow == 0) {
                    str += `2、速度全部合格`
                } else if (this.standard.speedOvertop != 0 && this.standard.speedLow != 0) {
                    str += `2、速度不合格的作业点共计${this.standard.speedOvertop + this.standard.speedLow}，包含：${this.standard.speedOvertop}个过高，${this.standard.speedLow}个过低，`
                } else if (this.standard.speedOvertop == 0) {
                    str += `2、速度不合格的作业点共计${this.standard.speedLow}，均为：过低，`
                } else {
                    str += `2、速度不合格的作业点共计${this.standard.speedOvertop}，均为：过高，`
                }
                str += `合格率${this.standard.speedQualRate}%；<br>`


                if (this.standard.flowOvertop == 0 && this.standard.flowLow == 0) {
                    str += `3、流量全部合格`
                } else if (this.standard.flowOvertop != 0 && this.standard.flowLow != 0) {
                    str += `3、流量不合格的作业点共计${this.standard.flowOvertop + this.standard.flowLow}，包含：${this.standard.flowOvertop}个过高，${this.standard.flowLow}个过低，`
                } else if (this.standard.flowOvertop == 0) {
                    str += `3、流量不合格的作业点共计${this.standard.flowLow}，均为：过低，`
                } else {
                    str += `3、流量不合格的作业点共计${this.standard.flowOvertop}，均为：过高，`
                }
                str += `合格率${this.standard.flowQualRate}%；<br>`

            }
            strformat()
            this.standardMessage = str

        },
        list(newValue, oldValue) {
            newValue.forEach(item => {
                var o = this.trajectory.find(it => {
                    return item.code == it.info.code
                })
                if (!o) {//不在旧数组里面 执行初始化 否则执行运动操作
                    var obj = {
                        // 是否初始化完成 ,未完成不能开启动画
                        init: false,
                        // 路线的点
                        route: null,
                        // 是否动画中
                        animating: false,
                        // 地图的元素
                        styles: null,
                        vectorLayer: null,
                        geoMarker: null,
                        routeFeature: null,
                        // 是否暂停
                        stop: false,
                        // 轨迹速度
                        speed: 120,
                        // 轨迹每次变化的量
                        speedChange: 40,
                        // 速度变化时记录变化前的速度
                        speedOld: 0,
                        // 动画开始时候的时间
                        startTime: null,
                        // 记录暂停时候的时间差
                        reTime: 0,
                        needRender: false,
                        info: item,
                        key: null,
                        p: item
                    }
                    this.trajectory.push(obj)
                    this.initFeature(obj, item)
                } else {
                    for (let index = 0; index <  this.trajectory.length; index++) {
                        const it =  this.trajectory[index];
                        if (item.code == it.info.code && item.geom != '') {
                            this.listChange(item, it)
                            break
                        }
                    }
                }
            })
        },
    },
    mounted() {
        setTimeout(() => {
            this.initMap();
        });
    },
    beforeDestroy() {
        this.trajectory.forEach(item => {
            if (item.vectorLayer) {
                unByKey(item.key);
            }
        })
    },
    created() {
    },
    methods: {
        cancel(){
            this.$emit('cancel')
        },
        initFeature(obj, item) {
            var geoms = item.geom.split(',').filter(item => item != '');
            var arr = []
            geoms.forEach(geom => {
                var g = geom.split(' ')
                var obj = {
                    lon: g[0],
                    lat: g[1]
                }
                arr.push(obj)
            })
            // console.log("init");
            obj.needRender = false
            obj.init = false
            var features = []
            // 初始创建 车辆 ,但没有位置
            if (arr.length == 0) {
                var c = new Point([NaN, NaN]).transform("EPSG:4326", "EPSG:3857")
                obj.geoMarker = new Feature({
                    info: item,
                    type: 'geoMarker',
                    geometry: c,
                });
                features = [obj.geoMarker]
            }
            // 初始创建 车辆 ,只有一个点 ,暂不绘制轨迹点
            else if (arr.length == 1) {
                var c = new Point([arr[0].lon, arr[0].lat]).transform("EPSG:4326", "EPSG:3857")
                obj.geoMarker = new Feature({
                    info: item,
                    type: 'geoMarker',
                    geometry: c,
                });
                features = [obj.geoMarker]
            } else { // 绘制轨迹点
                const routeCoords = arr.map(item => [item.lon, item.lat])
                obj.route = new LineString(routeCoords).transform("EPSG:4326", "EPSG:3857");
                obj.geoMarker = new Feature({
                    info: obj,
                    type: 'geoMarker',
                    geometry: new Point(obj.route.getCoordinateAt(1)),
                });
                obj.routeFeature = new Feature({
                    info: item,
                    type: 'route',
                    geometry: obj.route,
                });
                features = [obj.geoMarker, obj.routeFeature]
            }
            obj.styles = {
                'route': new Style({
                    stroke: new Stroke({
                        width: 2,
                        color: [4, 223, 4, 1],
                    }),
                }),
                'geoMarker': new Style({
                    image: new Icon({
                        src: item.aerobatType == "有人机" ? '/bdh-aviation-mission/static/icon/airmap.png' : '/bdh-aviation-mission/static/icon/uav4map.png',
                        scale: 0.5 ,
                        rotation: 0
                    }),
                }),
            };
            obj.animating = false;
            obj.vectorLayer = new VectorLayer({
                info: obj,
                source: new VectorSource({
                    features: features,
                    useSpatialIndex: false // 禁用空间索引
                }),
                style: (feature) => {
                    // if (obj.animating && feature.get('type') === 'geoMarker') {
                    //     return null;
                    // }
                    return obj.styles[feature.get('type')];
                },
            });
            this.map.addLayer(obj.vectorLayer);
            // if (arr.length == 0) {
            // this.map.getView().setCenter(c.flatCoordinates);
            // } else {
            // this.map.getView().setCenter(obj.route.getCoordinateAt(1))
            // }
            // this.map.getView().setZoom(17);
            obj.init = true
        },
        listChange(obj, item) {
            // console.log("change");
            var arr = obj.geom.split(',').filter(item => item != '');
            var point = []
            arr.forEach((item, index) => {
                var s = item.split(" ")
                var obj = {
                    lon: s[0],
                    lat: s[1]
                }
                point.push(obj);
            })
            if (!item.init) return
            // 正常最多只能多一个点 ,也可能不新增
            item.percent = 0
            item.needRender = true
            const routeCoords = point.map(item => [item.lon, item.lat])
            // LineString 画点连线
            item.route = new LineString(routeCoords).transform("EPSG:4326", "EPSG:3857");
            // 画路线
            item.routeFeature = new Feature({
                info: item,
                type: 'route',
                geometry: item.route,
            });
            item.vectorLayer.getSource().addFeature(item.routeFeature)
            ///计算角度
            let dx = routeCoords[1][0] - routeCoords[0][0];
            let dy = routeCoords[1][1] - routeCoords[0][1];
            let rotation = Math.atan(dx / dy);
            rotation = dy > 0 ? rotation : Math.PI + rotation;
            this.setIconRotationSec(rotation, item)
            // item.styles.geoMarker.getImage().setRotation((this.list[this.list.length - 1].azi - 90) * Math.PI/180)
            // item.styles.geoMarker.getText().setText("速度: ")
            if (item.key) {
                unByKey(item.key)
            }
            item.key = item.vectorLayer.on('postrender', (e) => {
                this.moveFeature(e, item)
            });
            item.startTime = new Date().getTime()
            item.animating = true
            this.map.render();
        },
        moveFeature(event, item) {
            const vectorContext = getVectorContext(event);
            const frameState = event.frameState;
            let lastCoordinate = item.route.getCoordinateAt(
                item.percent
            );
            item.percent = (frameState.time - item.startTime) / this.time;
            if (item.percent >= 1) {
                item.percent = 1
            }
            const coordinate = item.route.getCoordinateAt(item.percent)
            ///计算角度
            let dx = coordinate[0] - lastCoordinate[0];
            let dy = coordinate[1] - lastCoordinate[1];
            let rotation = Math.atan(dx / dy);
            rotation = dy > 0 ? rotation : Math.PI + rotation;
            this.setIconRotationSec(rotation, item)

            // const currentPoint = new Point(coordinate);
            // const feature = new Feature(currentPoint);
            // vectorContext.drawFeature(feature, item.styles.geoMarker);
            item.geoMarker.getGeometry().setCoordinates(coordinate);
            const extend = this.map.getView().calculateExtent(this.map.getSize())
            const isInSide = containsCoordinate(extend, coordinate)
            if (!isInSide) {
                var arr = this.trajectory.filter(it => {
                            return it.vectorLayer && it.vectorLayer.getVisible()
                        })
                if (arr.length == 1) {
                    // console.error("不在区域内")
                    this.map.getView().animate({ center: coordinate })
                }
            }
            this.map.render();
        },
        // 设置偏转角度
        setIconRotationSec(rotation, item) {
            item.styles.geoMarker.getImage().setRotation(rotation)

        },
        zoomToFeatures() {
            let minX = null; let minY = null; let maxX = null; let maxY = null

            const visibleFeatures = [];
            this.trajectory.forEach(item => {
                if (item.vectorLayer && item.init && item.vectorLayer.getVisible()) {
                    visibleFeatures.push(...item.vectorLayer.getSource().getFeatures());
                }
            });
            if (visibleFeatures.length == 0) {
                return
            }
            visibleFeatures.forEach(item => {
                let boundsArry = item.getGeometry().getExtent()
                if (minX === null) {
                    minX = boundsArry[0]
                    minY = boundsArry[1]
                    maxX = boundsArry[2]
                    maxY = boundsArry[3]
                } else {
                    minX = Math.min(boundsArry[0], minX)
                    minY = Math.min(boundsArry[1], minY)
                    maxX = Math.max(boundsArry[2], maxX)
                    maxY = Math.max(boundsArry[3], maxY)
                }
            })
            const bounds = [minX, minY, maxX, maxY]
            this.map.getView().fit(bounds, {
                'duration': 500,
                'padding': [50, 50, 50, 50]
            })
        },

        goPosition(row) {
            var position = new Point([row.longitude, row.latitude]).transform("EPSG:4326", "EPSG:3857")
            this.map.getView().setCenter(position.flatCoordinates);
            this.map.getView().setZoom(17);
        },
        selectable(row) {
            return true
            // if (row.geom != "") {
            //     return true;  //不禁用
            // } else {
            //     return false;  //禁用
            // }
        },
        handleSelectionChange(selection) {


        },
        selectChange(selection, row) {
            this.selection = selection
            var obj = selection.find(item => {
                return item == row
            })
            if (!obj) {
                this.closeToolTip(row.code)
            }
            this.trajectory.forEach((item, index) => {
                if (item.vectorLayer != null) {
                    if (item.vectorLayer.get("info").aerobatType == "有人机") {
                        if (item.vectorLayer.get("info").code == row.code) {
                            item.vectorLayer.setVisible(obj)
                        }
                    }
                }
            })
            if (this.selection.length == 0 && this.selection2.length == 0) {
                this.showSucess = false
            } else {
                this.showSucess = true
            }
            setTimeout(() => {
                this.zoomToFeatures()
            })
        },
        selectChange2(selection, row) {
            this.selection2 = selection
            var obj = selection.find(item => {
                return item == row
            })
            if (!obj) {
                this.closeToolTip(row.code)
            }
            this.trajectory.forEach((item, index) => {
                if (item.vectorLayer) {
                    if (item.vectorLayer.get("info").aerobatType == "无人机") {
                        if (item.vectorLayer.get("info").code == row.code) {
                            item.vectorLayer.setVisible(obj)
                        }
                    }
                }
            })
            if (this.selection.length == 0 && this.selection2.length == 0) {
                this.showSucess = false

            } else {
                this.showSucess = true
            }
            setTimeout(() => {
                this.zoomToFeatures()
            })
        },
        handleSelectionChange2(selection) {

        },
        toggle() {
            this.toggleshow = !this.toggleshow
        },
        tgsucess() {
            this.trajectory.forEach(item => {
                if (item.vectorLayer) {
                    item.vectorLayer.setVisible(!this.showSucess);
                    this.closeToolTip(item.p.code)
                }
            })
            this.tableDataAIR.forEach(item => {
                this.$refs.multipleTable.toggleRowSelection(item, !this.showSucess);
            })
            this.tableDataUAV.forEach(item => {
                this.$refs.multipleTableUAV.toggleRowSelection(item, !this.showSucess);
            })
            this.showSucess = !this.showSucess
            setTimeout(() => {
                this.zoomToFeatures()
            })


        },
        tgerror() {
            if (this.vLayerPoint) {
                this.vLayerPoint.setVisible(!this.showError);
                // this.tableDataUAV.forEach(item => {
                //     this.$refs.multipleTableUAV.toggleRowSelection(item, !this.showError);
                // })
                this.showError = !this.showError
            }


        },
        hide(index) {
            if (this.trajectory[index].vectorLayer !== null) {
                this.trajectory[index].vectorLayer.setVisible(!this.trajectory[index].vectorLayer.getVisible())
            }
            // this.startAnimation(this.trajectory[index])
            // this.resetAnimation(this.trajectory[index])
            // this.stopAnimation(false,this.trajectory[index])
        },
        initMap() {
            this.map = new Map({
                target: 'history-map',
                // layers: [
                //     new TileLayer({
                //         source: new TileWMS({
                //             url: 'http://***********:8088/geowebcache/service/wms',
                //             params: {
                //                 'FORMAT': 'image/png',
                //                 'VERSION': '1.1.1',
                //                 tiled: true,
                //                 'LAYERS': 'googleCatch',
                //                 'exceptions': 'application/vnd.ogc.se_inimage'
                //             }
                //         })
                //     }),
                //     // new TileLayer({
                //     //     source: new XYZ({
                //     //         url: 'https://t0.tianditu.gov.cn/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=' + TianDiTuKey,
                //     //     }),
                //     //     name: '天地图路网',
                //     //     visible: true
                //     // }),
                // ],
                view: new View({
                    center: [133.10384, 47.612166],//[120.655136, 30.573668],
                    projection: 'EPSG:3857',
                    zoom: 9,
                    minZoom: 2,
                    maxZoom: 18
                    , constrainResolution: true,
                }),
                interactions: new defaults({
                    doubleClickZoom: false
                })
            });
            this.map.on('click', e => {
                let feature = this.map.forEachFeatureAtPixel(e.pixel, function (feature, layer) {
                    return feature
                })
                if (feature) {
                    if (feature.get("type") == "geoMarker") {
                        this.feature = feature.get("info")
                        var position = feature.get("geometry").flatCoordinates
                        this.setToolTip(position, this.feature)
                    }
                }
            });
            //新加地图内容代码
            this.initMapGis();
        },
        // 轨迹部分
        drawGeo(item, index) {
            if (item && item.geom) {
                let points = item.geom.split(",")
                let data = [];
                points.forEach(point => {
                    let strings = point.split(" ");
                    let record = {};
                    record.lon = strings[0]
                    record.lat = strings[1]
                    data.push(record);
                });
                return this.draw(data, index, item)

            }
        },
        drawpoint() {
            var routeCoords = []
            this.errorpoints.forEach(point => {
                routeCoords.push([point.longitude, point.latitude])
            });
            var feature = new Feature({
                geometry: new MultiPoint(routeCoords).transform("EPSG:4326", "EPSG:3857")
            });
            const fill = new Fill({
                color: 'rgba(217, 0, 27, 1)',
            });
            feature.setStyle(new Style({
                image: new Circle({
                    fill: fill,
                    radius: 3,
                }),
                fill: fill,
            }));
            this.vLayerPoint = new VectorLayer({
                source: new VectorSource(),
            });
            this.vLayerPoint.getSource().addFeature(feature);
            this.map.addLayer(this.vLayerPoint);
        },
        createOverlay(options) {
            const overlay = new Overlay(options);
            return {
                overlay: overlay,
                addToMap: function (map) {
                    map.addOverlay(overlay);
                },
                removeFromMap: function (map) {
                    map.removeOverlay(overlay);
                }
            };
        },
        setToolTip(overLayPosition, item) {
            var overlay = this.createOverlay({
                id: item.code,
                position: overLayPosition,//设置Overlay的经纬度位置
                offset: [0, -20],//偏移量，单位像素 overlay 相对于放置位置（position）的偏移量，正值分别向右和向下偏移
                positioning: 'bottom-center',
                element: document.getElementById(item.code),// 把上面的创建的元素绑定在Overlay中
                stopEvent: false,// 地图事件传播是否停止 eg 当鼠标放在弹出层缩放地图，设置为true时会阻止地图的缩放
                autoPan: false, //超出地图边界时，地图自动移位，保证弹出层可见
                autoPanMargin: 190 //自动移位时，地图边缘与overlay的留白
            })
            overlay.addToMap(this.map);
        },
        //关闭弹出层
        closeToolTip(code) {
            const overlay = this.map.getOverlayById(code);
            if (overlay) {
                overlay.setPosition(undefined)
            }
        },
        buildCountFix(item) {
            if (item.data) {
                const fixLength = 10
                if (item.data.length >= fixLength) {
                    return fixLength / item.data.length
                }
            }
            return 1
        },
        initMapGis() {
            let _this = this;


            // 预留加载geoserver切片
            _this.vLayerBase = new TileLayer({
                source: new TileWMS({
                    url: "http://***********:8088/geowebcache/service/wms",
                    params: {
                        'FORMAT': 'image/png',
                        'VERSION': '1.1.1',
                        tiled: true,
                        "LAYERS": 'googleCatch',
                        "exceptions": 'application/vnd.ogc.se_inimage',
                    }
                }),
                visible: true
            });


            // _this.vLayerTdtImg = new TileLayer({
            //     source: new XYZ({
            //         url:
            //             `http://t{0-7}.tianditu.com/DataServer?T=img_w&x={x}&y={y}&l={z}&tk=${TianDiTuKey}`,
            //     }),
            //     visible: true
            // });
            // _this.vLayerTdt = new TileLayer({
            //     source: new XYZ({
            //         url:
            //             `http://t{0-7}.tianditu.com/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=${TianDiTuKey}`,
            //     }),
            //     visible: false
            // });
            // _this.vLayerTdtLable = new TileLayer({
            //     source: new XYZ({
            //         url:
            //             `http://t{0-7}.tianditu.com/DataServer?T=cia_w&x={x}&y={y}&l={z}&tk=${TianDiTuKey}`,
            //     }),
            //     visible: false
            // });


            //地块图层
            _this.vLayerArea = new VectorLayer({
                source: new VectorSource(),
            });
            _this.vLayerArea.layerName = 'Area'

            _this.vLayerCompany = new VectorLayer({
                source: new VectorSource(),
            });
            _this.vLayerCompany.layerName = 'Company'

            _this.vLayerFarm = new VectorLayer({
                source: new VectorSource(),
            });
            _this.vLayerFarm.layerName = 'Farm'

            _this.vLayerLineFarm = new VectorLayer({
                source: new VectorSource(),
            });
            _this.vLayerLineFarm.layerName = 'LineFarm'

            _this.vLayerManage = new VectorLayer({
                source: new VectorSource(),
            });
            _this.vLayerManage.layerName = 'Manage'

            _this.vLayerLineManage = new VectorLayer({
                source: new VectorSource(),
            });

            _this.vLayerStation = new VectorLayer({
                source: new VectorSource(),
            });
            _this.vLayerStation.layerName = 'Station'


            _this.vLayerLandType = new VectorLayer({
                source: new VectorSource(),
            });

            this.vLayerLandCrop = new VectorLayer({
                layerName: 'vLayerLand',
                source: new VectorSource(),
            });
            this.vLayerLandPay = new VectorLayer({
                layerName: 'vLayerLandPay',
                source: new VectorSource(),
            });
            this.vLayerAuthType = new VectorLayer({
                layerName: 'vLayerAuthType',
                source: new VectorSource(),
            });
            this.vLayerLandLevel = new VectorLayer({
                layerName: 'vLayerLandLevel',
                source: new VectorSource(),
            });
            this.vLayerResources = new VectorLayer({
                layerName: 'vLayerResources',
                source: new VectorSource(),
            });

            _this.vLayerResourcesLines = new VectorLayer({
                source: new VectorSource(),
            });
            _this.vLayerResourcesLines.layerName = 'Lines'


            _this.vLayerResourcesPoints = new VectorLayer({
                source: new VectorSource(),
            });
            _this.vLayerResourcesPoints.layerName = 'Points'



            _this.vLayerHighLight = new VectorLayer({
                source: new VectorSource(),
            });

            _this.vLayerCompanyHighLight = new VectorLayer({
                source: new VectorSource(),
            });

            _this.vLayerMobileStation = new VectorLayer({
                source: new VectorSource(),
                visible: false
            });

            _this.vLayerMobileStation_yd = new VectorLayer({
                source: new VectorSource(),
                visible: false
            });

            _this.vLayerMobileStation_lt = new VectorLayer({
                source: new VectorSource(),
                visible: false
            });


            //用户农场坐标
            getUserOrgInfoGps().then(res => {
                _this.centerGeo = res.data[0] ? res.data[0].geom : '';
                if (!!_this.centerGeo) {
                    let multiPolygonFeature = new Feature(new WKT().readGeometry(_this.centerGeo));
                    let geometryOrExtent = multiPolygonFeature.getGeometry().getExtent();
                    _this.map.getView().fit(geometryOrExtent, {
                        size: _this.map.getSize(),
                        maxZoom: 11,
                        callback: function () {
                        }
                    });
                } else {
                    _this.map.getView().setCenter(this.center)
                }
            })

            this.map.on("moveend", function (e) {
                //console.log(_this.map.getView().getAnimating(),"_this.map.getView().getAnimating()");
                //if (_this.map.getView().getAnimating()) return;

                _this.mapMoveend();
            });
            _this.map.on("pointermove", function (e) {
                //console.log(_this.map.getView().getAnimating(),"_this.map.getView().getAnimating()");
                if (_this.map.getView().getAnimating()) return;
                var features = _this.map.forEachFeatureAtPixel(e.pixel, function (feature, layer) {
                    return {
                        feature: feature,
                        layer: layer
                    };
                });

                if (features && features.layer && features.layer.getVisible() === true) {
                    if (features.layer.layerName == 'Company' || features.layer.layerName == 'Farm' || features.layer.layerName == 'Manage') {
                        if (features.feature == _this.highLightFeature) {
                            return;
                        }
                        if (_this.highLightFeature) {
                            _this.vLayerCompanyHighLight.getSource().clear();
                            _this.highLightFeature = null;
                        }
                        if (features.feature) {
                            if (features.feature.getGeometry() instanceof Polygon || features.feature.getGeometry() instanceof MultiPolygon) {

                                var feat = features.feature.clone();

                                var style = feat.getStyle().clone();

                                var oldColor = style.getFill().getColor();
                                var newColor = "";
                                var cols = oldColor.split(',');
                                for (var i = 0; i < cols.length; i++) {
                                    if (i != 0) {
                                        newColor += ","
                                    }
                                    if (i == cols.length - 1) {
                                        newColor += "1)"
                                    } else {
                                        newColor += cols[i];
                                    }
                                }
                                style.getFill().setColor(newColor);
                                feat.setStyle(style);
                                var Resl = _this.map.getView().getResolution();
                                if (features.layer.layerName == 'Manage') {

                                } else {
                                    feat.getGeometry().translate(Resl * 5, 0 - Resl * 5);
                                }
                                _this.vLayerCompanyHighLight.getSource().addFeature(feat);
                                _this.highLightFeature = features.feature;
                            }
                        }
                    }

                    if (features.layer.layerName && features.layer.layerName == 'Lines' && features.layer.getVisible() === true) {
                        //if (_this.lastLineFeat) _this.lastLineFeat.getStyle().setText(new Text());
                        if (_this.lastLineFeat) _this.vLayerResourcesLines.getSource().removeFeature(_this.lastLineFeat);
                        var coordinate = e.coordinate;
                        var pt = new Point(coordinate);
                        var newFeat = new Feature(pt);
                        var newStyle = new Style();
                        //pt.setCoordinates(coordinate);
                        //console.log(pt);
                        let textStyle = new Text({
                            font: 'bold 15px Mirosoft Yahei',
                            //placement: 'line',POINT
                            placement: 'POINT',
                            text: features.feature.getProperties()['nat_res_name'],
                            fill: new Fill({
                                color: '#000'
                            }),
                            offsetX: 10,
                            offsetY: 15,
                            stroke: new Stroke({
                                color: '#FFF',
                                width: 2
                            })
                        });
                        newStyle.setText(textStyle);
                        newFeat.setStyle(newStyle);
                        _this.vLayerResourcesLines.getSource().addFeature(newFeat);
                        //features.feature.getStyle().setText(textStyle);
                        _this.lastLineFeat = newFeat;
                        //features.layer.changed();
                    }
                } else {

                    _this.vLayerCompanyHighLight.getSource().clear();
                    _this.highLightFeature = null;
                }
            });


            _this.map.on("dblclick", function (e) {
                //console.log(_this.map.getView().getAnimating(),"_this.map.getView().getAnimating()");
                if (_this.map.getView().getAnimating()) return;
                var features = _this.map.forEachFeatureAtPixel(e.pixel, function (feature, layer) {
                    return {
                        feature: feature,
                        layer: layer
                    };
                });
                if (features && features.layer && features.layer.getVisible() === true) {
                    if (features.layer.layerName == 'Company') {
                        _this.map.getView().animate({
                            center: _this.map.getCoordinateFromPixel(e.pixel),
                            zoom: _this.zoomFarmMin,
                            duration: 1200
                        }, function () {
                            _this.vLayerCompanyHighLight.getSource().clear();
                            _this.highLightFeature = null;
                        });
                    } else if (features.layer.layerName == 'Farm') {
                        _this.map.getView().animate({
                            center: _this.map.getCoordinateFromPixel(e.pixel),
                            zoom: _this.zoomManageMin,
                            duration: 1800
                        }, function () {
                            _this.vLayerCompanyHighLight.getSource().clear();
                            _this.highLightFeature = null;
                        });
                    }
                }
            });
            //加载市界线数据
            getGeoWfs("bdh:gl_area", "parent_are=707").then((json) => {
                let _this = this
                let geo = new GeoJSON();
                let features = geo.readFeatures(json);
                //console.log(features);
                let style = _this.getGeolineStyle("rgba(203,203,203,.5)", 1);
                features.forEach(function (item, index) {
                    var feat = features[index];
                    feat.setStyle(style);
                    _this.vLayerArea.getSource().addFeature(feat);
                });
                //加载省界线数据
                getGeoWfs("bdh:gl_area", "area_id=707").then((json) => {
                    let _this = this
                    let geo = new GeoJSON();
                    let features = geo.readFeatures(json);
                    let style = _this.getGeolineStyle("rgba(203,203,203,.5)", 1);
                    features.forEach(function (item, index) {
                        var feat = features[index];
                        feat.setStyle(style);
                        _this.vLayerArea.getSource().addFeature(feat);
                    });
                });
            });

            this.initMapdata();
        },
        getLandStyle(text = '', fillColor = 'rgba(255, 255, 0, 0.2)', StrokeColor = 'rgba(255, 255, 0, 0.4)', offY = 0, strokeWidth = 1, font = '12px Mirosoft Yahei') {
            return new Style({
                fill: new Fill({
                    color: fillColor,
                }),
                stroke: new Stroke({
                    color: StrokeColor,
                    width: strokeWidth
                }),
                text: new Text({
                    font: font,
                    placement: 'point',
                    text: text,
                    fill: new Fill({
                        color: '#FFF',
                    }),
                    offsetY: offY,
                    //stroke: new ol.style.Stroke({
                    //color: '#FFF',
                    //width: 2
                    //})
                })
            })
        },


        async mapMoveend() {

let _this = this
if (_this.bolInit == false) {
    //去掉组件初始化默认加载的底图服务图层
    _this.map.removeLayer(_this.map.getLayers().array_[0]);
    //console.log(map.getLayers());
    _this.bolInit = true;
    _this.map.addLayer(_this.vLayerBase);
    // _this.map.addLayer(_this.vLayerTdtImg);
    // _this.map.addLayer(_this.vLayerTdt);
    // _this.map.addLayer(_this.vLayerTdtLable);
    _this.map.addLayer(_this.vLayerArea);
    _this.map.addLayer(_this.vLayerCompanyHighLight);
    _this.map.addLayer(_this.vLayerCompany);
    _this.map.addLayer(_this.vLayerFarm);
    _this.map.addLayer(_this.vLayerLineFarm);
    _this.map.addLayer(_this.vLayerManage);
    _this.map.addLayer(_this.vLayerLineManage);
    _this.map.addLayer(_this.vLayerStation);
    _this.map.addLayer(_this.vLayerLandType);
    _this.map.addLayer(_this.vLayerLandCrop);
    _this.map.addLayer(_this.vLayerLandPay);
    _this.map.addLayer(_this.vLayerAuthType);
    _this.map.addLayer(_this.vLayerLandLevel);
    _this.map.addLayer(_this.vLayerResources);
    _this.map.addLayer(_this.vLayerResourcesLines);
    _this.map.addLayer(_this.vLayerHighLight);
    _this.map.addLayer(_this.vLayerMobileStation);
    _this.map.addLayer(_this.vLayerMobileStation_yd);
    _this.map.addLayer(_this.vLayerMobileStation_lt);
}

let view = {};
if (_this.viewCollsBol == true) {
    view.center = _this.map.getView().getCenter();
    view.zoom = _this.map.getView().getZoom();
    if (_this.viewColls.length > 0) {
        _this.viewColls.splice(_this.viewCollsIndex + 1, 0, view)
        _this.viewCollsIndex += 1;
    } else {
        _this.viewColls.push(view);
    }
} else {
    _this.viewCollsBol = true
}

const zoom = _this.map.getView().getZoom(); //获取当前地图的缩放级别;
_this.nowZoom = zoom;

_this.vLayerCompanyHighLight.getSource().clear();
_this.highLightFeature = null;
//
if (zoom >= _this.zoomCompanyMin && zoom <= _this.zoomCompanyMax) {
    _this.vLayerCompany.setVisible(_this.bolCompany);
} else {
    _this.vLayerCompany.setVisible(false);
}
//
if (zoom >= _this.zoomFarmMin && zoom <= _this.zoomFarmMax) {
    _this.vLayerFarm.setVisible(_this.bolFarm);
} else {
    _this.vLayerFarm.setVisible(false);
}

if (zoom >= _this.zoomFarmLineMin && zoom <= _this.zoomFarmLineMax) {
    _this.vLayerLineFarm.setVisible(_this.bolFarm);
} else {
    _this.vLayerLineFarm.setVisible(false);
}
//
//
if (zoom >= _this.zoomManageMin && zoom <= _this.zoomManageMax) {
    _this.vLayerManage.setVisible(_this.bolManage);
    if (_this.bolManage) {
        let extent = _this.map.getView().calculateExtent(_this.map.getSize());
        getGeoWfs("bdh:res_org_gr", "BBOX(geom, " + extent[0] + "," + extent[1] + "," + extent[2] + "," + extent[3] + ")  and status_cd = 1 and year =" + _this.chooseYear + " and org_level = 4").then((json) => {
            let features = new GeoJSON().readFeatures(json);
            features.forEach(function (item, index) {
                let feat = features[index];
                var fillColor = 'rgba(255, 255, 0, 0.2)'
                var strokeColor = 'rgba(255, 255, 0, 0.8)'
                if (feat.values_.fill_color != null) {
                    // console.log(feat.values_.fill_color,'feat.values_.fill_color');
                    fillColor = feat.values_.fill_color
                }
                if (feat.values_.stroke_color != null) {
                    strokeColor = feat.values_.stroke_color
                }

                //let style = _this.getLandStyle(feat.values_.org_name + '\n' + '','rgba(255, 255, 0, 0.2)','rgba(255, 255, 0, 0.8)',0,3);
                let style = _this.getLandStyle(feat.values_.org_name + '\n' + '', fillColor, strokeColor, 0, 3, '15px Mirosoft Yahei');

                feat.setStyle(style);
                _this.vLayerManage.getSource().addFeature(feat);
            });
        });
    }
} else {
    _this.vLayerManage.setVisible(false);
}


if (zoom >= _this.zoomManageLineMin && zoom <= _this.zoomManageLineMax) {
    _this.vLayerLineManage.setVisible(_this.bolManage);
    if (_this.bolManage) {
        let extent = _this.map.getView().calculateExtent(_this.map.getSize());
        getGeoWfs("bdh:res_org_gr", "BBOX(geom, " + extent[0] + "," + extent[1] + "," + extent[2] + "," + extent[3] + ")  and status_cd = 1 and year =" + _this.chooseYear + " and org_level = 4").then((json) => {
            let features = new GeoJSON().readFeatures(json);
            features.forEach(function (item, index) {
                let feat = features[index];
                var fillColor = 'rgba(255, 255, 0, 0.2)'
                var strokeColor = 'rgba(255, 255, 0, 0.8)'

                if (feat.values_.stroke_color != null) {
                    strokeColor = feat.values_.stroke_color
                }

                let style = _this.getGeolineStyle(strokeColor, 3);
                feat.setStyle(style);
                _this.vLayerLineManage.getSource().addFeature(feat);
            });
        });
    }
} else {
    _this.vLayerLineManage.setVisible(false);
}


//
if (zoom >= _this.zoomStationMin && zoom <= _this.zoomStationMax) {
    _this.vLayerStation.setVisible(_this.bolStation);
    if (_this.bolStation) {
        let extent = _this.map.getView().calculateExtent(_this.map.getSize());
        getGeoWfs("bdh:res_org_gr", "BBOX(geom, " + extent[0] + "," + extent[1] + "," + extent[2] + "," + extent[3] + ")  and status_cd = 1 and year =" + _this.chooseYear + " and org_level = 5").then((json) => {
            let features = new GeoJSON().readFeatures(json);
            features.forEach(function (item, index) {
                let feat = features[index];
                let style = _this.getLandStyle(feat.values_.org_name + '\n' + '', 'rgba(255, 255, 0, 0.2)', 'rgba(255, 255, 0, 0.4)');
                feat.setStyle(style);
                _this.vLayerStation.getSource().addFeature(feat);
            });
        });
    }
} else {
    _this.vLayerStation.setVisible(false);
}


if (zoom >= _this.zoomLandLableMin && zoom <= _this.zoomLandLableMax) {
    if (!_this.bolZoomLandLable) {
        _this.vLayerLandCrop.getSource().clear();
        _this.vLayerLandType.getSource().clear();
        _this.vLayerLandPay.getSource().clear();
        _this.vLayerAuthType.getSource().clear();
        _this.vLayerLandLevel.getSource().clear();
    }
    _this.bolZoomLandLable = true;
} else {
    if (_this.bolZoomLandLable) {
        _this.vLayerLandCrop.getSource().clear();
        _this.vLayerLandType.getSource().clear();
        _this.vLayerLandPay.getSource().clear();
        _this.vLayerAuthType.getSource().clear();
        _this.vLayerLandLevel.getSource().clear();
    }
    _this.bolZoomLandLable = false;
}

if (zoom >= _this.zoomResourcesLinesMin && zoom <= _this.zoomResourcesLinesMax) {
    _this.vLayerResourcesLines.setVisible(true);

    let extent = _this.map.getView().calculateExtent(_this.map.getSize());
    getGeoWfs("bdh:res_nat_res_line", "BBOX(geom, " + extent[0] + "," + extent[1] + "," + extent[2] + "," + extent[3] + ")  and status_cd = 1  and year =" + _this.chooseYear).then((json) => {
        let features = new GeoJSON().readFeatures(json);
        features.forEach(function (item, index) {
            let feat = features[index];
            if (feat.getGeometry() instanceof LineString) {
                if (_this.vLayerResourcesLinesIds.indexOf(feat.getProperties()['plot_no']) < 0) {
                    _this.vLayerResourcesLines.getSource().addFeature(feat);
                    _this.vLayerResourcesLinesIds.push(feat.getProperties()['plot_no']);
                }
            } else if (feat.getGeometry() instanceof MultiLineString) {
                if (_this.vLayerResourcesLinesIds.indexOf(feat.getProperties()['plot_no']) < 0) {
                    _this.vLayerResourcesLinesIds.push(feat.getProperties()['plot_no']);
                    let lines = feat.getGeometry().getLineStrings();
                    let level = feat.getProperties()['canal_level'];
                    let type = feat.getProperties()['canal_type'];
                    let name = feat.getProperties()['nat_res_name'];

                    let lineColor = "rgba(30, 144, 255, 0.6)";
                    let lineWidth = 8
                    if (level == '1') {//灌溉渠
                        lineColor = "rgba(255, 0, 0, 0.6)"; //红色
                    }
                    if (level == '2') {//排水渠
                        lineColor = "rgba(0, 0, 255, 0.6)"; //蓝色
                    }

                    var buttomPathStyle = new Style({
                        stroke: new Stroke({
                            color: lineColor,
                            width: lineWidth,
                        }),
                    });


                    lines.forEach(function (it, i) {
                        let newFeat = new Feature(lines[i]);
                        newFeat.setProperties({ "canal_level": level });
                        newFeat.setProperties({ "canal_type": type });
                        newFeat.setProperties({ "nat_res_name": name });
                        // let lableStyle = new Style({
                        //   text: new Text({
                        //     font: 'bold 15px Mirosoft Yahei',
                        //     placement: 'line',
                        //     text: name,
                        //     fill: new Fill({
                        //       color: '#000'
                        //     }),
                        //     offsetX: 10,
                        //     offsetY: 15,
                        //     stroke: new Stroke({
                        //       color: '#FFF',
                        //       width: 2
                        //     })
                        //   })
                        // });
                        newFeat.setStyle(buttomPathStyle);
                        _this.vLayerResourcesLines.getSource().addFeature(newFeat);
                    });
                }
            }
        });
    });

} else {
    _this.vLayerResourcesLines.setVisible(false);
}

//
if (zoom >= _this.zoomLandMin && zoom <= _this.zoomLandMax) {

    _this.vLayerLandCrop.setVisible(_this.bolPolygon);
    _this.vLayerLandType.setVisible(_this.bolPolygon);
    _this.vLayerLandPay.setVisible(_this.bolPolygon);
    _this.vLayerAuthType.setVisible(_this.bolPolygon);
    _this.vLayerLandLevel.setVisible(_this.bolPolygon);
    _this.vLayerResources.setVisible(_this.bolPolygon);


    let extent = _this.map.getView().calculateExtent(_this.map.getSize());

    getGeoWfs("bdh:res_plot", "BBOX(geom, " + extent[0] + "," + extent[1] + "," + extent[2] + "," + extent[3] + ")  and status_cd = 1  and year =" + _this.chooseYear).then((json) => {
        //console.log(json)


        let bolAddLables = false;

        let featuresLandCrop = new GeoJSON().readFeatures(json);
        let featuresLandType = new GeoJSON().readFeatures(json);
        let featuresLandPay = new GeoJSON().readFeatures(json);
        let featuresAuthType = new GeoJSON().readFeatures(json);
        let featuresLandLevel = new GeoJSON().readFeatures(json);


        featuresLandCrop.forEach(function (item, index) {
            bolAddLables = false;
            let featLandCrop = featuresLandCrop[index];
            let featLandType = featuresLandType[index];
            let featLandPay = featuresLandPay[index];
            let featAuthType = featuresAuthType[index];
            let featLandLevel = featuresLandLevel[index];

            let style = null;
            let strLable = featLandCrop.values_.plot_name + '\n';

            //为了实现地块各类型的标注统一避让，需要优先组合标注内容。
            if (featLandCrop.values_.crop_type == 2 && _this.bolCorn == true) {
                strLable += '玉米' + '\n';
            } else if (featLandCrop.values_.crop_type == 3 && _this.bolSoybean == true) {
                strLable += '大豆' + '\n';
            } else if (featLandCrop.values_.crop_type == 1 && _this.bolRice == true) {
                strLable += '水稻' + '\n';
            } else if (featLandCrop.values_.crop_type != 2 && featLandCrop.values_.crop_type != 3 && featLandCrop.values_.crop_type != 1 && _this.bolOtherCrop == true) {
                strLable += '其他' + '\n';
            }

            if (featLandCrop.values_.land_type == 1 && _this.bolTypeWater == true) {
                strLable += '水田' + '\n';
            } else if (featLandCrop.values_.land_type == 2 && _this.bolTypeDry == true) {
                strLable += '旱田' + '\n';
            } else if (featLandCrop.values_.land_type != 1 && featLandCrop.values_.land_type != 2 && _this.bolTypeOther == true) {
                strLable += '其他' + '\n';
            }

            if (featLandCrop.values_.pay_type == 1 && _this.bolPaid == true) {
                strLable += '已缴费' + '\n';
            } else if (featLandCrop.values_.pay_type == 2 && _this.bolUnpaid == true) {
                strLable += '未缴费' + '\n';
            } else if (featLandCrop.values_.pay_type == 3 && _this.bolPartPaid == true) {
                strLable += '部分缴费' + '\n';
            }

            if (featLandCrop.values_.organic_type == 1 && _this.bolOrganic == true) {
                strLable += '有机' + '\n';
            } else if (featLandCrop.values_.organic_type == 2 && _this.bolGreen == true) {
                strLable += '绿色' + '\n';
            } else if (featLandCrop.values_.organic_type == 3 && _this.bolGeography == true) {
                strLable += '地理标志' + '\n';
            } else if (featLandCrop.values_.organic_type == 99 && _this.bolOrgOther == true) {
                strLable += '其他' + '\n';
            }

            if (featLandCrop.values_.plot_level == 1 && _this.bolLevel1 == true) {
                strLable += '一等地' + '\n';
            } else if (featLandCrop.values_.plot_level == 2 && _this.bolLevel2 == true) {
                strLable += '二等地' + '\n';
            } else if (featLandCrop.values_.plot_level == 3 && _this.bolLevel3 == true) {
                strLable += '三等地' + '\n';
            } else if (featLandCrop.values_.plot_level != 1 && featLandCrop.values_.plot_level != 2 && featLandCrop.values_.plot_level != 3 && _this.bolLevelOther == true) {
                strLable += '其他' + '\n';
            }
            ////////////////////////////////////////////////////////////种植作物//////////////////////////////////////////////////////////////////
            if (featLandCrop.values_.crop_type == 2 && _this.bolCorn == true) { //玉米
                style = _this.getLandStyle('', 'rgba(0, 238, 0, 0.2)', 'rgba(0, 238, 0, 0.4)');
                if (!bolAddLables) {
                    if (_this.bolZoomLandLable) {
                        style = _this.getLandStyle(strLable, 'rgba(0, 238, 0, 0.2)', 'rgba(0, 238, 0, 0.4)');
                        bolAddLables = true;
                    }
                }
                featLandCrop.setStyle(style);
                _this.vLayerLandCrop.getSource().addFeature(featLandCrop);
            } else if (featLandCrop.values_.crop_type == 3 && _this.bolSoybean == true) {//大豆
                style = _this.getLandStyle('', 'rgba(255, 255, 0, 0.2)', 'rgba(255, 255, 0, 0.4)');
                if (!bolAddLables) {
                    if (_this.bolZoomLandLable) {
                        style = _this.getLandStyle(strLable, 'rgba(255, 255, 0, 0.2)', 'rgba(255, 255, 0, 0.4)');
                        bolAddLables = true;
                    }
                }
                featLandCrop.setStyle(style);
                _this.vLayerLandCrop.getSource().addFeature(featLandCrop);
            } else if (featLandCrop.values_.crop_type == 1 && _this.bolRice == true) {//水稻
                style = _this.getLandStyle('', 'rgba(51, 153, 255, 0.4)', 'rgba(51, 153, 255, 0.6)');
                if (!bolAddLables) {
                    if (_this.bolZoomLandLable) {
                        style = _this.getLandStyle(strLable, 'rgba(51, 153, 255, 0.4)', 'rgba(51, 153, 255, 0.6)');
                        bolAddLables = true;
                    }
                }
                featLandCrop.setStyle(style);
                _this.vLayerLandCrop.getSource().addFeature(featLandCrop);
            } else if (featLandCrop.values_.crop_type != 2 && featLandCrop.values_.crop_type != 3 && featLandCrop.values_.crop_type != 1 && _this.bolOtherCrop == true) {//其他
                style = _this.getLandStyle('', 'rgba(0, 238, 0, 0.2)', 'rgba(255, 255, 0, 0.4)');
                if (!bolAddLables) {
                    if (_this.bolZoomLandLable) {
                        style = _this.getLandStyle(strLable, 'rgba(0, 238, 0, 0.2)', 'rgba(255, 255, 0, 0.4)');
                        bolAddLables = true;
                    }
                }
                featLandCrop.setStyle(style);
                _this.vLayerLandCrop.getSource().addFeature(featLandCrop);
            }
            ////////////////////////////////////////////////////////////土地类型//////////////////////////////////////////////////////////////////
            if (featLandType.values_.land_type == 1 && _this.bolTypeWater == true) {
                //style = _this.getLandStyle('水田','rgba(51, 153, 255, 0.2)','rgba(51, 153, 255, 0.4)',-28);
                if (bolAddLables) {
                    style = _this.getLandStyle('', 'rgba(51, 153, 255, 0.2)', 'rgba(51, 153, 255, 0.4)');
                } else {
                    style = _this.getLandStyle(strLable, 'rgba(51, 153, 255, 0.2)', 'rgba(51, 153, 255, 0.4)');
                    bolAddLables = true;
                }
                featLandType.setStyle(style);
                _this.vLayerLandType.getSource().addFeature(featLandType);
            } else if (featLandType.values_.land_type == 2 && _this.bolTypeDry == true) {
                //style = _this.getLandStyle('旱田','rgba(0, 238, 0, 0.2)','rgba(0, 238, 0, 0.4)',-28);
                if (bolAddLables) {
                    style = _this.getLandStyle('', 'rgba(0, 238, 0, 0.2)', 'rgba(0, 238, 0, 0.4)');
                } else {
                    style = _this.getLandStyle(strLable, 'rgba(0, 238, 0, 0.2)', 'rgba(0, 238, 0, 0.4)');
                    bolAddLables = true;
                }
                featLandType.setStyle(style);
                _this.vLayerLandType.getSource().addFeature(featLandType);
            } else if (featLandType.values_.land_type != 1 && featLandType.values_.land_type != 2 && _this.bolTypeOther == true) {
                //style = _this.getLandStyle('','rgba(255, 255, 0, 0.2)','rgba(255, 255, 0, 0.4)',-28);
                if (bolAddLables) {
                    style = _this.getLandStyle('', 'rgba(255, 255, 0, 0.2)', 'rgba(255, 255, 0, 0.4)');
                } else {
                    style = _this.getLandStyle(strLable, 'rgba(255, 255, 0, 0.2)', 'rgba(255, 255, 0, 0.4)');
                    bolAddLables = true;
                }
                featLandType.setStyle(style);
                _this.vLayerLandType.getSource().addFeature(featLandType);
            }
            ////////////////////////////////////////////////////////////缴费情况//////////////////////////////////////////////////////////////////
            if (featLandPay.values_.pay_type == 1 && _this.bolPaid == true) {
                //style = _this.getLandStyle('已缴费','rgba(0, 238, 0, 0.2)','rgba(0, 238, 0, 0.4)',-14);
                if (bolAddLables) {
                    style = _this.getLandStyle('', 'rgba(0, 238, 0, 0.2)', 'rgba(0, 238, 0, 0.4)');
                } else {
                    style = _this.getLandStyle(strLable, 'rgba(0, 238, 0, 0.2)', 'rgba(0, 238, 0, 0.4)');
                    bolAddLables = true;
                }
                featLandPay.setStyle(style);
                _this.vLayerLandPay.getSource().addFeature(featLandPay);
            } else if (featLandPay.values_.pay_type == 2 && _this.bolUnpaid == true) {
                //style = _this.getLandStyle('未缴费','rgba(255, 0, 0, 0.2)','rgba(255, 0, 0, 0.4)',-14);
                if (bolAddLables) {
                    style = _this.getLandStyle('', 'rgba(255, 0, 0, 0.2)', 'rgba(255, 0, 0, 0.4)');
                } else {
                    style = _this.getLandStyle(strLable, 'rgba(255, 0, 0, 0.2)', 'rgba(255, 0, 0, 0.4)');
                    bolAddLables = true;
                }
                featLandPay.setStyle(style);
                _this.vLayerLandPay.getSource().addFeature(featLandPay);
            } else if (featLandPay.values_.pay_type == 3 && _this.bolPartPaid == true) {
                //style = _this.getLandStyle('部分缴费','rgba(0, 0, 255, 0.2)','rgba(0, 0, 255, 0.4)',-14);
                if (bolAddLables) {
                    style = _this.getLandStyle('', 'rgba(0, 0, 255, 0.2)', 'rgba(0, 0, 255, 0.4)');
                } else {
                    style = _this.getLandStyle(strLable, 'rgba(0, 0, 255, 0.2)', 'rgba(0, 0, 255, 0.4)');
                    bolAddLables = true;
                }
                featLandPay.setStyle(style);
                _this.vLayerLandPay.getSource().addFeature(featLandPay);
            }
            ////////////////////////////////////////////////////////////认证类型//////////////////////////////////////////////////////////////////
            if (featAuthType.values_.organic_type == 1 && _this.bolOrganic == true) {
                //style = _this.getLandStyle('有机','rgba(0, 0, 255, 0.2)','rgba(0, 0, 255, 0.4)',0);
                if (bolAddLables) {
                    style = _this.getLandStyle('', 'rgba(0, 0, 255, 0.2)', 'rgba(0, 0, 255, 0.4)');
                } else {
                    style = _this.getLandStyle(strLable, 'rgba(0, 0, 255, 0.2)', 'rgba(0, 0, 255, 0.4)');
                    bolAddLables = true;
                }
                featAuthType.setStyle(style);
                _this.vLayerAuthType.getSource().addFeature(featAuthType);
            } else if (featAuthType.values_.organic_type == 2 && _this.bolGreen == true) {
                //style = _this.getLandStyle('绿色','rgba(0, 238, 0, 0.2)','rgba(0, 238, 0, 0.4)',0);
                if (bolAddLables) {
                    style = _this.getLandStyle('', 'rgba(0, 238, 0, 0.2)', 'rgba(0, 238, 0, 0.4)');
                } else {
                    style = _this.getLandStyle(strLable, 'rgba(0, 238, 0, 0.2)', 'rgba(0, 238, 0, 0.4)');
                    bolAddLables = true;
                }
                featAuthType.setStyle(style);
                _this.vLayerAuthType.getSource().addFeature(featAuthType);
            } else if (featAuthType.values_.organic_type == 3 && _this.bolGeography == true) {
                //style = _this.getLandStyle('地理标志','rgba(0, 238, 0, 0.2)','rgba(0, 238, 0, 0.4)',0);
                if (bolAddLables) {
                    style = _this.getLandStyle('', 'rgba(0, 238, 0, 0.2)', 'rgba(0, 238, 0, 0.4)');
                } else {
                    style = _this.getLandStyle(strLable, 'rgba(0, 238, 0, 0.2)', 'rgba(0, 238, 0, 0.4)');
                    bolAddLables = true;
                }
                featAuthType.setStyle(style);
                _this.vLayerAuthType.getSource().addFeature(featAuthType);
            } else if (featAuthType.values_.organic_type == 99 && _this.bolOrgOther == true) {
                //style = _this.getLandStyle('其他','rgba(0, 238, 0, 0.2)','rgba(0, 238, 0, 0.4)',0);
                if (bolAddLables) {
                    style = _this.getLandStyle('', 'rgba(0, 238, 0, 0.2)', 'rgba(0, 238, 0, 0.4)');
                } else {
                    style = _this.getLandStyle(strLable, 'rgba(0, 238, 0, 0.2)', 'rgba(0, 238, 0, 0.4)');
                    bolAddLables = true;
                }
                featAuthType.setStyle(style);
                _this.vLayerAuthType.getSource().addFeature(featAuthType);
            }
            ////////////////////////////////////////////////////////////耕地级别//////////////////////////////////////////////////////////////////
            if (featLandLevel.values_.plot_level == 1 && _this.bolLevel1 == true) {
                //style = _this.getLandStyle('一等地','rgba(0, 238, 0, 0.2)','rgba(0, 238, 0, 0.4)',14);
                if (bolAddLables) {
                    style = _this.getLandStyle('', 'rgba(0, 238, 0, 0.2)', 'rgba(0, 238, 0, 0.4)');
                } else {
                    style = _this.getLandStyle(strLable, 'rgba(0, 238, 0, 0.2)', 'rgba(0, 238, 0, 0.4)');
                    bolAddLables = true;
                }
                featLandLevel.setStyle(style);
                _this.vLayerLandLevel.getSource().addFeature(featLandLevel);
            } else if (featLandLevel.values_.plot_level == 2 && _this.bolLevel2 == true) {
                //style = _this.getLandStyle('二等地','rgba(0, 238, 122, 0.2)','rgba(0, 238, 122, 0.4)',14);
                if (bolAddLables) {
                    style = _this.getLandStyle('', 'rgba(0, 238, 122, 0.2)', 'rgba(0, 238, 122, 0.4)');
                } else {
                    style = _this.getLandStyle(strLable, 'rgba(0, 238, 122, 0.2)', 'rgba(0, 238, 122, 0.4)');
                    bolAddLables = true;
                }
                featLandLevel.setStyle(style);
                _this.vLayerLandLevel.getSource().addFeature(featLandLevel);
            } else if (featLandLevel.values_.plot_level == 3 && _this.bolLevel3 == true) {
                //style = _this.getLandStyle('三等地','rgba(0, 0, 255, 0.2)','rgba(0, 0, 255, 0.4)',14);
                if (bolAddLables) {
                    style = _this.getLandStyle('', 'rgba(0, 0, 255, 0.2)', 'rgba(0, 0, 255, 0.4)');
                } else {
                    style = _this.getLandStyle(strLable, 'rgba(0, 0, 255, 0.2)', 'rgba(0, 0, 255, 0.4)');
                    bolAddLables = true;
                }
                featLandLevel.setStyle(style);
                _this.vLayerLandLevel.getSource().addFeature(featLandLevel);
            } else if (featLandLevel.values_.plot_level != 1 && featLandLevel.values_.plot_level != 2 && featLandLevel.values_.plot_level != 3 && _this.bolLevelOther == true) {
                //style = _this.getLandStyle('其他','rgba(255, 0, 255, 0.2)','rgba(255, 0, 255, 0.4)',14);
                if (bolAddLables) {
                    style = _this.getLandStyle('', 'rgba(255, 0, 255, 0.2)', 'rgba(255, 0, 255, 0.4)');
                } else {
                    style = _this.getLandStyle(strLable, 'rgba(255, 0, 255, 0.2)', 'rgba(255, 0, 255, 0.4)');
                    bolAddLables = true;
                }
                featLandLevel.setStyle(style);
                _this.vLayerLandLevel.getSource().addFeature(featLandLevel);
            }
        });
    });
    ////////////////////////////////////////////////////////////自然资源//////////////////////////////////////////////////////////////////
    getGeoWfs("bdh:res_nat_res", "BBOX(geom, " + extent[0] + "," + extent[1] + "," + extent[2] + "," + extent[3] + ")  and status_cd = 1  and year =" + _this.chooseYear).then((json) => {
        let features = new GeoJSON().readFeatures(json);
        features.forEach(function (item, index) {
            let feat = features[index];
            let style = _this.getLandStyle(feat.values_.plot_name + '\n' + '', 'rgba(255, 255, 0, 0.2)', 'rgba(255, 255, 0, 0.4)');
            if (feat.values_.nat_res_type == 1 && _this.bolCanal == true) {
                style = _this.getLandStyle('水渠', 'rgba(0, 238, 0, 0.2)', 'rgba(0, 238, 0, 0.4)');
                feat.setStyle(style);
                _this.vLayerResources.getSource().addFeature(feat);
            } else if (feat.values_.nat_res_type == 2 && _this.bolForest == true) {
                style = _this.getLandStyle('森林', 'rgba(255, 255, 0, 0.2)', 'rgba(255, 255, 0, 0.4)');
                feat.setStyle(style);
                _this.vLayerResources.getSource().addFeature(feat);
            } else if (feat.values_.nat_res_type == 3 && _this.bolGrass == true) {
                style = _this.getLandStyle('草原', 'rgba(51, 153, 255, 0.2)', 'rgba(51, 153, 255, 0.4)');
                feat.setStyle(style);
                _this.vLayerResources.getSource().addFeature(feat);
            } else if (feat.values_.nat_res_type == 4 && _this.bolRivers == true) {
                style = _this.getLandStyle('河流', 'rgba(255, 255, 0, 0.2)', 'rgba(255, 255, 0, 0.4)');
                feat.setStyle(style);
                _this.vLayerResources.getSource().addFeature(feat);
            }
        });
    });
} else {
    _this.vLayerLandCrop.setVisible(false);
    _this.vLayerLandType.setVisible(false);
    _this.vLayerLandPay.setVisible(false);
    _this.vLayerAuthType.setVisible(false);
    _this.vLayerLandLevel.setVisible(false);
    _this.vLayerResources.setVisible(false);
}
        },

        initMapdata() {
            //加载分公司面数据和标注
            getGeoWfs("bdh:res_org_gr", "org_level = 2 and year=" + this.chooseYear).then((json) => {
                //bdh:gr_company
                let _this = this
                //console.log(json);
                let geo = new GeoJSON();
                //console.log(json);
                let features = geo.readFeatures(json);
                //console.log(features);
                features.forEach(function (item, index) {
                    let feat = features[index];
                    let style = _this.getGeoPolygonStyle(
                        feat.values_.fill_color,
                        feat.values_.stroke_color,
                        feat.values_.stroke_wide
                    );
                    feat.setStyle(style);
                    _this.vLayerCompany.getSource().addFeature(feat);
                    if (feat.values_.geo_center) {
                        let lable = new Feature(new WKT().readGeometry(feat.values_.geo_center));
                        let lableStyle = _this.getGeoTextStyle(feat.values_.org_name);
                        lable.setStyle(lableStyle);
                        _this.vLayerCompany.getSource().addFeature(lable);
                    }
                });
            });
            //加载农场面数据和标注
            getGeoWfs("bdh:res_org_gr", "org_level = 3 and year=" + this.chooseYear).then((json) => {
                let _this = this
                let geo = new GeoJSON();
                let features = geo.readFeatures(json);
                features.forEach(function (item, index) {
                    let feat = features[index];
                    let style = _this.getGeoPolygonStyle(
                        feat.values_.fill_color,
                        feat.values_.stroke_color,
                        feat.values_.stroke_wide
                    );
                    feat.setStyle(style);
                    _this.vLayerFarm.getSource().addFeature(feat);
                    if (feat.values_.geo_center) {
                        let lable = new Feature(new WKT().readGeometry(feat.values_.geo_center));

                        let lableStyle = _this.getGeoTextStyle(feat.values_.org_name);
                        lable.setStyle(lableStyle);
                        _this.vLayerFarm.getSource().addFeature(lable);
                    }
                });

                let featuresLine = geo.readFeatures(json);
                featuresLine.forEach(function (item, index) {
                    let feat = featuresLine[index];
                    let style = _this.getGeolineStyle(feat.values_.stroke_color, 3);
                    feat.setStyle(style);
                    _this.vLayerLineFarm.getSource().addFeature(feat);
                });
            });

            getGeoWfs("bdh:gp_station", "1=1").then((json) => {
                let _this = this
                let geo = new GeoJSON();
                let features = geo.readFeatures(json);
                features.forEach(function (item, index) {
                    let feat = features[index];
                    let style = new Style({
                        image: new Icon({
                            anchor: [0.5, 0.5],
                            offset: [0, 0],
                            //anchorOrigin: 'top-right',
                            //anchorXUnits: 'fraction',
                            //anchorYUnits: 'pixels',
                            //offsetOrigin: 'top-right',
                            opacity: 1,
                            scale: 0.2,
                            src: corsImg
                        })
                    });

                    feat.setStyle(style);
                    _this.vLayerMobileStation.getSource().addFeature(feat);
                });
            });

            getGeoWfs("bdh:gp_station_yd", "1=1").then((json) => {
                let _this = this
                let geo = new GeoJSON();
                let features = geo.readFeatures(json);
                features.forEach(function (item, index) {
                    let feat = features[index];
                    let style = new Style({
                        image: new Icon({
                            anchor: [0.5, 0.5],
                            offset: [0, 0],

                            opacity: 1,
                            scale: 0.2,
                            src: corsImg2
                        })
                    });

                    feat.setStyle(style);
                    _this.vLayerMobileStation_yd.getSource().addFeature(feat);
                });
            });

            getGeoWfs("bdh:gp_station_lt", "1=1").then((json) => {
                let _this = this
                let geo = new GeoJSON();
                let features = geo.readFeatures(json);
                features.forEach(function (item, index) {
                    let feat = features[index];
                    let style = new Style({
                        image: new Icon({
                            anchor: [0.5, 0.5],
                            offset: [0, 0],

                            opacity: 1,
                            scale: 0.2,
                            src: corsImg3
                        })
                    });

                    feat.setStyle(style);
                    _this.vLayerMobileStation_lt.getSource().addFeature(feat);
                });
            });


        },

        getGeoPolygonStyle(fillColor = "rgba(238, 0, 238, 0.4)", strokeColor = "rgba(238, 0, 238, 0.4)", strokeWidth = 2) {
            return new Style({
                fill: new Fill({
                    color: fillColor,
                }),
                stroke: new Stroke({
                    color: strokeColor,
                    width: strokeWidth,
                }),
            });
        },

        getGeoTextStyle(text = "", offsetY = 0, font = "15px Mirosoft Yahei", fillColor = "#000", strokeColor = "#FFF", strokewidth = 2, placement = "point") {
            return new Style({
                text: new Text({
                    font: font,
                    placement: placement,
                    text: text,
                    fill: new Fill({
                        //color: fillColor,
                        color: strokeColor,
                    }),
                    offsetY: offsetY,
                    stroke: new Stroke({
                        //color: strokeColor,
                        color: "rgba(102, 102, 102, 1)",
                        width: strokewidth,
                    }),
                }),
            });
        },
        getGeolineStyle(color = "rgba(0, 0, 255, 0.8)", width = 3, lineDash = null) {
            return new Style({
                stroke: new Stroke({
                    color: color,
                    width: width,
                    lineDash: lineDash,
                }),
            });
        },
        //图层显隐控制
        showLayer(layerName) {
            let _this = this;
            const zoom = _this.map.getView().getZoom(); //获取当前地图的缩放级别;

            //边界
            if (layerName == 'Area') {
                _this.bolArea = !_this.bolArea;
                _this.vLayerArea.setVisible(_this.bolArea);
            }

            //分公司
            if (layerName == 'Company') {
                if (_this.bolCompany) {
                    _this.bolCompany = false;//隐藏分公司
                    _this.vLayerCompany.setVisible(false);
                } else {
                    _this.bolCompany = true;//分公司

                    //因为分公司，农场等图层，是根据地图缩放级别来控制显隐，所以呢，开关控制图层显示的时候，判断一下地图的level
                    if (zoom >= _this.zoomCompanyMin && zoom <= _this.zoomCompanyMax) {
                        _this.vLayerCompany.setVisible(_this.bolCompany);
                    }

                }
            }

            //农场
            if (layerName == 'Farm') {
                if (_this.bolFarm) {
                    _this.bolFarm = false;
                    _this.vLayerFarm.setVisible(false);
                } else {
                    _this.bolFarm = true;
                    if (zoom >= _this.zoomFarmMin && zoom <= _this.zoomFarmMax) {
                        _this.vLayerFarm.setVisible(_this.bolFarm);
                    }
                }
            }

            //地块
            if (layerName == 'Polygon') {
                if (_this.bolPolygon) {
                    _this.bolPolygon = false;

                    //这里为什么这么多操作？问的好！因为地块图层展示根据作物类型，建了对应的图层，所以全部操作一遍
                    _this.vLayerLandCrop.setVisible(false);
                    _this.vLayerLandType.setVisible(false);
                    _this.vLayerLandPay.setVisible(false);
                    _this.vLayerAuthType.setVisible(false);
                    _this.vLayerLandLevel.setVisible(false);
                    _this.vLayerResources.setVisible(false);
                } else {
                    _this.bolPolygon = true;
                    if (zoom >= _this.zoomLandMin && zoom <= _this.zoomLandMax) {
                        _this.vLayerLandCrop.setVisible(_this.bolPolygon);
                        _this.vLayerLandType.setVisible(_this.bolPolygon);
                        _this.vLayerLandPay.setVisible(_this.bolPolygon);
                        _this.vLayerAuthType.setVisible(_this.bolPolygon);
                        _this.vLayerLandLevel.setVisible(_this.bolPolygon);
                        _this.vLayerResources.setVisible(_this.bolPolygon);
                    }
                }
            }
        },
        // change(e) {
        //     console.log(e)
        //     if (e) {
        //         this.draw()
        //     } else {
        //         this.map.removeLayer(item.vectorLayer)
        //     }
        // },
        rowStyle() {
            let style = {
                background: '#20b2aa60',
                color: '#ffffff'
            }
            return style;
        },
        cellStyle() {
            let style = {
                background: 'transparent',
                color: '#ffffff'
            }
            return style;
        }
    }
}
</script>

<style rel="stylesheet" lang="scss" scoped>
.drawinfo_box {
    position: absolute;
    right: 608px;
    bottom: 10px;
    background-color: rgba(168, 165, 165, 0.3);
    border-radius: 5px;
    padding: 10px;
    z-index: 4;

    .drawinfo {
        text-align: center;
        display: flex;
        align-items: center;
        font-size: 14px;
        color: white;
        cursor: pointer;

        .line {
            width: 30px;
            height: 4px;
            background-color: rgba(217, 0, 27, 1);
            margin-right: 5px;
        }

        .success {
            background-color: rgba(105, 229, 21, 1);
        }

        .activeClass {
            background-color: #dfdfdf;
        }

        .activeColor {
            color: #dfdfdf;
        }
    }
}

.history-map-container {
    background: rgba(0, 29, 61, 1);
    position: relative;
    flex-grow: 1;
    width: 100%;
    height: 100%;

    #history-map {
        width: calc(100% - 600px);
        float: left;
        height: 100%;

        &:deep(.ol-control) {
            top: 40% !important;
            right: 8px !important;
            left: unset !important;
            z-index: 100;
        }
    }


    .custom-control {
        position: absolute;
        bottom: 0;
        left: 140px;
        z-index: 2;
        background-color: rgba(0, 0, 0, 0.3);

        .control-btns {
            margin: 10px 20px;
            // padding-right: 100px;
        }

        .percent-input {
            width: 70px;
            margin-left: 10px;
            display: inline-block;
        }
    }

    .layer-control {
        text-align: center;
        background-color: rgba(255, 255, 255, 0.4);
        position: absolute;
        z-index: 100;
        top: 55%;
        right: 8px;
        padding: 3px 3px 2px 3px;
        border-radius: 3px;
        color: #FFFFFF;

        .custom-icon {
            width: 25px;
            height: 25px;
            line-height: 25px;
            margin: auto;
            background-color: rgba(0, 60, 136, 0.5);
            font-size: 18px;
            margin-bottom: 1px;

            &.hideBg {
                background-color: rgba(0, 60, 136, 0.2);
            }

            &:hover {
                background-color: rgba(0, 60, 136, 0.8);
            }
        }
    }
}

.extend-control {
    position: absolute;
    top: calc(40% - 84px);
    right: 608px;
    background-color: rgba(255, 255, 255, 0.4);
    border-radius: 4px;
    padding: 2px;

    &>div {
        display: block;
        margin: 1px;
        padding: 0;
        color: white;
        font-size: 1.14em;
        font-weight: bold;
        text-decoration: none;
        text-align: center;
        width: 20px;
        height: 20px;
        line-height: 23px;
        background-color: rgba(0, 60, 136, 0.5);
        border: none;
        border-radius: 2px;
    }

    &>div:hover {
        background-color: rgba(0, 60, 136, 0.7);
    }
}

.data-list-wrap {
    background-image: linear-gradient(rgba(5, 13, 17, 0.62), rgba(19, 49, 65, 0.66));
    //position: absolute;
    bottom: 0;
    left: 0;
    z-index: 10;
    color: #ffffff;

    .data-list {
        padding: 20px;
    }

    i.el-icon-caret-bottom,
    i.el-icon-caret-top {
        position: absolute;
        top: 5px;
        right: 20px;
        font-size: 38px;
        cursor: pointer;
    }

    .data-list-title {
        margin: 20px 0 5px 0;

        i {
            margin-right: 10px;
        }
    }

    .data-list-info {
        margin-bottom: 20px;
        color: #bbbbbb;
        padding-left: 25px;
        display: flex;
        flex-wrap: wrap;

        .el-input {
            width: 100px;
        }

        .el-input__inner {
            text-align: center;
            color: #ffffff;
            border-radius: 0;
            background: transparent;
            border: none;
            border-bottom: 1px solid #8f9ba2;
            font-size: 16px;
        }

        div {
            margin: 0 30px 0 10px;
            font-weight: bold;
        }

        .unok {
            color: #ff9c00;
        }

        .ok {
            color: #00ff48;
        }
    }
}

.pagination-container {
    background: none !important;
}

.el-pagination__jump {
    color: white;
}

.el-pagination__total {
    color: white;
}

.el-table {
    background: transparent;

    .el-button {
        color: #409EFF;
        font-weight: bold;
    }

    tr,
    td,
    th,
    .el-table__body-wrapper {
        background: transparent;
        color: #ffffff;
    }

    tr {

        td:nth-child(3),
        th:nth-child(3) {
            //border-right: none;
        }
    }

    .gutter {
        display: none;
    }

    .el-table__row:hover {

        tr,
        td,
        th {
            background: rgba(0, 29, 61, 0.85);
        }
    }

    .el-table__row.hover-row {

        tr,
        td,
        th {
            background: rgba(0, 29, 61, 0.85);

        }
    }

    .el-checkbox__inner {
        background: #001d3d;
    }
}

.fiex_right {
    width: 600px;
    // position: absolute;
    background-color: #fff;
    height: 100%;
    overflow: auto;
    top: 0;
    right: 0;
    z-index: 3;
    float: right;

    .title {
        font-size: 14px;
        margin: 5px 0 10px 0;
        padding-left: 10px;
        display: flex;
        align-items: center;
    }

    .lr {
        display: flex;
        align-items: center;
        font-size: 14px;
        margin: 5px 0 10px 0;
        padding-left: 10px;
        padding-right: 10px;
        justify-content: space-between;

    }
}

/*tooltip部分*/
.tooltip {
    clear: both;
    width: 180px;
    /*z-index: 9999;*/
    position: relative;
    background: white;
    /*position: absolute;*/
    /*top: 200px;*/
    /*left: 400px;*/
    border: solid 1px #009688;
    text-align: center;
    padding: 10px 0 15px 0;
    opacity: 0.7;

    .close {
        position: absolute;
        right: 0px;
        top: 0;
        cursor: pointer;
    }

    .tooltip-title-container {
        background-color: #009688;
        position: relative;
        height: 44px;
        text-align: center;
        line-height: 44px;
        color: #ffffff;


    }

    .tooltip-content-container {
        display: flex;
        padding: 10px 0;
        flex-wrap: wrap;

        &>div {
            border: transparent !important;
            line-height: 30px;
            font-size: 14px;
            width: 68%;
            flex-shrink: 0;

            &:nth-child(2n - 1) {
                text-align: right;
                width: 30%;
            }
        }
    }

    .tooltip-btn-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        padding: 0 50px 16px 50px;

        &>div {
            cursor: pointer;
            width: 94px;
            height: 38px;
            line-height: 38px;
            text-align: center;
            border-radius: 4px;
            background-color: #009688;
            color: #ffffff;
            margin-bottom: 16px;
        }
    }

    .angle {
        position: absolute;
        bottom: -10px;
        left: 80px;
        width: 20px;
        height: 20px;
        background-color: #ffffff;
        border-right: solid 1px #009688;
        border-bottom: solid 1px #009688;
        transform: rotate(45deg);
    }
}

:deep(.el-input-number .el-input__inner) {
    text-align: left;
}

:deep(#tableDataAIR .el-table__header .el-table-column--selection .cell .el-checkbox) {
    display: none;
}

:deep(#tableDataAIR .el-table__header .el-table-column--selection .cell:before) {
    content: "选择查看";
    text-align: center;
    position: absolute;
    left: 0;
    right: 0;
}

:deep(#tableDataUAV .el-table__header .el-table-column--selection .cell .el-checkbox) {
    display: none;
}

:deep(#tableDataUAV .el-table__header .el-table-column--selection .cell:before) {
    content: "选择查看";
    text-align: center;
    position: absolute;
    left: 0;
    right: 0;
}</style>
