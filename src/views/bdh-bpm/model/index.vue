<template>
  <div class="app-container" :style="{height:numHeight}">
    <div ref="searchDom">
      <el-form :model="queryParams" ref="queryFormRef" class="form-line" label-width="80px" v-show="showSearch" @submit.prevent>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="流程名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入流程名称" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="18" align="right">
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-col>
        </el-row>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
              type="primary"
              plain
              icon="Plus"
              @click="openForm('create')"
          >新建模型</el-button>
          <el-button
              type="primary"
              plain
              icon="Plus"
              @click="handleCategoryAdd"
          >新增分类</el-button>
        </el-col>
      </el-row>


    </div>
    <draggable
        :disabled="!isCategorySorting"
        v-model="categoryGroup"
        item-key="id"
        :animation="400"
    >
      <template #item="{ element }">
        <ContentWrap
            class="rounded-lg transition-all duration-300 ease-in-out hover:shadow-xl"
            :body-style="{ padding: 0 }"
            :key="element.id"
        >
          <CategoryDraggableModel
              :isCategorySorting="isCategorySorting"
              :categoryInfo="element"
              @success="getList"
          />
        </ContentWrap>
      </template>
    </draggable>

    <!-- 表单弹窗：添加分类 -->
    <CategoryForm ref="categoryFormRef" @success="getList" />
    <!-- 弹窗：表单详情 -->
    <Dialog title="表单详情" v-model="formDetailVisible" width="800">
      <form-create :rule="formDetailPreview.rule" :option="formDetailPreview.option" />
    </Dialog>
  </div>
</template>

<script lang="ts" setup>
//import 'virtual:uno.css'
import draggable from 'vuedraggable'
import {CategoryApi} from '@/api/bdh-bpm/category'
import * as ModelApi from '@/api/bdh-bpm/model'
import CategoryForm from '../category/CategoryForm.vue'
import { cloneDeep } from 'lodash-es'
import CategoryDraggableModel from './CategoryDraggableModel.vue'
import {ContentWrap} from "@/components/ContentWrap"
import { useRouter } from 'vue-router'
import {Dialog} from "@/components/Dialog";


import {useMessage} from "@/utils/web/useMessage";
import {useI18n} from "@/utils/web/useI18n";

defineOptions({ name: 'BpmModel' })

const { push } = useRouter()
const message = useMessage() // 消息弹窗
const loading = ref(true) // 列表的加载中
const isCategorySorting = ref(false) // 是否 category 正处于排序状态
const queryParams = reactive({
  name: undefined
})
const queryFormRef = ref() // 搜索的表单
const categoryGroup: any = ref([]) // 按照 category 分组的数据
const originalData: any = ref([]) // 原始数据

const searchDom = ref(); // 搜索栏dom
const tableHeight = ref(400); // 表格高度
const searchHeight = ref(0); // 搜索栏高度
const showSearch = ref(true);
const numHeight = ref('calc(100vh - 84px)');

/** 搜索按钮操作 */
const handleQuery = () => {
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value.resetFields()
    handleQuery()
}
/** 添加/修改操作 */
const openForm = (type: string, id?: number) => {
  if (type === 'create') {
    // push({ name: 'BpmModelCreate' })
      window.router.push({ path: "/bdh-bpm/bpm/manager/model/create"});
  } else {
    // push({
    //   name: 'BpmModelUpdate',
    //   params: { id }
    // })
      window.router.push({ path: "/bdh-bpm/bpm/manager/model/update",query:{id:id}});
  }
}

/** 流程表单的详情按钮操作 */
const formDetailVisible = ref(false)
const formDetailPreview = ref({
  rule: [],
  option: {}
})

/** 右上角设置按钮 */
const handleCommand = (command: string) => {
  switch (command) {
    case 'handleCategoryAdd':
      handleCategoryAdd()
      break
    case 'handleCategorySort':
      handleCategorySort()
      break
    default:
      break
  }
}

/** 新建分类 */
const categoryFormRef = ref()
const handleCategoryAdd = () => {
  categoryFormRef.value.open('create')
}

/** 分类排序的提交 */
const handleCategorySort = () => {
  // 保存初始数据
  originalData.value = cloneDeep(categoryGroup.value)
  isCategorySorting.value = true
}

/** 分类排序的取消 */
const handleCategorySortCancel = () => {
  // 恢复初始数据
  categoryGroup.value = cloneDeep(originalData.value)
  isCategorySorting.value = false
}

/** 分类排序的保存 */
const handleCategorySortSubmit = async () => {
  // 保存排序
  const ids = categoryGroup.value.map((item: any) => item.id)
  await CategoryApi.updateCategorySortBatch(ids)
  // 刷新列表
  isCategorySorting.value = false
  message.success('排序分类成功')
  await getList()
}

/** 加载数据 */
const getList = async () => {
  loading.value = true
  try {
    // 查询模型 + 分裂的列表
    const modelList = await ModelApi.getModelList(queryParams.name)
    const categoryList = await CategoryApi.getCategorySimpleList()

      if(modelList.length > 0 && categoryList.length > 0){
          numHeight.value = ((modelList.length *70) + (categoryList.length*80) + 200) + 'px'
      }
    // console.log('categoryList',modelList,categoryList)
    // 按照 category 聚合
    // 注意：必须一次性赋值给 categoryGroup，否则每次操作后，列表会重新渲染，滚动条的位置会偏离！！！
    categoryGroup.value = categoryList.map((category: any) => ({
      ...category,
      modelList: modelList.filter((model: any) => model.categoryName == category.name)
    }))
    // console.log(categoryGroup.value)
  } finally {
    loading.value = false
  }
}

/** 初始化 **/
getList()
</script>

<style lang="scss" scoped>
:deep() {
  .el-table--fit .el-table__inner-wrapper:before {
    height: 0;
  }
  .el-card {
    border-radius: 8px;
  }
  .el-form--inline .el-form-item {
    margin-right: 10px;
  }
  .el-divider--horizontal {
    margin-top: 6px;
  }
}
</style>
