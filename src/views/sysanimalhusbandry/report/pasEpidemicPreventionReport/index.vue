<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form :model="queryParams" ref="queryForm" class="queryClass form-line" :inline="true" v-show="showSearch"
          label-width="68px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年份" prop="statYear">
                <el-select v-model="queryParams.statYear" placeholder="请选择年份" :clearable="false">
                  <el-option v-for="dict in statYearOptions" :key="dict.code" :label="dict.name" :value="dict.code" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="季度" prop="statQuarter">
                <el-select v-model="queryParams.statQuarter" placeholder="请选择季度" :clearable="false">
                  <el-option v-for="dict in statQuarterOptions" :key="dict.code" :label="dict.name"
                    :value="dict.code" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="单位" prop="orgCode">
                <org-selects-inside style="width:100%" placeholderText="请选择单位" :defaultType="true" ref="queryOrgSelect"
                  :isclearable=false :orgTreeList="orgTreeList" @handleOrgChange="handleOrgChange"
                  :currentOrgValue="queryParams.orgCode"></org-selects-inside>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="周数" prop="statWeek">
                <el-select v-model="queryParams.statWeek" placeholder="请选择周数" :clearable="false">
                  <el-option v-for="dict in weekQuarterOptions" :key="dict" :label="dict" :value="dict" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="数据状态" prop="dataStatus">
                <el-select v-model="queryParams.dataStatus" placeholder="请选择数据状态" clearable>
                  <el-option v-for="dict in dataStatusOptions" :key="dict.code" :label="dict.name" :value="dict.code" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="18" style="flex-grow: 1;">
              <el-form-item align="right">
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5" v-hasPermi="['pasEpidemicPreventionReport:add']">
        <el-button type="primary" icon="Plus" @click="handleUpdate"
          :disabled="single || dataStatusFlag || lastLevelFlag"
          v-hasPermi="['pasEpidemicPreventionReport:add']">填报</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasEpidemicPreventionReport:submitCompanyByIds']">
        <el-button icon="Top" :disabled="single || dataStatusFlag" @click="companyDataCommitBtn"
          v-hasPermi="['pasEpidemicPreventionReport:submitCompanyByIds']">分公司提交</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasEpidemicPreventionReport:backCompanyByIds']">
        <el-button icon="Bottom" :disabled="single" @click="companyDataBackBtn"
          v-hasPermi="['pasEpidemicPreventionReport:backCompanyByIds']">分公司退回</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasEpidemicPreventionReport:submitFarmByIds']">
        <el-button icon="Top" :disabled="single || dataStatusFlag" @click="farmDataCommitBtn"
          v-hasPermi="['pasEpidemicPreventionReport:submitFarmByIds']">农场提交</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasEpidemicPreventionReport:backFarmByIds']">
        <el-button icon="Bottom" :disabled="single" @click="farmDataBackBtn"
          v-hasPermi="['pasEpidemicPreventionReport:backFarmByIds']">农场退回</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasEpidemicPreventionReport:submitManageByIds']">
        <el-button icon="Top" :disabled="single || dataStatusFlag" @click="precinctDataCommitBtn"
          v-hasPermi="['pasEpidemicPreventionReport:submitManageByIds']">管理区提交</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasEpidemicPreventionReport:backManageByIds']">
        <el-button icon="Bottom" :disabled="single" @click="precinctDataBackBtn"
          v-hasPermi="['pasEpidemicPreventionReport:backManageByIds']">管理区退回</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasEpidemicPreventionReport:lock']">
        <el-button icon="Lock" @click="optionReportWin(3)"
          v-hasPermi="['pasEpidemicPreventionReport:lock']">锁定</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasEpidPreMaterialReserve:exceldownload']">
        <el-button plain icon="Download" @click="handleExport"
          v-hasPermi="['pasEpidPreMaterialReserve:exceldownload']">导出
        </el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button :disabled="single" @click="forwardFinish" icon="Stamp">提前完成防疫</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button v-if="flagShow && flagStatus1 != true" icon="Top" @click="goBack">返回上级</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-checkbox v-model="checkedItem1" style="padding-left: 15px">口蹄疫</el-checkbox>
        <el-checkbox v-model="checkedItem2">禽流感</el-checkbox>
        <el-checkbox v-model="checkedItem3" v-if="tableShow === '1'">猪瘟</el-checkbox>
        <!-- <el-checkbox v-model="checkedItem4" v-if="tableShow === '1' && statQuarterFlag">高致病性猪蓝耳病</el-checkbox>
        <el-checkbox v-model="checkedItem5" v-if="tableShow === '1' && statQuarterFlag">新城疫</el-checkbox> -->
        <el-checkbox v-model="checkedItem6">布鲁氏菌病</el-checkbox>
      </el-col>

      <right-toolbar v-model:showSearch="showSearch" @queryTable="resetQuery"></right-toolbar>
    </el-row>
    <div class="top_title">{{ labelForTime }}</div>
    <el-table v-show="tableFlag && tableShow === '1'" :data="pasEpidemicPreventionReportList" stripe ref="multipleTable"
      :key="tableKey" :height="tableHeight" @selection-change="handleSelectionChange"
      :row-class-name="tableRowClassName">
      <!-- <el-table-column :label="labelForTime" align="center"></el-table-column> -->
      <el-table-column type="selection" width="55" align="center" :selectable="selectable"  />
      <el-table-column label="市、县、区" align="center" prop="orgName" width="140px" fixed="left" />
      <el-table-column label="口蹄疫" align="center" v-if="checkedItem1">
        <el-table-column label="疫苗使用情况" align="center">
          <el-table-column label="口蹄疫疫苗总计" align="center" prop="fmdVaccSum" v-if="columnShowMap.fmdVaccSum">
          </el-table-column>
          <el-table-column label="口蹄疫O型灭活疫苗(万毫升)" align="center">
            <el-table-column label="猪" align="center" prop="fmdPigInactVaccNum"
              v-if="columnShowMap.fmdPigInactVaccNum" />
          </el-table-column>
          <el-table-column label="口蹄疫O型合成肽疫苗(万毫升)" align="center">
            <el-table-column label="猪" v-if="columnShowMap.fmdPigSpvVaccNum" align="center" prop="fmdPigSpvVaccNum" />
          </el-table-column>

          <el-table-column label="口蹄疫O型-A型二价灭活疫苗(万毫升)" align="center">
            <el-table-column label="牛(不含奶牛)" align="center" v-if="columnShowMap.fmdBeefVaccNum" prop="fmdBeefVaccNum" />
            <el-table-column label="牛(含奶牛)" align="center" v-if="columnShowMap.fmdBeefAndMilkbeefVaccNum"
              prop="fmdBeefAndMilkbeefVaccNum" />
            <el-table-column label="羊" align="center" v-if="columnShowMap.fmdSheepVaccNum" prop="fmdSheepVaccNum" />
            <el-table-column label="其他畜" align="center" v-if="columnShowMap.fmdOtherVaccNum" prop="fmdOtherVaccNum" />
          </el-table-column>
          <el-table-column label="口蹄疫O型-A型二价灭活疫苗(万毫升)" align="center">
            <el-table-column label="奶牛" align="center" v-if="columnShowMap.fmdMilkbeefVaccNum"
              prop="fmdMilkbeefVaccNum" />
          </el-table-column>
        </el-table-column>
        <el-table-column label="免疫情况" align="center">
          <el-table-column label="口蹄疫免疫总计" align="center" v-if="columnShowMap.fmdImmuneSum" prop="fmdImmuneSum" />
          <el-table-column label="猪口蹄疫免疫合计" align="center" v-if="columnShowMap.fmdPigImmuneSum"
            prop="fmdPigImmuneSum" />
          <el-table-column label="口蹄疫O型灭活疫苗(万头)" align="center">
            <el-table-column label="猪" align="center" v-if="columnShowMap.fmdPigImmuneNum" prop="fmdPigImmuneNum" />
          </el-table-column>
          <el-table-column label="口蹄疫O型合成肽疫苗(万头)" align="center">
            <el-table-column label="猪" align="center" v-if="columnShowMap.fmdPigSpvImmuneNum"
              prop="fmdPigSpvImmuneNum" />
          </el-table-column>
          <el-table-column label="牛口蹄疫免疫合计" align="center" v-if="columnShowMap.fmdBeefImmuneSum"
            prop="fmdBeefImmuneSum" />
          <el-table-column label="口蹄疫O型-A型二价灭活疫苗(万头)" align="center">
            <el-table-column label="牛(不含奶牛)" align="center" v-if="columnShowMap.fmdBeefImmuneNum"
              prop="fmdBeefImmuneNum" />
            <el-table-column label="牛(含奶牛)" align="center" v-if="columnShowMap.fmdBeefAndMilkbeefImmuneNum"
              prop="fmdBeefAndMilkbeefImmuneNum" />
            <el-table-column label="羊" align="center" v-if="columnShowMap.fmdSheepImmuneNum" prop="fmdSheepImmuneNum" />
            <el-table-column label="其他畜" align="center" v-if="columnShowMap.fmdOtherImmuneNum"
              prop="fmdOtherImmuneNum" />
          </el-table-column>
          <el-table-column label="口蹄疫O型-A型二价灭活疫苗(万头)" align="center">
            <el-table-column label="奶牛" align="center" v-if="columnShowMap.fmdMilkbeefImmuneNum"
              prop="fmdMilkbeefImmuneNum" />
          </el-table-column>
        </el-table-column>
      </el-table-column>

      <el-table-column label="禽流感" align="center" v-if="checkedItem2">
        <el-table-column label="疫苗使用情况" align="center">
          <el-table-column label="禽流感疫苗合计" align="center" v-if="columnShowMap.aiVaccSum" prop="aiVaccSum" />
          <el-table-column label="重组禽流感病毒(H5+H7)三价灭活疫苗(万毫升)" align="center">
            <!-- <el-table-column label="重组禽流感病毒(H5+H7)三价灭活疫苗(H5N1 Re-11株+Re-12株，H7N9 H7-Re3株)(万毫升)" align="center"> -->
            <el-table-column label="鸡" align="center" v-if="columnShowMap.aiChickenInactVacc"
              prop="aiChickenInactVacc" />
            <el-table-column label="鸭" align="center" v-if="columnShowMap.aiDuckInactVacc" prop="aiDuckInactVacc" />
            <el-table-column label="鹅" align="center" v-if="columnShowMap.aiGooseInactVacc" prop="aiGooseInactVacc" />
            <el-table-column label="其他禽" align="center" v-if="columnShowMap.aiOtherInactVacc" prop="aiOtherInactVacc" />
          </el-table-column>
        </el-table-column>
        <el-table-column label="免疫情况" align="center">
          <el-table-column label="禽流感免疫合计" align="center" v-if="columnShowMap.aiImmuneSum" prop="aiImmuneSum" />
          <el-table-column label="重组禽流感病毒(H5+H7)三价免疫数(万羽)" align="center">
            <el-table-column label="鸡" align="center" v-if="columnShowMap.aiChickenImmuneNum"
              prop="aiChickenImmuneNum" />
            <el-table-column label="鸭" align="center" v-if="columnShowMap.aiDuckImmuneNum" prop="aiDuckImmuneNum" />
            <el-table-column label="鹅" align="center" v-if="columnShowMap.aiGooseImmuneNum" prop="aiGooseImmuneNum" />
            <el-table-column label="其他禽" align="center" v-if="columnShowMap.aiOtherImmuneNum" prop="aiOtherImmuneNum" />
          </el-table-column>
        </el-table-column>
      </el-table-column>

      <el-table-column label="猪瘟" align="center" v-if="checkedItem3">
        <!-- <el-table-column label="免疫情况" align="center"> -->
        <el-table-column label="疫苗使用情况" align="center">
          <!-- <el-table-column label="猪瘟活疫苗(万头份)" align="center" prop="sfInactVacc" /> -->
          <el-table-column label="猪瘟疫苗合计" align="center" v-if="columnShowMap.sfInactVaccSum" prop="sfInactVaccSum" />
          <el-table-column label="猪瘟活疫苗(细胞源)(万头份)" align="center" v-if="columnShowMap.sfCsInactVaccNum"
            prop="sfCsInactVaccNum" />
          <el-table-column label="猪瘟活疫苗(传代细胞源)(万头份)" align="center" v-if="columnShowMap.sfPcsInactVaccNum"
            prop="sfPcsInactVaccNum" />
          <el-table-column label="猪瘟活疫苗(脾淋源)(万头份)" align="center" v-if="columnShowMap.sfSdsInactVaccNum"
            prop="sfSdsInactVaccNum" />
          <el-table-column label="猪瘟耐热保护剂活疫苗(细胞源)(万头份)" align="center" v-if="columnShowMap.sfLvcsfhrpaInactVacc"
            prop="sfLvcsfhrpaInactVacc" />
        </el-table-column>
        <el-table-column label="免疫情况" align="center">
          <el-table-column label="猪瘟免疫合计" align="center" v-if="columnShowMap.sfImmuneSum" prop="sfImmuneSum" />
          <el-table-column label="猪瘟活疫苗(细胞源)(万头)" align="center" v-if="columnShowMap.sfCsImmuneNum"
            prop="sfCsImmuneNum" />
          <el-table-column label="猪瘟活疫苗(传代细胞源)(万头)" align="center" v-if="columnShowMap.sfPcsImmuneNum"
            prop="sfPcsImmuneNum" />
          <el-table-column label="猪瘟活疫苗(脾淋源)(万头)" align="center" v-if="columnShowMap.sfSdsImmuneNum"
            prop="sfSdsImmuneNum" />
          <el-table-column label="猪瘟耐热保护疫苗(细胞源)(万头)" align="center" v-if="columnShowMap.sfLvcsfhrpaImmuneNum"
            prop="sfLvcsfhrpaImmuneNum" />
        </el-table-column>
        <!-- </el-table-column> -->
      </el-table-column>
      <el-table-column label="布鲁氏菌病" align="center" v-if="checkedItem6">
        <!-- <el-table-column label="免疫情况" align="center"> -->
        <el-table-column label="疫苗使用情况" align="center">
          <el-table-column label="布病疫苗牛合计" align="center" v-if="columnShowMap.bruceVaccSum" prop="bruceVaccSum" />
          <el-table-column label="布病活疫苗(S2株)(万头份)" align="center" v-if="columnShowMap.bruceS2ActVaccNum"
            prop="bruceS2ActVaccNum" />
          <el-table-column label="布病活疫苗(A19株)(万头份)奶牛" align="center" v-if="columnShowMap.bruceA19ActVaccNum"
            prop="bruceA19ActVaccNum" />
          <el-table-column label="布病活疫苗(A19株)(万头份)" align="center" v-if="columnShowMap.bruceA19ActVaccMilkBeefNum"
            prop="bruceA19ActVaccMilkBeefNum" />
        </el-table-column>
        <el-table-column label="免疫情况" align="center">
          <el-table-column label="布病免疫总计" align="center" v-if="columnShowMap.bruceImmuneSum" prop="bruceImmuneSum" />
          <el-table-column label="布病免疫牛合计" align="center" v-if="columnShowMap.bruceBeefImmuneSum"
            prop="bruceBeefImmuneSum" />
          <el-table-column label="布病(S2株)免疫数(万头)" align="center">
            <el-table-column label="牛(不含奶牛)" align="center" v-if="columnShowMap.bruceS2BeefNoMilkbeefImmuneNum"
              prop="bruceS2BeefNoMilkbeefImmuneNum" />
          </el-table-column>
          <el-table-column label="布病(A19株)免疫数(万头)" align="center">
            <el-table-column label="奶牛" align="center" v-if="columnShowMap.bruceA19MilkbeefImmuneNum"
              prop="bruceA19MilkbeefImmuneNum" />
          </el-table-column>
          <el-table-column label="布病(S2株)免疫数(万头)" align="center">
            <el-table-column label="牛" align="center" v-if="columnShowMap.bruceS2BeefImmuneNum"
              prop="bruceS2BeefImmuneNum" />
            <el-table-column label="牛(含奶牛)" align="center" v-if="columnShowMap.bruceS2BeefAndMilkbeefImmuneNum"
              prop="bruceS2BeefAndMilkbeefImmuneNum" />
            <el-table-column label="羊" align="center" v-if="columnShowMap.bruceS2SheepImmuneNum"
              prop="bruceS2SheepImmuneNum" />
          </el-table-column>
          <el-table-column label="布病(A19株)免疫数(万头)" align="center">
            <el-table-column label="牛(含奶牛)" align="center" v-if="columnShowMap.bruceA19BeefAndMilkbeefImmuneNum"
              prop="bruceA19BeefAndMilkbeefImmuneNum" />
          </el-table-column>
        </el-table-column>
        <!-- </el-table-column> -->
      </el-table-column>

      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="数据状态" align="center" prop="dataStatus" :formatter="dataStatusFormat" fixed="right" />
      <!--      <el-table-column label="操作" align="center" v-if="!((flagStatus1 == true && ooorgCode.length == 10)||(flagStatus2 == true && ooorgCode.length == 6)||(flagStatus3 == true && ooorgCode.length == 2)||(flagStatus3 == true && ooorgCode.length == 4))" fixed="right" class-name="small-padding fixed-width" width="90">-->
      <!--        <template #default="scope">-->
      <!--          <el-button-->
      <!--            text-->
      <!--            @click="toDetail(scope.row)"-->
      <!--            v-if="!(scope.row.orgCode.length == 2 || (flagStatus1 && ooorgCode.length == 2 && scope.row.orgCode.length == 2)||(flagStatus2&& ooorgCode.length==2&& scope.row.orgCode.length == 4)||(flagStatus2&& ooorgCode.length==4&& scope.row.orgCode.length == 4)||scope.row.childCount == 0 || scope.row.childCount == null)"-->
      <!--          >查看明细</el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="操作" align="center"
        v-if="!(flagStatus3 == true || (flagStatus1 == true && ooorgCode.length == 10) || (flagStatus1 == true && ooorgCode.length == 6) || (flagStatus2 && ooorgCode.length == 4) || (flagStatus2 && ooorgCode.length == 10) || (flagStatus2 && ooorgCode.length == 6))"
        fixed="right" class-name="small-padding fixed-width" width="90">
        <template #default="scope">
          <el-button link type="primary" @click="toDetail(scope.row)"
            v-if="!((flagStatus1 && scope.row.orgCode.length == 2) || (flagStatus1 && ooorgCode.length == 4 && scope.row.orgCode.length == 4) || (flagStatus2 && ooorgCode.length == 2 && scope.row.orgCode.length == 4) || scope.row.orgCode.length == 12 || scope.row.childCount == 0 || scope.row.childCount == null)">查看明细</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-table v-show="tableFlag && tableShow === '2'" :data="pasEpidemicPreventionReportList" stripe ref="multipleTable"
      :key="tableKey" :height="tableHeight" @selection-change="handleSelectionChange"
      :row-class-name="tableRowClassName">
      <!-- <el-table-column :label="labelForTime" align="center"></el-table-column> -->
      <el-table-column type="selection" width="55" align="center" :selectable="selectable" />
      <el-table-column label="市、县、区" align="center" prop="orgName" width="140px" fixed="left" />
      <el-table-column label="口蹄疫" align="center" v-if="checkedItem1">
        <el-table-column label="疫苗使用情况" align="center">
          <el-table-column label="口蹄疫疫苗总计" align="center" prop="fmdVaccSum" v-if="columnShowMap.fmdVaccSum">
          </el-table-column>
          <el-table-column label="口蹄疫O型灭活疫苗(万毫升)" align="center">
            <el-table-column label="猪" align="center" prop="fmdPigInactVaccNum"
              v-if="columnShowMap.fmdPigInactVaccNum" />
          </el-table-column>
          <el-table-column label="口蹄疫O型合成肽疫苗(万毫升)" align="center">
            <el-table-column label="猪" v-if="columnShowMap.fmdPigSpvVaccNum" align="center" prop="fmdPigSpvVaccNum" />
          </el-table-column>

          <el-table-column label="口蹄疫O型-A型二价灭活疫苗(万毫升)" align="center">
            <el-table-column label="牛(不含奶牛)" align="center" v-if="columnShowMap.fmdBeefVaccNum" prop="fmdBeefVaccNum" />
            <el-table-column label="牛(含奶牛)" align="center" v-if="columnShowMap.fmdBeefAndMilkbeefVaccNum"
              prop="fmdBeefAndMilkbeefVaccNum" />
            <el-table-column label="羊" align="center" v-if="columnShowMap.fmdSheepVaccNum" prop="fmdSheepVaccNum" />
            <el-table-column label="其他畜" align="center" v-if="columnShowMap.fmdOtherVaccNum" prop="fmdOtherVaccNum" />
          </el-table-column>
          <el-table-column label="口蹄疫O型-A型二价灭活疫苗(万毫升)" align="center">
            <el-table-column label="奶牛" align="center" v-if="columnShowMap.fmdMilkbeefVaccNum"
              prop="fmdMilkbeefVaccNum" />
          </el-table-column>
        </el-table-column>
        <el-table-column label="免疫情况" align="center">
          <el-table-column label="口蹄疫免疫总计" align="center" v-if="columnShowMap.fmdImmuneSum" prop="fmdImmuneSum" />
          <el-table-column label="猪口蹄疫免疫合计" align="center" v-if="columnShowMap.fmdPigImmuneSum"
            prop="fmdPigImmuneSum" />
          <el-table-column label="口蹄疫O型灭活疫苗(万头)" align="center">
            <el-table-column label="猪" align="center" v-if="columnShowMap.fmdPigImmuneNum" prop="fmdPigImmuneNum" />
          </el-table-column>
          <el-table-column label="口蹄疫O型合成肽疫苗(万头)" align="center">
            <el-table-column label="猪" align="center" v-if="columnShowMap.fmdPigSpvImmuneNum"
              prop="fmdPigSpvImmuneNum" />
          </el-table-column>
          <el-table-column label="牛口蹄疫免疫合计" align="center" v-if="columnShowMap.fmdBeefImmuneSum"
            prop="fmdBeefImmuneSum" />
          <el-table-column label="口蹄疫O型-A型二价灭活疫苗(万头)" align="center">
            <el-table-column label="牛(不含奶牛)" align="center" v-if="columnShowMap.fmdBeefImmuneNum"
              prop="fmdBeefImmuneNum" />
            <el-table-column label="牛(含奶牛)" align="center" v-if="columnShowMap.fmdBeefAndMilkbeefImmuneNum"
              prop="fmdBeefAndMilkbeefImmuneNum" />
            <el-table-column label="羊" align="center" v-if="columnShowMap.fmdSheepImmuneNum" prop="fmdSheepImmuneNum" />
            <el-table-column label="其他畜" align="center" v-if="columnShowMap.fmdOtherImmuneNum"
              prop="fmdOtherImmuneNum" />
          </el-table-column>
          <el-table-column label="口蹄疫O型-A型二价灭活疫苗(万头)" align="center">
            <el-table-column label="奶牛" align="center" v-if="columnShowMap.fmdMilkbeefImmuneNum"
              prop="fmdMilkbeefImmuneNum" />
          </el-table-column>
        </el-table-column>
      </el-table-column>

      <el-table-column label="禽流感" align="center" v-if="checkedItem2">
        <el-table-column label="疫苗使用情况" align="center">
          <el-table-column label="禽流感疫苗合计" align="center" v-if="columnShowMap.aiVaccSum" prop="aiVaccSum" />
          <el-table-column label="重组禽流感病毒(H5+H7)三价灭活疫苗(万毫升)" align="center">
            <!-- <el-table-column label="重组禽流感病毒(H5+H7)三价灭活疫苗(H5N1 Re-11株+Re-12株，H7N9 H7-Re3株)(万毫升)" align="center"> -->
            <el-table-column label="鸡" align="center" v-if="columnShowMap.aiChickenInactVacc"
              prop="aiChickenInactVacc" />
            <el-table-column label="鸭" align="center" v-if="columnShowMap.aiDuckInactVacc" prop="aiDuckInactVacc" />
            <el-table-column label="鹅" align="center" v-if="columnShowMap.aiGooseInactVacc" prop="aiGooseInactVacc" />
            <el-table-column label="其他禽" align="center" v-if="columnShowMap.aiOtherInactVacc" prop="aiOtherInactVacc" />
          </el-table-column>
        </el-table-column>
        <el-table-column label="免疫情况" align="center">
          <el-table-column label="禽流感免疫合计" align="center" v-if="columnShowMap.aiImmuneSum" prop="aiImmuneSum" />
          <el-table-column label="重组禽流感病毒(H5+H7)三价免疫数(万羽)" align="center">
            <el-table-column label="鸡" align="center" v-if="columnShowMap.aiChickenImmuneNum"
              prop="aiChickenImmuneNum" />
            <el-table-column label="鸭" align="center" v-if="columnShowMap.aiDuckImmuneNum" prop="aiDuckImmuneNum" />
            <el-table-column label="鹅" align="center" v-if="columnShowMap.aiGooseImmuneNum" prop="aiGooseImmuneNum" />
            <el-table-column label="其他禽" align="center" v-if="columnShowMap.aiOtherImmuneNum" prop="aiOtherImmuneNum" />
          </el-table-column>
        </el-table-column>
      </el-table-column>

      <el-table-column label="猪瘟" align="center" v-if="checkedItem3">
        <!-- <el-table-column label="免疫情况" align="center"> -->
        <el-table-column label="疫苗使用情况" align="center">
          <!-- <el-table-column label="猪瘟活疫苗(万头份)" align="center" prop="sfInactVacc" /> -->
          <el-table-column label="猪瘟疫苗合计" align="center" v-if="columnShowMap.sfInactVaccSum" prop="sfInactVaccSum" />
          <el-table-column label="猪瘟活疫苗(细胞源)(万头份)" align="center" v-if="columnShowMap.sfCsInactVaccNum"
            prop="sfCsInactVaccNum" />
          <el-table-column label="猪瘟活疫苗(传代细胞源)(万头份)" align="center" v-if="columnShowMap.sfPcsInactVaccNum"
            prop="sfPcsInactVaccNum" />
          <el-table-column label="猪瘟活疫苗(脾淋源)(万头份)" align="center" v-if="columnShowMap.sfSdsInactVaccNum"
            prop="sfSdsInactVaccNum" />
          <el-table-column label="猪瘟耐热保护剂活疫苗(细胞源)(万头份)" align="center" v-if="columnShowMap.sfLvcsfhrpaInactVacc"
            prop="sfLvcsfhrpaInactVacc" />
        </el-table-column>
        <el-table-column label="免疫情况" align="center">
          <el-table-column label="猪瘟免疫合计" align="center" v-if="columnShowMap.sfImmuneSum" prop="sfImmuneSum" />
          <el-table-column label="猪瘟活疫苗(细胞源)(万头)" align="center" v-if="columnShowMap.sfCsImmuneNum"
            prop="sfCsImmuneNum" />
          <el-table-column label="猪瘟活疫苗(传代细胞源)(万头)" align="center" v-if="columnShowMap.sfPcsImmuneNum"
            prop="sfPcsImmuneNum" />
          <el-table-column label="猪瘟活疫苗(脾淋源)(万头)" align="center" v-if="columnShowMap.sfSdsImmuneNum"
            prop="sfSdsImmuneNum" />
          <el-table-column label="猪瘟耐热保护疫苗(细胞源)(万头)" align="center" v-if="columnShowMap.sfLvcsfhrpaImmuneNum"
            prop="sfLvcsfhrpaImmuneNum" />
        </el-table-column>
        <!-- </el-table-column> -->
      </el-table-column>
      <el-table-column label="布鲁氏菌病" align="center" v-if="checkedItem6">
        <!-- <el-table-column label="免疫情况" align="center"> -->
        <el-table-column label="疫苗使用情况" align="center">
          <el-table-column label="布病疫苗牛合计" align="center" v-if="columnShowMap.bruceVaccSum" prop="bruceVaccSum" />
          <el-table-column label="布病活疫苗(S2株)(万头份)" align="center" v-if="columnShowMap.bruceS2ActVaccNum"
            prop="bruceS2ActVaccNum" />
          <el-table-column label="布病活疫苗(A19株)(万头份)奶牛" align="center" v-if="columnShowMap.bruceA19ActVaccNum"
            prop="bruceA19ActVaccNum" />
          <el-table-column label="布病活疫苗(A19株)(万头份)" align="center" v-if="columnShowMap.bruceA19ActVaccMilkBeefNum"
            prop="bruceA19ActVaccMilkBeefNum" />
        </el-table-column>
        <el-table-column label="免疫情况" align="center">
          <el-table-column label="布病免疫总计" align="center" v-if="columnShowMap.bruceImmuneSum" prop="bruceImmuneSum" />
          <el-table-column label="布病免疫牛合计" align="center" v-if="columnShowMap.bruceBeefImmuneSum"
            prop="bruceBeefImmuneSum" />
          <el-table-column label="布病(S2株)免疫数(万头)" align="center">
            <el-table-column label="牛(不含奶牛)" align="center" v-if="columnShowMap.bruceS2BeefNoMilkbeefImmuneNum"
              prop="bruceS2BeefNoMilkbeefImmuneNum" />
          </el-table-column>
          <el-table-column label="布病(A19株)免疫数(万头)" align="center">
            <el-table-column label="奶牛" align="center" v-if="columnShowMap.bruceA19MilkbeefImmuneNum"
              prop="bruceA19MilkbeefImmuneNum" />
          </el-table-column>
          <el-table-column label="布病(S2株)免疫数(万头)" align="center">
            <el-table-column label="牛" align="center" v-if="columnShowMap.bruceS2BeefImmuneNum"
              prop="bruceS2BeefImmuneNum" />
            <el-table-column label="牛(含奶牛)" align="center" v-if="columnShowMap.bruceS2BeefAndMilkbeefImmuneNum"
              prop="bruceS2BeefAndMilkbeefImmuneNum" />
            <el-table-column label="羊" align="center" v-if="columnShowMap.bruceS2SheepImmuneNum"
              prop="bruceS2SheepImmuneNum" />
          </el-table-column>
          <el-table-column label="布病(A19株)免疫数(万头)" align="center">
            <el-table-column label="牛(含奶牛)" align="center" v-if="columnShowMap.bruceA19BeefAndMilkbeefImmuneNum"
              prop="bruceA19BeefAndMilkbeefImmuneNum" />
          </el-table-column>
        </el-table-column>
        <!-- </el-table-column> -->
      </el-table-column>

      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="数据状态" align="center" prop="dataStatus" :formatter="dataStatusFormat" fixed="right" />
      <!--      <el-table-column label="操作" align="center" v-if="!((flagStatus1 == true && ooorgCode.length == 10)||(flagStatus2 == true && ooorgCode.length == 6)||(flagStatus3 == true && ooorgCode.length == 2)||(flagStatus3 == true && ooorgCode.length == 4))" fixed="right" class-name="small-padding fixed-width" width="90">-->
      <!--        <template #default="scope">-->
      <!--          <el-button-->
      <!--            text-->
      <!--            @click="toDetail(scope.row)"-->
      <!--            v-if="!(scope.row.orgCode.length == 2 || (flagStatus1 && ooorgCode.length == 2 && scope.row.orgCode.length == 2)||(flagStatus2&& ooorgCode.length==2&& scope.row.orgCode.length == 4)||(flagStatus2&& ooorgCode.length==4&& scope.row.orgCode.length == 4)||scope.row.childCount == 0 || scope.row.childCount == null)"-->
      <!--          >查看明细</el-button>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <el-table-column label="操作" align="center"
        v-if="!(flagStatus3 == true || (flagStatus1 == true && ooorgCode.length == 10) || (flagStatus1 == true && ooorgCode.length == 6) || (flagStatus2 && ooorgCode.length == 4) || (flagStatus2 && ooorgCode.length == 10) || (flagStatus2 && ooorgCode.length == 6))"
        fixed="right" class-name="small-padding fixed-width" width="90">
        <template #default="scope">
          <el-button link type="primary" @click="toDetail(scope.row)"
            v-if="!((flagStatus1 && scope.row.orgCode.length == 2) || (flagStatus1 && ooorgCode.length == 4 && scope.row.orgCode.length == 4) || (flagStatus2 && ooorgCode.length == 2 && scope.row.orgCode.length == 4) || scope.row.orgCode.length == 12 || scope.row.childCount == 0 || scope.row.childCount == null)">查看明细</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-if="tableFlag" v-show="total > 0" :total="total" v-model:page="queryParams.page"
      v-model:limit="queryParams.rows" @pagination="switchList" />

    <!-- 添加或修改春/秋季防疫周报对话框 -->
    <el-dialog title="春/秋季防疫周报填写" v-model="open" :close-on-click-modal="false" width="1200px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="340px">
        <el-tabs v-model="first" type="card">
          <el-tab-pane label="口蹄疫" name="first">
            <!-- <el-form ref="formRef" :model="form" :rules="rules" label-width="340px"> -->
            <el-divider content-position="center">疫苗使用情况</el-divider>
            <el-row>
              <el-col :span="12"  v-if="columnShowMap.fmdPigInactVaccNum">
                <el-form-item label="口蹄疫O型灭活疫苗(万毫升)-猪" prop="fmdPigInactVaccNum" label-width="340px">
                  <el-input v-model="form.fmdPigInactVaccNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="columnShowMap.fmdPigSpvVaccNum">
                <el-form-item label="口蹄疫O型合成肽疫苗(万毫升)-猪" prop="fmdPigSpvVaccNum" label-width="340px">
                  <el-input v-model="form.fmdPigSpvVaccNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row style="display: flex;flex-wrap: wrap;">
              <el-col :span="12" v-if="columnShowMap.fmdBeefVaccNum">
                <el-form-item label="口蹄疫O型-A型二价灭活疫苗(万毫升)-牛(不含奶牛)" prop="fmdBeefVaccNum" label-width="340px">
                  <el-input v-model="form.fmdBeefVaccNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
               <el-col :span="12" v-if="columnShowMap.fmdBeefAndMilkbeefVaccNum">
                <el-form-item label="口蹄疫O型-A型二价灭活疫苗(万毫升)-牛(含奶牛)" prop="fmdBeefAndMilkbeefVaccNum" label-width="340px">
                  <el-input v-model="form.fmdBeefAndMilkbeefVaccNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="columnShowMap.fmdSheepVaccNum">
                <el-form-item    
                  label="口蹄疫O型-A型二价灭活疫苗(万毫升)-羊"
                  prop="fmdSheepVaccNum" label-width="340px">
                  <el-input v-model="form.fmdSheepVaccNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="columnShowMap.fmdOtherVaccNum">
                <el-form-item      
                  :label="'口蹄疫O型-A型二价灭活疫苗-其他畜'"
                  prop="fmdOtherVaccNum" label-width="340px">
                  <el-input v-model="form.fmdOtherVaccNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12" v-if="columnShowMap.fmdMilkbeefVaccNum">
                <el-form-item
                  :label="'口蹄疫O型-A型二价灭活疫苗(万毫升)-奶牛'"
                  prop="fmdMilkbeefVaccNum" label-width="340px">
                  <el-input v-model="form.fmdMilkbeefVaccNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-divider content-position="center">疫苗免疫情况</el-divider>
            <el-row style="display: flex;flex-wrap: wrap;">
              <el-col :span="12" v-if="columnShowMap.fmdPigImmuneNum">
                <el-form-item
                  :label="'口蹄疫O型灭活疫苗-猪'"
                  prop="fmdPigImmuneNum" label-width="340px">
                  <el-input v-model="form.fmdPigImmuneNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="columnShowMap.fmdPigSpvImmuneNum">
                <el-form-item
                  :label="'口蹄疫O型合成肽疫苗-猪'"
                  prop="fmdPigSpvImmuneNum" label-width="340px">
                  <el-input v-model="form.fmdPigSpvImmuneNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="columnShowMap.fmdBeefImmuneNum">
                <el-form-item label="口蹄疫O型-A型二价灭活疫苗(万头)-牛(不含奶牛)" prop="fmdBeefImmuneNum" label-width="340px">
                  <el-input v-model="form.fmdBeefImmuneNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="columnShowMap.fmdBeefAndMilkbeefImmuneNum">
                <el-form-item label="口蹄疫O型-A型二价灭活疫苗(万头)-牛(含奶牛)" prop="fmdBeefAndMilkbeefImmuneNum" label-width="340px">
                  <el-input v-model="form.fmdBeefAndMilkbeefImmuneNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="columnShowMap.fmdSheepImmuneNum">
                <el-form-item
                  :label="'口蹄疫O型-A型二价灭活疫苗-羊'"
                  prop="fmdSheepImmuneNum" label-width="340px">
                  <el-input v-model="form.fmdSheepImmuneNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="columnShowMap.fmdOtherImmuneNum">
                <el-form-item
                  :label="'口蹄疫O型-A型二价灭活疫苗-其他畜'"
                  prop="fmdOtherImmuneNum" label-width="340px">
                  <el-input v-model="form.fmdOtherImmuneNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12" v-if="columnShowMap.fmdMilkbeefImmuneNum">
                <el-form-item
                  :label="'口蹄疫O型-A型二价灭活疫苗(万头份)-奶牛'"
                  prop="fmdMilkbeefImmuneNum" label-width="340px">
                  <el-input v-model="form.fmdMilkbeefImmuneNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="禽流感" name="second">
            <el-divider content-position="left">疫苗使用情况-重组禽流感病毒(H5+H7)三价灭活疫苗(万毫升)</el-divider>
            <el-row>
              <el-col :span="12" v-if="columnShowMap.aiChickenInactVaccNum">
                <el-form-item label="鸡" prop="aiChickenInactVaccNum" label-width="280px">
                  <el-input v-model="form.aiChickenInactVaccNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="columnShowMap.aiDuckInactVaccNum">
                <el-form-item label="鸭" prop="aiDuckInactVaccNum" label-width="280px">
                  <el-input v-model="form.aiDuckInactVaccNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12"  v-if="columnShowMap.aiGooseInactVaccNum">
                <el-form-item label="鹅" prop="aiGooseInactVaccNum" label-width="280px">
                  <el-input v-model="form.aiGooseInactVaccNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="columnShowMap.aiOtherInactVaccNum">
                <el-form-item label="其他禽" prop="aiOtherInactVaccNum" label-width="280px">
                  <el-input v-model="form.aiOtherInactVaccNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-divider content-position="left">疫苗免疫情况-重组禽流感病毒(H5+H7)三价免疫数(万羽)</el-divider>
            <el-row>
              <el-col :span="12" v-if="columnShowMap.aiChickenImmuneNum">
                <el-form-item label="鸡" prop="aiChickenImmuneNum" label-width="280px">
                  <el-input v-model="form.aiChickenImmuneNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="columnShowMap.aiDuckImmuneNum">
                <el-form-item label="鸭" prop="aiDuckImmuneNum" label-width="280px">
                  <el-input v-model="form.aiDuckImmuneNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12" v-if="columnShowMap.aiGooseImmuneNum">
                <el-form-item label="鹅" prop="aiGooseImmuneNum" label-width="280px">
                  <el-input v-model="form.aiGooseImmuneNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="columnShowMap.aiOtherImmuneNum">
                <el-form-item label="其他禽" prop="aiOtherImmuneNum" label-width="280px">
                  <el-input v-model="form.aiOtherImmuneNum" placeholder="请输入" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          <!-- v-if="form.statQuarter == '1'" -->
          <el-tab-pane label="猪瘟"  name="third">
            <el-divider content-position="left">疫苗免疫情况</el-divider>
            <el-form-item label="猪瘟活疫苗(细胞源)(万头份)" v-if="columnShowMap.sfCsInactVaccNum" prop="sfCsInactVaccNum" label-width="280px">
              <el-input v-model="form.sfCsInactVaccNum" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="猪瘟活疫苗(传代细胞源)(万头份)" v-if="columnShowMap.sfPcsInactVaccNum" prop="sfPcsInactVaccNum" label-width="280px">
              <el-input v-model="form.sfPcsInactVaccNum" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="猪瘟活疫苗(脾淋源)(万头份)" v-if="columnShowMap.sfSdsInactVaccNum" prop="sfSdsInactVaccNum" label-width="280px">
              <el-input v-model="form.sfSdsInactVaccNum" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="猪瘟耐热保护剂活疫苗(细胞源)(万头份)" v-if="columnShowMap.sfLvcsfhrpaInactVacc" prop="sfLvcsfhrpaInactVacc" label-width="280px">
              <el-input v-model="form.sfLvcsfhrpaInactVacc" placeholder="请输入" />
            </el-form-item>
            <el-divider content-position="left">免疫情况</el-divider>
            <el-form-item label="猪瘟活疫苗(细胞源)(万头)" v-if="columnShowMap.sfCsImmuneNum" prop="sfCsImmuneNum" label-width="280px">
              <el-input v-model="form.sfCsImmuneNum" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="猪瘟活疫苗(传代细胞源)(万头)" v-if="columnShowMap.sfPcsImmuneNum" prop="sfPcsImmuneNum" label-width="280px">
              <el-input v-model="form.sfPcsImmuneNum" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="猪瘟活疫苗(脾淋源)(万头)" v-if="columnShowMap.sfSdsImmuneNum" prop="sfSdsImmuneNum" label-width="280px">
              <el-input v-model="form.sfSdsImmuneNum" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="猪瘟耐热保护疫苗(细胞源)(万头)" v-if="columnShowMap.sfLvcsfhrpaImmuneNum"  prop="sfLvcsfhrpaImmuneNum" label-width="280px">
              <el-input v-model="form.sfLvcsfhrpaImmuneNum" placeholder="请输入" />
            </el-form-item>
            <!-- </el-form> -->
          </el-tab-pane>
          <el-tab-pane label="布鲁氏菌病" name="six">
            <el-divider content-position="left">疫苗使用情况</el-divider>
             <el-form-item label="布病活疫苗(S2株)(万头份)" v-if="columnShowMap.bruceS2ActVaccNum" prop="bruceS2ActVaccNum" label-width="280px">
              <el-input v-model="form.bruceS2ActVaccNum" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="布病活疫苗(A19株)(万头份)奶牛" v-if="columnShowMap.bruceA19ActVaccNum" prop="bruceA19ActVaccNum" label-width="280px">
              <el-input v-model="form.bruceA19ActVaccNum" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="布病活疫苗(A19株)(万头份)"  v-if="columnShowMap.bruceA19ActVaccMilkBeefNum" prop="bruceA19ActVaccMilkBeefNum" label-width="280px">
              <el-input v-model="form.bruceA19ActVaccMilkBeefNum" placeholder="请输入" />
            </el-form-item>
            <el-divider content-position="left">免疫情况</el-divider>
             <el-form-item label="布病(S2株)免疫数(万头)-牛(不含奶牛)" v-if="columnShowMap.bruceS2BeefNoMilkbeefImmuneNum" prop="bruceS2BeefNoMilkbeefImmuneNum" label-width="280px">
              <el-input v-model="form.bruceS2BeefNoMilkbeefImmuneNum" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="布病(A19株)免疫数(万头)-奶牛" v-if="columnShowMap.bruceA19MilkbeefImmuneNum" prop="bruceA19MilkbeefImmuneNum" label-width="280px">
              <el-input v-model="form.bruceA19MilkbeefImmuneNum" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="布病(S2株)免疫数(万头)-牛" v-if="columnShowMap.bruceS2BeefImmuneNum" prop="bruceS2BeefImmuneNum" label-width="280px">
              <el-input v-model="form.bruceS2BeefImmuneNum" placeholder="请输入" />
            </el-form-item>
             <el-form-item label="布病(S2株)免疫数(万头)-牛(含奶牛)" v-if="columnShowMap.bruceS2BeefAndMilkbeefImmuneNum" prop="bruceS2BeefAndMilkbeefImmuneNum" label-width="280px">
              <el-input v-model="form.bruceS2BeefAndMilkbeefImmuneNum" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="布病(S2株)免疫数(万头)-羊" v-if="columnShowMap.bruceS2SheepImmuneNum" prop="bruceS2SheepImmuneNum" label-width="280px">
              <el-input v-model="form.bruceS2SheepImmuneNum" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="布病(A19株)免疫数(万头)-牛(含奶牛)"  v-if="columnShowMap.bruceA19BeefAndMilkbeefImmuneNum" prop="bruceA19BeefAndMilkbeefImmuneNum" label-width="280px">
              <el-input v-model="form.bruceA19BeefAndMilkbeefImmuneNum" placeholder="请输入" />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 添加或修改奶牛生产月报填报展示对话框 -->
    <el-dialog title="操作提示" v-model="optionBtnOpen" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="formRemark" label-width="80px">
        <el-form-item label="备注">
          <el-input style="width: 300px" v-model="formSubmit.remark" placeholder="请输入备注" clearable
            @keyup.enter.native="handleQuery" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="optionReportSubmit">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="/report/pasEpidemicPrevFillingSit/queryByPage">
import { onMounted } from "vue";
import '@/views/sysanimalhusbandry/assets/styles/index.scss' // global css
import OrgSelectsInside from '@/views/sysanimalhusbandry/components/OrgSelectsInside/index'
import {
  listPasEpidemicPreventionReport, epidemicPreventionReportBack, epidemicPreventionReportLock, epidemicPreventionReportSubmit, queryWeekByParam, queryWeekByParamNew, queryByPageOneLevel, submitCompanyByIds, backCompanyByIds, submitManageByIds, backManageByIds, submitFarmByIds, backFarmByIds, prevFillStatus, prevFill,
  listPasEpidemicPreventionReportGrp, epidemicPreventionReportBackGrp, epidemicPreventionReportLockGrp, epidemicPreventionReportSubmitGrp, queryWeekByParamGrp, queryTemplateColumnByTemplate, queryByPageOneLevelGrpNew, submitCompanyByIdsGrp, backCompanyByIdsGrp, submitManageByIdsGrp, backManageByIdsGrp, submitFarmByIdsGrp, backFarmByIdsGrp, prevFillStatusGrp, prevFillGrp
} from "@/api/sysanimalhusbandry/report/pasEpidemicPreventionReport";
import { getPasEpidemicPreventionW, updatePasEpidemicPreventionW, getPasEpidemicPreventionWGrp, updatePasEpidemicPreventionWGrp, updatePasEpidemicPrevention } from '@/api/sysanimalhusbandry/report/pasEpidemicPreventionW'
import { listPasCowProduceReport } from "@/api/sysanimalhusbandry/report/pasCowProduceReport";
import { getDicts, getOrg } from "@/api/sysanimalhusbandry/dict";
import { updateButtonStyle, getInitTreeCode, getInitTreeName } from "@/api/sysanimalhusbandry/utils/cop";
import { nextTick, ref, defineExpose } from 'vue'
import { postForExcel } from '@/api/sysanimalhusbandry/excel'

const router = useRouter();
const {
  proxy
} = getCurrentInstance();

const env = import.meta.env
var validateNum = (rule, value, callback) => {
  var reg = /^[0-9]+(.?[0-9]{1,4})?$/ //整数
  if (value === null || value === '' || typeof (value) == 'undefined') {
    callback()
  } else if (value.toString().split('.')[0].length > 8) {
    callback(new Error('输入过长，请确认'))
  } else if (reg.test(Number(value))) {
    callback()
  } else {
    callback(new Error('请输入数字(可保留四位小数)'))
  }
}
const data = reactive({
  //提交表单
  formSubmit: {
    remark: '',
    ids: []
  },
  // 查询参数
  queryParams: {
    page: 1,
    rows: 10,
    statYear: String(new Date().getFullYear()),
    statQuarter: null,
    statWeek: null,
    orgCode: null,
    dataStatus: null,
  },
  // 表单参数
  form: {},
  // 表单校验
  rules: {
    fmdCowVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdPigImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdPigInactVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdPigSpvVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdBeefInactVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdSheepInactVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdOtherInactVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdPigSpvImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdCowImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdBeefImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdSheepImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdOtherImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    aiDuckInactVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    aiGooseInactVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    aiChickenInactVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    aiOtherInactVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    aiChickenImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    aiDuckImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    aiGooseImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    aiOtherImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    sfInactVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    sfCsInactVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    sfPcsInactVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    sfSdsInactVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    sfLvcsfhrpaInactVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    sfImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    sfCsImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    sfPcsImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    sfSdsImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    sfLvcsfhrpaImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    hppbedInactVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    hppbedActVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    hppbedInactImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    hppbedActImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    ndInactVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    ndAttenVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    ndActVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    ndAttenImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    ndActImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    ndInactImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    bruceBeefActVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    bruceCowActVacc: [{ required: false, validator: validateNum, trigger: 'blur' }],
    bruceCowImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    bruceBeefImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    bruceSheepImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
  },

})
const {
  formSubmit,
  queryParams,
  form,
  rules
} = toRefs(data);
// 存储后端返回的列显示配置
const columnConfigList = ref([])
// 映射表：key=columnName，value=showFlag是否为'1'
const columnShowMap = ref({})
const orgTreeList = ref([]);
const statYearOptions = ref([]);
const statMonthOptions = ref([]);
const dataStatusOptions = ref([]);
const pasEpidemicPreventionReportList = ref([]);
const ooorgCode = ref('');
const loading = ref(false);
const total = ref(0);
const open = ref(false);
const queryOrgSelect = ref(null);
const flagStatus1 = ref(true);
const flagStatus2 = ref(false);
const flagStatus3 = ref(false);
const flagStatus4 = ref(false);
const dataStatusFlag = ref(true);
const lastLevelFlag = ref(true);
const single = ref(true);
const ids = ref([]);
const dataStatus = ref([]);
const multiple = ref(false);
const flagShow = ref(false);
const showSearch = ref(true);
const title = ref('');
const rowDataObj = ref({});
const labelForTime = ref('');
const first = ref('');
const checkedItem1 = ref(true);
const checkedItem2 = ref(true);
const checkedItem3 = ref(true);
const checkedItem4 = ref(true);
const checkedItem5 = ref(true);
const checkedItem6 = ref(true);
const statYear = ref(null);
const statQuarter = ref(null);
const tableShow = ref('2');
const optionBtnOpen = ref(false);
const optionBtnType = ref('');
const statQuarterOptions = ref([]);
const weekQuarterOptions = ref([]);
const tableKey = ref(1);
const tableFlag = ref(true);
const statQuarterFlag = ref(false);
const currentYear = ref(String(new Date().getFullYear()));
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
/** 获取字典 */
const getOptions = () => {
  queryParams.value.statWeek = proxy.$route.query.statWeek;
  queryParams.value.statYear = proxy.$route.query.statYear;
  currentYear.value = proxy.$route.query.statYear;
  queryParams.value.statQuarter = proxy.$route.query.statQuarter;

  getListInitial();
  getDicts("data_status").then(response => {
    dataStatusOptions.value = response.data;
  });
  getDicts("stat_year").then(response => {
    statYearOptions.value = response.data;
  });
  getDicts("stat_month").then(response => {
    statMonthOptions.value = response.data;
  });
  getDicts("stat_quarter").then(response => {
    statQuarterOptions.value = response.data;
  });

  getOrg().then(response => {
    orgTreeList.value = response.data;
    queryParams.value.orgCode = proxy.$route.query.orgCode;
    ooorgCode.value = getInitTreeCode(orgTreeList.value)[getInitTreeCode(orgTreeList.value).length - 1]
  })
}

getOptions()
updateButtonStyle()
onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 300
    : window.innerHeight - 300;
  // 调用接口获取后端配置（实际项目中替换为真实接口请求）
  fetchColumnConfig();

});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 300
    : window.innerHeight - 300;
});
watch(checkedItem1, (newValue, oldValue) => {
  switchList()
})
watch(checkedItem2, (newValue, oldValue) => {
  switchList()
})
watch(checkedItem3, (newValue, oldValue) => {
  switchList()
})
watch(checkedItem4, (newValue, oldValue) => {
  switchList()
})
watch(checkedItem5, (newValue, oldValue) => {
  switchList()
})
watch(checkedItem6, (newValue, oldValue) => {
  switchList()
})
watch(queryParams.value.statYear, (newValue, oldValue) => {
  if (val != null && val !== ''
    && queryParams.value.orgCode != null && queryParams.value.orgCode !== ''
    && queryParams.value.statQuarter != null && queryParams.value.statQuarter !== ''
  ) {
    let params = { statYear: val, orgCode: queryParams.value.orgCode, statQuarter: queryParams.value.statQuarter }
    getWeekByParam(params);
  } else {
    weekQuarterOptions.value = []
  }
})
watch(queryParams.value.orgCode, (newValue, oldValue) => {
  if (
    val != null && val !== ''
    && queryParams.value.statYear != null && queryParams.value.statYear !== ''
    && queryParams.value.statQuarter != null && queryParams.value.statQuarter !== ''
  ) {
    let params = { statYear: queryParams.value.statYear, orgCode: val, statQuarter: queryParams.value.statQuarter }
    if (!Array.isArray(val)) {
      getWeekByParam(params);
    }

  } else {
    weekQuarterOptions.value = []
  }
})
// destroyed() {
//   window.removeEventListener('resize', () => {
//     _this.tableKey++
//   })
// }
function fetchColumnConfig() {
  let data = JSON.parse(JSON.stringify(queryParams.value))
  const params = {
    templateName: '防疫周报及防疫汇总',
    statQuarter: data.statQuarter,
    statYear: data.statYear
  }
  // 后端返回的数据（实际项目中从接口获取）
  queryTemplateColumnByTemplate(params).then(response => {
    // 存储配置数据
    columnConfigList.value = response.data;
    // 构建映射表：columnName -> 是否显示（showFlag === '1'）
    columnShowMap.value = response.data.reduce((map, item) => {
      map[item.columnName] = item.showFlag === '1';
      return map;
    }, {});
    console.log(columnShowMap.value);

  });
}
/** 导出按钮操作 */
function handleExport() {
  let exportUrl = tableShow.value === '1' ? `/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasEpidemicPreventionReport/exportExcel` : `/bdh-animal-husbandry-api/report/pasEpidemicPreventionGrpReport/exportExcel`
  postForExcel(exportUrl, queryParams.value, '防疫周报表');
}
function getWeekByParam(data) {
  queryWeekByParamNew(data).then(response => {
    weekQuarterOptions.value = response.data;
    console.log(111, response);

  });
}
function getchange (row){
  console.log(11111,row);
  
}
function selectable(row, index) {;
  if (row.dataId == 0) {
    return false
  }
  return true

}
function forwardFinish() {
  if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
    prevFillStatusGrp(ids.value[0]).then(response => {
      proxy.$modal.confirm((response.data.fillStatus == '1' ? '确定提前完成所有防疫任务么？如确认，后续周数将无法继续填报' : '注意：请谨慎操作，解除提前防疫可能造成部分数据丢失'), "提示").then(function () {
        prevFillGrp(ids.value[0]).then(response => {
          let { data, success } = response
          if (success) {
            switchList();
            proxy.$modal.msgSuccess("操作成功");
          }
        }).catch(onerror => {
        });
      })
    })
  } else {
    prevFillStatus(ids.value[0]).then(response => {
      proxy.$modal.confirm((response.data.fillStatus == '1' ? '确定提前完成所有防疫任务么？如确认，后续周数将无法继续填报' : '注意：请谨慎操作，解除提前防疫可能造成部分数据丢失'), "提示").then(function () {
        prevFill(_this.ids[0]).then(response => {
          let { data, success } = response
          if (success) {
            switchList();
            proxy.$modal.msgSuccess("操作成功");
          }
        }).catch(onerror => {
        });
      })
    })
  }
}
function getListInitial() {
  loading.value = true;
  if (
    queryParams.value.orgCode !== null &&
    queryParams.value.orgCode &&
    typeof queryParams.value.orgCode !== 'string'
  ) {
    queryParams.value.orgCode =
      queryParams.value.orgCode[queryParams.value.orgCode.length - 1]
  }
  let params = JSON.parse(JSON.stringify(queryParams.value))
  // 2024年春季用新表/2024年之前、2024年秋季、及2024年之后用旧表
  if (queryParams.value.statQuarter == '0') {
    queryByPageOneLevelGrpNew(params).then(response => {
      pasEpidemicPreventionReportList.value = response.data.records;
      tableShow.value = '2'
      if (response.data.records.length > 0) {
        var year = response.data.records[0].statYear + '年'
        var season = response.data.records[0].statQuarter > 0 ? '秋季' : '春季'
        var week = response.data.records[0].statWeek + '周'
        labelForTime.value = Number(queryParams.value.statYear) == 2025 ? year + season + '防疫周报' : year + season + '第' + week + '防疫周报'
      } else {
        labelForTime.value = Number(queryParams.value.statYear) == 2025 ? (Number(queryParams.value.statYear) + '年' + (Number(queryParams.value.statQuarter) > 0 ? '秋季' : '春季') + '防疫周报') : queryParams.value.statYear + '年' + (queryParams.value.statQuarter == '0' ? '春季' : queryParams.value.statQuarter == '1' ? '秋季' : '') + (!!queryParams.value.statWeek ? '第' + queryParams.value.statWeek + '周' : '') + '防疫周报'
      }
      nextTick(() => {
        proxy.$refs.multipleTable.doLayout();
      })
      total.value = response.data.total;
      loading.value = false;
    });
  } else {
    queryByPageOneLevelGrpNew(params).then(response => {
      pasEpidemicPreventionReportList.value = response.data.records;
      tableShow.value = '1'
      if (response.data.records.length > 0) {
        var year = response.data.records[0].statYear + '年'
        var season = response.data.records[0].statQuarter > 0 ? '秋季' : '春季'
        var week = response.data.records[0].statWeek + '周'
        labelForTime.value = year + season + '第' + week + '防疫周报'
      } else {
        labelForTime.value = queryParams.value.statYear + '年' + (queryParams.value.statQuarter == '0' ? '春季' : queryParams.value.statQuarter == '1' ? '秋季' : '') + (!!queryParams.value.statWeek ? '第' + queryParams.value.statWeek : '') + '防疫周报'
      }
      nextTick(() => {
        proxy.$refs.multipleTable.doLayout();
      })
      total.value = response.data.total;
      loading.value = false;
    });
  }

}
/** 查询春/秋季防疫周报列表 */
function getList() {
  loading.value = true;
  if ((Number(queryParams.value.statYear) == 2024 || Number(queryParams.value.statYear) == 2025) && queryParams.value.statQuarter == '0') {
    listPasEpidemicPreventionReportGrp(queryParams.value).then(response => {
      pasEpidemicPreventionReportList.value = response.data.records;
      tableShow.value = '2'
      if (response.data.records.length > 0) {
        var year = response.data.records[0].statYear + '年'
        var season = response.data.records[0].statQuarter > 0 ? '秋季' : '春季'
        var week = response.data.records[0].statWeek + '周'
        labelForTime.value = year + season + '第' + week + '防疫周报'
      } else {
        labelForTime.value = ''
      }
      total.value = response.data.total;
      loading.value = false;
    });
  } else {
    listPasEpidemicPreventionReport(queryParams.value).then(response => {
      pasEpidemicPreventionReportList.value = response.data.records;
      tableShow.value = '1'
      if (response.data.records.length > 0) {
        var year = response.data.records[0].statYear + '年'
        var season = response.data.records[0].statQuarter > 0 ? '秋季' : '春季'
        var week = response.data.records[0].statWeek + '周'
        labelForTime.value = year + season + '第' + week + '防疫周报'
      } else {
        labelForTime.value = ''
      }
      total.value = response.data.total;
      loading.value = false;
    });
  }

}
// 数据状态字典翻译
function dataStatusFormat(row, column) {
  let dataStatus = null
  if (dataStatusOptions.value.length > 0) {
    dataStatusOptions.value.forEach((v, i) => {
      if (v.code == row.dataStatus) {
        dataStatus = v.name
      }
    })
  }
  return dataStatus;
}
// 取消按钮
function cancel() {
  open.value = false;
  optionBtnOpen.value = false;
  reset();
}
// 表单重置
function reset() {
  form.value = {
    statYear: null,
    statQuarter: null,
    aiVaccSum: null,
    aiChickenInactVacc: null,
    aiDuckInactVacc: null,
    aiGooseInactVacc: null,
    aiOtherInactVacc: null,
    aiImmuneSum: null,
    aiChickenImmuneNum: null,
    aiDuckImmuneNum: null,
    hppbedInactVacc: null,
    hppbedActVacc: null,
    hppbedInactImmuneNum: null,
    hppbedActImmuneNum: null,
    ndInactVacc: null,
    ndAttenVacc: null,
    ndActVacc: null,
    ndInactImmuneNum: null,
    ndAttenImmuneNum: null,
    fmdPigSpvImmuneNum: null,
    fmdBeefImmuneNum: null,
    fmdSheepImmuneNum: null,
    fmdOtherImmuneNum: null,
    fmdCowImmuneNum: null,
    aiGooseImmuneNum: null,
    aiOtherImmuneNum: null,
    sfInactVacc: null,
    sfImmuneNum: null,
    hppbedImmuneSum: null,
    dataId: null,
    statWeek: null,
    orgCode: null,
    orgName: null,
    fmdVaccSum: null,
    fmdPigInactVacc: null,
    fmdPigSpvVacc: null,
    fmdBeefInactVacc: null,
    fmdSheepInactVacc: null,
    fmdOtherInactVacc: null,
    fmdCowVacc: null,
    fmdImmuneSum: null,
    fmdPigImmuneSum: null,
    fmdPigImmuneNum: null,
    ndImmuneSum: null,
    fmdBeefImmuneSum: null,
    bruceCowImmuneNum: null,
    bruceSheepImmuneNum: null,
    remark: null,
    dataStatus: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    statusCd: null,
    ndActImmuneNum: null,
    bruceBeefVaccSum: null,
    bruceBeefActVacc: null,
    bruceCowActVacc: null,
    bruceImmuneSum: null,
    bruceBeefImmuneSum: null,
    bruceBeefImmuneNum: null,
    fmdOtherVacc: null,
    fmdSheepVacc: null,
  };
  proxy.resetForm("formRef");;
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  if (
    queryParams.value.orgCode !== null &&
    queryParams.value.orgCode &&
    typeof queryParams.value.orgCode !== 'string'
  ) {
    ooorgCode.value =
      queryParams.value.orgCode[queryParams.value.orgCode.length - 1]
  } else {
    ooorgCode.value = queryParams.value.orgCode
  }
  getListInitial();
  flagStatus1.value = true
  flagStatus2.value = false
  flagStatus3.value = false
  flagStatus4.value = false
  statQuarterFlag.value = queryParams.value.statYear < 2024
  currentYear.value = queryParams.value.statYear
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  queryParams.value.orgCode = getInitTreeCode(orgTreeList.value)
  queryParams.value.orgName = getInitTreeName(orgTreeList.value)
  proxy.$refs["queryOrgSelect"].resetOrgCode('reset')
  handleQuery();
}
const xzData = ref({})
// 多选框选中数据
function handleSelectionChange(selection) {
  xzData.value = selection
  ids.value = selection.map(item => item.dataId)
  dataStatus.value = selection.map(item => item.dataStatus)
  selection.map(item => {
    if (item.dataStatus == '02') {
      dataStatusFlag.value = true
    } else {
      dataStatusFlag.value = false
    }
    if (((item.orgCode.length == 6) && Number(item.childCount) == 0) || item.orgCode.length == 10) {
      lastLevelFlag.value = false
    } else {
      lastLevelFlag.value = true
    }
  })
  single.value = selection.length !== 1
  multiple.value = !selection.length
  if (!single.value) {
    statYear.value = selection[0].statYear
    statQuarter.value = selection[0].statQuarter
  } else {
    statYear.value = null
    statQuarter.value = null
  }
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const dataId = xzData.value[0].dataId || ids.value
  if (dataId === null || dataId.length < 1) {
    proxy.$modal.msgWarning("请选择要填写的数据");
    return;
  }
  if (dataId.length > 1) {
    proxy.$modal.msgWarning("选择数据过多，请选择其中一条数据");
    return;
  }
  // if (statQuarter.value == '0') {
  //   getPasEpidemicPreventionWGrp(dataId).then(response => {
  //     open.value = true;
  //     nextTick(() => {
  //       form.value = response.data;
  //       first.value = "first"
  //       title.value = "防疫周报填报-填写本月月报";
  //     })
  //   });
  // } else {
  const data ={
    dataId:dataId
  }
    getPasEpidemicPreventionW(data).then(response => {
      nextTick(() => {
        form.value = response.data;
      })
      open.value = true;
      first.value = "first"
      title.value = "防疫周报填报-填写本月月报";
    });
  // }
}

/** 操作报表状态弹出层*/
function optionReportWin(type) {
  //清空弹出层数据
  form.valueSubmit.ids = []
  form.valueSubmit.remark = null
  optionBtnType.value = type
  reset();
  const dataId = ids.value

  if (dataId === null || dataId.length < 1) {
    proxy.$modal.msgWarning("请选择要填写的数据");
    return;
  }
  if (dataId.length > 1) {
    proxy.$modal.msgWarning("选择数据过多，请选择其中一条数据");
    return;
  }

  form.valueSubmit.ids = dataId
  optionBtnOpen.value = true;

}
//弹出框提交
function optionReportSubmit() {
  if (optionBtnType.value == 1) {
    handleSubmit()
  }
  if (optionBtnType.value == 2) {
    handleBack()
  }
  if (optionBtnType.value == 3) {
    handleLock()
  }
}

/** 提交按钮操作 */
function handleSubmit() {
  const data = form.valueSubmit

  proxy.$modal.confirm('请确定是否提交已选记录', "提示").then(function () {
    if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
      epidemicPreventionReportSubmit(data).then(response => {
        let { data, success } = response
        if (success) {
          optionBtnOpen.value = false;
          switchList();
          proxy.$modal.msgSuccess("操作成功");
        }
      }).catch(onerror => {
      });
    } else {
      epidemicPreventionReportSubmit(data).then(response => {
        let { data, success } = response
        if (success) {
          optionBtnOpen.value = false;
          switchList();
          proxy.$modal.msgSuccess("操作成功");
        }
      }).catch(onerror => {
      });
    }
  })
}
/** 退回按钮操作 */
function handleBack() {
  const data = form.valueSubmit

  proxy.$modal.confirm('请确定是否退回选中记录', "提示").then(function () {
    if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
      epidemicPreventionReportBackGrp(data).then(response => {
        let { data, success } = response
        if (success) {
          optionBtnOpen.value = false;
          switchList();
          proxy.$modal.msgSuccess("操作成功");
        }
      }).catch(onerror => {
      });
    } else {
      epidemicPreventionReportBack(data).then(response => {
        let { data, success } = response
        if (success) {
          optionBtnOpen.value = false;
          switchList();
          proxy.$modal.msgSuccess("操作成功");
        }
      }).catch(onerror => {
      });
    }
  })
}
/** 锁定按钮操作 */
function handleLock() {
  const data = form.valueSubmit
  proxy.$modal.confirm('请确定是否锁定已选记录', "提示").then(function () {
    if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
      epidemicPreventionReportLockGrp(data).then(response => {
        let { data, success } = response
        if (success) {
          optionBtnOpen.value = false;
          switchList();
          proxy.$modal.msgSuccess("操作成功");
        }
      });
    } else {
      epidemicPreventionReportLock(data).then(response => {
        let { data, success } = response
        if (success) {
          optionBtnOpen.value = false;
          switchList();
          proxy.$modal.msgSuccess("操作成功");
        }
      });
    }
  })
}

let handleOrgChange = (value, label) => {
  queryParams.value.orgCode = value
  queryParams.value.orgName = label
}

function tableRowClassName({ row, rowIndex }) {
  let rowClass = ''
  if (row.dataStatus == '03') {
    rowClass = 'danger';
  }
  return rowClass;
}

/** 数据填报按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      if (form.value.dataId != null) {
        if (
          form.value.orgCode !== null &&
          form.value.orgCode &&
          typeof form.value.orgCode !== 'string'
        ) {
          form.value.orgCode =
            form.value.orgCode[form.value.orgCode.length - 1]
        }
        updatePasEpidemicPrevention(form.value).then(response => {
          proxy.$modal.msgSuccess(response.msg);
          open.value = false
          switchList();
        }).catch(onerror => {
        });
      }
    }
  });
}
function switchList() {
  proxy.$refs.multipleTable.clearSelection();
  if (flagStatus1.value) {
    getListInitial()
  } else if (flagStatus2.value) {
    let params = {}
    params.orgCode = rowDataObj.value.orgCode
    params.statYear = rowDataObj.value.statYear
    params.statMonth = rowDataObj.value.statMonth
    params.rows = queryParams.value.rows
    params.page = queryParams.value.page
    params.statWeek = rowDataObj.value.statWeek
    params.statQuarter = rowDataObj.value.statQuarter
    if ((Number(params.statYear) == 2024 || Number(params.statYear) == 2025) && params.statQuarter == '0') {
      listPasEpidemicPreventionReportGrp(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '2'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    } else {
      listPasEpidemicPreventionReport(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    }
  } else if (flagStatus3.value) {
    let params = {}
    params.orgCode = rowDataObj.value.orgCode
    params.statYear = rowDataObj.value.statYear
    params.statMonth = rowDataObj.value.statMonth
    params.statQuarter = rowDataObj.value.statQuarter
    params.statWeek = rowDataObj.value.statWeek
    params.rows = queryParams.value.rows
    params.page = queryParams.value.page
    if ((Number(params.statYear) == 2024 || Number(params.statYear) == 2025) && params.statQuarter == '0') {
      listPasEpidemicPreventionReportGrp(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '2'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    } else {
      listPasEpidemicPreventionReport(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    }
  } else {
    let params = {}
    params.orgCode = rowDataObj.value.orgCode
    params.statYear = rowDataObj.value.statYear
    params.statMonth = rowDataObj.value.statMonth
    params.statQuarter = rowDataObj.value.statQuarter
    params.statWeek = rowDataObj.value.statWeek
    params.rows = queryParams.value.rows
    params.page = queryParams.value.page
    if ((Number(params.statYear) == 2024 || Number(params.statYear) == 2025) && params.statQuarter == '0') {
      listPasEpidemicPreventionReportGrp(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '2'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    } else {
      listPasEpidemicPreventionReport(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    }
  }
}
//下级
function toDetail(row) {
  flagShow.value = true
  if (flagStatus1.value) {
    rowDataObj.value = row
    let params = {}
    params.orgCode = row.orgCode
    params.rows = queryParams.value.rows
    params.statYear = row.statYear
    params.statMonth = row.statMonth
    params.statQuarter = row.statQuarter
    params.statWeek = row.statWeek
    params.page = 1
    queryParams.value.page = 1
    if ((Number(params.statYear) == 2024 || Number(params.statYear) == 2025) && params.statQuarter == '0') {
      listPasEpidemicPreventionReportGrp(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '2'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    } else {
      listPasEpidemicPreventionReport(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    }
    flagStatus1.value = false
    flagStatus2.value = true
    flagStatus3.value = false
    flagStatus4.value = false
  } else if (flagStatus2.value) {
    rowDataObj.value = row
    let params = {}
    params.orgCode = row.orgCode
    params.rows = queryParams.value.rows
    params.statYear = row.statYear
    params.statMonth = row.statMonth
    params.statQuarter = row.statQuarter
    params.statWeek = row.statWeek
    params.page = 1
    queryParams.value.page = 1
    if ((Number(params.statYear) == 2024 || Number(params.statYear) == 2025) && params.statQuarter == '0') {
      listPasEpidemicPreventionReportGrp(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '2'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    } else {
      listPasEpidemicPreventionReport(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    }
    flagStatus1.value = false
    flagStatus2.value = false
    flagStatus3.value = true
    flagStatus4.value = false
  } else if (flagStatus3.value) {
    rowDataObj.value = row
    let params = {}
    params.orgCode = row.orgCode
    params.rows = queryParams.value.rows
    params.statYear = row.statYear
    params.statMonth = row.statMonth
    params.statQuarter = row.statQuarter
    params.statWeek = row.statWeek
    params.page = 1
    queryParams.value.page = 1
    if ((Number(params.statYear) == 2024 || Number(params.statYear) == 2025) && params.statQuarter == '0') {
      listPasEpidemicPreventionReportGrp(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '2'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    } else {
      listPasEpidemicPreventionReport(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    }
    flagStatus1.value = false
    flagStatus2.value = false
    flagStatus3.value = false
    flagStatus4.value = true
  }
}
// 返回上级列表
function goBack() {
  if (flagStatus2.value) {
    flagShow.value = false
    queryParams.value.page = 1
    getListInitial()
    flagStatus1.value = true
    flagStatus2.value = false
    flagStatus3.value = false
    flagStatus4.value = false
  } else if (flagStatus3.value) {
    flagShow.value = true
    let params = {}
    params.orgCode = rowDataObj.value.orgCode.substring(0, 4)
    params.rows = queryParams.value.rows
    params.statYear = rowDataObj.value.statYear
    params.statMonth = rowDataObj.value.statMonth
    params.statQuarter = rowDataObj.value.statQuarter
    params.statWeek = rowDataObj.value.statWeek
    params.page = 1
    queryParams.value.page = 1
    if ((Number(params.statYear) == 2024 || Number(params.statYear) == 2025) && params.statQuarter == '0') {
      listPasEpidemicPreventionReportGrp(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '2'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    } else {
      listPasEpidemicPreventionReport(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    }
    flagStatus1.value = false
    flagStatus2.value = true
    flagStatus3.value = false
    flagStatus4.value = false
  } else if (flagStatus4.value) {
    flagShow.value = true
    let params = {}
    ooorgCode.value.length == 2 ? params.orgCode = rowDataObj.value.orgCode.substring(0, 4) : ooorgCode.value.length == 4 ? params.orgCode = rowDataObj.value.orgCode.substring(0, 4) : ''
    params.rows = queryParams.value.rows
    params.statYear = rowDataObj.value.statYear
    params.statMonth = rowDataObj.value.statMonth
    params.statQuarter = rowDataObj.value.statQuarter
    params.statWeek = rowDataObj.value.statWeek
    params.page = 1
    queryParams.value.page = 1
    if ((Number(params.statYear) == 2024 || Number(params.statYear) == 2025) && params.statQuarter == '0') {
      listPasEpidemicPreventionReportGrp(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '2'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    } else {
      listPasEpidemicPreventionReport(params).then(response => {
        pasEpidemicPreventionReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    }

    flagStatus1.value = false
    flagStatus2.value = false
    flagStatus3.value = true
    flagStatus4.value = false
  }
}
function precinctDataCommitBtn() {
  let params = {
    ids: ids.value,
  }
  if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
    submitManageByIdsGrp(params).then(response => {
      proxy.$modal.msgSuccess("提交成功");
      switchList()
    });
  } else {
    submitManageByIds(params).then(response => {
      proxy.$modal.msgSuccess("提交成功");
      switchList()
    });
  }
}
function precinctDataBackBtn() {
  let params = {
    ids: ids.value,
  }
  if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
    backManageByIdsGrp(params).then(response => {
      proxy.$modal.msgSuccess("退回成功");
      switchList()
    });
  } else {
    backManageByIds(params).then(response => {
      proxy.$modal.msgSuccess("退回成功");
      switchList()
    });
  }
}
function farmDataCommitBtn() {
  let params = {
    ids: ids.value,
  }
  if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
    submitFarmByIdsGrp(params).then(response => {
      proxy.$modal.msgSuccess("提交成功");
      switchList()
    });
  } else {
    submitFarmByIds(params).then(response => {
      proxy.$modal.msgSuccess("提交成功");
      switchList()
    });
  }
}
function farmDataBackBtn() {
  let params = {
    ids: ids.value,
  }
  if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
    backFarmByIdsGrp(params).then(response => {
      proxy.$modal.msgSuccess("退回成功");
      switchList()
    });
  } else {
    backFarmByIds(params).then(response => {
      proxy.$modal.msgSuccess("退回成功");
      switchList()
    });
  }
}
function companyDataCommitBtn() {
  let params = {
    ids: ids.value,
  }
  if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
    submitCompanyByIdsGrp(params).then(response => {
      proxy.$modal.msgSuccess("提交成功");
      switchList()
    });
  } else {
    submitCompanyByIds(params).then(response => {
      proxy.$modal.msgSuccess("提交成功");
      switchList()
    });
  }

}
function companyDataBackBtn() {
  let params = {
    ids: ids.value,
  }
  if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
    backCompanyByIdsGrp(params).then(response => {
      proxy.$modal.msgSuccess("退回成功");
      switchList()
    });
  } else {
    backCompanyByIds(params).then(response => {
      proxy.$modal.msgSuccess("退回成功");
      switchList()
    });
  }
}

</script>



<style lang="scss">
.top_title {
  background: #F5F7FA;
  text-align: center;
  height: 40px;
  line-height: 40px;
  border-top: 1px solid #dfe6ec;
  border-left: 1px solid #dfe6ec;
  border-right: 1px solid #dfe6ec;
  color: #515a6e;
  font-size: 13px;
  font-weight: 500;
}

.specialColor {
  color: red;
}

.el-table--medium th {
  padding: 0px 0;
}

.el-table th>.cell {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.el-dialog__header {
  /* background-color: #d7dfe7; */
}

.el-table th>.cell {
  padding-left: 0px;
  padding-right: 0px;
}

.danger {
  color: #F56C6C;
}

.hidden-row {
  display: none;
  /* 隐藏整行 */
}
</style>
