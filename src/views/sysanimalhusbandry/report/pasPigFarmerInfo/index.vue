<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form :model="queryParams" ref="queryForm" class="queryClass form-line" :inline="true" v-show="showSearch"
          label-width="90px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年份" prop="statYear">
                <el-select v-model="queryParams.statYear" placeholder="请选择年份" :clearable="true">
                  <el-option v-for="dict in statYearOptions" :key="dict.code" :label="dict.name" :value="dict.code" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="所属单位" prop="orgCode">
                <org-selects-inside style="width:100%" placeholderText="请选择单位" :defaultType="true" ref="queryOrgSelect"
                  :isclearable=false :orgTreeList="orgTreeList" @handleOrgChange="handleOrgChange"
                  :currentOrgValue="queryParams.orgCode"></org-selects-inside>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="养殖户姓名" prop="farmerName">
                <el-input v-model="queryParams.farmerName" placeholder="请输入养殖户姓名" clearable
                  @keyup.enter.native="handleQuery" />
              </el-form-item>
            </el-col>
            <el-col :span="6" style="flex-grow: 1;">
              <el-form-item align="right">
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5" v-hasPermi="['pasPigFarmerInfo:insert']">
        <el-button type="primary" icon="Plus" @click="handleAdd" v-hasPermi="['pasPigFarmerInfo:insert']">新增</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasPigFarmerInfo:update']">
        <el-button icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['pasPigFarmerInfo:update']">修改</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasPigFarmerInfo:logicDeleteByIds']">
        <el-button icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['pasPigFarmerInfo:logicDeleteByIds']">删除</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasPigFarmerInfo:exportExcel']">
        <el-button plain icon="Download" @click="handleExport" v-hasPermi="['pasPigFarmerInfo:exportExcel']">导出
        </el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasPigFarmerInfo:importExcel']">
        <el-button plain icon="Upload" @click="handleImport" v-hasPermi="['pasPigFarmerInfo:importExcel']">导入
        </el-button>
      </el-col>

      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table :data="pasPigFarmerInfoList" :height="tableHeight" @selection-change="handleSelectionChange" stripe border>
      <el-table-column type="selection" width="55" align="center" :selectable="selectable" />
      <!--      <el-table-column type="selection" width="55" align="center" />-->

      <el-table-column label="年份" align="center" prop=statYear fixed />
      <el-table-column label="所属单位" align="center" prop="orgName" fixed />
      <el-table-column label="养殖户姓名" align="center" prop="farmerName" />
      <el-table-column label="身份证号" align="center" prop="idNumber" />
      <el-table-column label="手机号" align="center" prop="telephone" />
      <el-table-column label="实际养殖量(头)" align="center" prop="actBreedNum" />
      <el-table-column label="年出栏(头)" align="center" prop="offtakeNum" />
      <el-table-column label="地址" align="center" width="400" prop="addr" />

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="120">
        <template #default="scope">
          <el-button link type="primary" @click="handleUpdate(scope.row)"
            v-hasPermi="['pasPigFarmerInfo:update']">修改</el-button>
          <el-button link type="primary" @click="handleDelete(scope.row)"
            v-hasPermi="['pasPigFarmerInfo:logicDeleteById']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.rows"
      @pagination="getList" />

    <!-- 添加或修改畜牧工作人员管理对话框 -->
    <el-dialog :title="title" v-model="open" width="764px" append-to-body :close-on-click-modal="false">
      <el-form ref="formRef" :model="form" :rules="rules" label-width="80px" label-position="top">
        <el-row>
          <el-col :span="8">
            <el-form-item label="年份" prop="statYear" style="margin-right: 50px">
              <el-select v-model="form.statYear" placeholder="请选择年份" style="width: 192px;" clearable>
                <el-option v-for="dict in statYearOptions" :key="dict.code" :label="dict.name" :value="dict.code" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="所属单位" prop="orgCode" style="margin-right: 50px">
              <org-selects-inside style="width:100%" placeholderText="请选择单位" :defaultType="true"
                ref="queryOrgSelectForm" :isclearable=false :orgTreeList="orgTreeList"
                @handleOrgChange="handleOrgChangeForm" :currentOrgValue="form.orgCode"></org-selects-inside>
            </el-form-item>
          </el-col>

          <el-col :span="8">
            <el-form-item label="养殖户姓名" prop="farmerName" style="margin-right: 50px">
              <el-input v-model="form.farmerName" placeholder="请输入姓名" style="width: 192px;" maxlength="20" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="身份证号" prop="idNumber">
              <el-input v-model="form.idNumber" placeholder="请输入身份证号" style="width: 192px;" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="手机号" prop="telephone">
              <el-input v-model="form.telephone" placeholder="请输入手机号" style="width: 192px;" maxlength="20" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="实际养殖量(头)" prop="actBreedNum">
              <el-input v-model="form.actBreedNum" placeholder="请输入实际养殖量(头)" style="width: 192px;" maxlength="20"
                @input="handleInputActBreedNum" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item label="年出栏(头)" prop="offtakeNum">
              <el-input v-model="form.offtakeNum" placeholder="请输入年出栏(头)" style="width: 192px;" maxlength="20"
                @input="handleInputOfftakeNum" />
            </el-form-item>
          </el-col>
          <el-col :span="15">
            <el-form-item label="地址" prop="addr">
              <el-input v-model="form.addr" placeholder="请输入地址" style="width: 432px;" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 更新模板对话框 -->
    <el-dialog :title="importTitle" v-model="importopen" width="450px" append-to-body
               :before-close="cancel" :close-on-click-modal="false">
      <el-form ref="importRef" :model="importform" :rules="importrules" label-position="top"
               label-width="160px">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="所属单位" prop="orgCode" style="margin-right: 60px">
              <org-selects-inside style="width:100%" placeholderText="请选择单位" :defaultType="true"
                                  ref="queryOrgSelectForm" :isclearable=false :orgTreeList="orgTreeList"
                                  @handleOrgChange="handleOrgChangeImportForm" :currentOrgValue="importform.orgCode"></org-selects-inside>
            </el-form-item>
          </el-col>
        </el-row>
        <el-upload
            v-model:file-list="excelfileList"
            :action="uploadFileUrl"
            :show-file-list="true"
            :http-request="httpRequest"
            :limit="1"
            :headers="headers"
            :auto-upload="false"
            class="upload-file-uploader"
            ref="uploadRef"
            :disabled="isUpload || isInput"
        >
          <!-- 上传按钮替换为文字 -->
          <span class="upload-text" :style="{
              color: (isUpload || isInput) ? '#c0c4cc' : '#409eff',
              cursor: (isUpload || isInput) ? 'not-allowed' : 'pointer' }"
              @click="!isUpload && !isInput"
          >
            上传文件
          </span>
          <!-- 新增下载模板按钮 -->
          <span class="download-template" style="color: #409eff; cursor: pointer; margin-left: 10px;" @click="downloadTemplate" v-hasPermi="['pasPigFarmerInfo:downTemplate']">
          下载模板
           </span>
        </el-upload>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="updateSubmitForm()" :disabled="importing" v-hasPermi="['pasPigFarmerInfo:importExcel']">
          {{ importing ? '加载中...' : '确 定' }}</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="/report/pasPigFarmerInfo/queryByPage">
  import { onMounted } from "vue";
  import '@/views/sysanimalhusbandry/assets/styles/index.scss' // global css
  import OrgSelectsInside from '@/views/sysanimalhusbandry/components/OrgSelectsInside/index'
  import { listPasPigFarmerInfo, getPasPigFarmerInfo, delPasPigFarmerInfo, addPasPigFarmerInfo, updatePasPigFarmerInfo, delPasPigFarmerInfos } from "@/api/sysanimalhusbandry/report/pasPigFarmerInfo";
  import { getDicts, getOrg } from "@/api/sysanimalhusbandry/dict";
  import { getOrgOnlyFarmTree } from "@/api/sysanimalhusbandry/dict";
  import { updateButtonStyle, getInitTreeCode, getInitTreeName } from "@/api/sysanimalhusbandry/utils/cop";
  import { nextTick, ref, defineExpose } from 'vue'
  import { postForExcel } from '@/api/sysanimalhusbandry/excel'
  import { getToken } from "@/utils/auth";
  import axios from "axios";

  const {
    proxy
  } = getCurrentInstance();
  const env = import.meta.env

  var validateCon = (rule, value, callback) => {
    var conReg = /^((1[3|4|5|6|7|8|9][0-9]\d{8})|((\d{3,4}-)\d{7,8}))$/;
    if (value === null || value === '' || typeof value === 'undefined') {
      callback(new Error('请输入联系方式'));
    } else if (!conReg.test(value)) {
      callback(new Error('请输入正确的联系方式 手机号/座机号(区号-号码)'));
    } else {
      callback();
    }
  };
  let checkID = (rule, value, callback) => {
    var city = { 11: "北京", 12: "天津", 13: "河北", 14: "山西", 15: "内蒙古", 21: "辽宁", 22: "吉林", 23: "黑龙江 ", 31: "上海", 32: "江苏", 33: "浙江", 34: "安徽", 35: "福建", 36: "江西", 37: "山东", 41: "河南", 42: "湖北 ", 43: "湖南", 44: "广东", 45: "广西", 46: "海南", 50: "重庆", 51: "四川", 52: "贵州", 53: "云南", 54: "西藏 ", 61: "陕西", 62: "甘肃", 63: "青海", 64: "宁夏", 65: "新疆", 71: "台湾", 81: "香港", 82: "澳门", 91: "国外 " };
    var tip = "";
    var pass = true;

    var patt1 = new RegExp("(^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$)|(^[1-9]\\d{5}\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{2}$)");
    if (value == '') {
      callback(new Error("请输入身份证号"));
      pass = false;
    } else if (!patt1.test(value)) {
      callback(new Error("身份证号格式错误"));
      pass = false;
    } else if (!city[value.substr(0, 2)]) {
      callback(new Error("地址编码错误"));
      pass = false;
    } else {
      //18位身份证需要验证最后一位校验位
      if (value.length == 18) {
        value = value.split('');
        //∑(ai×Wi)(mod 11)
        //加权因子
        var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        //校验位
        var parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2];
        var sum = 0;
        var ai = 0;
        var wi = 0;
        for (var i = 0; i < 17; i++) {
          ai = value[i];
          wi = factor[i];
          sum += ai * wi;
        }
        var last = parity[sum % 11];
        if (parity[sum % 11] != value[17]) {
          callback(new Error("身份证号码错误"));
          pass = false;
        }
      }
    }
    if (pass) {
      callback();
    }

  };
  var checkNum = (rule, value, callback) => {
    if (!value && value != '0') {
      callback(new Error('年出栏数不得为空'));
    }
    if (form.value.actBreedNum || form.value.actBreedNum == '0') {
      if (Number(value) <= form.value.actBreedNum) {
        callback();
      }
      else {
        callback(new Error('年出栏数需不大于实际养殖量'));
      }
    }
  };
  const importopen = ref(false);
  const isUpload = ref(false);
  const isInput = ref(false);
  const importTitle = ref('导入文件');
  const importing = ref(false);
  const importform  = reactive({
    statYear: null,
    orgCode: null
  })
  const headers  = reactive({
    Authorization: "Bearer " + getToken()
  })
  const importrules = reactive({
    statYear: [
      { required: true, message: "请输入年份", trigger: 'blur' }
    ],
  })
  const uploadFileUrl = env.VITE_APP_BASE_API + '/bdh-animal-husbandry-api/report/pasPigFarmerInfo/importExcel';
  const excelfileList = ref([]);
  const excelfileType = ref(['xls', 'xlsx']);
  const uploadRef = ref(null);
  const orgTreeList = ref([]);
  const ids = ref([]);
  const queryOrgFormSelect = ref('');
  const queryOrgSelect = ref('');
  const loading = ref(false);
  const showSearch = ref(true);
  const total = ref(0);
  const open = ref(false);
  const pasPigFarmerInfoList = ref([]);
  const title = ref('');
  const statYearOptions = ref([]);
  const dataStatusFlag = ref(true);
  const optionLockType = ref('');
  const optionLockOpen = ref(false);
  const lastLevelFlag = ref(true);
  const single = ref(true);
  const multiple = ref(true);
  const searchHeight = ref(0); // 搜索栏高度
  const tableHeight = ref(400); // 表格高度
  const searchDom = ref(); // 搜索栏dom
  const data = reactive({
    // 查询参数
    queryParams: {
      page: 1,
      rows: 10,
      orgCode: null,
      farmerName: null,
      statYear: String(new Date().getFullYear()),
    },
    // 表单参数
    form: {
      statYear: String(new Date().getFullYear()),
      farmerName: null,
      orgCode: null,
      orgName: null,
      idNumber: null,
      telephone: null,
      actBreedNum: null,
      offtakeNum: null,
      addr: null,

    },


    // 表单校验
    rules: {
      orgCode: [
        { required: true, message: '必填项', trigger: 'change' }
      ],
      farmerName: [
        { required: true, message: '必填项', trigger: 'blur' }
      ],
      statYear: [{ required: true, message: "必填项", trigger: 'change' }],
      telephone: [
        { required: true, validator: validateCon, trigger: 'blur' }
      ],
      idNumber: [
        { required: true, validator: checkID, trigger: 'blur' }
      ],
      addr: [{ required: true, message: "必填项", trigger: 'change' }],
      actBreedNum: [{ required: true, message: "必填项", trigger: 'change' }],
      offtakeNum: [{ required: true, validator: checkNum, trigger: 'change' }],
    }
  })
  const {
    queryParams,
    form,
    rules
  } = toRefs(data);
  const getOptions = () => {
    getDicts("stat_year").then(response => {
      statYearOptions.value = response.data;
    });

    getOrgOnlyFarmTree().then(response => {
      orgTreeList.value = response.data;
      queryParams.value.orgCode = getInitTreeCode(orgTreeList.value)[0];
    })
    getList();
  }
  getOptions()
  updateButtonStyle()

  onMounted(() => {
		searchHeight.value = searchDom.value?.clientHeight;
		tableHeight.value = showSearch.value
			? window.innerHeight - searchHeight.value - 220
			: window.innerHeight - 220;
	});
	watch(showSearch, (value) => {
		tableHeight.value = showSearch.value
			? window.innerHeight - searchHeight.value - 220
			: window.innerHeight - 220;
	});

  /** 导出按钮操作 */
  function handleExport() {
    postForExcel('/bdh-animal-husbandry-api/report/pasPigFarmerInfo/exportExcel', queryParams.value, '生猪养殖户统计表');
  }
  /** 下载导入模板 */
  function downloadTemplate() {
    postForExcel('/bdh-animal-husbandry-api/report/pasPigFarmerInfo/downTemplate', queryParams.value, '生猪养殖户导入模板');
  }
  function handleImport() {
    importopen.value = true;
    nextTick(()=>{
      importform.orgCode = getInitTreeCode(orgTreeList.value)
    })
  }
  function handleTitle(val) {
    if (val === '99') {
      form.value.promotionDate = ''
    }
  }
  /** 查询生猪养殖户列表 */
  function getList() {
    loading.value = true;
    if (
      queryParams.value.orgCode !== null &&
      queryParams.value.orgCode &&
      typeof queryParams.value.orgCode !== 'string'
    ) {
      queryParams.value.orgCode =
        queryParams.value.orgCode[queryParams.value.orgCode.length - 1]
    }
    listPasPigFarmerInfo(queryParams.value).then(response => {
      pasPigFarmerInfoList.value = response.data.records;
      total.value = response.data.total;
      loading.value = false;
    });
  }
  function handleInputActBreedNum(value) {
    // 使用正则表达式验证输入值是否为正整数
    const pattern = /^[0-9]\d*$/;
    if (pattern.test(value)) {
      // 如果输入值为正整数，则直接将其赋值给 inputValue
      form.value.actBreedNum = value;
    } else {
      // 如果输入值不为正整数，则从中提取出数字，并将其拼接为一个字符串后赋值给 inputValue
      //const num = value.match(/\d+/g);
      //form.value.actBreedNum = num ? num.join("") : "";
      form.value.actBreedNum = null;
    }
  }
  function handleInputOfftakeNum(value) {
    // 使用正则表达式验证输入值是否为正整数
    const pattern = /^[0-9]\d*$/;
    if (pattern.test(value)) {
      // 如果输入值为正整数，则直接将其赋值给 inputValue
      form.value.offtakeNum = value;
    } else {
      // 如果输入值不为正整数，则从中提取出数字，并将其拼接为一个字符串后赋值给 inputValue
      //const num = value.match(/\d+/g);
      //form.value.actBreedNum = num ? num.join("") : "";
      form.value.offtakeNum = null;
    }
  }
  // 取消按钮
  function cancel() {
    open.value = false;
    importopen.value = false;
    importing.value = false;
    uploadRef.value.clearFiles()
    reset();
  }
  // 表单重置
  function reset() {
    form.value = {
      statYear: String(new Date().getFullYear()),
      pigFarmerId: null,
      orgCode: getInitTreeCode(orgTreeList.value)[0],
      orgName: null,
      farmerName: null,
      remark: null,

    };
    proxy.resetForm("formRef");;
  }
  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.page = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryForm");
    queryParams.value.orgCode = getInitTreeCode(orgTreeList.value)
    queryParams.value.orgName = getInitTreeName(orgTreeList.value)
    proxy.$refs["queryOrgSelect"].resetOrgCode('reset')
    handleQuery();
  }

  let handleOrgChange = (value, label) => {
    queryParams.value.orgCode = value
    queryParams.value.orgName = label
  }

  let handleOrgChangeForm = (value, label) => {
    form.value.orgCode = value
  }

  let handleOrgChangeImportForm = (value, label) => {
    importform.orgCode = value
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.pigFarmerId)
    single.value = selection.length !== 1
    multiple.value = !selection.length
  }
  function selectable(row, index) {
    if (row.isAutoTotal == '1') {
      return false
    }
    return true
  }

  function httpRequest(param) {
    importing.value = true;
    // 提取允许的文件类型
    const allowedTypes = excelfileType.value;
    const file = param.file;
    const fileExtension = file.name.split('.').pop().toLowerCase();

    // 校验文件类型
    if (!allowedTypes.includes(fileExtension)) {
      proxy.$message.error(`文件格式不正确, 请上传${allowedTypes.join('/')}格式文件!`);
      importing.value = false;
      return;
    }
    let fileObj = param.file; // 相当于input里取得的files
    let fd = new FormData(); // FormData 对象
    fd.append('file', fileObj); // 文件对象
    fd.append('statYear', importform.statYear); // 文件头行数
    fd.append('orgCode', importform.orgCode); // 数据头行数
    let url = uploadFileUrl;
    let config = {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    };

    axios.post(url, fd, config).then((res) => {
      if (res.data.code === 0 && res.data.success === true) {
        proxy.$message.success('文件上传成功');
        importopen.value = false;
        getList();
      } else {
        const errorMessages = res.data.data?.map(item => item.message).join('\n') || res.data.msg;
        proxy.$message.error(errorMessages);
      }
        importing.value = false;
        uploadRef.value.clearFiles();
      }).catch(() => {
        proxy.$message.error('文件上传失败');
        importing.value = false;
        uploadRef.value.clearFiles();
    });
  }

  function updateSubmitForm() {
    proxy.$refs["importRef"].validate((valid) => {
      if (valid) {
        if (excelfileList.value.length < 1) {
          proxy.$message.error('请上传文件！')
          importing.value = false;
          return
        }
        if (excelfileList.value.length > 1) {
          proxy.$message.error('只能上传一个文件！')
          importing.value = false;
          return
        }
        proxy.$refs.uploadRef.submit()
      }
    })

  }
  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "新增人员";
    nextTick(()=>{
      form.value.orgCode = getInitTreeCode(orgTreeList.value)
    })
  }
  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const pigFarmerId = row.pigFarmerId || ids.value
    getPasPigFarmerInfo(pigFarmerId).then(response => {
      open.value = true;
      nextTick(() => {
        form.value = response.data;
        title.value = "修改人员";
      });
    });
  }
  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["formRef"].validate(valid => {
      if (valid) {
        if (
          form.value.orgCode !== null &&
          form.value.orgCode &&
          typeof form.value.orgCode !== 'string'
        ) {
          form.value.orgCode =
            form.value.orgCode[form.value.orgCode.length - 1]
        }
        if (form.value.pigFarmerId != null) {
          updatePasPigFarmerInfo(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addPasPigFarmerInfo(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const pigFarmerIds = row.pigFarmerId || ids.value;
    proxy.$modal.confirm('是否确认删除数据？', '警告').then(function () {
      return Array.isArray(pigFarmerIds) ? delPasPigFarmerInfos(pigFarmerIds) : delPasPigFarmerInfo(pigFarmerIds);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
  }



</script>

