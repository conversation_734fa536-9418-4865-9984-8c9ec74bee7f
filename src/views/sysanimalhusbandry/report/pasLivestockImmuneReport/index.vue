<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form :model="queryParams" ref="queryForm" class="queryClass form-line" :inline="true" v-show="showSearch"
          label-width="68px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年份" prop="statYear">
                <el-select v-model="queryParams.statYear" placeholder="请选择年份" clearable>
                  <el-option v-for="dict in statYearOptions" :key="dict.code" :label="dict.name" :value="dict.code" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="季度" prop="statQuarter">
                <el-select v-model="queryParams.statQuarter" placeholder="请选择季度" clearable>
                  <el-option v-for="dict in statQuarterOptions" :key="dict.code" :label="dict.name"
                    :value="parseInt(dict.code)" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="单位" prop="orgCode">
                <org-selects-inside style="width:100%" placeholderText="请选择单位" :defaultType="true" ref="queryOrgSelect"
                  :isclearable=false :orgTreeList="orgTreeList" @handleOrgChange="handleOrgChange"
                  :currentOrgValue="queryParams.orgCode"></org-selects-inside>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="数据状态" prop="dataStatus">
                <el-select v-model="queryParams.dataStatus" placeholder="请选择数据状态" clearable>
                  <el-option v-for="dict in dataStatusOptions" :key="dict.code" :label="dict.name" :value="dict.code" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasLivestockImmuneReport:add']">
        <el-button type="primary" icon="Plus" @click="handleUpdate"
          v-hasPermi="['pasLivestockImmuneReport:add']">填报</el-button>
      </el-col>
      <!-- <el-col :span="1.5" v-hasPermi="['pasLivestockImmuneReport:add']">
        <el-button type="primary" icon="Plus" :disabled="single || dataStatusFlag || lastLevelFlag"
          @click="handleUpdate" v-hasPermi="['pasLivestockImmuneReport:add']">填报</el-button>
      </el-col> -->
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          icon="Check"-->
      <!--          @click="optionReportWin(1)"-->
      <!--          v-hasPermi="['pasLivestockImmuneReport:submit']"-->
      <!--        >提交</el-button>-->
      <!--      </el-col>-->
      <!--      <el-col :span="1.5">-->
      <!--        <el-button-->
      <!--          icon="Close"-->
      <!--          @click="optionReportWin(2)"-->
      <!--          v-hasPermi="['pasLivestockImmuneReport:back']"-->
      <!--        >退回</el-button>-->
      <!--      </el-col>-->

      <el-col :span="1.5" v-hasPermi="['pasLivestockImmuneReport:submitCompanyByIds']">
        <el-button icon="Top" :disabled="single || dataStatusFlag" @click="companyDataCommitBtn"
          v-hasPermi="['pasLivestockImmuneReport:submitCompanyByIds']">分公司提交</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasLivestockImmuneReport:backCompanyByIds']">
        <el-button icon="Bottom" :disabled="single" @click="companyDataBackBtn"
          v-hasPermi="['pasLivestockImmuneReport:backCompanyByIds']">分公司退回</el-button>
      </el-col>

      <el-col :span="1.5" v-hasPermi="['pasLivestockImmuneReport:submitFarmByIds']">
        <el-button icon="Top" :disabled="single || dataStatusFlag" @click="farmDataCommitBtn"
          v-hasPermi="['pasLivestockImmuneReport:submitFarmByIds']">农场提交</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasLivestockImmuneReport:backFarmByIds']">
        <el-button icon="Bottom" :disabled="single" @click="farmDataBackBtn"
          v-hasPermi="['pasLivestockImmuneReport:backFarmByIds']">农场退回</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasLivestockImmuneReport:submitManageByIds']">
        <el-button icon="Top" :disabled="single || dataStatusFlag" @click="precinctDataCommitBtn"
          v-hasPermi="['pasLivestockImmuneReport:submitManageByIds']">管理区提交</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasLivestockImmuneReport:backManageByIds']">
        <el-button icon="Bottom" :disabled="single" @click="precinctDataBackBtn"
          v-hasPermi="['pasLivestockImmuneReport:backManageByIds']">管理区退回</el-button>
      </el-col>

      <el-col :span="1.5" v-hasPermi="['pasLivestockImmuneReport:lockCompanyByIds']">
        <el-button icon="Lock" :disabled="single" @click="handleLock"
          v-hasPermi="['pasLivestockImmuneReport:lockCompanyByIds']">锁定</el-button>
      </el-col>
      <el-col :span="1.5" v-hasPermi="['pasLivestockImmuneReport:exceldownload']">
        <el-button plain icon="Download" @click="handleExport"
          v-hasPermi="['pasLivestockImmuneReport:exceldownload']">导出
        </el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button v-if="flagShow && flagStatus1 != true" icon="Top" @click="goBack">返回上级</el-button>
      </el-col>

      <right-toolbar v-model:showSearch="showSearch" @queryTable="getListInitial"></right-toolbar>
    </el-row>

    <el-table :data="pasLivestockImmuneReportList" stripe ref="multipleTable" :height="tableHeight" border
      @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
      <el-table-column :label="labelForTime" align="center">
        <el-table-column type="selection" width="55" align="center" :selectable="selectable" />
        <el-table-column label="市、县、区" align="center" prop="orgName" fixed="left" />
        <el-table-column label="春防期间存栏数量(万头/只/羽)" align="center">
          <el-table-column label="畜合计" align="center" v-if="columnShowMap.livestockAllSum" prop="livestockAllSum" />
          <el-table-column label="猪" align="center" v-if="columnShowMap.livestockPigSum" prop="livestockPigSum" />
          <el-table-column label="牛合计" align="center" v-if="columnShowMap.livestockBeefSum" prop="livestockBeefSum" />
          <el-table-column label="奶牛" align="center" v-if="columnShowMap.livestockCowNum" prop="livestockCowNum" />
          <el-table-column label="牛(不含奶牛)" align="center" v-if="columnShowMap.livestockBeefNoCowNum"
            prop="livestockBeefNoCowNum" />
          <el-table-column label="牛(含奶牛)" align="center" v-if="columnShowMap.livestockBeefAndCowNum"
            prop="livestockBeefAndCowNum" />
          <el-table-column label="羊" align="center" v-if="columnShowMap.livestockSheepNum" prop="livestockSheepNum" />
          <el-table-column label="其它动物" align="center" v-if="columnShowMap.livestockOtherNum"
            prop="livestockOtherNum" />
          <el-table-column label="禽合计" align="center" v-if="columnShowMap.poultrySum" prop="poultrySum" />
          <el-table-column label="鸡" align="center" v-if="columnShowMap.poultryChickenNum" prop="poultryChickenNum" />
          <el-table-column label="鸭" align="center" v-if="columnShowMap.poultryDuckNum" prop="poultryDuckNum" />
          <el-table-column label="鹅" align="center" v-if="columnShowMap.poultryGooseNum" prop="poultryGooseNum" />
          <el-table-column label="其它禽" align="center" v-if="columnShowMap.poultryOtherNum" prop="poultryOtherNum" />
        </el-table-column>
        <el-table-column label="春防期间牲畜口蹄疫应免数量(万头/只)" align="center">
          <el-table-column label="畜合计" align="center" v-if="columnShowMap.fmdImmuneSum" prop="fmdImmuneSum" />
          <el-table-column label="猪" align="center" v-if="columnShowMap.fmdPigImmuneNum" prop="fmdPigImmuneNum" />
          <el-table-column label="牛合计" align="center" v-if="columnShowMap.fmdBeefImmuneNum" prop="fmdBeefImmuneNum" />
          <el-table-column label="奶牛" align="center" v-if="columnShowMap.fmdCowImmuneNum" prop="fmdCowImmuneNum" />
          <el-table-column label="牛(不含奶牛)" align="center" v-if="columnShowMap.fmdBeefNoCowImmuneNum"
            prop="fmdBeefNoCowImmuneNum" />
          <el-table-column label="牛(含奶牛)" align="center" v-if="columnShowMap.fmdBeefAndCowImmuneNum"
            prop="fmdBeefAndCowImmuneNum" />
          <el-table-column label="羊" align="center" v-if="columnShowMap.fmdSheepImmuneNum" prop="fmdSheepImmuneNum" />
          <el-table-column label="其它动物" align="center" v-if="columnShowMap.fmdOtherImmuneNum"
            prop="fmdOtherImmuneNum" />
        </el-table-column>
        <el-table-column label="春防期间禽流感应免数量(万羽)" align="center">
          <el-table-column label="禽合计" align="center" v-if="columnShowMap.aiImmuneSum" prop="aiImmuneSum" />
          <el-table-column label="鸡" align="center" v-if="columnShowMap.aiChickenImmuneNum" prop="aiChickenImmuneNum" />
          <el-table-column label="鸭" align="center" v-if="columnShowMap.aiDuckImmuneNum" prop="aiDuckImmuneNum" />
          <el-table-column label="鹅" align="center" v-if="columnShowMap.aiGooseImmuneNum" prop="aiGooseImmuneNum" />
          <el-table-column label="其它禽" align="center" v-if="columnShowMap.aiOtherImmuneNum" prop="aiOtherImmuneNum" />
        </el-table-column>
        <el-table-column label="春防期间布鲁氏菌病免疫数量 (万头)" align="center">
          <el-table-column label="合计" align="center" v-if="columnShowMap.bruceImmuneSum" prop="bruceImmuneSum" />
          <el-table-column label="牛合计" align="center" v-if="columnShowMap.bruceBeefImmuneSum"
            prop="bruceBeefImmuneSum" />
          <el-table-column label="奶牛" align="center" v-if="columnShowMap.bruceCowImmuneNum" prop="bruceCowImmuneNum" />
          <el-table-column label="牛(不含奶牛)" align="center" v-if="columnShowMap.bruceBeefNoCowImmuneNum"
            prop="bruceBeefNoCowImmuneNum" />
          <el-table-column label="牛(含奶牛)" align="center" v-if="columnShowMap.bruceBeefAndCowImmuneNum"
            prop="bruceBeefAndCowImmuneNum" />
          <el-table-column label="羊" align="center" v-if="columnShowMap.bruceSheepImmuneNum"
            prop="bruceSheepImmuneNum" />
        </el-table-column>
        <el-table-column label="数据状态" align="center" prop="dataStatus" :formatter="dataStatusFormat" fixed="right" />
        <el-table-column label="备注" align="center" prop="remark" fixed="right" />
      </el-table-column>
      <el-table-column label="操作" align="center"
        v-if="!(flagStatus3 == true || (flagStatus1 == true && ooorgCode.length == 10) || (flagStatus1 == true && ooorgCode.length == 6) || (flagStatus2 && ooorgCode.length == 4) || (flagStatus2 && ooorgCode.length == 10) || (flagStatus2 && ooorgCode.length == 6))"
        fixed="right" class-name="small-padding fixed-width" width="90">
        <template #default="scope">
          <el-button link type="primary" @click="toDetail(scope.row)"
            v-if="!((flagStatus1 && scope.row.orgCode.length == 2) || (flagStatus1 && ooorgCode.length == 4 && scope.row.orgCode.length == 4) || (flagStatus2 && ooorgCode.length == 2 && scope.row.orgCode.length == 4) || scope.row.orgCode.length == 12 || scope.row.childCount == 0 || scope.row.childCount == null)">查看明细</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.page" v-model:limit="queryParams.rows"
      @pagination="switchList" />

    <!-- 添加或修改春/秋季应免存栏数量统计表对话框 -->
    <el-dialog :title="title" v-model="open" :close-on-click-modal="false" width="1000px" append-to-body>
      <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
        <el-divider content-position="center">{{ form.statQuarter == '0' ? '春防' : form.statQuarter == '1' ? '秋防' :
          '' }}期间存栏数量(万头/只/羽)</el-divider>

        <el-row style="display: flex;flex-wrap: wrap;">
          <el-col :span="8" v-if="columnShowMap.livestockPigSum">
            <el-form-item label="猪" prop="livestockPigSum">
              <el-input v-model="form.livestockPigSum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.livestockBeefAndCowNum">
            <el-form-item label="牛(含奶牛)" prop="livestockBeefAndCowNum">
              <el-input v-model="form.livestockBeefAndCowNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.livestockCowNum">
            <el-form-item label="奶牛" prop="livestockCowNum">
              <el-input v-model="form.livestockCowNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.livestockBeefNoCowNum">
            <el-form-item label="牛(不含奶牛)" prop="livestockBeefNoCowNum">
              <el-input v-model="form.livestockBeefNoCowNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.livestockSheepNum">
            <el-form-item label="羊" prop="livestockSheepNum">
              <el-input v-model="form.livestockSheepNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.livestockOtherNum">
            <el-form-item label="其它动物" prop="livestockOtherNum">
              <el-input v-model="form.livestockOtherNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.poultryChickenNum">
            <el-form-item label="鸡" prop="poultryChickenNum">
              <el-input v-model="form.poultryChickenNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.poultryDuckNum">
            <el-form-item label="鸭" prop="poultryDuckNum">
              <el-input v-model="form.poultryDuckNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.poultryGooseNum">
            <el-form-item label="鹅" prop="poultryGooseNum">
              <el-input v-model="form.poultryGooseNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.poultryOtherNum">
            <el-form-item label="其它禽" prop="poultryOtherNum">
              <el-input v-model="form.poultryOtherNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="center">{{ form.statQuarter == '0' ? '春防' : form.statQuarter == '1' ? '秋防' :
          '' }}期间牲畜口蹄疫应免数量(万头/只)</el-divider>
        <el-row style="display: flex;flex-wrap: wrap;">
          <el-col :span="8" v-if="columnShowMap.fmdPigImmuneNum">
            <el-form-item label="猪" prop="fmdPigImmuneNum">
              <el-input v-model="form.fmdPigImmuneNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.fmdBeefAndCowImmuneNum">
            <el-form-item label="牛(含奶牛)" prop="fmdBeefAndCowImmuneNum">
              <el-input v-model="form.fmdBeefAndCowImmuneNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.fmdCowImmuneNum">
            <el-form-item label="奶牛" prop="fmdCowImmuneNum">
              <el-input v-model="form.fmdCowImmuneNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.fmdBeefNoCowImmuneNum">
            <el-form-item label="牛(不含奶牛)" prop="fmdBeefNoCowImmuneNum">
              <el-input v-model="form.fmdBeefNoCowImmuneNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.fmdSheepImmuneNum">
            <el-form-item label="羊" prop="fmdSheepImmuneNum">
              <el-input v-model="form.fmdSheepImmuneNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.fmdOtherImmuneNum">
            <el-form-item label="其它动物" prop="fmdOtherImmuneNum">
              <el-input v-model="form.fmdOtherImmuneNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-divider content-position="center">{{ form.statQuarter == '0' ? '春防' : form.statQuarter == '1' ? '秋防' :
          '' }}期间禽流感应免数量(万羽)</el-divider>
        <el-row style="display: flex;flex-wrap: wrap;">
          <el-col :span="8" v-if="columnShowMap.aiChickenImmuneNum">
            <el-form-item label="鸡" prop="aiChickenImmuneNum">
              <el-input v-model="form.aiChickenImmuneNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.aiDuckImmuneNum">
            <el-form-item label="鸭" prop="aiDuckImmuneNum">
              <el-input v-model="form.aiDuckImmuneNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.aiGooseImmuneNum">
            <el-form-item label="鹅" prop="aiGooseImmuneNum">
              <el-input v-model="form.aiGooseImmuneNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.aiOtherImmuneNum">
            <el-form-item label="其它禽" prop="aiOtherImmuneNum">
              <el-input v-model="form.aiOtherImmuneNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="center">{{ form.statQuarter == '0' ? '春防' : form.statQuarter == '1' ? '秋防' :
          '' }}期间布鲁氏菌病免疫数量 (万头)</el-divider>
        <el-row style="display: flex;flex-wrap: wrap;">
          <el-col :span="8" v-if="columnShowMap.bruceBeefAndCowImmuneNum">
            <el-form-item label="牛(含奶牛)" prop="bruceBeefAndCowImmuneNum">
              <el-input v-model="form.bruceBeefAndCowImmuneNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.bruceCowImmuneNum">
            <el-form-item label="奶牛" prop="bruceCowImmuneNum">
              <el-input v-model="form.bruceCowImmuneNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.bruceBeefNoCowImmuneNum">
            <el-form-item label="牛(不含奶牛)" prop="bruceBeefNoCowImmuneNum">
              <el-input v-model="form.bruceBeefNoCowImmuneNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="columnShowMap.bruceSheepImmuneNum">
            <el-form-item label="羊" prop="bruceSheepImmuneNum">
              <el-input v-model="form.bruceSheepImmuneNum" placeholder="请输入" />
            </el-form-item>
          </el-col>
        </el-row>
        <!-- <el-divider content-position="center" v-if="columnShowMap.hppbedImmuneNum">{{ form.statQuarter == '0' ? '春防' :
          form.statQuarter == '1' ? '秋防' : '' }}期间高致病性猪蓝耳病应免数量(万头)</el-divider>
        <el-form-item label="猪" prop="hppbedImmuneNum" v-if="Number(form.statYear) < 2024">
          <el-input v-model="form.hppbedImmuneNum" placeholder="请输入" />
        </el-form-item>
        <el-divider content-position="center">{{
          form.statQuarter ==
            '0' ? '春防' : form.statQuarter == '1' ? '秋防' : '' }}期间猪瘟应免数量 (万头)</el-divider>
        <el-form-item label="猪" prop="sfUmmuneNum">
          <el-input v-model="form.sfUmmuneNum" placeholder="请输入" />
        </el-form-item>
        <el-divider content-position="center">{{ form.statQuarter == '0' ? '春防' :
          form.statQuarter == '1' ? '秋防' : '' }}期间新城疫应免数量(万羽)</el-divider>
        <el-form-item label="鸡" prop="ndImmuneNum" v-if="Number(form.statYear) < 2024">
          <el-input v-model="form.ndImmuneNum" placeholder="请输入" />
        </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="/report/pasLivestockImmuneReport/queryByPageOneLevel">
import { onMounted } from "vue";
import '@/views/sysanimalhusbandry/assets/styles/index.scss' // global css
import OrgSelectsInside from '@/views/sysanimalhusbandry/components/OrgSelectsInside/index'
import {
  listPasLivestockImmuneReport,
  pasLivestockImmuneReportSubmit,
  pasLivestockImmuneReportBack,
  pasLivestockImmuneReportLock,
  listPasLivestockImmuneReportOneLevel,
  submitManageByIds,
  backManageByIds,
  submitFarmByIds,
  backFarmByIds,
  submitCompanyByIds,
  backCompanyByIds,
  lockCompanyByIds,
  listPasLivestockImmuneReportGrp,
  pasLivestockImmuneReportSubmitGrp,
  pasLivestockImmuneReportBackGrp,
  pasLivestockImmuneReportLockGrp,
  listPasLivestockImmuneReportOneLevelGrp,
  submitManageByIdsGrp,
  backManageByIdsGrp,
  submitFarmByIdsGrp,
  backFarmByIdsGrp,
  submitCompanyByIdsGrp,
  backCompanyByIdsGrp,
  lockCompanyByIdsGrp,
  queryTemplateColumnByTemplate
} from "@/api/sysanimalhusbandry/report/pasLivestockImmuneReport";
import { getPasLivestockImmuneQ, getPasLivestockImmuneQGrp, updatePasLivestockImmuneQ, updatePasLivestockImmuneQGrp } from '@/api/sysanimalhusbandry/report/pasLivestockImmuneQ'
import { cowReportLock, listPasCowProduceReport } from "@/api/sysanimalhusbandry/report/pasCowProduceReport";
import { getDicts, getOrg } from "@/api/sysanimalhusbandry/dict";
import { updateButtonStyle, getInitTreeCode, getInitTreeName } from "@/api/sysanimalhusbandry/utils/cop";
import { nextTick, ref, defineExpose } from 'vue'
import { postForExcel } from '@/api/sysanimalhusbandry/excel'

const {
  proxy
} = getCurrentInstance();
const env = import.meta.env
var validateNum = (rule, value, callback) => {
  var reg = /^[0-9]+(.?[0-9]{1,4})?$/ //整数
  if (value === null || value === '' || typeof (value) == 'undefined') {
    callback()
  } else if (value.toString().split('.')[0].length > 8) {
    callback(new Error('输入过长，请确认'))
  } else if (reg.test(Number(value))) {
    callback()
  } else {
    callback(new Error('请输入数字(可保留四位小数)'))
  }
}
const data = reactive({
  // 查询参数
  queryParams: {
    page: 1,
    rows: 10,
    dataStatus: null,
    statYear: String(new Date().getFullYear()),
    statQuarter: null,
    orgCode: null,
    orgName: null,
  },
  // 表单参数
  form: {},
  // 表单校验
  rules: {
    livestockPigNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    livestockCowNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    livestockBeefNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    livestockSheepNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    livestockOtherNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    poultryChickenNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    poultryDuckNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    poultryGooseNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    poultryOtherNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdCowImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdBeefImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdSheepImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdOtherImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    fmdPigImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    aiChickenImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    aiDuckImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    aiGooseImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    aiOtherImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    bruceCowImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    bruceBeefImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    bruceSheepImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    hppbedImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    sfUmmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
    ndImmuneNum: [{ required: false, validator: validateNum, trigger: 'blur' }],
  },

})
const {
  queryParams,
  form,
  rules
} = toRefs(data);
const orgTreeList = ref([]);
const statYearOptions = ref([]);
const dataStatusOptions = ref([]);
const pasLivestockImmuneReportList = ref([]);
const ooorgCode = ref('');
const loading = ref(false);
const total = ref(0);
const open = ref(false);
const queryOrgSelect = ref(null);
const flagStatus1 = ref(true);
const flagStatus2 = ref(false);
const flagStatus3 = ref(false);
const flagStatus4 = ref(false);
const dataStatusFlag = ref(true);
const lastLevelFlag = ref(true);
const single = ref(true);
const ids = ref([]);
const dataStatus = ref([]);
const multiple = ref(false);
const flagShow = ref(false);
const showSearch = ref(true);
const title = ref('');
const rowDataObj = ref({});
const labelForTime = ref('');
const statYear = ref(null);
const statQuarter = ref(null);
const tableShow = ref('2');
const seasonLabel = ref('');
const statQuarterOptions = ref([]);
const statQuarterFlag = ref(false);
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
// 存储后端返回的列显示配置
const columnConfigList = ref([])
// 映射表：key=columnName，value=showFlag是否为'1'
const columnShowMap = ref({})
/** 获取字典 */
const getOptions = () => {
  // 创建一个数组用来存储所有的 Promise
  const promises = [
    getDicts("stat_quarter").then(response => {
      statQuarterOptions.value = response.data;

    }),
    getDicts("stat_year").then(response => {
      statYearOptions.value = response.data;
    }),
    getDicts("data_status").then(response => {
      dataStatusOptions.value = response.data;
    }),
    getOrg().then(response => {
      orgTreeList.value = response.data;
      queryParams.value.orgCode = getInitTreeCode(orgTreeList.value);
      queryParams.value.statQuarter = (new Date().getMonth() + 1) > 6 ? 1 : 0;
      ooorgCode.value = getInitTreeCode(orgTreeList.value)[getInitTreeCode(orgTreeList.value).length - 1];
    })
  ];

  // 使用 Promise.all 来等待所有 Promise 完成
  Promise.all(promises).then(() => {
    // 在所有的 Promise 完成后调用 getListInitial
    getListInitial();
  }).catch(error => {
  });
}
getOptions()
onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 250
    : window.innerHeight - 250;

});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 250
    : window.innerHeight - 250;
});
function fetchColumnConfig() {
  let data = JSON.parse(JSON.stringify(queryParams.value))
  console.log(data);
  const params = {
    templateName: '应免存栏',
    statQuarter: data.statQuarter,
    statYear: data.statYear
  }
  // 后端返回的数据（实际项目中从接口获取）
  queryTemplateColumnByTemplate(params).then(response => {
    // 存储配置数据
    columnConfigList.value = response.data;
    // 构建映射表：columnName -> 是否显示（showFlag === '1'）
    columnShowMap.value = response.data.reduce((map, item) => {
      map[item.columnName] = item.showFlag === '1';
      return map;
    }, {});
    console.log(columnShowMap.value);

  });
}
/** 导出按钮操作 */
function handleExport() {
  let exportUrl = tableShow.value === '1' ? `/bdh-animal-husbandry-api/report/pasAntiepidemicTemplate/pasLivestockImmuneReport/exportExcel` : `/bdh-animal-husbandry-api/report/pasLivestockImmuneGrpReport/exportExcel`
  postForExcel(exportUrl, queryParams.value, '应免存栏报表）');
}
function selectable(row, index) {
  if (row.dataId == 0) {
    return false
  }
  return true
}
/** 查询春/秋季应免存栏数量统计表列表 */
function getListInitial() {
  loading.value = true;
  if (
    queryParams.value.orgCode !== null &&
    queryParams.value.orgCode &&
    typeof queryParams.value.orgCode !== 'string'
  ) {
    queryParams.value.orgCode =
      queryParams.value.orgCode[queryParams.value.orgCode.length - 1]
  }
  let params = JSON.parse(JSON.stringify(queryParams.value))
  // 2024年春季用新表/2024年之前、2024年秋季、及2024年之后用旧表
  listPasLivestockImmuneReportOneLevelGrp(params).then(response => {
    pasLivestockImmuneReportList.value = response.data.records;
    // 调用接口获取后端配置（实际项目中替换为真实接口请求）
    fetchColumnConfig();
    tableShow.value = '2'
    if (response.data.records.length > 0) {
      var year = response.data.records[0].statYear + '年'
      var season = response.data.records[0].statQuarter == '0' ? '春季' : '秋季'
      seasonLabel.valuel = response.data.records[0].statQuarter == '0' ? '春防' : response.data.records[0].statQuarter == '1' ? '秋防' : ''
      labelForTime.value = Number(queryParams.value.statYear) == 2025 ? year + season + '畜禽存栏和应免数量统计表' : year + season + '畜禽存栏和应免数量统计'
    } else {
      var textTitle = (queryParams.value.statQuarter == '0' ? '春防' : queryParams.value.statQuarter == '1' ? '秋防' : '')
      labelForTime.value = queryParams.value.statYear + '年' + (Number(queryParams.value.statYear) == 2025 ? '春季畜禽存栏和应免数量统计表' : (textTitle + '畜禽存栏和应免数量统计'))
    }
    total.value = response.data.total;
    loading.value = false;
  });
}
// 数据状态字典翻译
function dataStatusFormat(row, column) {
  let dataStatus = null
  if (dataStatusOptions.value.length > 0) {
    dataStatusOptions.value.forEach((v, i) => {
      if (v.code == row.dataStatus) {
        dataStatus = v.name
      }
    })
  }
  return dataStatus;
}
// 季度字典翻译
function statQuarterFormat(row, column) {
  let statQuarter = null
  if (statQuarterOptions.value.length > 0) {
    statQuarterOptions.value.forEach((v, i) => {
      if (v.code == row.statQuarter) {
        statQuarter = v.name
      }
    })
  }
  return statQuarter;
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}
// 表单重置
function reset() {
  form.value = {
    livestockBeefSum: null,
    livestockCowNum: null,
    livestockBeefNum: null,
    livestockSheepNum: null,
    livestockOtherNum: null,
    poultrySum: null,
    poultryChickenNum: null,
    poultryDuckNum: null,
    poultryGooseNum: null,
    poultryOtherNum: null,
    fmdImmuneSum: null,
    fmdPigImmuneNum: null,
    fmdBeefImmuneSum: null,
    fmdCowImmuneNum: null,
    fmdBeefImmuneNum: null,
    fmdOtherImmuneNum: null,
    aiImmuneSum: null,
    aiChickenImmuneNum: null,
    aiDuckImmuneNum: null,
    aiGooseImmuneNum: null,
    aiOtherImmuneNum: null,
    hppbedImmuneNum: null,
    sfUmmuneNum: null,
    ndImmuneNum: null,
    bruceImmuneSum: null,
    bruceBeefImmuneSum: null,
    bruceCowImmuneNum: null,
    bruceBeefImmuneNum: null,
    bruceSheepImmuneNum: null,
    remark: null,
    dataStatus: null,
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    statusCd: null,
    dataId: null,
    statYear: null,
    statQuarter: null,
    orgCode: null,
    orgName: null,
    livestockSum: null,
    livestockPigNum: null,
    fmdSheepImmuneNum: null,
  };
  proxy.resetForm("formRef");;
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  if (
    queryParams.value.orgCode !== null &&
    queryParams.value.orgCode &&
    typeof queryParams.value.orgCode !== 'string'
  ) {
    ooorgCode.value =
      queryParams.value.orgCode[queryParams.value.orgCode.length - 1]
  } else {
    ooorgCode.value = queryParams.value.orgCode
  }
  getListInitial();
  flagStatus1.value = true
  flagStatus2.value = false
  flagStatus3.value = false
  flagStatus4.value = false
  statQuarterFlag.value = queryParams.value.statYear < 2024
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  queryParams.value.orgCode = getInitTreeCode(orgTreeList.value)
  queryParams.value.statQuarter = (new Date().getMonth() + 1) > 6 ? 1 : 0
  queryParams.value.orgCode = getInitTreeCode(orgTreeList.value)
  queryParams.value.orgName = getInitTreeName(orgTreeList.value)
  proxy.$refs["queryOrgSelect"].resetOrgCode('reset')
  handleQuery();
}
// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.dataId)
  dataStatus.value = selection.map(item => item.dataStatus)
  selection.map(item => {
    if (item.dataStatus == '02') {
      dataStatusFlag.value = true
    } else {
      dataStatusFlag.value = false
    }
    if (((item.orgCode.length == 6) && Number(item.childCount) == 0) || item.orgCode.length == 10) {
      lastLevelFlag.value = false
    } else {
      lastLevelFlag.value = true
    }
  })
  single.value = selection.length !== 1
  multiple.value = !selection.length
  if (!single.value) {
    statYear.value = selection[0].statYear
    statQuarter.value = selection[0].statQuarter
  } else {
    statYear.value = null
    statQuarter.value = null
  }
}

function switchList() {
  proxy.$refs.multipleTable.clearSelection();
  if (flagStatus1.value) {
    getListInitial()
  } else if (flagStatus2.value) {
    let params = {}
    params.orgCode = rowDataObj.value.orgCode
    params.statYear = rowDataObj.value.statYear
    params.statMonth = rowDataObj.value.statMonth
    params.rows = queryParams.value.rows
    params.page = queryParams.value.page
    params.statQuarter = rowDataObj.value.statQuarter
      listPasLivestockImmuneReport(params).then(response => {
        pasLivestockImmuneReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
  } else if (flagStatus3.value) {
    let params = {}
    params.orgCode = rowDataObj.value.orgCode
    params.statYear = rowDataObj.value.statYear
    params.statMonth = rowDataObj.value.statMonth
    params.rows = queryParams.value.rows
    params.page = queryParams.value.page
    params.statQuarter = rowDataObj.value.statQuarter
    if ((Number(params.statYear) == 2024 || Number(params.statYear) == 2025) && params.statQuarter == '0') {
      listPasLivestockImmuneReportGrp(params).then(response => {
        pasLivestockImmuneReportList.value = response.data.records;
        tableShow.value = '2'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    } else {
      listPasLivestockImmuneReport(params).then(response => {
        pasLivestockImmuneReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    }
  } else {
    let params = {}
    params.orgCode = rowDataObj.value.orgCode
    params.statYear = rowDataObj.value.statYear
    params.statMonth = rowDataObj.value.statMonth
    params.rows = queryParams.value.rows
    params.page = queryParams.value.page
    params.statQuarter = rowDataObj.value.statQuarter
    if ((Number(params.statYear) == 2024 || Number(params.statYear) == 2025) && params.statQuarter == '0') {
      listPasLivestockImmuneReportGrp(params).then(response => {
        pasLivestockImmuneReportList.value = response.data.records;
        tableShow.value = '2'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    } else {
      listPasLivestockImmuneReport(params).then(response => {
        pasLivestockImmuneReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    }
  }
}
//下级
function toDetail(row) {
  flagShow.value = true
  if (flagStatus1.value) {
    rowDataObj.value = row
    let params = {}
    params.orgCode = row.orgCode
    params.rows = queryParams.value.rows
    params.statYear = row.statYear
    params.statMonth = row.statMonth
    params.statQuarter = rowDataObj.value.statQuarter
    params.page = 1
    queryParams.value.page = 1
    if ((Number(params.statYear) == 2024 || Number(params.statYear) == 2025) && params.statQuarter == '0') {
      listPasLivestockImmuneReportGrp(params).then(response => {
        pasLivestockImmuneReportList.value = response.data.records;
        tableShow.value = '2'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    } else {
      listPasLivestockImmuneReport(params).then(response => {
        pasLivestockImmuneReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    }
    flagStatus1.value = false
    flagStatus2.value = true
    flagStatus3.value = false
    flagStatus4.value = false
  } else if (flagStatus2.value) {
    rowDataObj.value = row
    let params = {}
    params.orgCode = row.orgCode
    params.rows = queryParams.value.rows
    params.statYear = row.statYear
    params.statMonth = row.statMonth
    params.statQuarter = rowDataObj.value.statQuarter
    params.page = 1
    queryParams.value.page = 1
    if ((Number(params.statYear) == 2024 || Number(params.statYear) == 2025) && params.statQuarter == '0') {
      listPasLivestockImmuneReportGrp(params).then(response => {
        pasLivestockImmuneReportList.value = response.data.records;
        tableShow.value = '2'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    } else {
      listPasLivestockImmuneReport(params).then(response => {
        pasLivestockImmuneReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    }
    flagStatus1.value = false
    flagStatus2.value = false
    flagStatus3.value = true
    flagStatus4.value = false
  } else if (flagStatus3.value) {
    rowDataObj.value = row
    let params = {}
    params.orgCode = row.orgCode
    params.rows = queryParams.value.rows
    params.statYear = row.statYear
    params.statMonth = row.statMonth
    params.statQuarter = rowDataObj.value.statQuarter
    params.page = 1
    queryParams.value.page = 1
    if ((Number(params.statYear) == 2024 || Number(params.statYear) == 2025) && params.statQuarter == '0') {
      listPasLivestockImmuneReportGrp(params).then(response => {
        pasLivestockImmuneReportList.value = response.data.records;
        tableShow.value = '2'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    } else {
      listPasLivestockImmuneReport(params).then(response => {
        pasLivestockImmuneReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    }
    flagStatus1.value = false
    flagStatus2.value = false
    flagStatus3.value = false
    flagStatus4.value = true
  }
}
// 返回上级列表
function goBack() {
  if (flagStatus2.value) {
    flagShow.value = false
    queryParams.value.page = 1
    getListInitial()
    flagStatus1.value = true
    flagStatus2.value = false
    flagStatus3.value = false
    flagStatus4.value = false
  } else if (flagStatus3.value) {
    flagShow.value = true
    let params = {}
    params.orgCode = rowDataObj.value.orgCode.substring(0, 4)
    params.rows = queryParams.value.rows
    params.statYear = rowDataObj.value.statYear
    params.statMonth = rowDataObj.value.statMonth
    params.statQuarter = rowDataObj.value.statQuarter
    params.page = 1
    queryParams.value.page = 1
    if ((Number(params.statYear) == 2024 || Number(params.statYear) == 2025) && params.statQuarter == '0') {
      listPasLivestockImmuneReportGrp(params).then(response => {
        pasLivestockImmuneReportList.value = response.data.records;
        tableShow.value = '2'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    } else {
      listPasLivestockImmuneReport(params).then(response => {
        pasLivestockImmuneReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    }
    flagStatus1.value = false
    flagStatus2.value = true
    flagStatus3.value = false
    flagStatus4.value = false
  } else if (flagStatus4.value) {
    flagShow.value = true
    let params = {}
    ooorgCode.value.length == 2 ? params.orgCode = rowDataObj.value.orgCode.substring(0, 4) : ooorgCode.value.length == 4 ? params.orgCode = rowDataObj.value.orgCode.substring(0, 4) : ''
    params.rows = queryParams.value.rows
    params.statYear = rowDataObj.value.statYear
    params.statMonth = rowDataObj.value.statMonth
    params.statQuarter = rowDataObj.value.statQuarter
    params.page = 1
    queryParams.value.page = 1
    if ((Number(params.statYear) == 2024 || Number(params.statYear) == 2025) && params.statQuarter == '0') {
      listPasLivestockImmuneReportGrp(params).then(response => {
        pasLivestockImmuneReportList.value = response.data.records;
        tableShow.value = '2'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    } else {
      listPasLivestockImmuneReport(params).then(response => {
        pasLivestockImmuneReportList.value = response.data.records;
        tableShow.value = '1'
        nextTick(() => {
          proxy.$refs.multipleTable.doLayout();
        })
        total.value = response.data.total;
        loading.value = false;
      });
    }
    flagStatus1.value = false
    flagStatus2.value = false
    flagStatus3.value = true
    flagStatus4.value = false
  }
}



/** 修改按钮操作 */
function handleUpdate(row) {

  reset();
  const dataId = row.dataId || ids.value

  if (dataId === null || dataId.length < 1) {
    proxy.$modal.msgWarning("请选择要填写的数据");
    return;
  }
  if (dataId.length > 1) {
    proxy.$modal.msgWarning("选择数据过多，请选择其中一条数据");
    return;
  }
  if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
    getPasLivestockImmuneQGrp(dataId).then(response => {
      open.value = true;
      nextTick(() => {
        form.value = response.data;
        title.value = "应免存栏填报-填写本月月报";
      })
    });
  } else {
    getPasLivestockImmuneQ(dataId).then(response => {
      open.value = true;
      nextTick(() => {
        form.value.tableData = response.data;
        title.value = "应免存栏填报-填写本月月报";
      });
    });
  }
}


let handleOrgChange = (value, label) => {
  queryParams.value.orgCode = value
  queryParams.value.orgName = label
}

function handleLock() {
  const data = { ids: ids.value }
  const dataStatusCode = dataStatus.value[0]
  proxy.$modal.confirm(`请确定是否` + (dataStatusCode === '04' ? '解锁' : '锁定') + `已选记录`, "提示").then(function () {
    if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
      lockCompanyByIdsGrp(data).then(response => {
        let { data, success } = response
        if (success) {
          switchList();
          proxy.$modal.msgSuccess("操作成功");
        }
      });
    } else {
      lockCompanyByIds(data).then(response => {
        let { data, success } = response
        if (success) {
          switchList();
          proxy.$modal.msgSuccess("操作成功");
        }
      });
    }
  })
}

function tableRowClassName({ row, rowIndex }) {
  let rowClass = ''
  if (row.dataStatus == '03') {
    rowClass = 'danger';
  }
  return rowClass;
}

/** 数据填报按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate(valid => {
    if (valid) {
      if (form.value.dataId != null) {
        if (
          form.value.orgCode !== null &&
          form.value.orgCode &&
          typeof form.value.orgCode !== 'string'
        ) {
          form.value.orgCode =
            form.value.orgCode[form.value.orgCode.length - 1]
        }
        if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
          updatePasLivestockImmuneQGrp(form.value).then(response => {
            proxy.$modal.msgSuccess(response.msg);
            open.value = false
            switchList();
          }).catch(onerror => {
          });
        } else {
          updatePasLivestockImmuneQ(form.value).then(response => {
            proxy.$modal.msgSuccess(response.msg);
            open.value = false
            switchList();
          }).catch(onerror => {
          });
        }
      }
    }
  });
}

function precinctDataCommitBtn() {
  let params = {
    ids: ids.value,
  }
  if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
    submitManageByIdsGrp(params).then(response => {
      proxy.$modal.msgSuccess("提交成功");
      switchList()
    });
  } else {
    submitManageByIds(params).then(response => {
      proxy.$modal.msgSuccess("提交成功");
      switchList()
    });
  }
}
function precinctDataBackBtn() {
  let params = {
    ids: ids.value,
  }
  if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
    backManageByIdsGrp(params).then(response => {
      proxy.$modal.msgSuccess("退回成功");
      switchList()
    });
  } else {
    backManageByIds(params).then(response => {
      proxy.$modal.msgSuccess("退回成功");
      switchList()
    });
  }

}
function farmDataCommitBtn() {
  let params = {
    ids: ids.value,
  }
  if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
    submitFarmByIdsGrp(params).then(response => {
      proxy.$modal.msgSuccess("提交成功");
      switchList()
    });
  } else {
    submitFarmByIds(params).then(response => {
      proxy.$modal.msgSuccess("提交成功");
      switchList()
    });
  }
}
function farmDataBackBtn() {
  let params = {
    ids: ids.value,
  }
  if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
    backFarmByIdsGrp(params).then(response => {
      proxy.$modal.msgSuccess("退回成功");
      switchList()
    });
  } else {
    backFarmByIds(params).then(response => {
      proxy.$modal.msgSuccess("退回成功");
      switchList()
    });
  }
}
function companyDataCommitBtn() {
  let params = {
    ids: ids.value,
  }
  if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
    submitCompanyByIdsGrp(params).then(response => {
      proxy.$modal.msgSuccess("提交成功");
      switchList()
    });
  } else {
    submitCompanyByIds(params).then(response => {
      proxy.$modal.msgSuccess("提交成功");
      switchList()
    });
  }
}
function companyDataBackBtn() {
  let params = {
    ids: ids.value,
  }
  if ((Number(statYear.value) == 2024 || Number(statYear.value) == 2025) && statQuarter.value == '0') {
    backCompanyByIdsGrp(params).then(response => {
      proxy.$modal.msgSuccess("退回成功");
      switchList()
    });
  } else {
    backCompanyByIds(params).then(response => {
      proxy.$modal.msgSuccess("退回成功");
      switchList()
    });
  }
}

</script>

<style lang="scss">
.specialColor {
  color: red;
}

.el-table--medium th {
  padding: 0px 0;
}

.el-table th>.cell {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

.el-dialog__header {
  /* background-color: #d7dfe7; */
}

.danger {
  color: #F56C6C;
}
</style>