<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-collapse-transition>
        <el-form :model="queryParams" ref="queryForm" class="queryClass form-line" :inline="true" v-show="showSearch"
          label-width="90px">
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="年份" prop="statYear">
                <el-select v-model="queryParams.statYear" placeholder="请选择年份" :clearable="true">
                  <el-option v-for="dict in statYearOptions" :key="dict.code" :label="dict.name" :value="dict.code" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6" style="flex-grow: 1;">
              <el-form-item align="right">
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-collapse-transition>
    </div>

    <el-table :data="pasAntiepidemicTemplateList" :height="tableHeight" @selection-change="handleSelectionChange" stripe border>
      <el-table-column type="selection" width="55" align="center" :selectable="selectable" />
      <el-table-column label="年份" align="center" prop=statYear fixed />
      <el-table-column label="季度" align="center" prop="statQuarter" fixed>
        <template #default="scope">
          {{ scope.row.statQuarter === '0' ? '春季' : '秋季' }}
        </template>
      </el-table-column>
      <el-table-column label="功能" align="center" prop="templateName" />
      <el-table-column label="填报字段" align="center" prop="columnCount" />


      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="200">
        <template #default="scope">
            <el-button link type="primary" @click="configFields(scope.row)"
            v-hasPermi="['pasAntiepidemicTemplate:saveConfigFields']" style="flex-grow: 1;">配置字段</el-button>
          <el-button link type="primary" @click="autoInputImmuneDynamicTable(scope.row)"
                     v-hasPermi="['pasAntiepidemicTemplate:procAhAutoInputImmuneDynamicTable']" style="flex-grow: 1;">生成报表</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 防疫周报配置字段弹窗 -->
    <el-dialog v-model="configFieldsDialogVisible" title="配置字段" width="100%">
      <div style="margin-bottom: 20px; display: flex; gap: 20px;">
        <div>年份: {{ selectedYear }}</div>
        <div>季度: {{ selectedQuarter }}</div>
        <div>功能: {{ selectedFunction }}</div>
      </div>
      <el-table :data="configFieldsTableData" stripe border  height="300px">
        <!-- 多表头示例 -->
        <el-table-column label="防疫周报、防疫汇总" width="200"  align="center" >
          <el-table-column label="市、县、区" width="100"  align="center">
            <template #default="scope">
              <el-checkbox v-model="scope.row.orgCode" @change="updateCheckedColumnCount"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column label="口蹄疫" width="100"  align="center" >
            <el-table-column label="疫苗使用情况" width="100"  align="center">
              <el-table-column label="口蹄疫疫苗总计" width="100" align="center">
                <template #default="scope">
                  <el-checkbox v-model="scope.row.fmdVaccSum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="口蹄疫O型灭活疫苗(万毫升)" width="100" align="center">
                <el-table-column label="猪" width="100" align="center">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.fmdPigInactVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="口蹄疫O型合成肽疫苗(万毫升)" width="100" align="center">
                <el-table-column label="猪" width="100" align="center">
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.fmdPigSpvVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="口蹄疫O型-A型二价灭活疫苗(万毫升)" width="100" align="center">
                <el-table-column label="牛(不含奶牛)" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.fmdBeefVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="牛(含奶牛)" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.fmdBeefAndMilkbeefVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="羊" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.fmdSheepVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="其他畜" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.fmdOtherVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="奶牛" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.fmdMilkbeefVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table-column>
            <el-table-column label="免疫情况" width="100" align="center" >
              <el-table-column label="口蹄疫免疫总计" width="100" align="center">
                <template #default="scope">
                  <el-checkbox v-model="scope.row.fmdImmuneSum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="猪口蹄疫免疫合计" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.fmdPigImmuneSum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="猪口蹄疫O型免疫数(万头)" width="100" align="center" >
                <el-table-column label="猪" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.fmdPigImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="猪口蹄疫O型合成肽免疫数(万头)" width="100" align="center" >
                <el-table-column label="猪" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.fmdPigSpvImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="牛口蹄疫免疫合计" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.fmdBeefImmuneSum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="口蹄疫O型-A型二价灭活疫苗（万头）" width="100" align="center" >
                <el-table-column label="牛(不含奶牛)" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.fmdBeefImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="牛(含奶牛)" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.fmdBeefAndMilkbeefImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="羊" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.fmdSheepImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="其他畜" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.fmdOtherImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="奶牛" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.fmdMilkbeefImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table-column>

          </el-table-column>
          <el-table-column label="禽流感" width="100" align="center" >
            <el-table-column label="疫苗使用情况" width="100" align="center" >
              <el-table-column label="禽流感疫苗合计" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.aiVaccSum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="重组禽流感病毒(H5+H7)三价灭活疫苗(万毫升)" width="100" align="center" >
                <el-table-column label="鸡" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.aiChickenInactVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="鸭" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.aiDuckInactVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="鹅" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.aiGooseInactVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="其他畜" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.aiOtherInactVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table-column>
            <el-table-column label="免疫情况" width="100">
              <el-table-column label="禽流感免疫合计" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.aiImmuneSum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="重组禽流感病毒(H5+H7)三价免疫数(万羽)" width="100" align="center" >
                <el-table-column label="鸡" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.aiChickenImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="鸭" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.aiDuckImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="鹅" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.aiGooseImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="其他畜" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.aiOtherImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table-column>

          </el-table-column>
          <el-table-column label="猪瘟" width="100" align="center" >
            <el-table-column label="疫苗使用情况" width="100" align="center" >
              <el-table-column label="猪瘟疫苗合计" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.sfInactVaccSum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="猪瘟活疫苗(细胞源)(万头份)" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.sfCsInactVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="猪瘟活疫苗(传代细胞源)(万头份)" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.sfPcsInactVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="猪瘟活疫苗(脾淋源)(万头份)" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.sfSdsInactVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="猪瘟耐热保护剂活疫苗(细胞源)(万头份)" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.sfLvcsfhrpaInactVacc" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="免疫情况" width="100" align="center" >
              <el-table-column label="猪瘟免疫合计" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.sfImmuneSum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="猪瘟活疫苗(细胞源)(万头)" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.sfCsImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="猪瘟活疫苗(传代细胞源)(万头)" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.sfPcsImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="猪瘟活疫苗(脾淋源)(万头)" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.sfSdsImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="猪瘟耐热保护疫苗(细胞源)(万头)" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.sfLvcsfhrpaImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table-column>
          <el-table-column label="布鲁氏菌病" width="100" align="center" >
            <el-table-column label="疫苗使用情况" width="100" align="center" >
              <el-table-column label="布病疫苗合计" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.bruceVaccSum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="布病活疫苗(S2株)(万头份)" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.bruceS2ActVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="布病活疫苗(A19株)(万头份)" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.bruceA19ActVaccNum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="布病活疫苗(A19株)(万头份)奶牛" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.bruceA19ActVaccMilkBeefNum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="免疫情况" width="100">
              <el-table-column label="布病免疫总计" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.bruceImmuneSum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="布病免疫牛合计" width="100" align="center" >
                <template #default="scope">
                  <el-checkbox v-model="scope.row.bruceBeefImmuneSum" @change="updateCheckedColumnCount"></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column label="布病(S2株)免疫数(万头)" width="100" align="center" >
                <el-table-column label="牛(不含奶牛)" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.bruceS2BeefNoMilkbeefImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="布病(A19株)免疫数(万头)" width="100" align="center" >
                <el-table-column label="奶牛" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.bruceA19MilkbeefImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="布病(S2株)免疫数(万头)" width="100" align="center" >
                <el-table-column label="牛" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.bruceS2BeefImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="牛(含奶牛)" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.bruceS2BeefAndMilkbeefImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
                <el-table-column label="羊" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.bruceS2SheepImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="布病(A19株)免疫数(万头)" width="100" align="center" >
                <el-table-column label="牛(含奶牛)" width="100" align="center" >
                  <template #default="scope">
                    <el-checkbox v-model="scope.row.bruceA19BeefAndMilkbeefImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
                  </template>
                </el-table-column>
              </el-table-column>
            </el-table-column>
          </el-table-column>
        </el-table-column>
      </el-table>
      <div style="margin-bottom: 20px; display: flex; flex-direction: column; gap: 5px;">
        <div>可填报列:  {{ checkedColumnCount }}/{{ columnCount }}</div>
        <div>说明: 本设置完成后,用户填报时,仅可查看填报选中列内容</div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="configFieldsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveConfigFields">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 防疫周报配置字段弹窗 -->
    <el-dialog v-model="yyclConfigFieldsDialogVisible" title="配置字段" width="100%">
      <div style="margin-bottom: 20px; display: flex; gap: 20px;">
        <div>年份: {{ selectedYear }}</div>
        <div>季度: {{ selectedQuarter }}</div>
        <div>功能: {{ selectedFunction }}</div>
      </div>
      <el-table :data="configFieldsTableData" stripe border  height="300px">
        <!-- 多表头示例 -->
        <el-table-column label="畜禽存栏和应免数量统计表" width="200"  align="center" >
          <el-table-column label="市、县、区" width="100"  align="center">
            <template #default="scope">
              <el-checkbox v-model="scope.row.orgCode" @change="updateCheckedColumnCount"></el-checkbox>
            </template>
          </el-table-column>
          <el-table-column :label="selectedQuarter === '春季' ? '春防期间存栏数量(万头/只/羽)' : '秋防期间存栏数量(万头/只/羽)'" width="100"  align="center">
            <el-table-column label="畜合计" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.livestockAllSum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="猪" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.livestockPigSum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="牛合计" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.livestockBeefSum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="奶牛" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.livestockCowNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="牛(不含奶牛)" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.livestockBeefNoCowNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="牛(含奶牛)" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.livestockBeefAndCowNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="羊" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.livestockSheepNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="其它动物" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.livestockOtherNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="禽合计" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.poultrySum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="鸡" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.poultryChickenNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="鸭" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.poultryDuckNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="鹅" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.poultryGooseNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="其它禽" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.poultryOtherNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column :label="selectedQuarter === '春季' ? '春防期间牲畜口蹄疫应免数量(万头/只)' : '秋防期间牲畜口蹄疫应免数量(万头/只)'" width="100"  align="center">
            <el-table-column label="畜合计" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.fmdImmuneSum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="猪" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.fmdPigImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="牛合计" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.fmdBeefImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="奶牛" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.fmdCowImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="牛(不含奶牛)" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.fmdBeefNoCowImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="牛(含奶牛)" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.fmdBeefAndCowImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="羊" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.fmdSheepImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="其它动物" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.fmdOtherImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column :label="selectedQuarter === '春季' ? '春防期间禽流感应免数量(万羽)' : '秋防期间禽流感应免数量(万羽)'" width="100"  align="center">
            <el-table-column label="禽合计" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.aiImmuneSum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="鸡" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.aiChickenImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="鸭" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.aiDuckImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="鹅" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.aiGooseImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="其它禽" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.aiOtherImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column :label="selectedQuarter === '春季' ? '春防期间布鲁氏菌病免疫数量(万头)' : '秋防期间布鲁氏菌病免疫数量(万头)'" width="100"  align="center">
            <el-table-column label="合计" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.bruceImmuneSum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="牛合计" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.bruceBeefImmuneSum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="奶牛" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.bruceCowImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="牛(不含奶牛)" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.bruceBeefNoCowImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="牛(含奶牛)" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.bruceBeefAndCowImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
            <el-table-column label="羊" width="100"  align="center">
              <template #default="scope">
                <el-checkbox v-model="scope.row.bruceSheepImmuneNum" @change="updateCheckedColumnCount"></el-checkbox>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
      </el-table>
      <div style="margin-bottom: 20px; display: flex; flex-direction: column; gap: 5px;">
        <div>可填报列:  {{ checkedColumnCount }}/{{ columnCount }}</div>
        <div>说明: 本设置完成后,用户填报时,仅可查看填报选中列内容</div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="yyclConfigFieldsDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveConfigFields">确定</el-button>
        </span>
      </template>
    </el-dialog>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script setup name="/report/pasAntiepidemicTemplate/queryByPage">
  import { onMounted } from "vue";
  import '@/views/sysanimalhusbandry/assets/styles/index.scss' // global css
  import OrgSelectsInside from '@/views/sysanimalhusbandry/components/OrgSelectsInside/index'
  import { pasAntiepidemicTemplateListByPage, queryTemplateColumns,saveTemplateConfigField,procAhAutoInputImmuneDynamicTable } from "@/api/sysanimalhusbandry/report/pasAntiepidemicTemplateList";
  import { getDicts, getOrg } from "@/api/sysanimalhusbandry/dict";
  import { getOrgOnlyFarmTree } from "@/api/sysanimalhusbandry/dict";
  import { updateButtonStyle, getInitTreeCode, getInitTreeName } from "@/api/sysanimalhusbandry/utils/cop";
  import { nextTick, ref, defineExpose } from 'vue'
  import { postForExcel } from '@/api/sysanimalhusbandry/excel'
  import { getToken } from "@/utils/auth";
  import axios from "axios";

  const {
    proxy
  } = getCurrentInstance();
  const env = import.meta.env
  const orgTreeList = ref([]);
  const selectedYear = ref('');
  const templateId = ref('');
  const selectedQuarter = ref('');
  const selectedFunction = ref('');
  const columnCount = ref('');
  const total = ref(0);
  const checkedColumnCount = ref(0);
  const showSearch = ref(true);
  const importform  = reactive({
    statYear: null,
    orgCode: null
  })
  const headers  = reactive({
    Authorization: "Bearer " + getToken()
  })
  const importrules = reactive({
    statYear: [
      { required: true, message: "请输入年份", trigger: 'blur' }
    ],
  })


  const pasAntiepidemicTemplateList = ref([]);
  const title = ref('');
  const statYearOptions = ref([]);
  const lastLevelFlag = ref(true);
  const searchHeight = ref(0); // 搜索栏高度
  const tableHeight = ref(400); // 表格高度
  const searchDom = ref(); // 搜索栏dom
  const data = reactive({
    // 查询参数
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      statYear: String(new Date().getFullYear()),
    }
  })

  // 防疫周报控制配置字段弹窗的显示与隐藏
  const configFieldsDialogVisible = ref(false);

  // 应用存栏配置字段弹窗的显示与隐藏
  const yyclConfigFieldsDialogVisible = ref(false);
  // 配置字段表格数据
  const configFieldsTableData = ref([]);
  const updateCheckedColumnCount = () => {
    let count = 0;
    configFieldsTableData.value.forEach((row) => {
      for (const key in row) {
        if (typeof row[key] === 'boolean' && row[key]) {
          count++;
        }
      }
    });
    checkedColumnCount.value = count;
  };
  // 点击配置字段按钮触发的方法
  const configFields = (row) => {
    templateId.value = row.id;
    const body = {
      templateId: templateId.value
    };
    queryTemplateColumns(body).then(response => {
      configFieldsTableData.value = response.data.columns;
      columnCount.value = response.data.allColumnCount;
      updateCheckedColumnCount();
    });
    // 初始化勾选数量
    selectedYear.value = row.statYear;
    selectedQuarter.value = row.statQuarter === '0' ? '春季' : '秋季';
    selectedFunction.value = row.templateName;
    if (row.templateName === '防疫周报及防疫汇总') {
      configFieldsDialogVisible.value = true;
    } else {
      yyclConfigFieldsDialogVisible.value = true;
    }
  };

  function autoInputImmuneDynamicTable(row) {
    const body = {
      statQuarter: row.statQuarter,
      templateName: row.templateName,
      statYear: row.statYear
    };
    procAhAutoInputImmuneDynamicTable(body).then(response => {
      if (response.code === 0 && response.success === true) {
        proxy.$message.success('生成成功');
      } else {
        proxy.$message.error(response.msg);
      }
    });
  }

  // 保存配置字段的方法
  const saveConfigFields = () => {
    // 这里可以添加保存逻辑
    const body = {
      templateId: templateId.value,
      columns: configFieldsTableData.value
    };
    saveTemplateConfigField(body).then(response => {
      if (response.code === 0 && response.success === true) {
        configFieldsDialogVisible.value = false;
        yyclConfigFieldsDialogVisible.value = false;
        handleQuery()
      } else {
        configFieldsDialogVisible.value = false;
        yyclConfigFieldsDialogVisible.value = false;
        proxy.$message.error(errorMessages);
      }
    });

  };


  const {
    queryParams
  } = toRefs(data);
  const getOptions = () => {
    getDicts("stat_year").then(response => {
      statYearOptions.value = response.data;
    });

    getOrgOnlyFarmTree().then(response => {
      orgTreeList.value = response.data;
      queryParams.value.orgCode = getInitTreeCode(orgTreeList.value)[0];
    })
    getList();
  }
  getOptions()
  updateButtonStyle()

  onMounted(() => {
		searchHeight.value = searchDom.value?.clientHeight;
		tableHeight.value = showSearch.value
			? window.innerHeight - searchHeight.value - 220
			: window.innerHeight - 220;
	});
	watch(showSearch, (value) => {
		tableHeight.value = showSearch.value
			? window.innerHeight - searchHeight.value - 220
			: window.innerHeight - 220;
	});

  /** 查询生猪养殖户列表 */
  function getList() {
    pasAntiepidemicTemplateListByPage(queryParams.value).then(response => {
      pasAntiepidemicTemplateList.value = response.data.records;
      total.value = response.data.total;
    });
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryForm");
    handleQuery();
  }




</script>

