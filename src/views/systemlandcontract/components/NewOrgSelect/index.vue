<!--
@name:
@description:
@author: qikai
@time: 2021-07-05 10:55:09
注意： 无论什么时候handleOrgChange 都会返回值，如果返回 first 则表示第一次加载，可以调用 getlist,否则表示点击事件
-->
<template>
  <el-tooltip
    :content="titleHover"
    placement="top"
    :disabled="!titleHover || !farmValues"
  >
    <div style="width: 100%">
      <el-cascader
        style="width: 100%"
        :disabled="isdisabled"
        v-model="farmValues"
        :clearable="isclearable"
        :options="farmList"
        ref="orgSelect"
        :props="{
          label: 'orgName',
          value: 'orgCode',
          expandTrigger: 'hover',
          checkStrictly: checkStrictly,
        }"
        :placeholder="placeholderText"
        @change="handleOrgChange"
        filterable
      ></el-cascader>
    </div>
  </el-tooltip>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from "vue";
import useLandOrgCodeStore from "@/store/modules/landOrgCode";
import {
  queryOrgTreeByUserOrg,
  queryOrgTreeMapByUserOrg,
  queryOrgTreeByUserFarmOrg,
  queryAllOrgTree,
} from "@/api/systemlandcontract/common";
const landOrgCodeStore = useLandOrgCodeStore();
const props = defineProps({
  currentOrgValue: {
    default: null,
  },
  isclearable: {
    type: Boolean,
    default: true,
  },
  isdisabled: {
    type: Boolean,
    default: false,
  },
  //默认选择集团
  isGroup: {
    type: Boolean,
    default: false,
  },
  // 是否默认选择第一个
  defaultType: {
    type: Boolean,
    default: true,
  },
  placeholderText: {
    type: String,
    default: "请选择所在单位",
  },
  //显示层级
  showLevel: {
    type: Number,
    default: 0,
  },
  //是否单选框
  checkStrictly: {
    type: Boolean,
    default: true,
  },
  //使用哪个接口
  isInterface: {
    type: String,
    default: "queryOrgTreeByUserOrg",
  },
});

const emit = defineEmits(["handleOrgChange", "firstTrip"]);

const orgSelect = ref(null); // 用于访问 el-cascader 实例
const farmList = ref([]);
const farmValues = ref([]);
const titleHover = ref("");

const obtainData = (res, type) => {
  if (res.code === 0) {
    const processedData = processOrgData(res.data);
    farmList.value = processedData;
    if (type != "reset" && props.currentOrgValue) {
      farmValues.value = props.currentOrgValue;
      emit("handleOrgChange", props.currentOrgValue);
      nextTick(() => {
        if (orgSelect.value) {
          let arrNode = orgSelect.value.getCheckedNodes(false)[0];
          if (!!arrNode) {
            titleHover.value = arrNode.pathLabels.join("/");
          }
        }
      });
      if (type == "first") {
        emit("firstTrip");
      }
      return; // 如果有传入值，直接返回，不执行默认值逻辑
    }
    if (props.defaultType) {
      function findOrgId(arr) {
        if (arr[0] && arr[0].orgLevel >= 4) {
          return arr[0].orgCode;
        }
        for (let i = 0; i < arr.length; i++) {
          if (arr[i].orgLevel === 3 || arr[i].orgLevel === props.showLevel) {
            return arr[i].orgCode;
          } else if (arr[i].children && arr[i].children.length !== 0) {
            const found = findOrgId(arr[i].children);
            if (found) return found;
          }
        }
        return null;
      }

      if (props.isGroup) {
        farmValues.value = farmList.value[0].orgCode;
        emit("handleOrgChange", farmValues.value);
      } else {
        const defaultOrgId = findOrgId(farmList.value);
        if (defaultOrgId) {
          farmValues.value = defaultOrgId;
          emit("handleOrgChange", defaultOrgId);
        }
      }
    } else if (res.data.length === 1 && res.data[0].children === null) {
      farmValues.value = res.data[0].orgCode;
      emit("handleOrgChange", res.data[0].orgCode);
    }
  }
  if (type == "first") {
    emit("firstTrip");
  }
  nextTick(() => {
    if (orgSelect.value) {
      let arrNode = orgSelect.value.getCheckedNodes(false)[0];
      if (!!arrNode) {
        titleHover.value = arrNode.pathLabels.join("/");
      }
    }
  });
};

// 处理组织数据的方法
const processOrgData = (data) => {
  if (!data) return [];

  const processNode = (node) => {
    const newNode = { ...node };

    if (props.showLevel > 0 && props.showLevel <= 4) {
      if (node.orgLevel >= props.showLevel) {
        newNode.children = null;
      } else if (node.children && node.children.length > 0) {
        newNode.children = node.children.map((child) => processNode(child));
      }
    }

    return newNode;
  };

  return data.map((node) => processNode(node));
};

// 监听showLevel的变化
watch(
  () => props.showLevel,
  (newValue, oldValue) => {
    if (newValue !== oldValue) {
      // 重新处理现有数据
      const reprocessedData = processOrgData(farmList.value);
      farmList.value = reprocessedData;

      // 如果当前选中的值不在新的层级范围内，重置选择
      if (farmValues.value) {
        const findNode = (list, value) => {
          for (const node of list) {
            if (node.orgCode === value) return true;
            if (node.children && node.children.length > 0) {
              if (findNode(node.children, value)) return true;
            }
          }
          return false;
        };

        if (!findNode(reprocessedData, farmValues.value)) {
          farmValues.value = null;
          emit("handleOrgChange", null);
        }
      }
    }
  },
  { immediate: true }
);

// 监听组织数据源的变化
watch(
  () => landOrgCodeStore.orgTree,
  (newValue) => {
    if (newValue && Object.keys(newValue).length > 0) {
      obtainData(newValue);
    }
  },
  { deep: true }
);

const getCascader = (type) => {
  farmValues.value = props.currentOrgValue;
  if (props.isInterface == "queryOrgTreeMapByUserOrg") {
    //收费报表专用
    if (Object.keys(landOrgCodeStore.reportOrgTree).length != 0) {
      obtainData(landOrgCodeStore.reportOrgTree, type);
    } else {
      landOrgCodeStore.getReportOrgTree().then((res) => {
        if (!res) {
          queryOrgTreeMapByUserOrg().then((resdata) => {
            obtainData(resdata, type);
          });
        } else {
          obtainData(res, type);
        }
      });
    }
  } else if (props.isInterface == "allTree") {
    // 展示所有组织机构
    if (Object.keys(landOrgCodeStore.allOrgTree).length != 0) {
      obtainData(landOrgCodeStore.allOrgTree, type);
    } else {
      landOrgCodeStore.getAllOrgTree().then((res) => {
        if (!res) {
          queryAllOrgTree().then((resdata) => {
            obtainData(resdata, type);
          });
        } else {
          obtainData(res, type);
        }
      });
    }
  } else if (props.isInterface == "queryOrgTreeByUserFarmOrg") {
    //相对方,承包准入专用
    if (Object.keys(landOrgCodeStore.userOrgTree).length != 0) {
      obtainData(landOrgCodeStore.userOrgTree, type);
    } else {
      landOrgCodeStore.getUserOrgTree().then((res) => {
        if (!res) {
          queryOrgTreeByUserFarmOrg().then((resdata) => {
            obtainData(resdata, type);
          });
        } else {
          obtainData(res, type);
        }
      });
    }
  } else {
    if (Object.keys(landOrgCodeStore.orgTree).length != 0) {
      obtainData(landOrgCodeStore.orgTree, type);
    } else {
      landOrgCodeStore.getQueryOrgTreeByUserOrg().then((res) => {
        if (!res) {
          queryOrgTreeByUserOrg().then((resdata) => {
            obtainData(resdata, type);
          });
        } else {
          obtainData(res, type);
        }
      });
    }
  }
};

const getChangeName = () => {
  const selectedCode = farmValues.value;
  if (!selectedCode || !farmList.value || farmList.value.length === 0) return "";

  // 深度查找匹配的节点
  const findNode = (list, code) => {
    for (let node of list) {
      if (node.orgCode === code) return [node.orgName];
      if (node.children && node.children.length > 0) {
        const res = findNode(node.children, code);
        if (res) return [node.orgName, ...res];
      }
    }
    return null;
  };

  const pathLabels = findNode(farmList.value, selectedCode);
  return pathLabels ? pathLabels.join("-") : "";
};

const handleOrgChange = (orgList) => {
  let orgCode;
  if (farmValues.value == "") {
    orgCode = null;
  } else {
    // 获取选中的节点
    // 确保 orgSelect 已经被渲染并且有值
    if (orgSelect.value) {
      // const checkedNodes = orgSelect.value.getCheckedNodes(true); // 原来的代码
      const checkedNodes = orgSelect.value.getCheckedNodes(false); // 修改为获取所有选中的节点
      // ... existing code ...
      // 判断选中的节点数组是否为空
      if (checkedNodes && checkedNodes.length > 0) {
        orgCode = checkedNodes[0].value;
      } else {
        // 如果数组为空，说明没有选中任何节点，将orgCode设置为null
        orgCode = null;
      }
    } else {
      orgCode = null; // 如果 orgSelect.value 不存在，也设置为 null
    }
  }
  emit("handleOrgChange", orgCode, "click");
  // 确保 orgSelect 已经被渲染并且有值

  if (orgSelect.value) {
    orgSelect.value.togglePopperVisible(false);
  }
};

onMounted(async () => {
  getCascader("first");
});

// 监听 currentOrgValue 的变化
watch(
  () => props.currentOrgValue,
  (newValue) => {
    if (newValue !== undefined && newValue !== null) {
      farmValues.value = newValue;
      // 如果组织机构数据已经加载，直接更新值
      if (farmList.value && farmList.value.length > 0) {
        emit("handleOrgChange", newValue);
      } else {
        // 如果组织机构数据还未加载，重新获取数据
        getCascader();
      }
    }
  },
  { immediate: true }
);

// 监听 showLevel 的变化
watch(
  () => props.showLevel,
  (newValue) => {
    if (newValue && farmList.value) {
      const processedData = processOrgData(farmList.value);
      farmList.value = processedData;
    }
  },
  { immediate: true }
);
// 监听 farmValues 的变化并更新 titleHover
watch(
  () => farmValues.value,
  (newVal) => {
    if (!newVal) {
      titleHover.value = "";
      return;
    }

    nextTick(() => {
      if (orgSelect.value) {
        const checkedNodes = orgSelect.value.getCheckedNodes(false);
        if (checkedNodes && checkedNodes.length > 0) {
          titleHover.value = checkedNodes[0].pathLabels.join("/");
        } else {
          titleHover.value = "";
        }
      }
    });
  },
  { immediate: true }
);

// 如果需要暴露方法给父组件调用，可以在这里定义并返回
defineExpose({
  getChangeName,
  getCascader,
  farmValues
});
</script>

<style scoped>
.el-cascader {
  width: 100%;
}
.el-cascader-panel .el-radio {
  width: 100%;
  height: 100%;
  z-index: 10;
  position: absolute;
  top: 10px;
  right: 10px;
}
.el-cascader-panel .el-radio__input {
  visibility: hidden;
}
.el-cascader-panel .el-cascader-node__postfix {
  top: 10px;
}
</style>
