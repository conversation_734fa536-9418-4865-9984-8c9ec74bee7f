<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-form :model="queryParams" ref="queryForm" v-show="showSearch" label-width="100px" class="form-line" >
        <el-row style="display: flex; flex-wrap: wrap" :gutter="20">
          <el-col :span="6">
            <el-form-item label="年度" prop="yearNo" label-width="45px">
              <el-select v-model="queryParams.yearNo" placeholder="请选择年度" clearable >
                <el-option
                    v-for="dict in yearNoOptions"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="所在单位" prop="organizationNo">
              <new-org-select
                  style="width: 100%"
                  @handleOrgChange="handleOrgChange"
                  :currentOrgValue="queryParams.organizationNo"
                  ref="newOrgSelect"
                  :showLevel="3"
                  @firstTrip="firstTripFun"
              ></new-org-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" align="right">
            <el-button icon="Refresh"  @click="resetQuery">重置</el-button>
            <el-button type="primary" icon="Search"  @click="handleQuery">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <el-row :gutter="10" class="mb8" style="margin-bottom: 16px;">
      <el-col :span="1.5">
        <el-button
          icon="Plus"
          type="primary"
          @click="handleAdd"
          v-hasPermi="['farmChargeConfig:insert']"
        >新建</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          icon="Edit"-->
<!--          -->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['farmChargeConfig:update']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['farmChargeConfig:removeAll']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          icon="Document"
          @click="handleCopy"
          v-hasPermi="['farmChargeConfig:copy']"
        >复制年度</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border ref="tables" :height="heightCalc" :data="farmChargeConfigList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center"/>
      <!--      <el-table-column label="主键" align="center" prop="lcFarmChargeConfigId" />-->
      <el-table-column label="年度" align="center" prop="yearNo" :formatter="yearNoFormat"/>
      <el-table-column label="所在单位" width="120" align="center" prop="organizationName"/>
      <el-table-column label="是否实名认证" align="center" prop="ifCertification" :formatter="ifCertificationFormat"/>
      <el-table-column label="是否线上签字" align="center" prop="billSignOnline" :formatter="billSignOnlineFormat"/>
      <el-table-column label="收费是否拍照" align="center" prop="ifChargePhoto" :formatter="ifChargePhotoFormat"/>
      <el-table-column label="线上收费是否自动确认" align="center" prop="onlinePayAutoConfirm" :formatter="onlinePayAutoConfirmFormat"/>
      <el-table-column label="票据联数" align="center" prop="billNumber"/>
      <el-table-column label="默认打印票据联数" align="center" prop="printBillNumberNos">
        <template #default="scope">

          {{ !!scope.row.printBillNumberNos ? scope.row.printBillNumberNos : '全部' }}
        </template>
      </el-table-column>
      <el-table-column label="收费人数" align="center" prop="chargePerson"/>
      <el-table-column label="手续费比例" align="center" prop="commissionRate"/>
      <el-table-column label="手续费封顶" align="center" prop="commissionMax"/>
      <el-table-column label="线上业务办理开始时间" align="center" prop="onlinePayStartTime"/>
      <el-table-column label="线上业务办理结束时间" align="center" prop="onlinePayEndTime"/>

<!--      <el-table-column label="承包费农贷数据来源" align="center" prop="contractChargeTypeNo"-->
<!--                       :formatter="contractChargeTypeNoFormat"/>-->
<!--      <el-table-column label="其他收费农贷数据来源" align="center" prop="otherChargeTypeNo" :formatter="otherChargeTypeNoFormat"/>-->

      <!--      <el-table-column label="合同开始日期" align="center" prop="contractStartDate" />-->
      <!--      <el-table-column label="合同终止日期" align="center" prop="contractEndDate" />-->
      <!--      <el-table-column label="收费截止日期" align="center" prop="chargeEndDate" />-->
      <!--      <el-table-column label="违约金%" align="center" prop="penalty" />-->
      <!--      <el-table-column label="双方约定的其他事项" show-overflow-tooltip align="center" prop="otherMatters" />-->

      <!--      <el-table-column label="所在单位" align="center" prop="organizationNo" />-->
      <el-table-column label="备注" align="center" prop="remark"/>
      <el-table-column label="操作" width="150px" fixed="right" align="center" class-name="small-padding fixed-width" >
        <template #default="scope">
            <el-button
              link type="primary"
              @click="signBill(scope.row)"
              v-hasPermi="['farmChargeConfig:preview']"
              :disabled="pdfloading"
            >打印票据</el-button>
          <el-button
            link type="primary"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['farmChargeConfig:update']"
          >修改</el-button>
          <el-button
            link type="primary"
            @click="handleDelete(scope.row)"
            v-hasPermi="['farmChargeConfig:removeOne']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      v-model:page="queryParams.page"
      v-model:limit="queryParams.rows"
      @pagination="getList"
    />
    <!-- 添加或修改sysChargeSubject对话框 -->
    <el-dialog :title="title" v-model="openCopy" width="764px" append-to-body :close-on-click-modal="false">
      <el-form ref="formCopy" :model="formCopy" :rules="rules" label-width="80px" label-position="top">
        <el-row style="display: flex;flex-wrap: wrap;align-items: center;">
          <el-col :span="9">
            <el-form-item label="所在单位" prop="organizationNo">
              <new-org-select
                ref="copyNewOrgSelect"
                :size="size"
                @handleOrgChange="handleOrgChangeCopy"
                :currentOrgValue="formCopy.organizationNo"
                :showLevel="3"
              ></new-org-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="display: flex;flex-wrap: wrap">
          <el-col :span="9">
            <el-form-item label="年度" prop="copyByYear">
              <el-select v-model="formCopy.copyByYear" placeholder="请选择年度">
                <el-option
                  v-for="dict in yearNoOptions"
                  :key="dict.code"
                  :label="dict.name"
                  :value="parseInt(dict.code)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6" style="text-align: center;    display: flex;justify-content: center;align-items: center;">--复制到-></el-col>
          <el-col :span="9">
            <el-form-item label="年度" prop="yearNo">
              <el-select v-model="formCopy.yearNo" placeholder="请选择年度">
                <el-option
                  v-for="dict in yearNoOptions"
                  :key="dict.code"
                  :label="dict.name"
                  :value="parseInt(dict.code)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitCopyForm" :loading="copyLoad" :disabled="copyLoad">确 定</el-button>
          <el-button @click="openCopy=false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 添加或修改农场收费配置对话框 -->
    <el-dialog :title="title" v-model="open" v-if="open" width="1004px" @opened="opened" append-to-body :close-on-click-modal="false">
      <div style="height: 400px;">
        <el-scrollbar style="height: 100%">
          <el-form ref="form" :model="form" style="margin-right: 10px;" :rules="rules" label-width="180px" label-position="top">
            <el-row style="display: flex;flex-wrap: wrap" :gutter="10">
              <el-col :span="8">
                <el-form-item label="年度" prop="yearNo">
                  <el-select v-model="form.yearNo" @change="yearNofun" placeholder="请选择年度"  :disabled="updateType">
                    <el-option
                      v-for="dict in yearNoOptions"
                      :key="dict.code"
                      :label="dict.name"
                      :value="parseInt(dict.code)"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所在单位" prop="organizationNo">
                  <new-org-select
                    :isclearable="false"
                    @handleOrgChange="handleOrgChange3"
                    :currentOrgValue="form.organizationNo"
                    :showLevel="3"
                    :isdisabled="updateType"
                  ></new-org-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="是否实名认证" prop="ifCertification">
                  <el-select v-model="form.ifCertification" placeholder="请选择是否实名认证">
                    <el-option
                      v-for="dict in ifCertificationOptions"
                      :key="dict.code"
                      :label="dict.name"
                      :value="parseInt(dict.code)"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="选择财务软件" prop="financialSoftwareNo">
                  <el-select v-model="form.financialSoftwareNo" placeholder="请选择财务软件">
                    <el-option
                      v-for="dict in financialSoftwareNoOptions"
                      :key="dict.code"
                      :label="dict.name"
                      :value="parseInt(dict.code)"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="手续费封顶" prop="commissionMax">
                  <el-input v-model="form.commissionMax" placeholder="请输入手续费封顶" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="票据联数" prop="billNumber">
                  <el-input-number
                      style="width: 100%"
                    @change="changebillNumber"
                    v-model="form.billNumber"
                    placeholder="请输入票据联数"
                    :min="3"
                    :max="4"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="手续费比例" prop="commissionRate" style="position: relative;margin-bottom: 35px;">
                  <el-input v-model="form.commissionRate" placeholder="请输入手续费比例"/>
                  <div class="titleTips">
                    参考格式：0.0000,无手续费：为空，或填0
                  </div>
                </el-form-item>
              </el-col>
              <!-- 多选【逗号分隔】,0.全部【默认】 -->
              <el-col :span="8">

                <el-form-item label="默认打印票据联数" prop="printBillNumberNos">
                  <!--              <el-input v-model="form.printBillNumberNos" placeholder="请勾选" disabled/>-->

                  <el-cascader
                    collapse-tags
                    :options="checkList"
                    v-model="form.printBillNumberNosArr"
                    :props="{ multiple: true, checkStrictly: true }"
                    placeholder="0"
                    clearable></el-cascader>
                  <div class="titleTips">请根据"票据联数"后进行勾选,默认0：代表全部</div>
                </el-form-item>
              </el-col>
              <!--          <el-col :span="12">-->
              <!--            <el-form-item label="收费截至日期" prop="chargeEndDate">-->
              <!--              <el-date-picker-->
              <!--                format="yyyy 年 MM 月 dd 日"-->
              <!--                value-format="yyyy-MM-dd"-->
              <!--                v-model="form.chargeEndDate"-->
              <!--                type="date"-->
              <!--                placeholder="选择日期">-->
              <!--              </el-date-picker>-->
              <!--            </el-form-item>-->
              <!--          </el-col>-->
              <!--          <el-col :span="12">-->
              <!--            <el-form-item label="违约金比例" prop="penalty">-->
              <!--              <el-input v-model="form.penalty" placeholder="请输入违约金比例" />-->
              <!--            </el-form-item>-->
              <!--          </el-col>-->
              <el-col :span="8">
                <el-form-item label="签字设备" prop="signDeviceTypeNo">
                  <el-select v-model="form.signDeviceTypeNo" placeholder="请选择签字设备" >
                    <el-option
                      v-for="dict in signDeviceTypeNoOptions"
                      :key="dict.code"
                      :label="dict.name"
                      :value="parseInt(dict.code)"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="视频采集设备" prop="photoDeviceTypeNo">
                  <el-select v-model="form.photoDeviceTypeNo" placeholder="请选择视频采集设备" >
                    <el-option
                      v-for="dict in photoDeviceTypeNoOptions"
                      :key="dict.code"
                      :label="dict.name"
                      :value="parseInt(dict.code)"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="收费人数" prop="chargePerson">
                  <el-select v-model="form.chargePerson" placeholder="请选择收费人数">
                    <el-option
                      v-for="item in chargePersonOptions"
                      :key="item"
                      :label="item"
                      :value="parseInt(item)"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!--          <el-col :span="12">-->
              <!--            <el-form-item label="合同日期范围" prop="contract">-->
              <!--              <el-date-picker-->
              <!--                v-model="form.contract"-->
              <!--                type="daterange"-->
              <!--                format="yyyy 年 MM 月 dd 日"-->
              <!--                value-format="yyyy-MM-dd"-->
              <!--                range-separator="至"-->
              <!--                start-placeholder="合同开始日期"-->
              <!--                end-placeholder="合同结束日期">-->
              <!--              </el-date-picker>-->
              <!--            </el-form-item>-->
              <!--          </el-col>-->
              <el-col :span="8">
                <el-form-item label="备注" prop="remark">
                  <el-input v-model="form.remark" maxlength="100" placeholder="请输入备注" />
                </el-form-item>
              </el-col>
              <!--          <el-col :span="12">-->
              <!--            <el-form-item label="承包费农贷数据来源" prop="contractChargeTypeNo">-->
              <!--              <el-select v-model="form.contractChargeTypeNo" placeholder="请选择承包费农贷数据来源" >-->
              <!--                <el-option-->
              <!--                  v-for="dict in chargeTypeNoOptions"-->
              <!--                  :key="dict.code"-->
              <!--                  :label="dict.name"-->
              <!--                  :value="parseInt(dict.code)"-->
              <!--                ></el-option>-->
              <!--              </el-select>-->
              <!--            </el-form-item>-->
              <!--          </el-col>-->
              <!--          <el-col :span="12">-->
              <!--            <el-form-item label="其他收费农贷数据来源" prop="otherChargeTypeNo">-->
              <!--              <el-select v-model="form.otherChargeTypeNo" placeholder="请选择其他收费农贷数据来源" >-->
              <!--                <el-option-->
              <!--                  v-for="dict in chargeTypeNoOptions"-->
              <!--                  :key="dict.code"-->
              <!--                  :label="dict.name"-->
              <!--                  :value="parseInt(dict.code)"-->
              <!--                ></el-option>-->
              <!--              </el-select>-->
              <!--            </el-form-item>-->
              <!--          </el-col>-->
              <el-col :span="8">
                <el-form-item label="统一收款人姓名" prop="unifiedChargeStaffId">
                  <el-select clearable v-model="form.unifiedChargeStaffId" placeholder="请选择统一收款人姓名"
                             @clear="form.unifiedChargeStaffId = null,form.unifiedChargeStaffName = null">
                    <el-option
                      v-for="dict in peoplelist"
                      :key="dict.staffId"
                      :label="dict.staffName"
                      :value="parseInt(dict.staffId)"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="统一复核人姓名" prop="unifiedCheckStaffId">
                  <el-select clearable v-model="form.unifiedCheckStaffId" placeholder="请选择统一复核人姓名"
                             @clear="form.unifiedCheckStaffId = null,form.unifiedCheckStaffName = null">
                    <el-option
                      v-for="dict in peoplelist"
                      :key="dict.staffId"
                      :label="dict.staffName"
                      :value="parseInt(dict.staffId)"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="统一退费账户" prop="corporateBankAccount">
                  <el-select clearable v-model="form.corporateBankAccount" placeholder="请选择统一退费账户"
                  >
                    <el-option
                      v-for="(item,index) in accontOptions"
                      :key="index"
                      :label="item.bankAccount"
                      :value="item.bankAccount"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!--          <el-col :span="8" v-if="!form.yearNo || !form.organizationNo" style="font-size: 12px;color: #006be1;text-indent: 12px;line-height: 14px;    text-align: center;-->
              <!--    margin-top: -18px;">-->
              <!--            请先选择年度和所在单位-->
              <!--          </el-col>-->
              <!--          <el-col :span="12">-->
              <!--            <el-form-item label="双方约定的其他事项" prop="otherMatters">-->
              <!--              <el-input type="textarea"-->
              <!--                        :autosize="{ minRows: 2, maxRows: 4}" v-model="form.otherMatters" placeholder="请输入双方约定的其他事项" />-->
              <!--            </el-form-item>-->
              <!--          </el-col>-->
              <el-col :span="8">
                <el-form-item label="线上签字" prop="billSignOnline">
                  <el-select v-model="form.billSignOnline" placeholder="请选择是否线上签字">
                    <el-option
                      v-for="dict in ifCertificationOptions"
                      :key="dict.code"
                      :label="dict.name"
                      :value="parseInt(dict.code)"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="收费是否拍照" prop="ifChargePhoto">
                  <el-select v-model="form.ifChargePhoto" placeholder="请选择收费是否拍照">
                    <el-option
                      v-for="dict in ifCertificationOptions"
                      :key="dict.code"
                      :label="dict.name"
                      :value="parseInt(dict.code)"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="线上业务办理开始时间" prop="onlinePayStartTime">
                  <el-time-picker
                    value-format='HH:mm:ss'
                    v-model="form.onlinePayStartTime"
                    placeholder="请选择线上业务办理开始时间">
                  </el-time-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="线上业务办理结束时间" prop="onlinePayEndTime">
                  <el-time-picker
                    value-format='HH:mm:ss'
                    v-model="form.onlinePayEndTime"
                    placeholder="请选择线上业务办理结束时间">
                  </el-time-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="线上收费是否自动确认" prop="onlinePayAutoConfirm">
                  <el-select v-model="form.onlinePayAutoConfirm" placeholder="请选择线上收费是否自动确认">
                    <el-option
                      v-for="dict in ifCertificationOptions"
                      :key="dict.code"
                      :label="dict.name"
                      :value="parseInt(dict.code)"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item
                  label="票据印章"
                  prop="trusteeIdFront"
                  label-width="180px"
                  style="margin-top: 10px"
                >
                  <el-upload
                    ref="upload"
                    :action="action"
                    :on-remove="handleRemove"
                    :on-change="handleChangeUpload"
                    :auto-upload="false"
                    list-type="picture-card"
                    accept=".jpg,.jpeg,.png"
                    :file-list="fileList"
                    :limit ="limitnum"
                    :on-exceed="exceedfun"
                    :before-upload="beforeAvatarUpload"
                  >
                    <el-button  type="primary">点击上传</el-button>
                  </el-upload>
                </el-form-item>
              </el-col>

            </el-row>
          </el-form>
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      title="提示"
      v-model="iframeVisible"
      width="70%"
      :close-on-click-modal="false"
    >
      <iframe style="width:100%;height:700px;" :src="iframeUrl" frameborder="0" width="800"></iframe>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="iframeVisible = false;pdfloading = false">关 闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {getDicts} from "@/api/systemlandcontract/system/dict/data";
import {
  listFarmChargeConfig,
  getFarmChargeConfig,
  delFarmChargeConfig,
  addFarmChargeConfig,
  updateFarmChargeConfig,
  delFarmChargeConfigs,
  copyFarmChargeConfig,
  checkerPeopleList,
  printBill
} from "@/api/systemlandcontract/config/farmChargeConfig";
import photoUpload from "@/views/systemlandcontract/components/photoUpload";
import axios from "axios";
import {corporateBank} from "@/api/systemlandcontract/charge/serialPrint";
import minxins  from "@/views/systemlandcontract/utils/tableheight.js";
import {selectDictLabel} from "@/views/systemagriculturalmachineryv2/utils/cop.js";
import NewOrgSelect from "@/views/systemlandcontract/components/NewOrgSelect/index.vue";

export default {
  name: "/config/farmChargeConfig/queryByPag",
  components: {
    photoUpload,
    NewOrgSelect
  },
  mixins: [minxins],
  data() {
    var checkIsFloatFour = (rule, value, callback) => {
      var patrn = /^([0]|([0]\.[0-9]{1,4}))$/;//数字校验
      var errorMsg = '';
      if(value){
        if (!patrn.exec(value)) {
          errorMsg = "请输入0-1之间的数字"
        }
      }
      if (errorMsg) {
        callback(new Error(errorMsg));
      } else {
        callback();
      }
    };
    var checkIsNumber = (rule, value, callback) => {
      var patrn = /(^[0-9]*$)/;//数字校验
      var errorMsg = '';
      if(value){
        if (!patrn.exec(value)) {
          errorMsg = "请输入数字"
        }
      }

      if (errorMsg) {
        callback(new Error(errorMsg));
      } else {
        callback();
      }
    };
    var checkIsFloat = (rule, value, callback) => {
      var patrn = /^(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*))$/;//数字校验
      var errorMsg = '';
      if(value){
        if (!patrn.exec(value)) {
          errorMsg = "请输入大于0的数字"
        }
      }
      if (errorMsg) {
        callback(new Error(errorMsg));
      } else {
        callback();
      }
    };
    return {
      size:"medium",
      // 凡软页面
      pdfloading:false,
      iframeUrl: '',
      iframeVisible:false,
      limitnum:1,
      //收费账户信息
      accontOptions:[],
      //审核人收费人列表
      peoplelist: [],
      //选中年份合集
      yearList: [],
      //复制弹窗
      openCopy: false,
      //复制按钮load
      copyLoad: false,
      //修改按钮load
      updateLoad: false,
      //是否修改
      updateType: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      //收费人数
      chargePersonOptions: [1,2],
      // 农场收费配置表格数据
      farmChargeConfigList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 年度字典
      yearNoOptions: [],
      //
      financialSoftwareNoOptions: [],
      // 财务软件
      signDeviceTypeNoOptions: [],
      // 签字设备字典
      photoDeviceTypeNoOptions: [],
      // 是否实名认证字典
      ifCertificationOptions: [],
      //收费类型字典
      chargeTypeNoOptions:[],
      //默认票据联数
      checkList:[],
      checkListFour:[{
        value: '1',
        label:'【第一联 记账联】'
      },{
        value: '2',
        label:'【第二联 农户留存联】'
      },{
        value: '3',
        label:'【第三联 存根联】'
      },{
        value: '4',
        label:'【第四联 基层存根联】'
      }],
      checkListThree:[{
        value: '1',
        label:'【第一联 记账联】'
      },{
        value: '2',
        label:'【第二联 农户留存联】'
      },{
        value: '3',
        label:'【第三联 存根联】'
      }],
      // 查询参数
      queryParams: {
        page: 1,
        rows: 10,
        yearNo: null,
        organizationNo: null,
      },
      // 表单参数
      form: {
        printBillNumberNos:null,
        printBillNumberNosArr:[],
        signPath:null
      },
      fileList:[],
      isup:false, //是否上传新的图片
      formCopy:{
        organizationNo: null,
      },
      //图片上传
      action: "#",
      //上传地址
      uploadFileUrl: window.VITE_APP_BASE_API + "/" + import.meta.env.VITE_APP_GATEWAYPATH_LANDCONTRACT + "/file/uploadBybase64",
      isUploading : false,
      // 表单校验
      rules: {
        yearNo: [{required: true, message: '请选择年度', trigger: 'change'}],
        chargePerson: [{required: true, message: '请选择收费人数', trigger: 'change'}],
        copyByYear: [{required: true, message: '请选择年度', trigger: 'change'}],
        organizationNo: [{required: true, message: '请选择所在单位', trigger: 'change'}],
        signDeviceTypeNo: [{required: true, message: '请选择签字设备', trigger: 'change'}],
        photoDeviceTypeNo: [{required: true, message: '请选择视频采集设备', trigger: 'change'}],
        financialSoftwareNo: [{required: true, message: '请选择财务软件', trigger: 'change'}],
        ifCertification: [{required: true, message: '请选择是否实名认证', trigger: 'change'}],
        billSignOnline: [{required: true, message: '请选择是否线上签字', trigger: 'change'}],
        ifChargePhoto:[{required: true, message: '请选择收费是否拍照', trigger: 'change'}],
        onlinePayAutoConfirm:[{required: true, message: '请选择线上收费是否自动确认', trigger: 'change'}],
        billNumber: [
         // {required: true, message: '请输入票据联数', trigger: 'blur'},
          { validator: checkIsNumber, trigger: "blur" },
        ],
        printBillNumberNos: [
          //{required: true, message: '请勾选默认打印票据联数', trigger: 'blur'},
        ],
        commissionRate: [
         // {required: true, message: '请输入手续费比例', trigger: 'blur'},
          { validator: checkIsFloatFour, trigger: "blur" },
        ],
        commissionMax: [
          //{required: true, message: '请输入手续费封顶', trigger: 'blur'},
          { validator: checkIsFloat, trigger: "blur" },
        ],
        //contractChargeTypeNo: [{required: true, message: '请选择承包费农贷数据来源', trigger: 'change'}],
        //otherChargeTypeNo: [{required: true, message: '请选择其他收费农贷数据来源', trigger: 'change'}]
      }
    };
  },
  created() {
    this.getList();
    getDicts("year_cd").then(response => {
      this.yearNoOptions = response.data;
    });
    getDicts("pub_if").then(response => {
      this.ifCertificationOptions = response.data;
    });
    getDicts("photoDeviceTypeNo").then(response => {
      this.photoDeviceTypeNoOptions = response.data;
    });
    getDicts("signDeviceTypeNo").then(response => {
      this.signDeviceTypeNoOptions = response.data;
    });
    getDicts("financial_software_no").then(response => {
      this.financialSoftwareNoOptions = response.data;
    });
    getDicts("charge_type_no").then(response => {
      this.chargeTypeNoOptions = response.data;
    });
    checkerPeopleList().then(response => {
      this.peoplelist = response.data
    });
  },
  mounted() {
      },
  watch: {
    'form.yearNo': function (val) {
      // this.form.corporateBankAccount = null
      this.accontOptions = []
      if (val != null && val !== '' && this.form.organizationNo != null && this.form.organizationNo !== ''){
        this.accInfofun()
      }
    },
    'form.organizationNo': function (val) {
      // this.form.corporateBankAccount = null
      this.accontOptions = []
      if (val != null && val !== '' && this.form.yearNo != null && this.form.yearNo !== ''){
        this.accInfofun()
      }
    },
  },
  methods: {
    yearNofun(){
      this.form.corporateBankAccount = null
    },
    /** 查询农场收费配置列表 */
    getList() {
      this.loading = true;
      listFarmChargeConfig(this.queryParams).then(response => {
        this.farmChargeConfigList = response.data.records;
        this.$nextTick(() => {
          this.$refs.tables.doLayout();
        })
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 年度字典翻译
    yearNoFormat(row, column) {
      return this.selectDictLabel(this.yearNoOptions, row.yearNo != null && row.yearNo != undefined ? row.yearNo.toString() : "");
    },
    // 是否实名认证字典翻译
    ifCertificationFormat(row, column) {
      return this.selectDictLabel(this.ifCertificationOptions, row.ifCertification != null && row.ifCertification != undefined ? row.ifCertification.toString() : "");
    },
    // 是否线上签字
    billSignOnlineFormat(row, column) {
      return this.selectDictLabel(this.ifCertificationOptions, row.billSignOnline != null && row.billSignOnline != undefined ? row.billSignOnline.toString() : "");
    },
    //是否拍照
    ifChargePhotoFormat(row, column) {
      return this.selectDictLabel(this.ifCertificationOptions, row.ifChargePhoto != null && row.ifChargePhoto != undefined ? row.ifChargePhoto.toString() : "");
    },
    onlinePayAutoConfirmFormat(row, column) {
      return this.selectDictLabel(this.ifCertificationOptions, row.onlinePayAutoConfirm != null && row.onlinePayAutoConfirm != undefined ? row.onlinePayAutoConfirm.toString() : "");
    },
    contractChargeTypeNoFormat(row, column) {
      return this.selectDictLabel(this.chargeTypeNoOptions, row.contractChargeTypeNo != null && row.contractChargeTypeNo != undefined ? row.contractChargeTypeNo.toString() : "");
    },
    otherChargeTypeNoFormat(row, column) {
      return this.selectDictLabel(this.chargeTypeNoOptions, row.otherChargeTypeNo != null && row.otherChargeTypeNo != undefined ? row.otherChargeTypeNo.toString() : "");
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    changebillNumber(){
      this.form.printBillNumberNosArr = []
      if(this.form.billNumber == 3){
        this.checkList = this.checkListThree
      }else{
        this.checkList = this.checkListFour
      }
    },
    // 表单重置
    reset() {
      this.form = {
        createTime: null,
        financialSoftwareNo: null,
        photoDeviceTypeNo: null,
        updateBy: null,
        lcFarmChargeConfigId: null,
        yearNo: null,
        ifCertification: null,
        billSignOnline:null,
        ifChargePhoto:null,
        onlinePayAutoConfirm:null,
        onlinePayStartTime:null,
        onlinePayEndTime:null,
        billNumber: 4,
        printBillNumberNos: null,
        printBillNumberNosArr: [],
        commissionRate: null,
        commissionMax: null,
        organizationNo: null,
        sourceDesc: null,
        createBy: null,
        updateTime: null,
        statusCd: null,
        remark: null,
        contract: [],
        chargeEndDate: null,
        penalty: null,
        otherMatters: null,
        contractChargeTypeNo: null,
        otherChargeTypeNo: 1,
        unifiedChargeStaffId: null,
        unifiedChargeStaffName: null,
        unifiedCheckStaffId: null,
        unifiedCheckStaffName: null,
        signPath:null,
        corporateBankAccount:null
      };
      this.fileList = []
      this.accontOptions = []
      this.checkList = this.checkListFour
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.$refs.newOrgSelect.getCascader('reset')
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.yearList = selection.map(item => item.yearNo);
      this.ids = selection.map(item => item.lcFarmChargeConfigId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.accontOptions = [];
      this.updateType = false;
      this.title = "新建农场收费配置";
    },
    // 弹窗开启触发
    opened() {
      if (this.form.signPath) {
        // 如果上传组件内部有一个方法或者属性来控制显示
        // this.$refs["upload"].hideUploadButton();
        // 或者直接修改样式
        console.log(123, this.$refs["upload"].$children)
        this.$refs["upload"].$children[1].$el.style.display = 'none';
      } else {
        // 如果上传组件内部有一个方法或者属性来控制显示
        // this.$refs["upload"].showUploadButton();
        // 或者直接修改样式
        this.$refs["upload"].$children[1].$el.style.display = '';
      }
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.isup = false
      this.reset();
      const lcFarmChargeConfigId = row.lcFarmChargeConfigId || this.ids
      getFarmChargeConfig(lcFarmChargeConfigId).then(response => {
        this.updateType = true;
        this.form = response.data;
        this.form.printBillNumberNosArr = response.data.printBillNumberNos.split(',')
        if (this.form.unifiedChargeStaffId && this.peoplelist.length > 0) {
            this.form.unifiedChargeStaffName = this.peoplelist.find((v) => {
                if (v.staffId == this.form.unifiedChargeStaffId) {
                    return v
                }
            })
        }
        if (this.form.unifiedChargeStaffId == '' || !this.form.unifiedChargeStaffId) {
            this.form.unifiedChargeStaffName = null
        }
        if (this.form.unifiedChargeStaffName == '' || !this.form.unifiedChargeStaffName) {
            this.form.unifiedChargeStaffId = null
        }
        if (this.form.unifiedCheckStaffId && this.peoplelist.length > 0) {
            this.form.unifiedCheckStaffName = this.peoplelist.find((v) => {
                if (v.staffId == this.form.unifiedCheckStaffId) {
                    return v
                }
            })
        }
        if (this.form.unifiedCheckStaffId == '' || !this.form.unifiedCheckStaffId) {
            this.form.unifiedCheckStaffName = null
        }
        if (this.form.unifiedCheckStaffName == '' || !this.form.unifiedCheckStaffName) {
            this.form.unifiedCheckStaffId = null
        }
        if(this.form.billNumber == 3){
          this.checkList = this.checkListThree
        }else{
          this.checkList = this.checkListFour
        }
        this.open = true;
        if(response.data.signPath){
          this.fileList.push({
            url: response.data.signPath ,
            name: '',
            percentage:'',
            raw:'',
            size: '',
            status:'',
            uid: ''});
        }
        this.title = "修改农场收费配置";
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          this.updateLoad = true;
          if(this.title.includes('修改')&& !this.isup){
            this.form.signPath = null;
          }else{
            this.form.signPath = this.fileList.length>0?this.fileList[0].url:null;
          }
          this.form.printBillNumberNos = this.form.printBillNumberNosArr.join(',');
          if (this.form.unifiedChargeStaffId && this.peoplelist.length > 0) {
            this.form.unifiedChargeStaffName = this.peoplelist.find((v) => {
              if (v.staffId == this.form.unifiedChargeStaffId) {
                return v
              }
            }).staffName
          }
          if (this.form.unifiedChargeStaffId == '' || !this.form.unifiedChargeStaffId) {
            this.form.unifiedChargeStaffName = null
          }
          if (this.form.unifiedCheckStaffId && this.peoplelist.length > 0) {
            this.form.unifiedCheckStaffName = this.peoplelist.find((v) => {
              if (v.staffId == this.form.unifiedCheckStaffId) {
                return v
              }
            }).staffName
          }
          if (this.form.unifiedCheckStaffId == '' || !this.form.unifiedCheckStaffId) {
            this.form.unifiedCheckStaffName = null
          }
          if (this.form.lcFarmChargeConfigId != null) {
            updateFarmChargeConfig(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.updateLoad = false;
              this.getList();
            }).catch(error => {
              this.updateLoad = false;
            });
          }else {
            addFarmChargeConfig(this.form).then(response => {
              this.$modal.msgSuccess("新建成功");
              this.open = false;
              this.updateLoad = false;
              this.getList();
            }).catch(error => {
              this.updateLoad = false;
            });
          }
        }

      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const lcFarmChargeConfigIds = row.lcFarmChargeConfigId || this.ids;
      const yearList = row.yearNo || [...new Set(this.yearList)];
      this.$confirm('是否确认删除的年度为"' + yearList + '"的数据项?', "警告", {
        customClass: 'farmChargeConfigmessage',
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return Array.isArray(lcFarmChargeConfigIds) ? delFarmChargeConfigs(lcFarmChargeConfigIds) : delFarmChargeConfig(lcFarmChargeConfigIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      })
    },
    //所在单位下拉
    handleOrgChange(orgCode) {
      this.queryParams.organizationNo = orgCode;
      // this.$nextTick(()=>{
      //   this.handleQuery();
      // });
    },
    handleOrgChange3(orgCode) {
      this.form.corporateBankAccount = null
      this.form.organizationNo = orgCode;
    },
    handleOrgChangeCopy(orgCode) {
      this.formCopy.organizationNo = orgCode;
    },
    /** 获取收费账户信息 */
    accInfofun(){
      const param = {
        resType: 'getBankAccountFy',
        organizationNo: this.form.organizationNo,
        yearNo: this.form.yearNo
      }
      this.accontOptions = [];
      corporateBank(param).then(response => {
        this.accontOptions = response.data
      });
    },
    /** copy操作 */
    handleCopy() {
      this.formCopy = {
        organizationNo: null
      }
      this.resetForm("formCopy");
      this.openCopy = true;
      this.title = "复制年度";
      this.$nextTick(()=>{
        this.$refs.copyNewOrgSelect.getCascader('reset')
      })
    },

    /** 提交按钮 */
    submitCopyForm(row) {
      this.$refs["formCopy"].validate(valid => {
        if (valid) {
          this.copyLoad = true;
          copyFarmChargeConfig(this.formCopy).then(response => {
            this.$modal.msgSuccess("修改成功");
            this.openCopy = false;
            this.copyLoad = false;
            this.getList();
          }).catch(error => {
            this.copyLoad = false;
          });
        }
      });
    },
    exceedfun(){
      this.$modal.msgError("只能上传一张图片");
    },

    //改变图片上传
    handleChangeUpload(file, fileList) {
     this.isup = true
      if(this.isUploading) return;
      let imgName = file.name;
      let _this = this;
      this.isUploading = true
      this.getBase64(file.raw).then((res) => {
        // console.log("文件信息======》", file);
        let formData = new FormData();
        formData.append("fileStr", res);
        axios({
          method: "post",
          headers: { "content-type": "application/x-www-form-urlencoded" },
          url: _this.uploadFileUrl,
          data: formData,
        }).then((res) => {
          if (res.status == 200) {
            // const d = _this.$refs["upload"]
            // d.$children[1].$el.style.display = 'none'
            _this.isUploading = false
            this.$nextTick(() => {
              _this.fileList = []
              _this.fileList.push({
                name: imgName,
                url: res.data.data,
                percentage:file.percentage,
                raw:file.raw,
                size: file.size,
                status:file.status,
                uid: file.uid,
              });
            });
          }
        });
      });
    },
    getBase64(file) {
      return new Promise(function (resolve, reject) {
        let reader = new FileReader();
        let imgResult = "";
        reader.readAsDataURL(file);
        reader.onload = function () {
          imgResult = reader.result;
        };
        reader.onerror = function (error) {
          reject(error);
        };
        reader.onloadend = function () {
          resolve(imgResult);
        };
      });
    },
    handleRemove(file, fileList) {
      this.isup = true
      const d = this.$refs["upload"]
      d.$children[1].$el.style.display = ''
      this.fileList = [];
    },
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.$message.error('上传头像图片大小不能超过 2MB!');
      }
      return isLt2M;
    },
    signBill(item) {
      this.iframeUrl = ''
      let _this = this
      const prefix = window.VITE_APP_BASE_API
      this.pdfloading = true
      axios({
        method: "post",
        url:`${prefix}/${import.meta.env.VITE_APP_GATEWAYPATH_LANDCONTRACT}/config/farmChargeConfig/preview/` + item.lcFarmChargeConfigId,
        responseType: 'blob',
      }).then(function (response) {
        _this.pdfloading = false
        _this.$nextTick(() => {
          if(response.data.type=="text/html"){
            _this.$modal.msgError("打印票据失败");
          }else{
            const blob = new Blob([response.data], { type: 'application/pdf;charset=utf-8' })
            _this.iframeUrl = window.URL.createObjectURL(blob)
            _this.iframeVisible = true
          }
        });
      }).catch(function (response) {
        _this.$modal.msgError("打印票据失败");
        _this.pdfloading = false
      })
    },
    selectDictLabel(options, cellValue){
      return selectDictLabel(
          options,
          cellValue
      );
    },
    firstTripFun(){
      this.handleQuery();
    }
  }
};


</script>
<style lang="scss" scoped>
.farmChargeConfigmessage {
  .el-message-box__message {
    p {
      word-wrap: break-word;
    }
  }
}
:deep{
  .el-scrollbar__wrap {
    overflow-x: hidden;
  }
  .el-date-editor.el-input, .el-date-editor.el-input__inner{
    width: 100% !important;
  }
  .el-range-editor.el-input__inner {
    width: 100% !important;
  }
  .el-input-number--medium{
    width: 100% !important;
  }
  .el-cascader{
    width: 100% !important;
  }
}
</style>
