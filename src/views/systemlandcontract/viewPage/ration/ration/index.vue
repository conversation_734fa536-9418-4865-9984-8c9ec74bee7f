<!-- 基本田资格导入 -->
<template>
  <div class="app-container ration">
    <div ref="searchDom">

      <el-form :model="queryParams" ref="queryRef" :rules="rulesfresh"  v-show="showSearch" label-width="100px" class="form-line">
        <el-row style="display: flex; flex-wrap: wrap;" :gutter="20">

          <el-col :span="6" >
            <el-form-item label="年度" prop="yearNo"  label-width="70px">
              <el-select v-model="queryParams.yearNo" clearable placeholder="请选择年度" >
                <el-option v-for="dict in yearNoOptions" :key="dict.code" :label="dict.name" :value="dict.code"/>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="所在单位" prop="organizationNo">
              <new-org-select style="width: 100%"  ref="newOrgSelect" :currentOrgValue="queryParams.organizationNo"  @handleOrgChange="handleOrgChange" @firstTrip="firstTripFun"></new-org-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="人员分类" prop="farmerType">
              <el-select clearable v-model="queryParams.farmerType" @change="getList"  placeholder="请选择人员分类" >
                <el-option  v-for="dict in farmerTypeOption"  :key="dict.code" :label="dict.name" :value="parseInt(dict.code)"/>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="农户姓名">
              <el-input
                  v-model="queryParams.name"
                  placeholder="请输入农户姓名"
                  clearable
                  @keyup.enter.native="handleQuery"/>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="性别" prop="sexNo" class="halving"  label-width="70px">
              <el-select
                  v-model="queryParams.sexNo"
                  placeholder="请选择性别"
                  clearable >
                <el-option
                    v-for="dict in sexNoOptions"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code" />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="最小年龄" prop="startAge">
              <el-input-number  style="width: 100%" v-model="queryParams.startAge" placeholder="请输入年龄" :min="0" :max="99" />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="最大年龄" prop="endAge">
              <el-input-number  style="width: 100%" v-model="queryParams.endAge" placeholder="请输入年龄" :min="0" :max="99" />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="农户身份证号">
              <el-input
                  v-model="queryParams.idNumber"
                  placeholder="请输入农户身份证号"
                  clearable
                  @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="手机号"  label-width="70px">
              <el-input
                  v-model="queryParams.phone"
                  placeholder="请输入手机号"
                  clearable
                  @keyup.enter.native="handleQuery" />
            </el-form-item>
          </el-col>

          <el-col :span="6" class="num" >
            <el-form-item label="认证次数"  prop="validateCountStart" style="display: flex">
              <el-input-number  style="width: calc(50% - 5px);" v-model="queryParams.validateCountStart" placeholder="认证次数" :min="0" :max="99"/>
              ~
              <el-input-number  style="width: calc(50% - 5px);" v-model="queryParams.validateCountEnd" placeholder="认证次数" :min="0" :max="99" />
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="审核级别" prop="auditLevel">
              <el-select
                  @clear = "queryParams.auditLevel =null"
                  v-model="queryParams.auditLevel"
                  placeholder="请选择审核级别"
                  clearable >
                <el-option
                    v-for="dict in auditLevelOptions"
                    :key="dict.code"
                    :label="dict.name"
                    :value="parseInt(dict.code)">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="审核状态" prop="approvalStatusNo">
              <el-select v-model="queryParams.approvalStatusNo" placeholder="审核状态" clearable >
                <el-option v-for="dict in auditNoOptions" :key="dict.code" :label="dict.name"
                           :value="dict.code" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6"
          ><el-form-item label="超龄日期" prop="overageDate"  label-width="70px">
            <el-date-picker
                style="width: 100%"
                v-model="queryParams.overageDate"
                type="date"
                placeholder="选择超龄日期"
                value-format="YYYY-MM-DD"
            >
            </el-date-picker>
          </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button
              icon="Plus"
              @click="handleAdd"
              v-hasPermi="['rationDatabase:insert']"
          >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button  icon="Edit"  :disabled="single" @click="handleUpdate"
                      v-hasPermi="['rationDatabase:update']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button  icon="Delete"  :disabled="multiple"
                      @click="handleDelete" v-hasPermi="['rationDatabase:logicDeleteByIds','rationDatabase:logicDeleteById']">删除</el-button>
        </el-col>

        <el-col :span="1.5" >
          <FileUpload1 :isShowTip="false" text1="普通农户导入" :fileType="['xls','xlsx']"
                       @newlist="getList" :uploadFileUrl="uploadFileUrl"
                       v-hasPermi="['rationDatabase:importExcel']"></FileUpload1>
        </el-col>
        <el-col :span="1.5" >
          <FileUpload1 :isShowTip="false" text1="特殊农户导入" :fileType="['xls','xlsx']"
                       :uploadFileUrl="specialuploadFileUrl" @newlist="getList"
                       v-hasPermi="['rationDatabase:importExcelSpecial']"></FileUpload1>
        </el-col>
        <el-col :span="1.5">
          <el-button  icon="Plus"  :disabled="multiple"
                      @click='examinefun' v-hasPermi="['rationDatabase:auditPassByIds']">审核
          </el-button>
        </el-col>
        <el-col :span="1.5" >
          <el-button  icon="Download"  @click="exportTemplate"
                      v-hasPermi="['rationDatabase:exportTemplate']">普通农户模板下载</el-button>
        </el-col>
        <el-col :span="1.5" >
          <el-button  icon="Download"  @click="specialExportTemplate"
                      v-hasPermi="['rationDatabase:exportTemplateSpecial']">特殊农户模板下载</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button

              :disabled="multiple"
              @click="unlockfun"
              v-hasPermi="['rationDatabase:deblocking']">
            解锁审核
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <exceldownload
              ref="exceldownload"
              :exceltext="exceltext"
              icon="Download"
              :param="queryParams"
              :url="exportUrl"
              v-hasPermi="['rationDatabase:export']"
          ></exceldownload>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
      <el-table border ref="table" :height="heightCalc" :data="rationplanList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" fixed="left" />
        <el-table-column fixed="left" label="年度" align="center" prop="yearNo" :formatter="yearNoFormat"/>
        <el-table-column fixed="left" label="所在单位" width="200" align="center" prop="organizationName"/>
        <el-table-column  fixed="left" label="人员分类" align="center" prop="farmerType"  :formatter="farmerTypeFormat" />
        <el-table-column fixed="left" label="农户姓名" align="center" prop="name">
          <template #default="scope">

            <span style="color: #00bcd4;cursor: pointer;" @click="lookUp(scope.row)">{{scope.row.name}}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="身份证号" align="center" prop="idNumber" /> -->
        <el-table-column fixed="left" label="农户身份证号" width="200" align="center">
          <template #default="scope">
            {{ mask(scope.row.idNumber, 6, 17) }}</template>
        </el-table-column>
        <el-table-column label="性别" align="center" prop="sexNo" :formatter="sexNoFormat"/>
        <el-table-column label="年龄" align="center" prop="age" >
          <template #default="scope">
  <span
      :style="{ color: (scope.row.age > 55 &&  scope.row.sexNo == 2) || (scope.row.age > 60 &&  scope.row.sexNo == 1)  ? 'red' : '',fontWeight: (scope.row.age > 55 &&  scope.row.sexNo == 2) || (scope.row.age > 60 &&  scope.row.sexNo == 1)  ? 'bolder' : ''}">{{
      scope.row.age
    }}</span>
          </template>
        </el-table-column>
        <el-table-column
            label="身份证所在地址"
            width="200px"
            align="center"
            prop="idAddress"
        />


        <el-table-column
            label="发证机关"
            width="200px"
            align="center"
            prop="issuingAuthority"
        />
        <el-table-column
            label="有效期"
            width="200px"
            align="center"
            prop="validDate"
        />
        <el-table-column label="审核状态" align="center" prop="approvalStatusNo" :formatter="auditNoFormat" />
        <el-table-column show-overflow-tooltip label="审核意见" align="center" prop="approvalRemark" />
        <!--      <el-table-column-->
        <!--        label="身份证所在地址"-->
        <!--        width="200px"-->
        <!--        align="center"-->
        <!--        prop="idAddress"-->
        <!--      />-->
        <el-table-column label="与户主关系" align="center" prop="householderRelation" :formatter="relationFormat"/>
        <el-table-column
            label="开户银行"
            width="200px"
            align="center"
            prop="bankName"
            :formatter="bankNameFormat" />
        <el-table-column
            label="银行卡片类型"
            width="200px"
            align="center"
            prop="bankcardTypeNo"
            :formatter="bankcardTypeNoFormat"
        />
        <el-table-column
            label="银行卡号"
            width="200px"
            align="center"
            prop="bankAccount"
        />
        <!--      <el-table-column label="来源" align="center" prop="sourceNo" :formatter="sourceNoFormat" />-->
        <el-table-column label="认证次数" align="center" prop="validateCount"/>

        <!--      <el-table-column label="档案年龄" align="center" prop="archiveAge" >-->
        <!--        <template #default="scope">
        <span-->
        <!--          :style="{ color: (scope.row.archiveAge > 55 &&  scope.row.sexNo == 2) || (scope.row.archiveAge > 60 &&  scope.row.sexNo == 1)  ? 'red' : '',fontWeight: (scope.row.archiveAge > 55 &&  scope.row.sexNo == 2) || (scope.row.archiveAge > 60 &&  scope.row.sexNo == 1)  ? 'bolder' : ''}">{{-->
        <!--            scope.row.archiveAge-->
        <!--          }}</span>-->
        <!--        </template>-->
        <!--      </el-table-column>-->

        <el-table-column align="center" label="当前审核角色名称" prop="currentAuditRoleName" width="130"/>
        <el-table-column v-for="(v,i) in tabledata" :key="i" :formatter="formatter" :label="v.label" :prop="v.name"
                         align="center" width="120"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="100px">
          <template #default="scope">

            <el-button  link type="primary"  @click="handleUpdate(scope.row)"
                        v-hasPermi="['rationDatabase:update']">修改</el-button>
            <el-button  link type="primary"  @click="handleDelete(scope.row)"
                        v-hasPermi="['rationDatabase:logicDeleteById']">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.page"
          v-model:limit="queryParams.rows"
          @pagination="getList"
      />

      <!-- 减员 -->
      <el-dialog
          :title="'减员操作'"
          v-model="openScreen"
          width="900px"
          append-to-body
          :close-on-click-modal="false"
      >
        <el-form
            ref="formScreen"
            :model="formScreen"
            :rules="rules"
            label-width="110px"
        >
          <el-row style="display: flex;flex-wrap: wrap">
            <el-col :span="12">
              <el-form-item label="年度" prop="yearNo">
                <el-select v-model="formScreen.yearNo" placeholder="请选择年度" disabled>
                  <el-option
                      v-for="dict in yearNoOptions"
                      :key="dict.code"
                      :label="dict.name"
                      :value="parseInt(dict.code)"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="农户姓名" prop="name" disabled>
                <el-input
                    v-model="formScreen.name"
                    placeholder="请输入农户姓名"
                    disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份证号" prop="idNumber" disabled>
                <el-input
                    v-model="formScreen.idNumber"
                    placeholder="请输入身份证号"
                    disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="年龄" prop="age">
                <el-input v-model.number="formScreen.age" placeholder="请输入年龄" disabled/>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="身份证所在地址" prop="idAddress">
                <el-input
                    v-model="formScreen.idAddress"
                    placeholder="请输入身份证所在地址"
                    disabled
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="性别" prop="sexNo" disabled>
                <el-select v-model="formScreen.sexNo" placeholder="请选择性别" disabled>
                  <el-option
                      v-for="dict in sexNoOptions"
                      :key="dict.code"
                      :label="dict.name"
                      :value="parseInt(dict.code)"
                      disabled
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="不合格原因" prop="reason" :rules="rules.reason">
                <el-input v-model="formScreen.reason" placeholder="请输入不合格原因" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
              type="primary"
              @click="submitFormScreen"
          >确 定</el-button
          >
          <el-button @click="openScreen=false">关 闭</el-button>
          <!--        <el-button @click="cancel">取 消</el-button>-->
        </div>
      </el-dialog>

      <!-- 添加或修改基本田资格导入对话框 -->
      <el-dialog v-if='open' :title="title" v-model="open" width="800px" append-to-body
                 :close-on-click-modal="false">
        <el-tabs  v-model="activeName" type="card"  @tab-click="clicktab">
          <el-tab-pane label="基本田资格（普通人员）" name="farm" v-if="title.includes('添加') || (title.includes('修改') && tabsName=='基本田资格（普通人员）')">
            <el-form :validate-on-rule-change=false ref="formRef" :model="form" :rules="rulesform" label-width="140px">
              <el-row type="flex"  justify="flex-start" style="flex-wrap: wrap">
                <el-col :span="12">
                  <el-form-item label="年度" prop="yearNo">
                    <el-select v-model="form.yearNo" placeholder="请选择年度">
                      <el-option v-for="dict in yearNoOptions" :key="dict.code" :label="dict.name"
                                 :value="parseInt(dict.code)"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="资格所在单位" prop="organizationNo">
                    <new-org-select placeholderText="请选择资格所在单位"
                                    @handleOrgChange="handleOrgChange3"
                                    ref="orgFarm"
                                    :defaultType="false"
                                    :currentOrgValue="form.organizationNo">
                    </new-org-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="农户姓名" prop="name">
                    <el-input v-model="form.name" placeholder="请选择农户姓名" disabled />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="身份证号" prop="idNumber">
                    <el-input v-model="form.idNumber" placeholder="请选择身份证号" disabled />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="性别" prop="sexNo">
                    <el-select v-model="form.sexNo" disabled @change="$forceUpdate()" placeholder="请选择性别">
                      <el-option
                          v-for="dict in sexNoOptions"
                          :key="dict.code"
                          :label="dict.name"
                          :value="parseInt(dict.code)"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="年龄" prop="age">
                    <el-input v-model.number="form.age" disabled @input="$forceUpdate()" placeholder="请选择年龄" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-tooltip :content="form.idAddress" placement="top" :disabled="!form.idAddress">
                    <el-form-item label="身份证所在地址" prop="idAddress">
                      <el-input v-model="form.idAddress" disabled @input="$forceUpdate()" placeholder="请选择身份证所在地址" />
                    </el-form-item>
                  </el-tooltip>

                </el-col>
                <el-col :span="12">
                  <el-form-item label="开户银行" prop="bankName">
                    <el-select placeholder="请选择开户银行"  @change="$forceUpdate()" v-model="form.bankName">
                      <el-option v-for="dict in bankNameOptions" :key="dict.code" :label="dict.name"
                                 :value="parseInt(dict.code)"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="银行卡号" prop="bankAccount">
                    <el-input v-model="form.bankAccount" oninput="value=value.replace(/[^0-9.]/g,'')" maxlength="20"  @input="$forceUpdate()" placeholder="请输入银行卡号" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="与户主关系" prop="householderRelation">
                    <el-select  placeholder="请选择与户主关系" @change="$forceUpdate()" v-model="form.householderRelation">
                      <el-option v-for="dict in relationsOptions" :key="dict.code" :label="dict.name"
                                 :value="parseInt(dict.code)"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="13" class="buttons" >
                  <el-button type="primary"  @click="peoplefun()">选择人员</el-button>
                  <el-button type="info" plain  @click="clear()">清空</el-button>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>
          <el-tab-pane label="基本田资格（特殊人员）" name="special" v-if="title.includes('添加') || (title.includes('修改') && tabsName=='基本田资格（特殊人员）')">
            <el-form :validate-on-rule-change=false ref="specialformRef" :model="specialform" :rules="specialrules" label-width="140px">
              <el-row type="flex"  justify="flex-start" style="flex-wrap: wrap">
                <el-col :span="12">
                  <el-form-item label="年度" prop="yearNo">
                    <el-select v-model="specialform.yearNo" placeholder="请选择年度">
                      <el-option v-for="dict in yearNoOptions" :key="dict.code" :label="dict.name"
                                 :value="parseInt(dict.code)"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="资格所在单位" prop="organizationNo">
                    <new-org-select
                        placeholderText="请选择资格所在单位"
                        @handleOrgChange="handleOrgChange4"
                        ref="orgBuss"
                        :defaultType="false"
                        :currentOrgValue="specialform.organizationNo">
                    </new-org-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="农户姓名" prop="name">
                    <el-input v-model="specialform.name" clearable placeholder="请输入农户姓名"  />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="身份证号" prop="idNumber">
                    <el-input v-model="specialform.idNumber" clearable placeholder="请输入身份证号" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="性别" prop="sexNo">
                    <el-select v-model="specialform.sexNo"   placeholder="请选择性别">
                      <el-option
                          v-for="dict in sexNoOptions"
                          :key="dict.code"
                          :label="dict.name"
                          :value="parseInt(dict.code)"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="年龄" prop="age">
                    <el-input v-model.number="specialform.age" clearable   placeholder="请输入年龄"  disabled/>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="发证机关" prop="issuingAuthority">
                    <el-input v-model="specialform.issuingAuthority" clearable  placeholder="请输入发证机关" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="身份证有效期" prop="validDate">
                    <el-input v-model="specialform.validDate"  clearable placeholder="请输入身份证有效期" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="与户主关系" prop="householderRelation">
                    <el-select  placeholder="请选择与户主关系"  v-model="specialform.householderRelation">
                      <el-option v-for="dict in relationsOptions" :key="dict.code" :label="dict.name"
                                 :value="parseInt(dict.code)"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="开户银行" prop="bankName">
                    <el-select placeholder="请选择开户银行"   v-model="specialform.bankName">
                      <el-option v-for="dict in bankNameOptions" :key="dict.code" :label="dict.name"
                                 :value="parseInt(dict.code)"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="银行卡片类型" prop="bankcardTypeNo">
                    <el-select placeholder="请选择银行卡片类型"   v-model="specialform.bankcardTypeNo">
                      <el-option v-for="dict in bankcardTypeNoOptions" :key="dict.code" :label="dict.name"
                                 :value="parseInt(dict.code)"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="银行卡号" prop="bankAccount">
                    <el-input v-model="specialform.bankAccount"  oninput="value=value.replace(/[^0-9.]/g,'')" maxlength="20" clearable   placeholder="请输入银行卡号" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="联系方式" prop="phone">
                    <el-input v-model="specialform.phone" clearable  placeholder="请输入联系方式" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="农户身份" prop="farmerIdentityNo">
                    <el-select
                        v-model="specialform.farmerIdentityNo"
                        placeholder="请选择农户身份"
                        clearable

                    >
                      <el-option
                          v-for="dict in farmerIdentityNoOptions"
                          :key="dict.code"
                          :label="dict.name"
                          :value="parseInt(dict.code)"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="身份证所在地址" prop="idAddress">
                    <el-input type="textarea" :rows="3" v-model="specialform.idAddress" clearable  placeholder="请输入身份证所在地址" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>
        </el-tabs>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>
      <el-dialog title="审核操作" v-model="dialogVisible" width="30%" top>
        <span>备注：</span>
        <el-input type="textarea" :rows="2" placeholder="请输入备注" v-model="textarea">
        </el-input>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="danger" @click="handleNoAudit">审核拒绝</el-button>
            <el-button type="primary" @click="handleAudit">审核通过</el-button>
          </div>
        </template>
      </el-dialog>
      <ChangePeople ref="ChangePeople" @transmit="transmit" :isonly="isonly"></ChangePeople>
      <residentInfoTan ref="residentInfoTan" type="insertRationplan"></residentInfoTan>
    </div>
  </div>
</template>

<script>

import NewOrgSelect from "@/views/systemlandcontract/components/NewOrgSelect/index.vue";
import * as utils from "@/views/systemlandcontract/utils/cop.js";
import {getDicts} from "@/api/systemlandcontract/system/dict/data";
import OrgSelect from "@/views/systemlandcontract/components/OrgSelect";
import basicManage from "@/views/systemlandcontract/components/basicManage";
import ChangePeople from "@/views/systemlandcontract/components/ChangePeople";
import FileUpload1 from "@/views/systemlandcontract/components/FileUpload1";
import exceldownload from "@/views/systemlandcontract/components/exceldownload";
import residentInfoTan from "@/views/systemlandcontract/components/residentInfoTan/index";
import {Levelfun} from '@/views/systemlandcontract/utils/cop.js'
import {
  createLastYearRation,
  listRationplan,
  auditPassFirst,
  auditCancelFirst,
  exportExcel,
  exportTemplate,
  specialExportTemplate,
  rationupdate,
  rationinsert,
  rationinfo,
  rationlogicDeleteById,
  rationauditPassByIds,
  deblocking,
  rationauditCancelByIds,
  rationlogicDeleteByIds
} from "@/api/systemlandcontract/ration/ration";
import {isIdentityId} from "@/views/systemlandcontract/utils/cop";
import {getLandcontractfarmers} from "@/api/systemlandcontract/farmer/landcontractfarmers";
import {
  addRationplan
} from "@/api/systemlandcontract/ration/rationscreen";
import {getMaxAuditLevel} from "@/api/systemlandcontract/landappinitialize/permit";

import minxins from "@/views/systemlandcontract/utils/tableheight.js";
import {selectDictLabel} from "@/views/systemlandcontract/utils/cop";

export default {
  name: "/ration/ration/queryByPage",
  mixins: [minxins],
  data() {
    var checkIdentitytionId = (rule, value, callback) => {
      var errorMsg = isIdentityId(value);
      if (errorMsg) {
        callback(new Error(errorMsg));
      } else {
        callback();
      }
    };
    return {
      //分类
      farmerTypeOption:[
        {
          code:'0',
          name:'普通农户'
        },
        {
          code:'1',
          name:'特殊农户'
        },
      ],
      activeName:'farm',
      tabsName:null,
      //多加的数据
      tabledata: [],
      // 审核级别字典
      auditLevelOptions: [{
        'code':'-1',
        'name':'全部',
      },{
        'code':'0',
        'name':'一级审核'
      },{
        'code':'1',
        'name':'二级审核'
      },{
        'code':'2',
        'name':'三级审核'
      },{
        'code':'3',
        'name':'四级审核'
      },{
        'code':'4',
        'name':'五级审核'
      },{
        'code':'5',
        'name':'六级审核'
      },{
        'code':'6',
        'name':'七级审核'
      },{
        'code':'7',
        'name':'八级审核'
      }],
      //卡片类型
      bankcardTypeNoOptions:[],
      tableHeight:200,
      // 是否公用字典
      pubifOptions: [],
      // 与户主关系字典
      relationsOptions: [],
      // 银行字典
      bankNameOptions: [],
      //上传地址
      uploadFileUrl:
        window.VITE_APP_BASE_API + "/" + import.meta.env.VITE_APP_GATEWAYPATH_LANDCONTRACT + "/ration/ration/importExcel",
      specialuploadFileUrl:
        window.VITE_APP_BASE_API + "/" + import.meta.env.VITE_APP_GATEWAYPATH_LANDCONTRACT + "/ration/ration/importExcelSpecial",
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      //选中名字数据
      names: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 基本田资格导入表格数据
      rationplanList: [],
      // 来源
      sourceNoOptions: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 年度字典
      yearNoOptions: [],
      // 管理区审核标志字典
      auditNoOptions: [],
      // 农场审核标志字典
      approverNoOptions: [],
      // 性别字典
      sexNoOptions: [],
      // // 农户身份字典
      farmerIdentityNoOptions: [],
      exportUrl: window.VITE_APP_BASE_API + "/" + import.meta.env.VITE_APP_GATEWAYPATH_LANDCONTRACT + "/ration/ration/exportExcel",
      //导出
      exceltext: "导出",

      // 查询参数
      queryParams: {
        page: 1,
        rows: 10,
        yearNo: String(new Date().getFullYear()),
        farmerType:0,
        auditNo: null,
        farmerId: null,
        age: null,
        orgValue: null,
        validateCountStart: 0,
        validateCountEnd: 99,
        name: null,
        idNumber: null,
        organizationNo: null,
        isCollect: null,
        isFingerprint: null,
		    archiveAge:null,
		    startAge:0,//年龄开始
		    endAge:99,//年龄结束
		    startArchiveAge:null,//档案年龄范围开始
		    endArchiveAge:null,//档案年龄范围结束
        phone:null,
        sexNo:null,
        auditLevel:-1,
        approvalStatusNo:"0",
        overageDate:null

      },
      dialogVisible: false, // 审核弹窗
      textarea: "", // 审核原因
      // 表单参数
      form: {
        name: null,
        yearNo: null,
        idNumber: null,
        bankName: null,
        bankAccount: null,
        householderRelation: null,
        scaleArea: 0,
        rationArea: 0,
        organizationNo: null,
        idAddress: null,
      },
      specialform:{
        name: null,
        yearNo: null,
        idNumber: null,
        bankName: null,
        bankcardTypeNo: null,
        bankAccount: null,
        householderRelation: null,
        scaleArea: 0,
        rationArea: 0,
        organizationNo: null,
        idAddress: null,
        issuingAuthority: null,
        validDate: null,
        phone: null,
        farmerIdentityNo:null,
        age: 0
      },
      // 表单校验
      isonly: true, // 人员组件是否是单选,
      peopleindex: -1, // 当前选的是第几个
      rules: {
        name: [{required: true, message: '请输入农户姓名', trigger: 'blur'}],
        yearNo: [{required: true, message: '请输入年度', trigger: 'blur'}],
        idNumber: [{required: true, message: '请输入身份证号', trigger: 'blur'}],
        bankName: [{required: true, message: '请输入', trigger: 'blur'}],
        bankAccount: [{required: true, message: '请输入', trigger: 'blur'}],
        householderRelation: [{required: true, message: '请输入', trigger: 'blur'}],
        scaleArea: [{required: true, message: '请输入', trigger: 'blur'}],
        rationArea: [{required: true, message: '请输入', trigger: 'blur'}],
        organizationNo: [{required: true, message: '请选择资格所在单位', trigger: 'blur'}],
        reason: [{required: true, message: '请输入原因', trigger: 'blur'}],
      },
      rulesform: {
        // name: [{
        //   required: true,
        //   message: "请输入农户姓名",
        //   trigger: "blur"
        // }],
        //   yearNo: [{
        //   required: true,
        //   message: "请输入年度",
        //   trigger: "blur"
        // }],
        //   idNumber: [{
        //   required: true,
        //   message: "请输入身份证号",
        //   trigger: "blur"
        // }],
        //   scaleArea: [{
        //   required: true,
        //   message: "请输入",
        //   trigger: "blur"
        // }],
        //   rationArea: [{
        //   required: true,
        //   message: "请输入",
        //   trigger: "blur"
        // }],
        //   organizationNo: [{
        //   required: true,
        //   message: "请输入资格所在单位",
        //   trigger: "blur"
        // }, ],
        //   rationPrecinctNo: [{
        //   required: true,
        //   message: "请输入",
        //   trigger: "blur"
        // }, ],
     },
      specialrules:{

      },
      rulesfresh: {
        idNumber: [{validator: checkIdentitytionId, trigger: "blur"}]
      },
      openScreen: false,
      formScreen:{

      },
      selectionRowData: [],//选中的全部数据

    };
  },
  components: {
    OrgSelect,
    FileUpload1,
    ChangePeople,
    basicManage,
    exceldownload,
    residentInfoTan,
    NewOrgSelect
  },
  created() {
    // this.getList();
    getDicts("pub_if").then(response => {
      this.pubifOptions = response.data;
    });
    getDicts("bank_name").then(response => {
      this.bankNameOptions = response.data;
    });
    getDicts("relations").then(response => {
      this.relationsOptions = response.data;
    });
    getDicts("year_cd").then(response => {
      this.yearNoOptions = response.data;
    });
    getDicts("aproval_status_no").then(response => {
      this.auditNoOptions = response.data;
      this.approverNoOptions = response.data;
    });
    getDicts("farmer_identity").then(response => {
      this.farmerIdentityNoOptions = response.data;
    });
    getDicts("sex").then(response => {
      this.sexNoOptions = response.data;
    });
    getDicts("source_no").then(response => {
      this.sourceNoOptions = response.data;
    });
    getDicts("bankcard_type_no").then(response => {
      this.bankcardTypeNoOptions = response.data;
    });
    getDicts("bank_name").then(response => {
      this.bankNameOptions = response.data;
    });
  },
  mounted() {
      },
  watch:{
    'specialform.yearNo': function (val) {
        if (this.specialform.idNumber && isIdentityId(this.specialform.idNumber)==''){
          this.specialform.age = val - this.specialform.idNumber.substring(6, 10)
        }else{
          this.specialform.age = 0
        }
    },
    'specialform.idNumber': function (val) {
        if (val && isIdentityId(val) == '') {
          this.specialform.age = this.specialform.yearNo - val.substring(6, 10)
        } else {
          this.specialform.age = 0
        }
    },
  },
  methods: {
    ...utils,
    firstTripFun(){
      this.handleQuery();
    },
    //审核
    examinefun(){
      this.dialogVisible = true
      this.textarea = ''
    },
    formatter(row, column, cellValue, index) {
      if (column.property == 'auditAFlag') {
        return selectDictLabel(this.approverNoOptions, row.auditAFlag != null && row.auditAFlag != undefined ? row.auditAFlag.toString() : "");
      } else if (column.property == 'auditBFlag') {
        return selectDictLabel(this.approverNoOptions, row.auditBFlag != null && row.auditBFlag != undefined ? row.auditBFlag.toString() : "");
      } else if (column.property == 'auditCFlag') {
        return selectDictLabel(this.approverNoOptions, row.auditCFlag != null && row.auditCFlag != undefined ? row.auditCFlag.toString() : "");
      } else if (column.property == 'auditDFlag') {
        return selectDictLabel(this.approverNoOptions, row.auditDFlag != null && row.auditDFlag != undefined ? row.auditDFlag.toString() : "");
      } else if (column.property == 'auditEFlag') {
        return selectDictLabel(this.approverNoOptions, row.auditEFlag != null && row.auditEFlag != undefined ? row.auditEFlag.toString() : "");
      } else if (column.property == 'auditFFlag') {
        return selectDictLabel(this.approverNoOptions, row.auditFFlag != null && row.auditFFlag != undefined ? row.auditFFlag.toString() : "");
      } else if (column.property == 'auditGFlag') {
        return selectDictLabel(this.approverNoOptions, row.auditGFlag != null && row.auditGFlag != undefined ? row.auditGFlag.toString() : "");
      } else if (column.property == 'auditHFlag') {
        return selectDictLabel(this.approverNoOptions, row.auditHFlag != null && row.auditHFlag != undefined ? row.auditHFlag.toString() : "");
      } else {
        return row[column.property]
      }
    },
    //选择人员
    peoplefun(item) {
      this.$refs.ChangePeople.init();
      // var index = this.form.farmdata.indexOf(item);
      // if (index !== -1) {
      //   this.peopleindex = index;
      // }
    },
    //获取选中人员信息
    transmit(payload) {
      this.form.bankName = null;
      this.form.idAddress =  null;
      // this.form.name =  null;
      this.form.age =  null;
      this.form.sexNo =  null;
      this.form.idNumber =  null;
      this.form.bankAccount =  null;
      this.form.householderRelation =  null;
      this.form.organizationNo =  null;
      this.$refs.formRef.clearValidate(['yearNo','name', 'organizationNo', 'idNumber'])
      // this.resetForm("form");
      // if(this.title.includes('添加')){
      //   this.reset()
      // }
      this.form.bankName = payload[0].bankName;
      this.form.idAddress = payload[0].address;
      this.form.name = payload[0].name;
      // this.form.name = payload[0].name;
      this.form.age = payload[0].age;
      this.form.sexNo = payload[0].sexNo;
      this.form.idNumber = payload[0].idNumber;
      this.form.bankAccount = payload[0].bankAccount;
      this.form.householderRelation = payload[0].householderRelation;
      this.form.organizationNo = payload[0].organizationNo;
    },
    /** 查看采集人员信息页 */
    lookUp(row) {
      this.$refs.residentInfoTan.changeInfo(row.rationPlanId, true);
    },
    handleOrgChange(orgCode) {
      this.queryParams.organizationNo = orgCode;
    },
    handleOrgChange3(orgCode) {
      this.form.organizationNo = orgCode;
    },
    handleOrgChange4(orgCode) {
      this.specialform.organizationNo = orgCode;
    },
    handleOrgChangeBasic1(orgCode) {
      this.queryParams.rationPrecinctNo = orgCode;
    },
    handleOrgChangeBasic2(orgCode) {
      this.form.rationPrecinctNo = orgCode;
    },

    //调用父组件方法，重新加载基本田资格导入列表
    newlist(val) {
      if (val) {
        this.getList();
      }
    },
    unlockfun() {
      let _this = this;
      const rationPlanIds =  _this.ids;
      this.$confirm(
        '是否进行解锁?',
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(function () {
          deblocking(rationPlanIds).then((res) => {
            _this.$modal.msgSuccess("解锁成功")
            _this.getList();
            return true;
          }).catch(()=>{
            return false;
          });
        })
        .then(() => {
          this.getList();
        });
    },
    /** 查询基本田资格导入列表 */
    getList() {
      this.loading = true;
      var param =  Object.assign({},this.queryParams)
      param.auditLevel = this.queryParams.auditLevel=='-1'?null:this.queryParams.auditLevel
      listRationplan(param).then(response => {
        this.rationplanList = response.data.page.records;
        //this.tabledata = Levelfun(response.data.level)
        this.total = response.data.page.total;
        this.loading = false;
        this.$nextTick(()=>{
          this.$refs.table.doLayout();
        })
      });
      let _this = this
      let params = {
        organizationNo: _this.queryParams.organizationNo,
        billType: 20
      }
      getMaxAuditLevel(params).then(response => {
        if(response.data) {
          _this.tabledata = Levelfun(response.data)
        } else {
          _this.tabledata = 0
        }
        this.$nextTick(()=>{
          this.$refs.table.doLayout();
        })
      })
    },
    // 审核字典
    auditAFlagFormat(row, column) {
      let objs = [
        {
          code: 0,
          name: "未审核",
        },
        {
          code: 1,
          name: "通过",
        },
        {
          code: 2,
          name: "未通过",
        }
      ]
      return selectDictLabel(objs, row.auditAFlag);
    },
    // 审核字典
    auditBFlagFormat(row, column) {
      let objs = [
        {
          code: 0,
          name: "未审核",
        },
        {
          code: 1,
          name: "通过",
        },
        {
          code: 2,
          name: "未通过",
        }
      ]
      return selectDictLabel(objs, row.auditBFlag);
    },
    // 审核字典
    auditCFlagFormat(row, column) {
      let objs = [
        {
          code: 0,
          name: "未审核",
        },
        {
          code: 1,
          name: "通过",
        },
        {
          code: 2,
          name: "未通过",
        }
      ]
      return selectDictLabel(objs, row.auditCFlag);
    },
    // 审核字典
    auditFlagFormat(row, column) {
      let objs = [
        {
          code: 0,
          name: "未审核",
        },
        {
          code: 1,
          name: "通过",
        },
        {
          code: 2,
          name: "未通过",
        }
      ]
      return selectDictLabel(objs, row.auditFlag);
    },
    //卡片类型
    bankcardTypeNoFormat(row, column) {
      return selectDictLabel(this.bankcardTypeNoOptions, row.bankName != null && row.bankcardTypeNo != undefined ? row.bankcardTypeNo.toString() : "");
    },
    // 性别字典翻译
    sexNoFormat(row, column) {
      return selectDictLabel(this.sexNoOptions, row.sexNo);
    },
    // 户主关系字典
    relationFormat(row, column) {
      return selectDictLabel(this.relationsOptions, row.householderRelation);
    },
    // 性别字典翻译
    sourceNoFormat(row, column) {
      return selectDictLabel(this.sourceNoOptions, row.sourceNo);
    },
    // 年度字典翻译
    yearNoFormat(row, column) {
      return selectDictLabel(this.yearNoOptions, row.yearNo);
    },
    // 是否字典翻译
    isCollectFormat(row, column) {
      return selectDictLabel(this.pubifOptions, row.isCollect);
    },
    // 是否指纹采集
    isFingerFormat(row, column) {
      return selectDictLabel(this.pubifOptions, row.isFingerprint);
    },
    // 管理区审核标志字典翻译
    auditNoFormat(row, column) {
      return selectDictLabel(this.auditNoOptions, row.approvalStatusNo);
    },
    // 农场审核标志字典翻译
    approverNoFormat(row, column) {
      return selectDictLabel(this.approverNoOptions, row.approverNo);
    },
    // 开户银行名称(数据字典)字典翻译
    bankNameFormat(row, column) {
      return selectDictLabel(this.bankNameOptions, row.bankName != null && row.bankName != undefined ? row.bankName.toString() : "");
    },
    //分类
    farmerTypeFormat(row, column) {
      return selectDictLabel(this.farmerTypeOption, row.farmerType);
    },
    // 农户身份字典翻译
    farmerIdentityNoFormat(row, column) {
      return selectDictLabel(
        this.farmerIdentityNoOptions,
        row.farmerIdentityNo
      );
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    //人员选择清空按钮
    clear() {
      this.reset()
    },
    // 表单重置
    reset() {
      (this.form = {
        yearNo: String(new Date().getFullYear()),
        idNumber: null,
        bankName: null,
        bankAccount: null,
        householderRelation: null,
        scaleArea: 0,
        rationArea: 0,
        organizationNo: null,
        farmerType: 0
      }),
        this.specialform = {
          yearNo: String(new Date().getFullYear()),
          idNumber: null,
          bankName: null,
          bankcardTypeNo: null,
          bankAccount: null,
          householderRelation: null,
          scaleArea: 0,
          rationArea: 0,
          organizationNo: null,
          issuingAuthority: null,
          validDate: null,
          phone: null,
          farmerIdentityNo:null,
          farmerType: 1,
          age:0
        }
      this.$nextTick(()=>{
        if( this.$refs.farmOrg){
          this.$refs.farmOrg.getCascader('reset')
        }
        if( this.$refs.bussOrg){
          this.$refs.bussOrg.getCascader('reset')
        }
      })
      this.resetForm("formRef");
      this.resetForm("specialformRef");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
     this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams = {
        page: 1,
        rows: 10,
        yearNo: String(new Date().getFullYear()),
        farmerType: 0,
        auditNo: null,
        farmerId: null,
        age: null,
        orgValue: null,
        validateCountStart: 0,
        validateCountEnd: 99,
        name: null,
        idNumber: null,
        organizationNo: null,
        isCollect: null,
        isFingerprint: null,
        archiveAge:null,
        startAge:0,//年龄开始
        endAge:99,//年龄结束
        startArchiveAge:null,//档案年龄范围开始
        endArchiveAge:null,//档案年龄范围结束
        auditLevel:-1,
        approvalStatusNo:"0",
        overageDate:null
      }
      this.resetForm("queryForm");
      this.$refs.newOrgSelect.getCascader('reset')
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.rationPlanId);
      this.names = selection.map(item => item.name);
      // console.log("selection==============",selection);
      this.selectionRowData = selection[0];
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    handleAudit() {
      // if(this.hasScreen.length > 0){
      //   this.$modal.msgError("已减员的信息不允许审核操作");
      // }
      const rationPlanIds = this.ids;
      rationauditPassByIds({
        rationPlanIds,
        approvalRemark: this.textarea
      }).then(
        (res) => {
          if (res.code === 0) {
            this.getList();
            this.$modal.msgSuccess("操作成功");
          }
        }
      );
      this.dialogVisible = false;
    },
    /** 审核拒绝按钮操作 */
    handleNoAudit() {
      // if(this.hasScreen.length > 0){
      //   this.$modal.msgError("已减员的信息不允许审核操作");
      // }

      if (this.textarea == "") return this.$modal.msgError("请填写备注");
      const rationPlanIds = this.ids;
      rationauditCancelByIds({
        rationPlanIds,
        approvalRemark: this.textarea
      }).then(
        (res) => {
          if (res.code === 0) {
            this.getList();
            this.$modal.msgSuccess("操作成功");
          }
        }
      );
      this.dialogVisible = false;
    },
    /** 减员按钮操作 */
    handleScreen() {

      this.formScreen = {}
      let farmerId = this.selectionRowData.farmerId;
      let yearNoScreen = this.selectionRowData.yearNo;
      getLandcontractfarmers(farmerId).then(response => {
        const {name, idNumber, address, bankAccount, organizationNo,sexNo} = response.data;
        this.formScreen.yearNo = yearNoScreen;
        this.formScreen.name = name;
        this.formScreen.idNumber = idNumber;
        this.formScreen.idAddress = address;
        this.formScreen.bankAccount = bankAccount
        this.formScreen.organizationNo = organizationNo
        this.formScreen.sexNo = sexNo
        this.formScreen.age = this.getAge(yearNoScreen,idNumber)
        this.openScreen = true;
      });

    },
    getAge(yearNoScreen,idNumber){
      var UUserCard = idNumber;
      if (UUserCard != null && UUserCard != '') {
        var age = yearNoScreen?(yearNoScreen - UUserCard.substring(6, 10) ):(myDate.getFullYear() - UUserCard.substring(6, 10) );
        return age;
      }
    },
    //点击切换
    clicktab(e){
      if(this.tabsName != e.props.label){
        this.tabsName = e.props.label
        if(e.props.label=='基本田资格（普通人员）'){
          this.form = {
            name: null,
            yearNo: String(new Date().getFullYear()),
            idNumber: null,
            bankName: null,
            bankAccount: null,
            householderRelation: null,
            scaleArea: 0,
            rationArea: 0,
            organizationNo: null,
            idAddress: null,
            farmerType: 0
          };
          this.specialrules = {}
          this.rulesform = {
            name: [{
              required: true,
              message: "请选择农户姓名",
              trigger: "blur"
            }],
              yearNo: [{
              required: true,
              message: "请选择年度",
              trigger: "blur"
            }],
              idNumber: [{
              required: true,
              message: "请选择身份证号",
              trigger: "blur"
            }],
              organizationNo: [{
              required: true,
              message: "请选择资格所在单位",
              trigger: "change"
            }, ],
          }
        }
        else{
          this.specialform = {
            name: null,
            yearNo: String(new Date().getFullYear()),
            idNumber: null,
            bankName: null,
            bankcardTypeNo: null,
            bankAccount: null,
            householderRelation: null,
            scaleArea: 0,
            rationArea: 0,
            organizationNo: null,
            idAddress: null,
            issuingAuthority: null,
            validDate: null,
            phone: null,
            farmerIdentityNo:null,
            farmerType: 1,
            age:0
          }
          let phoneNumber = (rule, value, callback) => {
            if (
              value &&
              (!/^[1][*********]\d{9}$/.test(value) ||
                !/^[1-9]\d*$/.test(value) ||
                value.length !== 11)
            ) {
              callback(new Error("手机号码不符合规范"));
            } else {
              callback();
            }
          };
          let checkIdentitytionId = (rule, value, callback) => {
            var errorMsg = isIdentityId(value);
            if (errorMsg) {
              callback(new Error(errorMsg));
            } else {
              callback();
            }
          };
          this.specialrules = {
            name: [{
              required: true,
              message: "请输入农户姓名",
              trigger: "blur"
            }],
            yearNo: [{
              required: true,
              message: "请输入年度",
              trigger: "blur"
            }],
            idNumber: [{
              required: true,
              message: "请输入身份证号",
              trigger: "blur"
            },
              { validator: checkIdentitytionId, trigger: "blur" }
            ],
            organizationNo: [{
              required: true,
              message: "请选择资格所在单位",
              trigger: "change"
            }, ],
            sexNo:[{
              required: true,
              message: "请选择性别",
              trigger: "change"
            }],
            idAddress:[{
              required: true,
              message: "请输入身份证所在地址",
              trigger: "blur"
            }],
            issuingAuthority:[{
              required: true,
              message: "请输入发证机关",
              trigger: "blur"
            }],
            validDate:[{
              required: true,
              message: "请输入身份证有效期",
              trigger: "blur"
            }],
            phone: [{ validator: phoneNumber, trigger: "blur" }],
            farmerIdentityNo:[{
              required: true,
              message: "请选择农户身份",
              trigger: "change"
            }],
          }
          this.rulesform = {}
        }
        this.$nextTick(()=>{
          this.$refs.orgFarm.getCascader('reset');
          this.$refs.orgBuss.getCascader('reset');
          //如果实在清不掉
          this.$refs.orgBuss.farmValues = null;
          this.$refs.orgFarm.farmValues = null;
          if(this.$refs.formRef){
            this.$refs.formRef.clearValidate();
          }
          if(this.$refs.specialformRef){
            this.$refs.specialformRef.clearValidate();
          }
        })
      }

    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加基本田资格";
      this.activeName = 'farm',
      this.tabsName = '基本田资格（普通人员）'
      this.specialrules = {
      }
      this.rulesform = {
        name: [{
          required: true,
          message: "请选择农户姓名",
          trigger: "blur"
        }],
        yearNo: [{
          required: true,
          message: "请选择年度",
          trigger: "blur"
        }],
        idNumber: [{
          required: true,
          message: "请选择身份证号",
          trigger: "blur"
        }],
        organizationNo: [{
          required: true,
          message: "请选择资格所在单位",
          trigger: "change"
        }, ],
      }
      this.$nextTick(()=>{
        if(this.$refs.formRef){
          this.$refs.formRef.clearValidate();
        }
      })
    },
    mask(value, start = 0, end = value.length) {
      if (!value) return '';
      const len = value.length;
      const maskLen = end - start;
      if (maskLen <= 0 || start >= len || end > len) return value;

      // 将需要掩码的部分替换为*
      return (
          value.substring(0, start) +
          '*'.repeat(maskLen) +
          value.substring(end)
      );
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const rationPlanId = row.rationPlanId || this.ids;
      rationinfo(rationPlanId).then(response => {
        if(response.data.farmerType == 0){
          this.form = response.data
          this.activeName = 'farm'
          this.tabsName = '基本田资格（普通人员）'
          this.specialrules = {
          }
          this.rulesform = {
            name: [{
              required: true,
              message: "请选择农户姓名",
              trigger: "blur"
            }],
            yearNo: [{
              required: true,
              message: "请选择年度",
              trigger: "blur"
            }],
            idNumber: [{
              required: true,
              message: "请选择身份证号",
              trigger: "blur"
            }],
            organizationNo: [{
              required: true,
              message: "请选择资格所在单位",
              trigger: "change"
            }, ],
          }
          this.$nextTick(()=>{
            if(this.$refs.formRef){
              this.$refs.formRef.clearValidate();
            }
          })
        }else{
          this.specialform = response.data
          this.activeName = 'special'
          this.tabsName = '基本田资格（特殊人员）'
          let phoneNumber = (rule, value, callback) => {
            if (
              value &&
              (!/^[1][*********]\d{9}$/.test(value) ||
                !/^[1-9]\d*$/.test(value) ||
                value.length !== 11)
            ) {
              callback(new Error("手机号码不符合规范"));
            } else {
              callback();
            }
          };
          let checkIdentitytionId = (rule, value, callback) => {
            var errorMsg = isIdentityId(value);
            if (errorMsg) {
              callback(new Error(errorMsg));
            } else {
              callback();
            }
          };
          this.specialrules = {
            name: [{
              required: true,
              message: "请输入农户姓名",
              trigger: "blur"
            }],
            yearNo: [{
              required: true,
              message: "请输入年度",
              trigger: "blur"
            }],
            idNumber: [{
              required: true,
              message: "请输入身份证号",
              trigger: "blur"
            },
              { validator: checkIdentitytionId, trigger: "blur" }],
            organizationNo: [{
              required: true,
              message: "请选择资格所在单位",
              trigger: "change"
            }],
            sexNo:[{
              required: true,
              message: "请选择性别",
              trigger: "change"
            }],
            idAddress:[{
              required: true,
              message: "请输入身份证所在地址",
              trigger: "blur"
            }],
            issuingAuthority:[{
              required: true,
              message: "请输入发证机关",
              trigger: "blur"
            }],
            validDate:[{
              required: true,
              message: "请输入身份证有效期",
              trigger: "blur"
            }],
            phone: [{ validator: phoneNumber, trigger: "blur" }],
            farmerIdentityNo:[{
              required: true,
              message: "请选择农户身份",
              trigger: "change"
            }],
          }
          this.rulesform = {}
          this.$nextTick(()=>{
            if(this.$refs.specialformRef){
              this.$refs.specialformRef.clearValidate();
            }
          })
        }
        this.open = true;
        this.title = "修改基本田资格";
      });
    },
    /** 提交按钮 */
    submitForm() {
      if(this.tabsName=='基本田资格（特殊人员）'){
        this.$refs["specialformRef"].validate((valid) => {
            if (valid) {
              if (this.specialform.rationPlanId != null) {
                let paramsDetail = JSON.parse(JSON.stringify(this.specialform))
                delete paramsDetail.age
                rationupdate(paramsDetail).then(response => {
                  this.$modal.msgSuccess("修改成功");
                  this.open = false;
                  this.getList();
                });
              } else {
                let paramsAdd = JSON.parse(JSON.stringify(this.specialform))
                delete paramsAdd.age
                rationinsert(this.specialform).then(response => {
                  this.$modal.msgSuccess("新增成功");
                  this.open = false;
                  this.getList();
                });
              }
            }
        });
      }else{
        this.$refs["formRef"].validate(valid => {
          if (valid) {
            if (this.form.rationPlanId != null) {
              rationupdate(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            } else {
              rationinsert(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      }
    },
    submitFormScreen(){
      this.$refs["formScreen"].validate((valid) => {
        if (valid) {
          addRationplan(this.formScreen).then((response) => {
            this.$modal.msgSuccess("减员成功");
            this.openScreen = false;
            this.getList();
          });
        }
      });

    },
    isFamilyMemberFormat(row, column) {
      //
      return selectDictLabel(this.pubifOptions, row.isFamilyMember);
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const rationPlanIds = row.rationPlanId || this.ids;
      const name = row.name || this.names;

      this.$confirm(
        '是否确认删除农户姓名为"' + name + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(function () {
          return Array.isArray(rationPlanIds)
            ? rationlogicDeleteByIds(rationPlanIds)
            : rationlogicDeleteById(rationPlanIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        });
    },
    /** 全部管理区审核按钮操作 */
    auditPassFirst(row) {
      const rationPlanIds = this.ids;
      this.$confirm(
        '是否确认审核基本田资格导入序号为"' + rationPlanIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(function () {
          return auditPassFirst(rationPlanIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("审核成功");
        });
    } /** 全部取消管理区审核按钮操作 */,
    auditCancelFirst(row) {
      const rationPlanIds = this.ids;
      this.$confirm(
        '是否确认审核基本田资格导入序号为"' + rationPlanIds + '"的数据项?',
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      )
        .then(function () {
          return auditCancelFirst(rationPlanIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("取消审核成功");
        });
    },
    exportTemplate() {
      window.location.href = exportTemplate();
    },
    specialExportTemplate() {
      window.location.href = specialExportTemplate();
    },
    exportExcel() {
      window.location.href = exportExcel();
    },

    submitfun() {
      this.$confirm("是否批量引入上一年度得数据", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(function () {
          return createLastYearRation();
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("引入数据成功");
        });
    }

  }
};
</script>
<style scoped>
 .num .el-form-item__content, .num .el-form-item {
  display: flex;
}
</style>
<style lang="scss" scoped>
:deep{
  .el-form-item__content{
    width: 220px;
  }
  .el-cascader--small{
    width: 100%;
  }

}
.num{
  .el-form-item__content{
    display: flex;
  }
}
.ration {
  .el-form-item__content {
    // width: 63%;
    //width: 203px;

    .el-date-editor {
      width: 203px;
    }
  }

  .validate-count{
    .el-form-item__content {
      display: flex;
    }
  }
  .el-form--inline .el-form-item {
    width: 100%;
  }
  .el-input-number--mini{
    width: 100%;
  }
}

.buttons {
  text-align: center;
  line-height: 30px;
}

.buttons1 {
  display: flex;
  align-content: flex-end;
  justify-content: flex-end;
  align-items: flex-start;
}

// .el-form-item{
//   text-align: center;
// }
.jianju{
  width: 100px;
  margin-left: 45px;
  margin-top: 10px;

}
:deep{
  .el-table__fixed{
    height: calc(100% - 14px) !important;
  }
}

</style>
