<!--
@name:
@description: 日报单
@author: qikai
@time: 2022-03-18 14:13:31
-->
<template>
    <div class="app-container report">
        <el-form ref="queryForm" :model="queryParams" v-show="showSearch" label-width="68px" class="form-line">
            <el-row :gutter="30">
                <el-col :span="6">
                    <el-form-item label="归属" prop="orgCode">
                        <OrgSelects ref="orgCodeRef" :apiUrl="orgDictsUrl()" :defaultOrgCode="queryParams.orgCode" :placeholderText="'请选择归属'" style="width: 100%;"
                            @handleOrgCode="handleOrgChange"></OrgSelects>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="年份" prop="year">
                        <el-select v-model="queryParams.year" clearable placeholder="请选择年份">
                            <el-option v-for="dict in yearNoOptions" :key="dict.code" :label="dict.name"
                                :value="dict.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="日期" label-width="100px">
                        <el-date-picker v-model="dateTime" type="daterange" range-separator="至" start-placeholder="开始日期"
                            end-placeholder="结束日期" style="width: 190px" value-format="YYYY-MM-DD">
                        </el-date-picker>
                        <!-- <el-date-picker
                            v-model="queryParams.createTime"
                            placeholder="请选择日期"

                            style="width: 190px"
                            value-format="YYYY-MM-DD"
                        >
                        </el-date-picker> -->
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="种植作物" prop="raiseCropsCd">
                        <el-select v-model="queryParams.raiseCropsCd" clearable placeholder="请选择种植作物">
                            <el-option v-for="dict in raiseCropsCdOptions" :key="dict.raiseCropsCd"
                                :label="dict.raiseCropsNm" :value="dict.raiseCropsCd" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="生产流程" prop="prodProcessCode">
                        <el-select v-model="queryParams.prodProcessCode" clearable placeholder="请选择生产流程">
                            <el-option v-for="dict in prodProcessOptions" :key="dict.code" :label="dict.name"
                                :value="dict.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="作业环节" prop="linkCode">
                        <el-select v-model="queryParams.linkCode" clearable placeholder="请选择作业环节">
                            <el-option v-for="dict in linkCodeOptions" :key="dict.linkCode" :label="dict.linkName"
                                :value="dict.linkCode" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="地块名称" prop="plotName" label-width="100px">
                        <el-input v-model="queryParams.plotName" clearable placeholder="请输入地块名称" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="牌照号" prop="licenseNo">
                        <el-input v-model="queryParams.licenseNo" clearable placeholder="请输入牌照号" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="机主" prop="amOwnerName">
                        <el-input v-model="queryParams.amOwnerName" clearable placeholder="请输入机主" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="作业人员" prop="driverName">
                        <el-input v-model="queryParams.driverName" clearable placeholder="请输入作业人员" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="家庭农场场长" prop="familyFarmLeaderName" label-width="100px">
                        <el-input v-model="queryParams.familyFarmLeaderName" clearable placeholder="请输入家庭农场场长姓名" />
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-show="more">
                    <el-form-item label="质量监管员" prop="qualSignName"  class="auto-line-height">
                        <el-input v-model="queryParams.qualSignName" clearable placeholder="请输入质量监管员姓名" />
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-show="more">
                    <el-form-item label="任务编号" prop="workSubtaskNo">
                        <el-input v-model="queryParams.workSubtaskNo" clearable placeholder="请输入任务编号" />
                    </el-form-item>
                </el-col>
                <el-col :span="6" v-show="more">
                    <el-form-item label="是否审核" prop="auditType">
                        <el-select v-model="queryParams.auditType" placeholder="请选择是否审核" clearable>
                            <el-option v-for="dict in pubIfOptions" :key="dict.code" :label="dict.name"
                                :value="dict.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :lg="more ? 12 : 6" :xl="more ? 12 : 6" class="text-right" align="right" style="margin-bottom: 10px;">
                    <el-button :icon="more ? 'ArrowUpBold' : 'ArrowDownBold'"
                        @click="moreHander">更多
                    </el-button>
                    <el-button icon="Refresh"  @click="resetQuery">重置</el-button>
                    <el-button v-hasPermi="['report:search']" icon="Search"  type="primary"
                        @click="handleQuery">搜索
                    </el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8 ">
            <el-col :span="1.5">
                <el-button v-show="false" v-hasPermi="['report:print']" v-print="printObj" ref="printButton"
                    icon="Search" plain  type="primary"></el-button>
                <el-button v-show="false" v-hasPermi="['report:printWorkReport']" v-print="printObjMap"
                    ref="printButtonMap" icon="Search" plain  type="primary"></el-button>
                <el-button class="ml0" style="margin-left: 0px;" v-hasPermi="['report:print']" @click="onClickPrint"
                    icon="Search" plain >打印
                </el-button>
                <el-button class="ml0" v-hasPermi="['report:printWorkReport']" @click="onClickPrintMap"
                    icon="Search" plain >打印作业报告
                </el-button>
            </el-col>
            <el-col :span="1.5">
                <!--导出excel-->
                <el-button icon="Download"  v-hasPermi="['report:exportExcel']"
                    @click="downloadFn">导出</el-button>
                <exceldownload v-show="false" ref="exceldownload" :exceltext="'导出'" icon="Download"
                    :param="{ ...queryParams, columnFiledList: columnsValue }" :url="url"
                    v-hasPermi="['report:exportExcel']"></exceldownload>
            </el-col>
            <!-- <el-col :span="1.5">
                <el-button
                    icon="Setting"

                    @click="columConfig"
                    >配置列</el-button
                >
            </el-col> -->
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>



        <div>
            <el-table ref="tableRef" :data="subsoilData"
                :height="tableHeight"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" align="center" width="55">
                </el-table-column>
                <el-table-column v-if="column.createTime" align="center" label="日期" prop="createTime" width="180" />
                <el-table-column v-if="column.orgName" align="center" label="归属" prop="orgName" width="180" />
                <el-table-column width="220" v-if="column.plotName" align="center" label="地块名称" prop="plotName" />
                <el-table-column v-if="column.plotArea" align="center" label="地块面积(亩)" prop="plotArea" />
                <el-table-column v-if="column.allocatedArea" align="center" label="分配面积(亩)" prop="allocatedArea" />
                <el-table-column v-if="column.workArea" align="center" label="作业面积(亩)" prop="workArea" />
                <!--            <el-table-column  v-if="column.orgName" align="center" label="单价(元)" prop="createTime" width="180"/>-->
                <el-table-column v-if="column.passArea" align="center" label="合格面积(亩)" prop="passArea" />
                <el-table-column v-if="column.workUnitCost" align="center" label="作业单价(元/亩)" prop="workUnitCost" />
                <el-table-column v-if="column.workTotalCost" align="center" label="结算费用(元)" prop="workTotalCost" />
                <el-table-column v-if="column.raiseCropsNm" align="center" label="种植作物" prop="raiseCropsNm" />
                <el-table-column v-if="column.prodProcessName" align="center" label="生产流程" prop="prodProcessName" />
                <el-table-column v-if="column.linkName" align="center" label="作业环节" prop="linkName" width="180" />
                <el-table-column v-if="column.licenseNo" align="center" label="牌照号" prop="licenseNo" width="100" />
                <el-table-column v-if="column.amOwnerName" align="center" label="机主" prop="amOwnerName" />
                <!--
                <el-table-column  v-if="column.orgName" align="center" label="作业人员" prop="driverName"/>
                -->
                <el-table-column v-if="column.driverName" align="center" label="作业人员" prop="driverName" />
                <el-table-column v-if="column.qualSignName" align="center" label="质量监管员" prop="qualSignName" />
                <el-table-column v-if="column.leadSignName" align="center" label="带班领导" prop="leadSignName" />
                <el-table-column v-if="column.familyFarmLeaderName" align="center" label="家庭农场场长"
                    prop="familyFarmLeaderName" />
            </el-table>
            <!--            <div style="display: flex;flex-direction: row-reverse;flex-wrap: nowrap;line-height: 40px;text-align: center">-->
            <!--                <div style="margin-right: 150px">质量监管员:</div>-->
            <!--                <div style="margin-right: 150px">作业人员:</div>-->
            <!--                <div style="margin-right: 150px">带班领导:</div>-->
            <!--            </div>-->
        </div>
        <pagination v-show="total > 0" v-model:limit="queryParams.rows" :page-sizes="[10, 20, 50, 100]"
             v-model:page="queryParams.page" :total="total" @pagination="getList" />
        <!--配置展示列-->
        <el-dialog v-model="columnCfgVisual" title="配置列" width="20%">
            <div>
                <template v-for="column in columns">
                    <div>
                        <el-checkbox :checked="column.checked" :disabled="column.disabled" :label="column.label"
                            @change="
                                (checked) => columnValueChg(checked, column.key)
                            "></el-checkbox>
                    </div>
                </template>
            </div>
            <template #footer class="dialog-footer">
                <el-button  type="primary" @click="columnCfgVisual = false">确 定</el-button>
            </template>
        </el-dialog>
        <template>
            <div v-show="false">
                <div id="print-data" class="print" style="zoom: 58%">
                    <div style="page-break-after: always; height: 100%" v-for="(item, index) in subsoilDataPrintPage"
                        :key="index">
                        <el-table :data="item" border style="height: 1080px">
                            <el-table-column v-if="column.createTime" align="center" label="日期" prop="createTime"
                                width="180" />
                            <el-table-column v-if="column.orgName" align="center" label="归属" prop="orgName"
                                width="180" />
                            <el-table-column v-if="column.plotName" align="center" label="地块名称" width="220"
                                prop="plotName" />
                            <el-table-column v-if="column.plotArea" align="center" label="地块面积(亩)" prop="plotArea" />
                            <el-table-column v-if="column.allocatedArea" align="center" label="分配面积(亩)"
                                prop="allocatedArea" />
                            <el-table-column v-if="column.workArea" align="center" label="作业面积(亩)" prop="workArea" />
                            <!--            <el-table-column  v-if="column.orgName" align="center" label="单价(元)" prop="createTime" width="180"/>-->
                            <el-table-column v-if="column.passArea" align="center" label="合格面积(亩)" prop="passArea" />


                            <el-table-column v-if="column.workUnitCost" align="center" label="作业单价(元/亩)"
                                prop="workUnitCost" />
                            <el-table-column v-if="column.workTotalCost" align="center" label="结算费用(元)"
                                prop="workTotalCost" />


                            <el-table-column v-if="column.raiseCropsNm" align="center" label="种植作物"
                                prop="raiseCropsNm" />
                            <el-table-column v-if="column.linkName" align="center" label="作业环节" prop="linkName"
                                width="180" />
                            <el-table-column v-if="column.licenseNo" align="center" label="牌照号" prop="licenseNo"
                                width="100" />
                            <!--
                            <el-table-column  v-if="column.orgName" align="center" label="作业人员" prop="driverName"/>
                            -->
                            <el-table-column v-if="column.driverName" align="center" label="作业人员" prop="driverName" />
                            <el-table-column v-if="column.qualSignName" align="center" label="质量监管员"
                                prop="qualSignName" />
                            <el-table-column v-if="column.leadSignName" align="center" label="带班领导"
                                prop="leadSignName" />
                            <el-table-column v-if="column.familyFarmLeaderName" align="center" label="家庭农场场长"
                                width="120" prop="familyFarmLeaderName" />
                        </el-table>
                        <div style="
                                display: flex;
                                flex-direction: row-reverse;
                                flex-wrap: nowrap;
                                line-height: 40px;
                                text-align: center;
                                bottom: 10px;
                                right: 100px;
                            ">
                            <div style="margin-right: 150px">质量监管员:</div>
                            <div style="margin-right: 150px">作业人员:</div>
                            <div style="margin-right: 150px">带班领导:</div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 元素display:none 时图表不渲染 采用 opacity style="opacity: 0;"-->
            <div v-if="checkedListAll.length > 0" style="opacity: 0;">
                <div id="print-data-map" style="zoom: 100%">
                    <land :ref="'land' + index" @end="end" @toImage="toImage" :meansproduni="meansprodunit"
                        :meansProdType="meansProdType" :current="current" :index="index"
                        v-for="(current, index) in checkedListAll"></land>
                </div>
            </div>
        </template>
        <el-dialog v-model="fullscreenLoading" width="350px" custom-class="download-dialog" append-to-body
            :close-on-click-modal="false" :show-close="false" style="top:35vh;">
            <div style="text-align: center;padding-bottom: 20px;">
                <el-icon style="font-size: 32px;color: #00afff;margin-bottom: 10px"><Loading class="rotate-animation"/></el-icon>
                <br> 加载中，请稍等
            </div>
        </el-dialog>
        <el-dialog title="导出" width="725px" v-model="opentransfer" append-to-body>
            <div style="font-size: 16px;margin-bottom: 20px;">
                <span style="color: red;">*</span>
                <span>请选择需要导出的列，可上下拖拽列表调整导出列顺序</span>
            </div>
            <el-transfer v-if="opentransfer" :titles="['源列表', '目标列表']" v-model="columnsValue" :data="columns"
                ref="transfer" id="transfer">
                <template class="drag" #default="{ option }"
                    style="cursor: move;display: flex;justify-content: space-between;align-items: center;width: 180px;"
                    @dragstart="drag($event, option)">{{ option.label }}<i class="el-icon-rank"></i></template>
            </el-transfer>
            <template #footer class="dialog-footer">
                <el-button type="primary" @click="submittransfer">确 定</el-button>
                <el-button @click="canceltransfer">取 消</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { cropProcess, workbillList } from "@/api/systemagriculturalmachineryv2/dispatch/operational";
import exceldownload from "@/views/systemagriculturalmachineryv2/components/exceldownload";
import land from "./land.vue";
import Sortable from 'sortablejs'
import { getDicts, getRaiseCrops } from "@/api/systemagriculturalmachineryv2/dict";
import useUserStore from "@/store/modules/user"
import { selectDictLabelForRaiseCrops } from "@/views/systemagriculturalmachineryv2/utils/cop";
let _this;
import print from 'vue3-print-nb'
import calcTabelHeight from "@/views/systemagriculturalmachineryv2/mixins/calcTabelHeight";
import {orgDictsUrl} from "@/api/systemagriculturalmachineryv2/orgDict.js";
export default {
    name: "/schedulingPlan/workbill/queryByPage",
    components: {
        exceldownload,
        land
    },
    mixins: [calcTabelHeight],
    directives: {
        print
    },
    data() {
        _this = this;
        return {
            draggingKey: null,
            columnsValue: [],
            opentransfer: false,
            more: false,
            showSearch: true,
            columnCfgVisual: false,
            columns: [
                {
                    label: "日期",
                    disabled: false,
                    checked: true,
                    key: "createTime",
                },
                {
                    label: "归属",
                    disabled: false,
                    checked: true,
                    key: "orgName",
                },
                {
                    label: "地块名称",
                    disabled: false,
                    checked: true,
                    key: "plotName",
                },
                {
                    label: "地块面积(亩)",
                    disabled: false,
                    checked: true,
                    key: "plotArea",
                },
                {
                    label: "分配面积(亩)",
                    disabled: false,
                    checked: true,
                    key: "allocatedArea",
                },
                {
                    label: "作业面积(亩)",
                    disabled: false,
                    checked: true,
                    key: "workArea",
                },
                {
                    label: "合格面积(亩)",
                    disabled: false,
                    checked: true,
                    key: "passArea",
                },
                {
                    label: "作业单价(元/亩)",
                    disabled: false,
                    checked: true,
                    key: "workUnitCost",
                },
                {
                    label: "结算费用(元)",
                    disabled: false,
                    checked: true,
                    key: "workTotalCost",
                },
                {
                    label: "种植作物",
                    disabled: false,
                    checked: true,
                    key: "raiseCropsNm",
                },
                {
                    label: "生产流程",
                    disabled: false,
                    checked: true,
                    key: "prodProcessName",
                },
                {
                    label: "作业环节",
                    disabled: false,
                    checked: true,
                    key: "linkName",
                },
                {
                    label: "牌照号",
                    disabled: false,
                    checked: true,
                    key: "licenseNo",
                },
                {
                    label: "机主",
                    disabled: false,
                    checked: true,
                    key: "amOwnerName",
                },
                {
                    label: "作业人员",
                    disabled: false,
                    checked: true,
                    key: "driverName",
                },
                {
                    label: "质量监管员",
                    disabled: false,
                    checked: true,
                    key: "qualSignName",
                },
                {
                    label: "带班领导",
                    disabled: false,
                    checked: true,
                    key: "leadSignName",
                },
                {
                    label: "家庭农场场长",
                    disabled: false,
                    checked: true,
                    key: "familyFarmLeaderName",
                },
            ],
            column: {
                createTime: true,
                orgName: true,
                plotName: true,
                plotArea: true,
                allocatedArea: true,
                workArea: true,
                passArea: true,
                workUnitCost: true,
                workTotalCost: true,
                raiseCropsNm: true,
                linkName: true,
                licenseNo: true,
                operSignName: true,
                qualSignName: true,
                leadSignName: true,
                driverName: true,
                familyFarmLeaderName: true,
                prodProcessName: true,
                amOwnerName: true,
            },
            url:
                window.VITE_APP_BASE_API +
                `/${import.meta.env.VITE_APP_GATEWAYPATH_AGRICMATCHINE}/schedulingPlan/workbill/exportExcel`,
            printObj: {
                id: "print-data",
                popTitle: "日报单",
                extraHead:
                    '<meta http-equiv="Content-Language" content="zh-cn"/>',
                beforeEntryIframe(val) {
                    //console.log('>>>>>>>>>beforeEntryIframe: ', val)
                    const widthArr = [
                        "5%",
                        "5%",
                        "5%",
                        "5%",
                        "5%",
                        "5%",
                        "5%",
                        "5%",
                        "5%",
                        "5%",
                        "5%",
                        "5%",
                    ];
                    const tables = document
                        .getElementById("print-data")
                        .getElementsByTagName("table"); //获取打印的表格“tablediv7是表格的Id”
                    const headcolgroupCol = tables[0]
                        .getElementsByTagName("colgroup")[0]
                        .getElementsByTagName("col");
                    const headcolgroupCol1 = tables[1]
                        .getElementsByTagName("colgroup")[0]
                        .getElementsByTagName("col");
                    widthArr.forEach((val, index) => {
                        headcolgroupCol[index].width = val;
                        headcolgroupCol1[index].width = val;
                    });
                },
                beforeOpenCallback(val) {
                    //console.log('>>>>>>>打开之前: ', val)
                },
                openCallback(val) {
                    //console.log('>>>>>>>>执行了打印: ', val)
                },
                closeCallback(val) {
                    //console.log('>>>>>>>>>关闭了打印工具: ', val)
                },
                clickMounted() {
                    // that.getListAll()
                },
            },
            printObjMap: {
                id: "print-data-map",
                popTitle: "农机作业报告",//农机作业报告
                extraHead:
                    '<meta http-equiv="Content-Language" content="zh-cn"/>',
                beforeEntryIframe(val) {
                    console.log('>>>>>>>beforeEntryIframe: ')
                },
                previewOpenCallback(val) {
                    console.log('>>>>>>>previewOpenCallback: ')
                },
                openCallback(val) {
                    console.log('>>>>>>>>openCallback: ')
                    _this.endcount = 0
                    _this.imagecount = 0

                },
                closeCallback(val) {
                    console.log('>>>>>>>>>closeCallback: ')
                    _this.endcount = 0
                    _this.imagecount = 0
                    _this.checkedListAll = []
                },
                clickMounted(val) {
                    console.log('>>>>>>>>>clickMounted: ')
                    _this.endcount = 0
                    _this.imagecount = 0
                },
            },
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,

            // 总条数
            total: 0,
            //
            subsoilData: [],
            subsoilDataPageTmp: [],
            //打印分页带表头
            subsoilDataPrintPage: [],
            //机具分档
            linkCodeOptions: [],
            amGradeNameOptions: [],
            raiseCropsCdOptions: [],
            workStatusOptions: [],
            workExtentOptions: [],
            prodProcessOptions: [],
            // 年度字典
            yearNoOptions: [],
            dateTime: null,
            // 查询参数
            queryParams: {
                columnFiledList: [],
                driverName: null,
                raiseCropsCd: null,
                prodProcessCode: null,
                linkCode: null,
                startTime: null,
                endTime: null,
                orgCode: useUserStore().currentOrgCode,
                amOwnerName: null,
                plotName: null,
                licenseNo: null,
                auditType: "1",
                workSubtaskNo: null,
                page: 1,
                rows: 10,
                workBillIds: [],
                year: String(new Date().getFullYear()),
                familyFarmLeaderName: null,
                qualSignName: null,
            },
            // 表单参数
            form: {},
            // 表单校验
            rules: {},
            checkedList: [], // 选择的数据集合
            checkedListAll: [],
            endcount: 0,
            imagecount: 0,
            fullscreenLoading: false,
            meansProdType: [],
            meansprodunit: [],
            pubIfOptions: []
        };
    },
    watch: {
        dateTime(val) {
            this.queryParams.startTime = val ? val[0] : null;
            this.queryParams.endTime = val ? val[1] : null;
        },
        'queryParams.orgCode': function (val, oldVal) {
            if (val != null && val !== '') {
                this.linkList = [];
                this.queryParams.linkCode = '';
                this.getRaise();
            }
        },
        "queryParams.raiseCropsCd": function (val, oldVal) {
            this.queryParams.linkCode = null;
            if (
                val != null &&
                val !== "" &&
                this.queryParams.prodProcessCode != null &&
                this.queryParams.prodProcessCode !== ""
            ) {
                //环节字典
                cropProcess({
                    raiseCrops: val,
                    prodProcessCode: this.queryParams.prodProcessCode,
                }).then((response) => {
                    console.log(response);
                    this.linkCodeOptions = response.data;
                });
            } else {
                this.linkCodeOptions = [];
            }
        },
        "queryParams.prodProcessCode": function (val, oldVal) {
            this.queryParams.linkCode = null;
            if (
                val != null &&
                val !== "" &&
                this.queryParams.raiseCropsCd != null &&
                this.queryParams.raiseCropsCd !== ""
            ) {
                //查机具品目
                cropProcess({
                    raiseCrops: this.queryParams.raiseCropsCd,
                    prodProcessCode: val,
                }).then((response) => {
                    this.linkCodeOptions = response.data;
                    console.log(response);
                });
            } else {
                this.linkCodeOptions = [];
            }
        },
    },
    created() {
        getDicts("means_prod_type").then((response) => {
            this.meansProdType = response.data || [];
        });
        getDicts("means_use_unit").then((response) => {
            this.meansprodunit = response.data || [];
        });
        getDicts("year_cd").then((response) => {
            this.yearNoOptions = response.data;
        });
        // getDicts("raise_crops").then((response) => {
        //     this.raiseCropsCdOptions = response.data;
        // });
        getDicts("prod_process").then((response) => {
            this.prodProcessOptions = response.data;
        });
        getDicts("pub_if").then(response => {
            this.pubIfOptions = response.data;
        });
        // console.log(this.columns);
        // let columns = this.getCookie("am_report");
        // if (columns) {
        //     columns = JSON.parse(unescape(columns));
        //     console.log("kk", columns);
        //     this.columns.forEach((c) => {
        //         c.checked = columns[c.key];
        //     });
        //     this.column = columns;
        // }
        this.getRaise();
        this.getList();

    },
    computed: {},
    methods: {
      orgDictsUrl,
        submittransfer() {
            if (this.columnsValue.length == 0) {
                this.$message.error('请选择需要导出的列');
                return
            }
            this.opentransfer = false
            localStorage.setItem("columnsValue",JSON.stringify(this.columnsValue))
            this.$refs.exceldownload.execDownload()
        },
        canceltransfer() {
            this.opentransfer = false
        },
        drag(ev, option) {
            this.draggingKey = option.key
        },
        downloadFn() {
            this.opentransfer = true;
            this.$nextTick(() => {
                let columnsValue = localStorage.getItem("columnsValue")
                if(columnsValue){
                    this.columnsValue = JSON.parse(columnsValue)
                    let cache = JSON.parse(JSON.stringify(this.columns))
                    this.columns = this.columns.filter(item=>{
                        return  this.columnsValue.indexOf(item.key)==-1
                    })
                    let arr = []
                    this.columnsValue.forEach(item=>{
                        let obj = cache.find(it=>{
                            return it.key == item
                        })
                        if(obj){
                            arr.push(obj)
                        }
                    })
                    this.columns = [...arr,...this.columns]
                }else{
                    this.columnsValue = []
                }
                const transfer = this.$refs.transfer.$el
                const rightPanel = transfer.getElementsByClassName('el-transfer-panel')[1].getElementsByClassName('el-transfer-panel__body')[0]
                const rightEl = rightPanel.getElementsByClassName('el-transfer-panel__list')[0]
                Sortable.create(rightEl, {
                    animation: 100,
                    onEnd: (evt) => {
                        const { oldIndex, newIndex } = evt
                        const temp = this.columnsValue[oldIndex]
                        if (!temp || temp === 'undefined') {
                            return
                        }
                        for (var i = 0; i < this.columnsValue.length; i++) {
                            if (this.columnsValue[i] === '' || this.columnsValue[i] === null || typeof (this.columnsValue[i]) === 'undefined') {
                                this.columnsValue.splice(i, 1)
                                i = i - 1
                            }
                        }
                        const arr_temp = [].concat(this.columnsValue) // 创建一个新的临时数组，用以操作后不变更原数组
                        arr_temp.splice(newIndex, 0, arr_temp.splice(oldIndex, 1)[0]) // 在b位置插入从a位置截取的元素
                        this.columnsValue = arr_temp
                    }
                })
                rightPanel.ondragover = (ev) => {
                    ev.preventDefault()
                }
                rightPanel.ondrop = (ev) => {
                    ev.preventDefault()
                    if (this.columnsValue.indexOf(this.draggingKey) === -1) {
                        this.columnsValue.push(this.draggingKey)
                    }
                }

            })
        },
        moreHander() {
            this.more = !this.more
        },
        handleSelectionChange(val) {
            // console.log('>>>>>>选择的数据：', val)
            this.checkedList = val;
            this.queryParams.workBillIds = val.map(({ workBillId }) => workBillId)
        },
        getRaise() {
            //作物
            this.raiseCropsCdOptions = [];
            this.queryParams.raiseCropsCd = '';
            getRaiseCrops({
                orgCode: this.queryParams.orgCode,
                year: ''
            }).then(response => {
                this.raiseCropsCdOptions = response.data
            })

        },
        dataFormat(row, column) {
            return selectDictLabelForRaiseCrops(
                this.raiseCropsCdOptions,
                row[column.property] ? row[column.property].toString() : ""
            );
        },
        columConfig() {
            this.columnCfgVisual = true;
        },
        columnValueChg(checked, key) {
            let _this = this;
            _this.queryParams.columnFiledList = [];
            this.column[key] = checked;
            this.delCookie("am_report");
            this.setCookie("am_report", JSON.stringify(this.column));
            let keys = Object.keys(this.column);
            console.log("~~~~" + this.column)
            keys.map((item) => {
                console.log(item);
                if (this.column[item] == true) {
                    console.log(item);
                    _this.queryParams.columnFiledList.push(item);
                }
            });
            console.log(_this.queryParams.columnFiledList);
            _this.queryParams.columnFiledList = Array.from(
                new Set(_this.queryParams.columnFiledList)
            );
            console.log(_this.queryParams.columnFiledList);
        },
        //组织机构下拉
        handleOrgChange({orgCode}) {
            this.queryParams.orgCode = orgCode
        },
        onClickPrint() {
            // if(!this.checkedList.length) return this.$message.error('请选择需要打印的数据');
            if (!this.checkedList.length) return this.getListAll();
            this.subsoilDataPageTmp = this.checkedList;
            //将数据转为二维数组，用于分页打印
            const pages = [];
            this.subsoilDataPageTmp.forEach((item, index) => {
                const page = Math.floor(index / 7);
                if (!pages[page]) {
                    pages[page] = [];
                }
                pages[page].push(item);
            });
            this.subsoilDataPrintPage = pages;
            setTimeout(() => {
                this.$refs.printButton.$el.click();
            }, 500);
        },
        onClickPrintMap() {
            if (this.checkedList.length == 0) {
                this.$message.error('请选择需要打印的数据');
                return
            }
            // else if (this.checkedList.length > 1) {
            //     this.$message.error('请选择一条数据');
            //     return
            // }
            else {
                this.checkedListAll = JSON.parse(JSON.stringify(this.checkedList))
                console.log('this.checkedListAll', this.checkedListAll)
                console.time()
                this.fullscreenLoading = true
                setTimeout(() => {
                    this.$refs['land' + this.endcount][0].init();
                }, 100);
            }
        },
        end() {
            this.endcount += 1
            console.log('this.endcount', this.endcount)
            if (this.$refs['land' + this.endcount]) {
                this.$refs['land' + this.endcount][0]?.init();
            }
            // if(this.endcount >= this.checkedListAll.length){
            //     this.$refs.printButtonMap.$el.click();
            // console.timeEnd()
            // }
        },
        toImage() {
            this.imagecount += 1
            if (this.imagecount >= this.checkedListAll.length) {
                setTimeout(() => {
                    this.fullscreenLoading = false
                    this.$refs.printButtonMap.$el.click();
                }, 1000);
                console.timeEnd()
            }
        },
        //打印获取所有数据(同步方法，打印要等待该方法完成)
        getListAll() {
            //打印全量筛选数据
            /**
             * bug 4259  打印全量数据修改
             * @type {boolean}
             */
            this.queryParams.needPagination = false;
            workbillList(this.queryParams).then((res) => {
                const response = res.data;
                this.subsoilDataPageTmp = response.records;
                //将数据转为二维数组，用于分页打印
                const pages = [];
                this.subsoilDataPageTmp.forEach((item, index) => {
                    const page = Math.floor(index / 7);
                    if (!pages[page]) {
                        pages[page] = [];
                    }
                    pages[page].push(item);
                });
                this.subsoilDataPrintPage = pages;
                setTimeout(() => {
                    this.$refs.printButton.$el.click();
                }, 1500);
            });

            //该方法打印当前页，打印全量请打开上方注释，注释此代码
            /*const pages = []
            this.subsoilData.forEach((item, index) => {
                    const page = Math.floor(index / 20)
                    if (!pages[page]) {
                        pages[page] = []
                    }
                    pages[page].push(item)
                })
            this.subsoilDataPrintPage=pages
            this.$refs.printButton.$el.click()*/
        },
        /** 查询表集合 */
        getList() {
            if (this.queryParams.year && this.queryParams.startTime && this.queryParams.endTime) {
                const year = this.queryParams.startTime.split("-")[0];
                const endyear = this.queryParams.endTime.split("-")[0];
                if (year != this.queryParams.year || endyear != this.queryParams.year) {
                    this.$message.error('不可选择超出选择年份的日期');
                    return
                }
            }
            this.queryParams.needPagination = true;
            const params = { ...this.queryParams, workBillIds: [], }
            workbillList(params).then((res) => {
                const response = res.data;
                this.subsoilData = response.records;
                this.total = response.total;
            });
        },
        handleQuery() {
            this.queryParams.page = 1;
            // this.queryParams.rows = 10;
            this.getList();
        },

        doPrint() { },
        /** 重置按钮操作 */
        resetQuery() {
            this.dateTime = null;
            this.queryParams = {
                driverName: null,
                raiseCropsCd: null,
                prodProcessCode: null,
                linkCode: null,
                auditType: "1",
                startTime: null,
                endTime: null,
                orgCode: useUserStore().currentOrgCode,
                amOwnerName: null,
                plotName: null,
                licenseNo: null,
                page: 1,
                rows: 10,
                workBillIds: [],
                year: String(new Date().getFullYear()),
                familyFarmLeaderName: null,
                qualSignName: null,
                amOwnerName: null,
            };
            this.getList();
        },
        getCookie(name) {
            let arr,
                reg = new RegExp("(^| )" + name + "=([^;]*)(;|$)");
            if ((arr = document.cookie.match(reg))) return arr[2];
            else return null;
        },
        setCookie(c_name, value, expiredays) {
            let exdate = new Date();
            exdate.setDate(exdate.getDate() + expiredays);
            document.cookie =
                c_name +
                "=" +
                escape(value) +
                (expiredays == null ? "" : ";expires=" + exdate.toGMTString());
        },
        delCookie(name) {
            let exp = new Date();
            exp.setTime(exp.getTime() - 1);
            let cval = this.getCookie(name);
            if (cval != null)
                document.cookie =
                    name + "=" + cval + ";expires=" + exp.toGMTString();
        },
    },
};
</script>

<style lang="scss" >
// @import '@/views/systemagriculturalmachineryv2/assets/styles/index.scss';
.auto-line-height :deep(.el-form-item__label) {
    line-height: 1.2 !important;
}

.dialog-tab {
    font-weight: bold;
    height: 40px;
    background: #e6f0fc;
    line-height: 40px;
    margin: 20px 0;
    padding: 0 15px;
}

.print :deep(.el-table__fixed),
.print :deep(.el-table__fixed-right) {
    height: 535px !important;
}

.print :deep(table) {
    table-layout: auto !important;
}

.print :deep(.el-table__header-wrapper .el-table__header) {
    width: 100% !important;
}

.print :deep(.el-table__body-wrapper .el-table__body) {
    width: 100% !important;
}

@media print {
    #box {
        height: 100%;
    }
}

.con-search-form .el-select,
.con-search-form .el-date-editor {
    width: 100% !important;
}

:deep(.el-transfer-panel) {
    width: 250px;
}

:deep(.el-transfer-panel__item ){
    margin-right: 0 !important;
}

.el-transfer>.el-transfer-panel:first-child .el-icon-rank {
    display: none !important;
}

.el-transfer>.el-transfer-panel:first-child .drag {
    cursor: pointer !important;
}

.rotate-animation {
  animation: spin 2s linear infinite;
}
</style>
