<template>
    <el-dialog
        class="map-plot-dlg"
        title="农机调度-作业任务派发"
        append-to-body
        @close="closeHandler"
        v-model="showDlg"
        :width="580"
        :modal="false"
        :close-on-click-modal="false"
        modal-class="dialog_class"
        destroy-on-close
    >
        <div class="map-plot-dlg-wrapper">
            <el-tabs v-model="chartName" @tab-click="handleClick" class="am-car-bg">
                <el-tab-pane :label="dlgLabel" name="firstChart">
                    <div class="work-dispatch-dia" style="height: 55vh;">
                        <div class="am-car-title">
                            <div class="border-l"></div>
                            作业农机
                        </div>
                        <div>
                            <div v-for="(item, index) in planDataInfo.planMachineInfoList" :key="index" class="am-car-card">
                                <el-icon v-show="item.amMachineWorkSubtask === null || item.amMachineWorkSubtask.workStatus === '0' || item.amMachineWorkSubtask.workStatus === '-1'" class="el-icon-delete-solid" @click="deleteDriverDistribute(item)"><Delete /></el-icon>
                                <!-- <i v-show="item.amMachineWorkSubtask === null || item.amMachineWorkSubtask.workStatus === '0' || item.amMachineWorkSubtask.workStatus === '-1'" class="el-icon-delete-solid" @click="deleteDriverDistribute(item)"></i> -->
                                <!-- <div class="successicon" v-show="!!(item.amMachineWorkSubtask)">
                                    <i class="el-icon-circle-check"></i>
                                </div> -->
                                <img :src="al" class="successicon" v-show="!!(item.amMachineWorkSubtask)" alt="">
                                <div class="flex-row align-center justify-between border-b">
                                    <div class="flex-row align-center">
                                        <div class="amTypeName3">
                                            {{ item.amTypeName3 }}
                                        </div>
                                        <div class="licenseNo">
                                            {{ item.licenseNo }}
                                        </div>
                                    </div>
                                    <div class="text">
                                        {{ item.amModelName }}
                                    </div>
                                </div>
                                <!-- <div class="am-car-num">
                                    <span><i class="custom-icon custom-icon-nongjinongju"></i>{{ item.licenseNo }}</span>
                                    <span>{{ item.amTypeName3 }}    {{ item.amModelName }}</span>
                                </div> -->
                                <div class="am-car-driver">
                                    <span style="letter-spacing: 14px" class="lable">机主</span>
                                    <div class="text" style="width: 230px;display: flex;justify-content: space-between;flex-shrink:0">
                                        <span style="width: 60px;">
                                            {{ !!item.ownerNameAndPhone ? item.ownerNameAndPhone.amOwnerName : '' }}
                                        </span>
                                        <span style="flex: 1;text-align: right;">
                                            {{ !!item.ownerNameAndPhone ? item.ownerNameAndPhone.contactNum : '' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="am-car-driver">
                                    <span class="lable">驾驶员</span>
                                    <div class="text" v-if="!!item.amMachineWorkSubtask && item.amMachineWorkSubtask.driverName" style="width: 230px;flex-shrink:0;">
                                        <div v-for="(driver, index) in getDriverNameList(item.amMachineWorkSubtask.driverName)" :key="index" style="width: 230px;display: flex;justify-content: space-between;flex-shrink:0">
                                            <span style="width: 60px;">
                                                {{ driver.split(" ")[0] }}
                                             </span>
                                            <span style="flex: 1;text-align: right;">
                                                {{ driver.split(" ")[1] }}
                                             </span>
                                        </div>
                                    </div>
                                    <div v-else>
                                        <div v-for="(driver, index) in item.driverNameAndPhoneList" :key="index" class="text" style="width: 230px;display: flex;justify-content: space-between;flex-shrink:0">
                                            <span style="width: 60px;">
                                                {{ driver.driverName }}
                                             </span>
                                            <span style="flex: 1;text-align: right;">
                                                {{ driver.phoneNo }}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="am-car-driver">
                                    <span class="lable">分配面积(亩)</span>
                                    <div class="area-border text" :class="{'readonly':!!(item.amMachineWorkSubtask && item.amMachineWorkSubtask.workStatus === '4' )}">
                                        <el-input
                                            :readonly="!!(item.amMachineWorkSubtask && item.amMachineWorkSubtask.workStatus === '4' )"
                                            v-model="item.allocatedArea"
                                            placeholder="输入分配面积"
                                            class="text"
                                        >
                                        </el-input>
                                    </div>
                                </div>
                                <div class="am-car-driver">
                                    <span class="lable">作业单价(元/亩)</span>
                                    <div class="area-border text" :class="{'readonly':!!(item.amMachineWorkSubtask && item.amMachineWorkSubtask.workStatus === '4' )}">
                                        <el-input
                                            :readonly="!!(item.amMachineWorkSubtask && item.amMachineWorkSubtask.workStatus === '4' )"
                                            v-model="item.workUnitCost"
                                            placeholder="输入作业单价"
                                            class="text"
                                        />
                                        <!-- <el-select v-model="search_statYear" placeholder="请选择作业单价">
                                                <el-option v-for="dict in yearNoOptions" :label="dict.name" :key="dict.code"
                                                                        :value="dict.code" />
                                        </el-select> -->
                                        <!-- <span class="unit">元/亩</span> -->
                                    </div>
                                </div>
                                <div class="am-car-driver" style="padding-top: 0;">
                                    <span class="lable">家庭农场场长</span>
                                    <div class="text"  :title="formatFarmLeaderList(item.familyFarmLeaderList)" :class="{'readonly':!!(item.amMachineWorkSubtask && item.amMachineWorkSubtask.workStatus === '4' )}">
                                        <div v-if="item.familyFarmLeaderListold" class="chart-select-top">
                                            <el-select multiple value-key="familyFarmLeaderIdNumber" v-model="item.familyFarmLeaderList" placeholder="请选择家庭农场场长" clearable :disabled="!!(item.amMachineWorkSubtask && item.amMachineWorkSubtask.workStatus === '4' )">
                                                <el-option v-for="dict in item.familyFarmLeaderListold" :key="dict.familyFarmLeaderIdNumber" :label="dict.familyFarmLeaderName" :value="dict">
                                                    <span style="float: left">{{ dict.familyFarmLeaderName }}</span>
                                                    <span style="float: right; color: #8492a6; font-size: 13px">{{ dict.familyFarmLeaderPhone }}</span>
                                                </el-option>
                                            </el-select>
                                        </div>
                                        <div v-else class="chart-select-top">
                                            <el-select multiple value-key="familyFarmLeaderIdNumber" v-model="item.familyFarmLeaderList" placeholder="请选择家庭农场场长" clearable :disabled="!!(item.amMachineWorkSubtask && item.amMachineWorkSubtask.workStatus === '4' )">
                                                <el-option v-for="dict in familyFarmLeaderList" :key="dict.familyFarmLeaderIdNumber" :label="dict.familyFarmLeaderName" :value="dict">
                                                    <span style="float: left">{{ dict.familyFarmLeaderName }}</span>
                                                    <span style="float: right; color: #8492a6; font-size: 13px">{{ dict.familyFarmLeaderPhone }}</span>
                                                </el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                </div>
                                <div class="am-car-driver" style="padding-top: 0;">
                                    <span class="lable">终端选择</span>
                                    <div class="text" :class="{'readonly':!!( item.amMachineWorkSubtask && (item.amMachineWorkSubtask.workStatus == '2'||item.amMachineWorkSubtask.workStatus == '3'||item.amMachineWorkSubtask.workStatus == '4'||item.amMachineWorkSubtask.workStatus == '5') )}">
                                        <div class="chart-select-top">
                                            <el-select v-model="item.deviceCode" @change="registerChange($event,item)" placeholder="请选择终端" clearable :disabled="!!( item.amMachineWorkSubtask && (item.amMachineWorkSubtask.workStatus == '2'||item.amMachineWorkSubtask.workStatus == '3'||item.amMachineWorkSubtask.workStatus == '4'||item.amMachineWorkSubtask.workStatus == '5') )">
                                                <el-option v-for="dict in item.registerImports" :key="dict.dsn" :label="dict.dsn + ' ' + (dict.facName||'')" :value="dict.dsn">
                                                </el-option>
                                            </el-select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="am-car-title">
                            <div class="border-l"></div>
                            作业地块
                        </div>
                        <el-table :data="planDataInfo.landResourceInfoList" header-align="center" style="width: 510px">
                            <el-table-column label="地块编号" prop="plotNo" align="center"></el-table-column>
                            <el-table-column label="地块名称" align="center">
                                 <template #default="scope">
                                    {{ scope.row.plotName }}<br/>
                                    {{ scope.row.pastPlotName == null ? '' : `(${scope.row.pastPlotName})` }}
                                    {{ `${scope.row.plotArea}亩` }}
                                </template>
                            </el-table-column>

                            <el-table-column label="种植作物" prop="raiseCropsNm" align="center"></el-table-column>
                            <!--<el-table-column label="地块面积(亩)" prop="plotArea"></el-table-column>-->
                            <el-table-column label="分配面积(亩)" prop="allocatedArea" width="95" align="center">
                                 <template #default="scope">
                                    {{ scope.row.allocatedArea }}<br/>
                                    <span style="color: #FF8400"> {{ scope.row.unAllocatedArea == null ? '' : `(未分配${scope.row.unAllocatedArea})` }}</span>
                                </template>
                            </el-table-column>
                            <el-table-column label="作业面积(亩)" prop="workArea" align="center"></el-table-column>
                            <el-table-column label="合格面积(亩)" prop="passArea" align="center"></el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="地块作业环节完成情况" name="secondChart" v-hasPermi="['workDispatch:statistic']">
                    <div class="chart-select">
                        <el-select v-model="chartProcess" placeholder="请选择作业环节" clearable style="width: 140px;">
                            <el-option v-for="dict in prodProcessOptions" :key="dict.code" :label="dict.name" :value="dict.code"/>
                        </el-select>
                    </div>
                    <div style="width: 530px; height: 43vh; overflow: auto">
                        <div :class="!!item.plotArea ? 'chart-box' : 'chart-box-none'" v-for="(item, index) in chartProcessList" :key="index">
                            <div :class="['chart-name',item.linkCode == linkCode ? 'is-red' : '',]">
                                {{ item.linkName }}
                            </div>
                            <div class="chart-bar">
                                <div class="chart-bg">
                                    <div :style="{width:parseInt(item.ratio) >= 100? '100%': item.ratio,}"></div>
                                </div>
                                <div class="chart-dia">
                                    <div class="dia-name">
                                        {{ item.linkName }}
                                    </div>
                                    <div class="dia-work">
                                        作业面积{{ item.workArea }}亩
                                    </div>
                                    <div class="dia-plot">
                                        地块面积{{ item.plotArea }}亩
                                    </div>
                                    <div class="dia-process">
                                        作业进度{{ item.ratio }}
                                    </div>
                                </div>
                            </div>
                            <div class="chart-value">{{ item.ratio }}</div>
                            <div class="chart-car">
                                <div v-for="itemC in item.machList" :key="itemC.licenseNo">
                                    {{ itemC.licenseNo }}
                                    <div class="chart-dia">
                                        <div class="dia-name">
                                            {{ itemC.amOwnerName }}
                                            {{ itemC.licenseNo }}
                                        </div>
                                        <div class="dia-distribute">
                                            分配面积{{ itemC.allocatedArea }}亩
                                        </div>
                                        <div class="dia-work">
                                            作业面积{{ itemC.workArea }}亩
                                        </div>
                                        <div class="dia-plot">
                                            地块面积{{ itemC.plotArea }}亩
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="chart-line" v-if="chartProcessList.length > 0"></div>
                        <div v-else style="color: #b1b1b1;margin-top: 100px;text-align: center;">
                            暂无数据
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
        <template #footer class="dialog-footer" style="text-align: right;margin-top:20px;padding-right: 24px;">
                <el-button @click="showDlg = false" class="chartName">
                    {{ chartName == 'firstChart' ? '取 消' : '关 闭' }}
                </el-button>
                <el-button
                    v-hasPermi="['workDispatch:editDistribute']"
                    :loading="sentEditLoading"
                    type="primary"
                    @click="sentEditDistribute()"
                    v-if="isShowDispatchBtn"
                >派 发
                </el-button>
                <!-- <el-button
                    v-hasPermi="['worktask:update']"
                    :loading="sentEditLoading"
                    type="success"
                    v-if="plotStatus == 3 && chartName == 'firstChart'"
                    @click="forceFinish()"
                >强制完成
                </el-button> -->
            </template>
    </el-dialog>
</template>

<script>
import al from "@/views/systemagriculturalmachineryv2/assets/images/al.png"
import useWorkDispatchStore from "@/store/modules/workDispatch"
import { mapWritableState, mapActions } from 'pinia'
import {editPlanForMap, familyFarmLeader, forceFinish, queryMachineListByPlotNo, statistic} from '@/api/systemagriculturalmachineryv2/dispatch/workDispatch'
import {isEmpty} from "ol/obj";
import { getDicts,getRaiseCrops } from "@/api/systemagriculturalmachineryv2/dict";
import { selectDictLabelForRaiseCrops,selectDictLabel } from "@/views/systemagriculturalmachineryv2/utils/cop";
export default {
    name: 'mapPlotDlg',
    data() {

        return {
            al,
            chartName: 'firstChart',
            planDataInfo: {},
            chartProcess: '', //地块作业环节完成情况-选择流程
            chartProcessList: [],
            prodProcessOptions: [],

            isPassArea: true, //判断是否有合格面积，决定是否允许派发
            sentEditLoading: false,
            showDlg: false,
            raiseCropsOptions: [],

            showWorkingNew: false,
            workingNewState: null,
            familyFarmLeaderList: [],
        }
    },
    props: {
        plotNo: {
            type: String,
            default: '',
        },
        plotStatus: {
            type: String,
            default: '',
        },
        properties: {
            type: Object,
            default: '',
        },
    },
    computed: {
         ...mapWritableState(useWorkDispatchStore, ['year','orgCode','raiseCropsCd','prodProcessCode','linkCode','linkId','yearNow','planDataInfoObj','machineCheck','linkList','showPlotDlg','showUnDispatchPlotDlg']),
        dlgLabel() {
            if (
                this.plotStatus == '0' ||
                this.plotStatus == '-1' ||
                this.plotStatus == null
            ) {
                return '农机调度-作业任务派发'
            } else if (
                this.plotStatus == '1' ||
                this.plotStatus == '2' ||
                this.plotStatus == '3' ||
                this.plotStatus == '5'
            ) {
                return '正在作业'
            } else if (this.plotStatus == '4') {
                return '作业完成'
            }
        },
        isShowDispatchBtn() {
            return this.yearNow == this.year && this.chartName == 'firstChart'
        },
    },
    watch: {
        plotNo(val) {
            if (!val) {
                return false
            }
            this.getMachineListByPlotNo()
            if (this.chartName === 'secondChart') {
                this.getSuccessStatus()
            }
        },
        //监听地块作业环节完成情况
        chartProcess: function (val, oldVal) {
            if (val != oldVal) {
                this.getSuccessStatus()
            }
        },
        planDataInfoObj(val) {
            this.planDataInfo = val
        },
        showDlg(val) {
            this.SET_showPlotDlg(val)
            if (!val) {
                this.chartProcess = this.prodProcessCode
                this.chartName = 'firstChart' // 默认展示第一tab
            }
        },
        showPlotDlg(val) {
            this.showDlg = val
            if (val) {
                this.getMachineListByPlotNo()
            }
        },

        linkCode() {
            this.showWorkingNew = false
        },
        showWorkingNew(val) {
            this.SET_showWorkingNewGlobal(val)
        },
    },
    created() {
        //生产流程
        getDicts('prod_process').then((response) => {
            this.prodProcessOptions = response.data
        })
        // 作物
        getRaiseCrops({}).then((response) => {
            this.raiseCropsOptions = response.data
        })
    },
    methods: {
        ...mapActions(useWorkDispatchStore, ['SET_amGradeIds','SET_year','SET_orgCode','SET_raiseCropsCd','SET_prodProcessCode','SET_linkCode','SET_linkId','SET_planDataInfoObj','SET_showPlotDlg','SET_machineCheck','SET_disabledArr','SET_showWorkingNewGlobal']),
        registerChange(e,item){
            let obj = item.registerImports.find(it=>{
                return it.dsn == item.deviceCode
            })
            if(obj){
                item.equipmentFact = obj.facCode
            }else{
                item.equipmentFact = null
            }
        },
        formatFarmLeaderList(farmLeaderLists){
            if(farmLeaderLists&&farmLeaderLists.length>0){
                let arr = []
                farmLeaderLists.forEach(item=>{
                    arr.push(item.familyFarmLeaderName +" "+ item.familyFarmLeaderPhone)
                })
                return arr.join(",")
            }else{
                return ""
            }
            
        },
        getFamilyFarmLeader() {
            return familyFarmLeader({plotName: this.plotNo}).then(res => {
                this.familyFarmLeaderList = res.data
            })
        },
        handleClick(tab, event) {
            this.chartName = tab.paneName
            if (tab.paneName == 'secondChart') {
                this.chartProcess = this.prodProcessCode
                this.getSuccessStatus()
            }
        },
        // 地块作业环节完成情况
        getSuccessStatus() {
            let params = {
                year: this.year,
                orgCode: this.planDataInfo.landResourceInfoList[0]?.orgCode,
                raiseCropsCd: this.raiseCropsCd,
                prodProcessCode: this.chartProcess,
                plotNo: this.planDataInfo.landResourceInfoList[0]?.plotNo,
                linkCode: this.linkCode,
            }
            statistic(params).then((response) => {
                this.chartProcessList = response.data.map((item) => {
                    return {
                        ...item,
                        machList: !!item.machList ? item.machList : [],
                    }
                })
            })
        },
        // 查询地块关联农机
        async getMachineListByPlotNo() {
            await this.getFamilyFarmLeader()
            // 未分配
            if (this.plotStatus === '0' || this.plotStatus === '-1') {
                this.setDispatchData(this.properties)
            }

            // 部分完成,已分配,作业完成
            if (
                this.plotStatus === '1' ||
                this.plotStatus === '2' ||
                this.plotStatus === '3' ||
                this.plotStatus === '4' ||
                this.plotStatus === '5'
            ) {
                let params = {
                    raiseCropsCd: this.raiseCropsCd,
                    prodProcessCode: this.prodProcessCode,
                    linkCode: this.linkCode,
                    plotNo: this.plotNo,
                    year: this.year,
                    orgCode: this.orgCode,
                }
                queryMachineListByPlotNo(params).then((response) => {
                    this.SET_machineCheck([]) //清空作业面积分配选中状态
                    this.planDataInfo = response.data
                    this.planDataInfo.planMachineInfoList = response.data.planMachineInfoList.filter(
                        (item) => !!item.machineId
                    )
                    this.planDataInfo.isDispatched = true
                    this.showWorkingNew = true
                    //正在作业，部分作业，作业完成，已有农机，在左侧列表禁止勾选
                    //允许已作业完成的农机再次派发
                    let _disabledArr = this.planDataInfo.planMachineInfoList.map((item) => {
                        return (item.amMachineWorkSubtask.workStatus !== "4") ? item.machineId : null;
                    })

                    //规模家庭农场场长处理，遍历planMachineInfoList，压入familyFarmLeaderList，如果familyFarmLeaderIdNumber在familyFarmLeaderList中不存在，在familyFarmLeaderList添加familyFarmLeaderName和familyFarmLeaderPhone
                    this.planDataInfo.planMachineInfoList.forEach(item => {
                        item.familyFarmLeaderListold = [...this.familyFarmLeaderList];
                        if (item.familyFarmLeaderList && item.familyFarmLeaderList.length > 0) {
                            item.familyFarmLeaderList.forEach(it => {
                                let o = item.familyFarmLeaderListold.find(i => {
                                    return i.familyFarmLeaderIdNumber == it.familyFarmLeaderIdNumber;
                                })
                                if (!o) {
                                    item.familyFarmLeaderListold.push({
                                        familyFarmLeaderIdNumber: it.familyFarmLeaderIdNumber,
                                        familyFarmLeader: it.familyFarmLeaderName + ' ' + it.familyFarmLeaderPhone,
                                        familyFarmLeaderName: it.familyFarmLeaderName,
                                        familyFarmLeaderPhone: it.familyFarmLeaderPhone
                                    })
                                }
                            })
                        }
                    })

                    this.SET_disabledArr(_disabledArr)
                    this.SET_planDataInfoObj(this.planDataInfo)
                })
            }
        },
        setDispatchData(item) {
            console.log(this.properties, 'properties')

            this.SET_machineCheck([]) //清空作业面积分配选中状态

            this.planDataInfo = {
                landResourceInfoList: [
                    {
                        orgCode:
                            item.bl_station_no ||
                            item.blStationNo ||
                            item.bl_precinct_no ||
                            item.blPrecinctNo ||
                            item.bl_farm_no ||
                            item.blFarmNo ||
                            item.bl_branch_com_no ||
                            item.blBranchComNo,
                        landType: item.land_type || item.landType,
                        plotArea: item.contr_area || item.contrArea,
                        allocatedArea:
                            item.allocated_area || item.allocatedArea,
                        plotName:`${item.plotName}${item.pastPlotName||''}`,
                            // (item.plot_name || item.plotName) +
                            // (`${item.pastPlotName == null ? '' : '(' + item.pastPlotName + ')'}` || `${item.past_plot_name == null ? '' : '(' + item.past_plot_name + ')'}`),
                        plotNo: item.plot_no || item.plotNo,
                        raiseCropsCd: item.crop_type || item.cropType,
                        workTaskId: item.work_task_id || item.workTaskId,
                        raiseCropsNm: selectDictLabelForRaiseCrops(
                            this.raiseCropsOptions,
                            item.crop_type || item.cropType
                        ),
                    },
                ],
                planMachineInfoList: [],
                linkCode: this.linkCode,
                linkName: this.filterLink(this.linkCode),
                prodProcessCode: this.prodProcessCode,
                prodProcessName: selectDictLabel(
                    this.prodProcessOptions,
                    this.prodProcessCode
                ),
                startTime: '',
                endTime: '',
                isDispatched: false,
            }

            this.showWorkingNew = false

            //判断是否有合格面积，决定是否允许派发
            this.isPassArea =
                !!this.planDataInfo.landResourceInfoList[0].passArea

            this.SET_planDataInfoObj(this.planDataInfo)
        },
        // 任务删除农机-作业面积同步选中状态
        deleteDriverDistribute(driver) {
            this.planDataInfo.planMachineInfoList = this.planDataInfo.planMachineInfoList.filter((item) => item != driver)

            let _machineCheck = [...this.machineCheck]
            _machineCheck = _machineCheck.filter((item) => item.machineId != driver.machineId)
            this.SET_machineCheck(_machineCheck)

            // 分配面积
            if (this.planDataInfo.planMachineInfoList.length > 0) {
                //拆分为旧数据（已分配）和新数据（本次自动分配）
                //已分配
                let oldList = this.planDataInfo.planMachineInfoList.filter((item) => !isEmpty(item.amMachineWorkSubtask));
                let newListSize = this.planDataInfo.planMachineInfoList.length - oldList.length
                //未分配
                let newList = this.planDataInfo.planMachineInfoList
                    .filter((item) => isEmpty(item.amMachineWorkSubtask))
                    .map(
                        (info) => {
                            let allocatedArea = newListSize > 0 ? (Math.floor((this.planDataInfo.landResourceInfoList[0].unAllocatedArea / newListSize) * 10000) / 10000) : 0
                            return {...info, allocatedArea: allocatedArea.toFixed(4),}
                        }
                    )
                console.log(oldList, '已分配')
                console.log(newList, '未分配')

                //除最后一个数据，其余数据之和
                let numArr = newList.filter((x, index, self) => {
                    return index < newList.length - 1
                })
                let num = numArr.reduce((pre, cur) => {
                    return pre + cur.allocatedArea
                }, 0)
                //最后一个农机分配的面积
                let lastAllocatedArea = (this.planDataInfo.landResourceInfoList[0].unAllocatedArea.toFixed(4) - num).toFixed(4)
                if (newList.length > 0) {
                    newList[newList.length - 1].allocatedArea = lastAllocatedArea
                }
                this.planDataInfo.planMachineInfoList = [...oldList, ...newList]
            }
        },

        // 派发
        sentEditDistribute() {
            let _this = this
            if (this.planDataInfo.planMachineInfoList.length === 0) {
                this.$modal.msgError(`请选择农机`)
                return false
            }

            //对list进行校验
            let addValidate = this.planDataInfo.planMachineInfoList.every(
                function (item, index) {
                    return (
                        !!item.allocatedArea && Number(item.allocatedArea) > 0
                    )
                }
            )
            if (!addValidate) {
                this.$modal.msgError('分配面积为大于0的数值')
                return false
            }
            let addValidate1 = this.planDataInfo.planMachineInfoList.reduce(
                function (pre, cur) {
                    let allocatedTemp = Number(pre.allocatedArea) + Number(cur.allocatedArea);
                    //计算结果保留4位小数，与地块面积默认精度保持一致
                    return allocatedTemp.toFixed(4);
                }
            )
            if (addValidate1 - this.planDataInfo.landResourceInfoList[0].plotArea > 0) {
                this.$modal.msgError('分配面积总和大于地块面积')
                return false
            }
            var validateNum = (value) => {
                var reg = /^[0-9]+(\.?[0-9]{1,2})?$/;
                if (value === null || value === "" || typeof value == "undefined") {
                    return ''
                } else if (value.toString().split(".")[0].length > 7) {
                    return "作业单价不得超过100w，请确认"
                } else if (reg.test(value)) {
                    if (value.toString() === '0') {
                        return "作业单价请输入正数(最多带两位小数)"
                    } else {
                        return ''
                    }
                } else {
                    return "作业单价请输入正数(最多带两位小数)"
                }
            };
            for (let index = 0; index < this.planDataInfo.planMachineInfoList.length; index++) {
                const item = this.planDataInfo.planMachineInfoList[index];
                let val = validateNum(item.workUnitCost)
                if (val) {
                    return this.$modal.msgError(val)
                }
            }
            this.planDataInfo.planMachineInfoList.forEach(item => {
                delete item.familyFarmLeaderListold
                delete item.registerImports
                let arr = []
                if (item.familyFarmLeaderList && item.familyFarmLeaderList.length > 0) {
                    item.familyFarmLeaderList.forEach(it => {
                        arr.push({
                            familyFarmLeaderIdNumber: it.familyFarmLeaderIdNumber,
                            familyFarmLeaderName: it.familyFarmLeaderName,
                            familyFarmLeaderPhone: it.familyFarmLeaderPhone
                        })
                    })
                    item.familyFarmLeaderList = arr
                }
            })
            this.sentEditLoading = true
            this.planDataInfo.year = this.year
            this.planDataInfo.orgCode = this.orgCode
            let plot = this.planDataInfo.landResourceInfoList[0]
            this.planDataInfo.orgCode = plot.orgCode
            this.planDataInfo.linkCode = this.linkCode
            this.planDataInfo.linkName = this.filterLink(this.linkCode)
            this.planDataInfo.prodProcessCode = this.prodProcessCode
            this.planDataInfo.prodProcessName = selectDictLabel(
                this.prodProcessOptions,
                this.prodProcessCode
            )
            this.planDataInfo.startTime = ''
            this.planDataInfo.endTime = ''

            delete this.planDataInfo.isDispatched
            editPlanForMap(this.planDataInfo)
                .then((res) => {
                    this.sentEditDistributeCallback()
                })
                .catch((e) => {
                    _this.sentEditLoading = false
                })
        },

        // 派发回调
        sentEditDistributeCallback() {
           this.$modal.msgSuccess('派发成功')
            this.showDlg = false
            this.sentEditLoading = false
            this.showWorkingNew = false

            // this.$emit('getAmplot', true)
            this.$emit('refresh')

            // 如果打开了未派发地块弹窗 刷新数据
            if (this.showUnDispatchPlotDlg) {
                this.$emit('getUnDispatchPlotData')
            }
        },

        closeHandler() {
            this.SET_disabledArr([])
            this.SET_machineCheck([]) //清空作业面积分配选中状态
        },

        filterLink(value) {
            if (!value) return ''
            var actions = []
            let datas = this.linkList
            Object.keys(datas).some((key) => {
                if (datas[key].linkCode == '' + value) {
                    actions.push(datas[key].linkName)
                    return true
                }
            })
            return actions.join('')
        },

        //强制完成
        forceFinish() {
            const data = {
                workTaskId:
                this.planDataInfo.landResourceInfoList[0].workTaskId,
                workStatus: '4',
            }
            let _this = this
            this.$confirm('该地块状态强制完成，是否确认?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(function () {
                forceFinish(data).then((response) => {
                    _this.forceFinishCallback()
                })
            })
        },

        //强制完成后回调
        forceFinishCallback() {
           this.$modal.msgSuccess('修改成功')
            this.showDlg = false
            this.sentEditLoading = false
            this.showWorkingNew = false

            this.$emit('getAmplot')
        },
        getDriverNameList(str) {
            if (!!str) {
                return str.split(',')
            } else {
                return []
            }
        },
    }
    ,
    deactivated() {
        this.showDlg = false
    }
}
</script>

<style lang="scss" scoped>
.map-plot-dlg-wrapper {
    /*农机调度-作业任务派发*/
    .work-dispatch-dia {
        color: #ffffff;
        overflow-x: hidden;
        overflow-y: auto;

        .am-car-title {
            position: relative;
            margin: 20px 0 12px 0;
            font-weight: bold;
            font-size: 16px;
            padding-left: 8px;
        }

        .am-car-card {
            padding: 20px 0 12px 24px;
            width: 510px;
            background: rgba(38, 56, 100, 0.3);
            border-radius: 5px 5px 5px 5px;
            border: 1px solid rgba(58, 89, 166, 0.2);
            margin-bottom: 24px;
            position: relative;

            .successicon {
                position: absolute;
                right: 0px;
                top: 0px;
                width: 64px;
                height: 64px;
            }

            &:hover {
                // box-shadow: 0 2px 10px 0px #a3a3a3;
            }

            .el-icon-delete-solid {
                position: absolute;
                right: 15px;
                top: 25px;
                cursor: pointer;
                font-size: 16px;
                color: #bf4c00;

                &:hover {
                    text-decoration: underline;
                    color: #ff5a00;
                }
            }

            .el-icon-map-location,
            .el-icon-location {
                font-size: 22px;
                font-weight: bold;
                cursor: pointer;
                color: #00dbd5;

                &:hover {
                    text-decoration: underline;
                    color: #00fff9;
                }
            }

            .am-car-num {
                display: flex;

                i {
                    font-size: 18px;
                    margin-right: 5px;
                }

                span {
                    display: block;
                }

                span:first-child {
                    width: 130px;
                    font-size: 16px;
                    font-weight: bold;
                }

                span:last-child {
                    text-align: left;
                    width: 194px;
                    padding-right: 34px;
                }
            }

            .am-dikuai-tit {
                font-size: 16px;
                font-weight: bold;

                i {
                    font-size: 18px;
                    margin-right: 5px;
                }
            }

            .am-dikuai-num {
                text-decoration: underline;

                i {
                    margin-right: 8px;
                }
            }

            .am-dikuai-text {
                width: 383px;
                display: flex;
                flex-wrap: wrap;

                div {
                    flex: auto;
                    display: block;
                    width: 183px;
                    padding-left: 24px;
                    margin-top: 8px;

                    &:nth-child(even) {
                        margin-right: 10px;
                        padding-left: 10px;
                    }
                }
            }

            .am-car-driver {
                display: flex;
                padding-top: 12px;
                align-items: center;
                padding-right: 24px;

                .area-border {
                    display: flex;
                    justify-content: flex-end;
                    align-items: center;

                    .el-input__inner {
                        width: 110px;
                        height: 25px;
                        padding: 0 5px;
                        text-align: right;
                        border: none;
                        border-bottom: 1px solid;
                        border-radius: 0;
                        background: transparent;
                    }
                }

                .area {
                    display: flex;

                    .el-input__inner {
                        width: 110px;
                        height: 25px;
                        padding: 0 5px;
                        text-align: right;
                        border: none;
                        border-radius: 0;
                        background: transparent;
                    }

                    &.bt-border .el-input__inner {
                        border-bottom: 1px solid;
                    }
                }

                // span {
                //     display: block;
                // }

                // & > span:first-child {
                //     font-size: 14px;
                //     font-weight: bold;
                //     flex: 1;
                // }

                // & > span:last-child {
                //     text-align: right;
                //     flex: 1;
                //     padding-right: 24px;
                // }

                // & > div {
                //     padding-right: 24px;
                //     text-align: right;
                // }
            }

            .am-car-company > span {
                display: block;
                padding-right: 34px;
                text-align: right;
            }
        }

        .work-no {
            span {
                font-size: 12px;
                float: right;
            }
        }

        .alarm-text {
            width: 100%;
            display: flex;
            flex-direction: row;
            justify-content: center;
            color: red;
        }

        .select-area {
            width: 100%;
            display: flex;
            flex-direction: row;
            justify-content: center;
            margin: 10px 0;
        }
    }
    :deep(.el-table--border .el-table__inner-wrapper:after){
        background-color: transparent;
    }
    :deep(.el-table--fit .el-table__inner-wrapper:before){
        background-color: transparent;
    }
    :deep(.el-table__border-left-patch){
        background-color: transparent;
    }
    .chart-select {
        margin: 20px 0 30px 0;
    }

    .chart-line {
        border-bottom: 1px solid #b1b1b1;
        margin: 0 30px 0 109px;
    }

    .chart-box,
    .chart-box-none {
        color: #ffffff;
        height: 60px;
        // line-height: 70px;
        display: flex;
        flex-direction: row;
        justify-content: left;
        position: relative;

        .chart-name {
            display: flex;
            justify-content: center;
            flex-direction: column;
            border-right: 1px solid #b1b1b1;
            width: 110px;

            &.is-red {
                color: #ff8181 !important;
            }
        }

        .chart-bar {
            width: 160px;
            display: flex;
            justify-content: center;
            flex-direction: column;
        }

        .chart-bg {
            width: 160px;
            background: #bbdefd;
            height: 16px;
            position: relative;

            div {
                position: absolute;
                left: 0;
                top: 0;
                height: 100%;
                background: #45aff5;
            }
        }

        .chart-value {
            display: flex;
            justify-content: center;
            flex-direction: column;
            width: 60px;
            text-align: center;
            font-size: 12px;
        }

        .chart-car {
            flex: 1;
            margin-left: 5px;
            font-size: 12px;
            line-height: 24px;
            word-break: break-all;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            align-content: center;

            & > div {
                position: relative;
                word-break: break-all;
                white-space: nowrap;
                // margin-top: 5px;
                height: 14px;
                line-height: 14px;
                cursor: pointer;
                margin-right: 8px;

                &:hover {
                    text-decoration: underline;
                }
            }
        }

        .chart-dia {
            position: absolute;
            left: 50%;
            top: 70px;
            z-index: 100;
            padding: 10px;
            min-width: 100px;
            border-radius: 2px;
            background: rgba(137, 137, 137);
            font-size: 14px;
            color: #ffffff;
            white-space: nowrap;
            display: none;

            .dia-work,
            .dia-plot,
            .dia-distribute {
                position: relative;
                text-indent: 15px;

                &:before {
                    position: absolute;
                    left: 0;
                    top: 5px;
                    display: block;
                    width: 10px;
                    height: 10px;
                    content: '';
                    background: #00afff;
                }
            }

            .dia-plot {
                &::before {
                    background-color: #bbdefd;
                }
            }

            .dia-distribute {
                &::before {
                    background-color: #0170c0;
                }
            }

            .dia-process {
                text-indent: 18px;
            }
        }
    }

    .chart-box-none {
        .chart-name {
            color: #b1b1b1;
        }

        .chart-bg {
            background: #828282;
        }
    }

    .chart-box .chart-bar:hover {
        background: rgba(137, 137, 137, 0.7);

        .chart-dia {
            display: block;
        }
    }

    .chart-box .chart-car > div:hover {
        background: rgba(137, 137, 137, 0.7);

        .chart-dia {
            display: block;
            right: 0;
            top: 25px;
            left: unset;

            div {
                line-height: 20px;
            }
        }
    }
}
:deep(.el-select .el-tag){
    margin-left: 0;
    margin-right: 4px;
}
.el-dialog__wrapper {
    width: 560px;
    position: absolute;
    left: 400px;
    top: -80px;
}
</style>

<style lang="scss">
.map-plot-dlg.el-dialog {
    margin-top: 15vh !important;
    background-color: rgba(0, 12, 40, 0.7);

    .el-dialog__body {
        padding: 0 4px 24px 24px;
    }

    .el-dialog__header {
        text-align: left;
        padding: 16px 24px 24px;

        .el-dialog__title,
        .el-dialog__headerbtn .el-dialog__close {
            color: #fff;
        }
    }
    .el-input__wrapper{
        line-height: 24px;
        height: 24px;
        border: none;
        // border-bottom: 1px solid #ffffff;
        border-radius: 0;
        padding: 0;
        background-color: transparent;
        color: #fff;
        text-align: left;
        box-shadow: none;
    }
    .area-border .el-input {
        width: 230px;
        float: right;

        .el-input__suffix {
            right: 0;
            top: -8px;
        }

        input {
            line-height: 24px;
            height: 24px;
            border: none;
            border-bottom: 1px solid #ffffff;
            border-radius: 0;
            padding: 0;
            background-color: transparent;
            color: #fff;
            text-align: left;
        }
    }

    .unit {
        padding-top: 2px;
        width: 38px;
        text-align: right;
        border-bottom: 1px solid #ffffff;
        height: 24px;
    }

    .readonly {
        .unit {
            border-bottom: none;
        }
    }

    .area-border.readonly .el-input {
        input {
            border-bottom: none !important;
        }
    }
    .area-border.readonly .el-input__wrapper{
         border-bottom: none !important;
    }
    .readonly .el-select__wrapper{
         border-bottom: none !important;
    }
    .area-border.readonly {
        border-bottom: none !important;
    }

    .chart-select .el-select {
        text-align: left;

        .el-input__suffix {
            right: 0;
        }

        input {
            border: 1px solid rgba(50, 109, 255, 0.5);
            background: transparent;
            padding: 0 5px;
            text-align: left;
            color: #fff;
        }
        .el-select__wrapper{
            border: 1px solid rgba(50, 109, 255, 0.5);
            background: transparent;
            padding: 0 5px;
            text-align: left;
            color: #fff;
            box-shadow: none;
        }
        .el-select__selected-item{
            color: #fff;
        }
    }

    .chart-select-top {
        position: relative;
        .el-select {
            text-align: left;

            .el-input__suffix {
                right: -6px;
            }
            .el-select__suffix{
                position: absolute;
                right: 0px;
            }
            .el-select__wrapper {
                border: none;
                border-bottom: 1px solid #ffffff;
                background: transparent;
                padding: 0 36px 0 0;
                text-align: left;
                color: #fff;
                border-radius: 0;
                width: 230px;
                box-shadow: none;
            }
            input {
                border: none;
                border-bottom: 1px solid #ffffff;
                background: transparent;
                padding: 0 36px 0 0;
                text-align: left;
                color: #fff;
                border-radius: 0;
                width: 230px;
            }
            .el-select__selected-item{
                color: #fff;
            }
            .is-transparent{
                color: #a3a5aa;
            }
            .el-select__selection.is-near {
                margin-left: 0px;
            }
        }
    }


    .el-tabs__item {
        color: #C2D3FF;
    }

    .el-tabs__item.is-active {
        color: #fff;
    }

    .el-tabs__nav-wrap::after {
        background-color: transparent;
    }

    .el-tabs__active-bar {
        background-color: #fff;
    }

    .custom-icon-nongjinongju {
        color: rgba(67, 175, 210, 0.9);
    }

    .el-table {
        background: transparent;

        .el-button {
            color: #409eff;
            font-weight: bold;
        }

        tr,
        td,
        th,
        .el-table__body-wrapper {
            background: transparent !important;
            color: #ffffff;
        }

        tr {
            td:nth-child(3),
            th:nth-child(3) {
                //border-right: none;
            }
        }

        .gutter {
            display: none;
        }

        .el-table__row:hover {
            tr,
            td,
            th {
                background: rgba(0, 29, 61, 0.85);
            }
        }

        .el-table__row.hover-row {
            tr,
            td,
            th {
                background: rgba(0, 29, 61, 0.85);
            }
        }

        .el-checkbox__inner {
            background: #001d3d;
        }
    }
    .el-table th.el-table__cell.is-leaf,
    .el-table td.el-table__cell {
        border: none;
        background: rgba(38, 56, 100, 0.5) !important;
        border-radius: 0px 0px 0px 0px !important;
    }

    .el-table--group,
    .el-table--border {
        border: none;
    }

    .el-table--group::after,
    .el-table--border::after {
        content: none;
    }

    .el-table::before {
        content: none;
    }

    .el-table .cell {
        color: #C9D8FF;
    }

    .el-table thead {
        background: rgba(38, 56, 100, 0.5) !important;
    }

    .el-input.is-disabled .el-input__inner {
        background-color: transparent !important;
        color: #ffffff !important;
        border: none !important;
    }
    .el-input.is-disabled .el-input__wrapper{
        background-color: transparent !important;
        color: #ffffff !important;
        border: none !important;
    }
    .el-input.is-disabled .el-select__wrapper{
        background-color: transparent !important;
        color: #ffffff !important;
        border: none !important;
    }
    .el-tag.el-tag--info{
        background-color: transparent;
        border:none;
        color: #fff;
        padding-left: 0;
        font-size: 14px;
        padding-right: 0;
        .el-tag__close{
            display: none;
        }
    }
    .am-car-card .el-tag--info+.el-tag--info .el-select__tags-text::before{
        content: ',';
    }
    .el-select .el-tag{
        margin-right: 0;
    }

}
</style>
<style lang="scss" scoped>
.flex-col {
    display: flex;
    flex-direction: column;
}

.flex-row {
    display: flex;
    flex-direction: row;
}

.justify-start {
    display: flex;
    justify-content: flex-start;
}

.justify-center {
    display: flex;
    justify-content: center;
}

.justify-end {
    display: flex;
    justify-content: flex-end;
}

.justify-evenly {
    display: flex;
    justify-content: space-evenly;
}

.justify-around {
    display: flex;
    justify-content: space-around;
}

.justify-between {
    display: flex;
    justify-content: space-between;
}

.align-start {
    display: flex;
    align-items: flex-start;
}

.align-center {
    display: flex;
    align-items: center;
}

.align-end {
    display: flex;
    align-items: flex-end;
}

.border-b {
    padding-bottom: 14px;
    width: 440px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    margin-bottom: 4px;
}

.amTypeName3 {
    margin-right: 16px;
    padding: 2px 4px;
    background: rgba(50, 109, 255, 0.3);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid rgba(50, 109, 255, 0.72);
}

.licenseNo {
    font-weight: 600;
    font-size: 18px;
    color: #FFFFFF;
}

.text {
    font-weight: 500;
    font-size: 14px;
    color: #FFFFFF;
}

.lable {
    font-weight: 400;
    font-size: 14px;
    color: #C2D3FF;
    flex: 1;
}

.chartName {
    background: rgba(50, 109, 255, 0.1);
    border-radius: 4px 4px 4px 4px;
    border: 1px solid rgba(50, 109, 255, 0.5);
    font-weight: 400;
    font-size: 14px;
    color: #C2D3FF;
}

.border-l {
    width: 3px;
    height: 12px;
    background: #4379FF;
    box-shadow: 0px 2px 4px 0px rgba(50, 109, 255, 0.55);
    border-radius: 2px 2px 2px 2px;
    position: absolute;
    left: 0;
    top: 4px;
}

:deep(.map-plot-dlg-wrapper) {

    .el-table th.el-table__cell.is-leaf,
    .el-table td.el-table__cell {
        border: none;
        background: rgba(38, 56, 100, 0.5) !important;
        border-radius: 0px 0px 0px 0px !important;
    }

    .el-table--group,
    .el-table--border {
        border: none;
    }

    .el-table--group::after,
    .el-table--border::after {
        content: none;
    }

    .el-table::before {
        content: none;
    }

    .el-table .cell {
        color: #C9D8FF;
    }

    .el-table thead {
        background: rgba(38, 56, 100, 0.5) !important;
    }

    .el-input.is-disabled .el-input__inner {
        background-color: transparent !important;
        color: #ffffff !important;
        border: none !important;
    }
    .el-input.is-disabled .el-input__wrapper{
        background-color: transparent !important;
        color: #ffffff !important;
        border: none !important;
    }
    .el-input.is-disabled .el-select__wrapper{
        background-color: transparent !important;
        color: #ffffff !important;
        border: none !important;
    }
    .el-tag.el-tag--info{
        background-color: transparent;
        border:none;
        color: #fff;
        padding-left: 0;
        font-size: 14px;
        padding-right: 0;
        .el-tag__close{
            display: none;
        }
    }
    .am-car-card .el-tag--info+.el-tag--info .el-select__tags-text::before{
        content: ',';
    }
    .el-select .el-tag{
        margin-right: 0;
    }
   
}

</style>

