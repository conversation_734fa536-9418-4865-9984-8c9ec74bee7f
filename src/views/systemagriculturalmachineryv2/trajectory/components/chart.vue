<template>
  <div id="realtime-chart" />
</template>

<script>
/**
   * 组件说明:实时轨迹echarts
   *
   */
import * as echarts from 'echarts'
import { dateFormat } from '@/views/systemagriculturalmachineryv2/script/Utils/DateAndTimeCommon'
export default {
  name: "Chart",
  props: {
    list: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      chart: null,
      firstTimeEnter: true
    }
  },
  watch: {
    list() {
      if (this.list.length === 0) return
      if (this.firstTimeEnter) {
        this.firstTimeEnter = false
        this.initChart()
      }
      this.setRankChart()
    }
  },
  // mounted() {
  //   this.initChart()
  // },
  methods: {
    initChart() {
      if (!this.chart) {
        this.chart = echarts.init(document.getElementById("realtime-chart"));
        // 绘制图表
        this.chart.setOption({
          backgroundColor: 'rgba(255,255,255,0.5)',
          grid: {
            left: "5%",
            right: "5%",
            top: "15%",
            bottom: "8%",
            containLabel: true
          },
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow"
            },
            formatter: function(params) {
              let str = '';
              if (params[0]) {
                str += params[0].name+ '<br/>'
                str += `速度: ${params[0].data.spd}<br />`
              }
              return str
            }
          },
          legend: {
            data:['spd'],
            right: "50%",
            top: "2%",
            formatter:  () => '速度',
          },
          xAxis: {
            type: 'category',
            // data: [],
            axisLabel:  {
              interval: 'auto',
              // rotate: 10,
              show: true
            }
          },
          yAxis: [
            { type: 'value',
              boundaryGap: [0, '100%'],
              nameTextStyle: {
                padding: 20
              },
              name: '速度 km/h',
              nameLocation: 'middle',
            }
          ],
          dataset: {
            dimensions: [
              'utc',
              'spd',
            ],
            source: this.list
          },
          series: [
            {
              type: "line",
            }
          ]
        })
      }
    },
    setRankChart() {
      this.chart.setOption({
        backgroundColor: 'rgba(255,255,255,0.5)',
        dataset: {
          source: this.list
        }
      }
      )
      // this.chart.setOption({
      //   xAxis: {
      //     data: this.c_xAxis
      //   },
      //   yAxis: {
      //     data: this.c_yAxis
      //   }
      // })
    },
  }
}
</script>

<style rel="stylesheet" lang="scss" scoped>
#realtime-chart {
  height: 100%;
}
</style>
