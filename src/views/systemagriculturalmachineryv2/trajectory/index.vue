<template>
    <div class="wrapper">
        <div class="wrapper-trajectory-realtime" v-if="realTime">
            <trajectory-realtime />
        </div>
        <div class="wrapper-trajectory-history" v-else-if="workInfo && workInfo.uuid">
            <trajectory-history-work />
        </div>
        <div class="wrapper-trajectory-history" v-else>
            <trajectory-history />
        </div>
    </div>
</template>

<script>
/**
 * 路由说明: 轨迹查询 2.0入口
 */
import TrajectoryRealtime from './realtime'
import TrajectoryHistory from './history'
import TrajectoryHistoryWork from './historyWork'
import { mapWritableState, mapActions } from 'pinia'
import useTrajectoryStore from "@/store/modules/trajectory"
export default {
    name: '/monitoringcenter/vehicleTrajectory',
    components: { TrajectoryRealtime, TrajectoryHistory, TrajectoryHistoryWork },
    data() {
        return {
        }
    },
    computed: {
         ...mapWritableState(useTrajectoryStore, ['realTime','uuid','workInfo']),
    },
    watch: {
        $route(to, from) {
            // console.log(to,'to')
            if(to.path == "/systemagriculturalmachineryv2/monitoringCenter/vehicleTrajectory"){
                this.initParams()
            }
        },
    },
    created() {
        this.SET_INIT_WORKING(false);
        this.SET_SHOW_RIGHT_BTN(false);
    },
    mounted() {
        this.initParams()
    },
    beforeDestroy() {
        this.RESET()
    },
    methods: {
        ...mapActions(useTrajectoryStore, [
                'SET_Real_Time',
                'SET_Uuid',
                'SET_MACHINE_ID',
                'SET_Start_Date',
                'SET_End_Date',
                'SET_Work_Info',
                'SET_Is_Offline_Count',
                'SET_Is_Online_Count',
                'RESET',
                'SET_Search_Type',
                'SET_INIT_WORKING',
                'SET_SHOW_RIGHT_BTN',

        ]),
        initParams() {
            this.RESET()
            this.SET_Real_Time(!!this.$route.query.realTime)
            
            this.$nextTick(() => {
                if(!this.realTime || !this.workInfo || !this.workInfo.uuid) {
                    let type;
                    switch (this.$route.query.jumpSource) {
                        case '2':
                            type = ''
                            break
                        case '1':
                        default :
                            type = 'local'
                            break

                    }
                    this.SET_Search_Type(type)
                }
                this.SET_Uuid(
                    !!this.$route.query.uuid ? this.$route.query.uuid : ''
                )
                this.SET_MACHINE_ID(
                    !!this.$route.query.machineId ? this.$route.query.machineId : ''
                )

                this.SET_Is_Offline_Count(!!!this.$route.query.realTime)
            })

            if (this.$route.query.startDate && this.$route.query.endDate) {
                //startDate &&  endDate
                this.SET_Start_Date(this.$route.query.startDate)
                this.SET_End_Date(this.$route.query.endDate)
            } else if (this.$route.query.date) {
                //date
                this.SET_Start_Date(this.$route.query.date + ' 00:00:00')
                this.SET_End_Date(this.$route.query.date + ' 23:59:59')
            } else {
                //都没有，取当日
                // this.SET_Start_Date( format(new Date(), 'yyyy-MM-dd')+"  00:00:00" )
                // this.SET_End_Date( format(new Date(), 'yyyy-MM-dd')+"  23:59:59" )
            }
            if(!!this.$route.query.workInfo){
                this.SET_Work_Info(JSON.parse(this.$route.query.workInfo))
            }else{
                this.SET_Work_Info({})
            }
        },
    },
}
</script>

<style scoped lang="scss">
@import './variables.scss';
.wrapper {
    --color: 255, 255, 255;
    --background-color: 18, 33, 28;
    --active-color: 42, 190, 177;
    --font-sieze: 14px;
    position: relative;
    color: rgba(var(--color), 1);
    height: calc(100vh - 84px);
    .wrapper-trajectory-history {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
    .wrapper-trajectory-realtime {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
}
</style>
