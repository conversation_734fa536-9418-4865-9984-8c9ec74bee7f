<template>
    <div class="app-container">
        <el-form v-show="showSearch" ref="queryForm" :model="queryParams" label-width="90px" class="form-line">
            <el-row :gutter="20">
                <el-col :span="6">
                    <el-form-item label="归属" prop="orgCode">
                        <OrgSelects ref="orgCodeRef" :apiUrl="orgDictsUrl()" :defaultOrgCode="queryParams.orgCode" :placeholderText="'请选择归属'" style="width: 100%;"
                            @handleOrgCode="handleOrgChange">
                        </OrgSelects>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="申请日期" prop="createTime">
                        <el-date-picker v-model="queryParams.createTime" placeholder="请选择申请日期" style="width: 100%"
                            value-format="YYYY-MM-DD">
                        </el-date-picker>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="种植作物" prop="raiseCropsCd">
                        <el-select style="width: 100%" v-model="queryParams.raiseCropsCd" clearable
                            placeholder="请选择种植作物">
                            <el-option v-for="dict in raiseCropsCdOptions" :key="dict.raiseCropsCd"
                                :label="dict.raiseCropsNm" :value="dict.raiseCropsCd" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="生产流程" prop="prodProcessCode">
                        <el-select style="width: 100%" v-model="queryParams.prodProcessCode" clearable
                            placeholder="请选择生产流程">
                            <el-option v-for="dict in prodProcessOptions" :key="dict.code" :label="dict.name"
                                :value="dict.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="作业环节" prop="linkCode">
                        <el-select style="width: 100%" v-model="queryParams.linkCode" clearable placeholder="请选择作业环节">
                            <el-option v-for="dict in linkCodeOptions" :key="dict.linkCode" :label="dict.linkName"
                                :value="dict.linkCode" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="地块名称" prop="plotName">
                        <el-input v-model="queryParams.plotName" clearable placeholder="请输入地块名称" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="申请人" prop="createName">
                        <el-input v-model="queryParams.createName" clearable placeholder="请输入申请人" />
                    </el-form-item>
                </el-col>
                <el-col :span="6">
                    <el-form-item label="审核状态" prop="applyAuditStatus">
                        <el-select style="width: 100%" v-model="queryParams.applyAuditStatus" clearable filterable
                            placeholder="请选择审核状态">
                            <el-option v-for="dict in apply_audit_status" :key="dict.code" :label="dict.name"
                                :value="dict.code" />
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="24" class="text-right" align="right">
                    <el-button icon="Refresh"  @click="resetQuery">重置</el-button>
                    <el-button icon="Search"  type="primary" @click="handleQuery">搜索
                    </el-button>
                </el-col>
            </el-row>
        </el-form>
        <el-row :gutter="10" class="mb8">
            <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <el-table :data="amcompanyList" border ref="table"
            :height="tableHeight">
            <template v-for="item in columns">
                <el-table-column :key="item.label" :label="item.label" :align="item.align" :prop="item.prop"
                    :width="item.width || 'auto'" v-if="showParentsColumn(item)" :fixed="item.fixed">
                    <template #default="scope">
                        <span v-if="scope.column.label === '申请时间'">{{
                            parseTime(
                                scope.row.createTime,
                                "{y}-{m}-{d} {h}:{i}:{s}"
                            )
                        }}</span>
                        <span v-else-if="scope.column.label === '申请人'">
                            {{ scope.row.createName
                            }}{{
                                scope.row.phoneNo ? "," + scope.row.phoneNo : ""
                            }}</span>
                        <div v-else-if="scope.column.label === '已完成作业环节'">
                            <el-tag style="margin: 4px" :key="index" v-for="(it, index) in scope.row[item.prop]"
                                v-show="index < 4">{{ it }}</el-tag>
                            <el-popover placement="top" width="200" trigger="hover">
                                <el-link style="display: block" type="primary" :key="index"
                                    v-for="(it, index) in scope.row[item.prop]">{{ index + 1 + ": " + it }}</el-link>
                                <el-tag #reference v-if="scope.row[item.prop].length > 4">...</el-tag>
                            </el-popover>
                        </div>
                        <span v-else-if="item.formatter">
                            <span v-text="item.formatter(
                                scope.row,
                                scope.column,
                                scope.row[item.prop]
                            )
                                ">
                            </span>
                        </span>
                        <span v-else>{{
                            scope.row[scope.column.property]
                        }}</span>
                         <template v-if="item.childs && item.childs.length > 0">
                            <template v-for="it in item.childs">
                                <el-table-column
                                    :key="it.label"
                                    :label="it.label"
                                    :align="it.align"
                                    :prop="it.prop"
                                    v-if="it.show"
                                    :width="it.width || 'auto'"
                                >
                                </el-table-column>
                            </template>
                        </template>
                    </template>
                </el-table-column>
            </template>

            <el-table-column fixed="right" align="center" class-name="small-padding fixed-width" label="操作" width="200">
                <!-- v-if="scope.row.applyAuditStatus==1" -->
                <template #default="scope">
                    <el-button v-hasPermi="['workApplyApprove:passById']"  link type="primary" icon="DocumentChecked"
                        v-if="scope.row.applyAuditStatus == 1" @click="handleUpdate(scope.row)">审核
                    </el-button>
                    <el-button v-else v-hasPermi="['workApplyApprove:info']"  link type="primary" icon="Search"
                        @click="handleUpdate(scope.row)">详情查看
                    </el-button>
                    <el-button  link type="primary" @click="handleApplyLog(scope.row)"  icon="Notebook"
                        v-hasPermi="['workApplyApprove::workApplyApproveLog']">审核日志
                    </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination v-show="total > 0" v-model:limit="queryParams.rows" :page-sizes="[10, 20, 50, 100]"
             v-model:page="queryParams.page" :total="total" @pagination="getList" />

        <!-- 添加或编辑对话框 -->
        <el-dialog :close-on-click-modal="false" :title="title" v-model="open" append-to-body class="protoForm"
            width="85%">
            <div class="box_m">
                <img :src="u339" alt="" />
                <div style="flex: 1; padding-top: 10px">
                    <div class="bar">
                        <div>{{ form.plotName }}</div>
                        <div style="position: relative">
                            <div>
                                申请时间:{{
                                    parseTime(
                                        form.createTime,
                                        "{y}-{m}-{d} {h}:{i}:{s}"
                                    )
                                }}
                            </div>
                            <div class="pass" v-if="form.applyAuditStatus == 2">
                                审核通过
                            </div>
                            <div class="turn" v-if="form.applyAuditStatus == 3">
                                驳回
                            </div>
                        </div>
                    </div>
                    <div class="bar">
                        <div>{{ form.orgName }}</div>
                        <el-button type="success">作业申请</el-button>
                    </div>
                </div>
            </div>
            <el-descriptions border :column="4">
                <el-descriptions-item label="种植作物">{{
                    form.raiseCropsNm
                }}</el-descriptions-item>
                <el-descriptions-item label="申请生产流程">{{
                    form.prodProcessName
                }}</el-descriptions-item>
                <el-descriptions-item label="申请作业环节">{{
                    form.linkName
                }}</el-descriptions-item>
                <el-descriptions-item label="申请人">{{
                    form.createName
                }}</el-descriptions-item>
                <el-descriptions-item label="已完成作业环节" :span="4">{{
                    form.doneLinkName
                }}</el-descriptions-item>
                <template v-for="(item, index) in form.attrList">
                    <el-descriptions-item :label="'关联机具所有者或组织名称-' + (index + 1)">{{ item.amOwnerName
                    }}</el-descriptions-item>
                    <el-descriptions-item label="手机号">{{
                        item.phoneNo
                    }}</el-descriptions-item>
                    <el-descriptions-item label="农机型号">{{
                        item.amGradeName
                    }}</el-descriptions-item>
                    <el-descriptions-item label="农具型号">{{
                        item.amToolsName
                    }}</el-descriptions-item>
                </template>
            </el-descriptions>
            <el-row>
                 <el-col :span="24">
                    <div class="bac">
                        <div class="bactitle">
                            <div class="title">作业前照片</div>
                        </div>
                    </div>
                </el-col>
                <el-col :span="24">
                    <div label="作业前照片" prop="applyPicUrl">
                        <img :key="it.url" @click="openpic(form, i)" :src="it.url" alt=""
                            v-for="(it, i) in form.applyPicUrl" style="width: 350px; height: 150px; margin-right: 10px" />
                    </div>
                </el-col>
            </el-row>
            <el-row v-if="islook == true && form.applyAuditStatus == 3">
                <el-col :span="24">
                    <div class="bac">
                        <div class="bactitle">
                            <div class="title">驳回理由</div>
                        </div>
                    </div>
                </el-col>
                 <el-col :span="24">
                    <div>
                        {{ form.reasonContent }}
                    </div>
                </el-col>
            </el-row>
            <template #footer class="dialog-footer">
                <el-button type="danger" v-if="islook == false" @click="openReason">驳 回</el-button>
                <el-button type="primary" v-if="islook == false" @click="submitForm">通 过</el-button>
                <el-button @click="cancel">取 消</el-button>
            </template>
        </el-dialog>
        <el-dialog v-model="dialogVisible" title="预览" append-to-body width="840px" class="pictureparent">
            <img v-if="pics.length > 0" :src="pics[picIndex]" class="picture" />
            <div class="picboc">
                <img class="picbocimg" :class="{ actice: index == picIndex }" @click="picIndex = index" :src="item"
                    alt="" v-for="(item, index) in pics" :key="item" />
                <i class="el-icon-arrow-left" @click="arrowleft"></i>
                <i class="el-icon-arrow-right" @click="arrowright"></i>
            </div>
        </el-dialog>
        <el-dialog v-model="reasondialog" title="请选择驳回原因" append-to-body width="440px" class="pictureparent">
            <el-form ref="formReason" :model="formReason" :rules="formReasonrules">
                <el-radio-group v-model="formReason.applyAuditReason" @change="reasonChange" style="display: flex;flex-direction: column;align-items: flex-start;">
                    <el-radio :label="it.code" :key="it.code" v-for="it in apply_audit_reason">{{ it.name }}</el-radio>
                    <el-form-item label=" " prop="reasonContent" label-width="20px"
                        v-if="formReason.applyAuditReason == 3">
                        <el-input style="width: 360px" type="textarea" placeholder="" :maxlength="200"
                            v-model="formReason.reasonContent">
                        </el-input>
                    </el-form-item>
                </el-radio-group>
            </el-form>
            <template #footer class="dialog-footer">
                <el-button type="primary" @click="subminReason">确 定</el-button>
                <el-button @click="reasondialog = false">取 消</el-button>
            </template>
        </el-dialog>
        <el-dialog v-model="logdialog" title="审核日志" append-to-body width="840px">
            <el-table :data="logs">
                <template v-for="item in columnslog">
                    <el-table-column :key="item.label" :label="item.label" :align="item.align" :prop="item.prop"
                        :width="item.width || 'auto'" v-if="showParentsColumn(item)" :fixed="item.fixed">
                        <template #default="scope">
                            <span v-if="scope.column.label === '申请时间'">{{
                                parseTime(
                                    scope.row.createTime,
                                    "{y}-{m}-{d} {h}:{i}:{s}"
                                )
                            }}</span>
                            <span v-else-if="
                                scope.column.label === '作业前地块照片'
                            ">
                                <img class="applyPicUrl" @click="openpic(scope.row, 0)" :src="scope.row.applyPicUrl1"
                                    alt="" />
                            </span>
                            <span v-else-if="item.formatter">
                                <span v-text="item.formatter(
                                    scope.row,
                                    scope.column,
                                    scope.row[item.prop]
                                )
                                    ">
                                </span>
                            </span>
                            <span v-else>{{
                                scope.row[scope.column.property]
                            }}</span>
                            <template v-if="item.childs && item.childs.length > 0">
                                <template v-for="it in item.childs">
                                    <el-table-column
                                        :key="it.label"
                                        :label="it.label"
                                        :align="it.align"
                                        :prop="it.prop"
                                        v-if="it.show"
                                        :width="it.width || 'auto'"
                                    >
                                    </el-table-column>
                                </template>
                            </template>
                        </template>
                    </el-table-column>
                </template>
            </el-table>
        </el-dialog>
    </div>
</template>

<script>
import { cropProcess } from "@/api/systemagriculturalmachineryv2/dispatch/operational";
import u339 from "@/views/systemagriculturalmachineryv2/assets/images/u339.png"
import calcTabelHeight from "@/views/systemagriculturalmachineryv2/mixins/calcTabelHeight";
import {
    getList,
    getAmcompany,
    queryApproveLog,
    turn,
    turnPass,
} from "@/api/systemagriculturalmachineryv2/processApprove/workApplyApprove";
import configureColumns from "@/views/systemagriculturalmachineryv2/components/ConfigureColumns";
import { getDicts,getRaiseCrops } from "@/api/systemagriculturalmachineryv2/dict";
import useUserStore from "@/store/modules/user"
import { selectDictLabel, selectDictLabels} from '@/views/systemagriculturalmachineryv2/utils/cop'
import {orgDictsUrl} from "@/api/systemagriculturalmachineryv2/orgDict.js";
export default {
    name: "/approval/workApply/queryByPage",
    components: {
        configureColumns,
    },
    mixins: [calcTabelHeight],
    data() {
        let self = this;
        return {
            u339,
            pics: [],
            picIndex: 0,
            dialogVisible: false,
            columns: [
                {
                    label: "申请时间",
                    align: "center",
                    show: true,
                    prop: "createTime",
                },
                {
                    label: "归属",
                    align: "center",
                    width: "380px",
                    show: true,
                    prop: "orgName",
                },
                {
                    label: "申请人",
                    align: "center",
                    show: true,
                    prop: "createName",
                },
                {
                    label: "申请地块名称",
                    align: "center",
                    show: true,
                    prop: "plotName",
                },
                {
                    label: "种植作物",
                    align: "center",
                    show: true,
                    prop: "raiseCropsNm",
                },
                {
                    label: "已完成作业环节",
                    align: "center",
                    show: true,
                    width: 200,
                    prop: "doneLinkNameArr",
                },
                {
                    label: "申请作业环节",
                    align: "center",
                    show: true,
                    prop: "linkName",
                },
                {
                    label: "审核状态",
                    align: "center",
                    show: true,
                    prop: "applyAuditStatus",
                    formatter: self.formatter,
                },
            ],
            columnslog: [
                {
                    label: "申请时间",
                    align: "center",
                    show: true,
                    prop: "createTime",
                },
                {
                    label: "归属",
                    align: "center",
                    show: true,
                    prop: "orgName",
                },
                {
                    label: "申请人",
                    align: "center",
                    show: true,
                    prop: "createName",
                },
                {
                    label: "申请地块名称",
                    align: "center",
                    show: true,
                    prop: "plotName",
                },
                {
                    label: "种植作物",
                    align: "center",
                    show: true,
                    prop: "raiseCropsNm",
                },
                {
                    label: "申请作业环节",
                    align: "center",
                    show: true,
                    prop: "linkName",
                },
                {
                    label: "审核状态",
                    align: "center",
                    show: true,
                    prop: "applyAuditStatus",
                    formatter: self.formatter,
                },
                {
                    label: "审核时间",
                    align: "center",
                    show: true,
                    prop: "updateTimeF",
                },
            ],
            // 选中数组
            ids: [],
            // 非单个禁用
            single: true,
            // 非多个禁用
            multiple: true,
            // 显示搜索条件
            showSearch: true,
            // 总条数
            total: 0,
            // 表格数据
            amcompanyList: [],
            // 弹出层标题
            title: "",
            // 是否显示弹出层
            open: false,
            // 查询参数
            queryParams: {
                statYear: '',
                orgCode: useUserStore().currentOrgCode, //组织机构编码
                createTime: "", //创建时间
                raiseCropsCd: "", //作物编码
                prodProcessCode: "", //生产流程编码
                linkCode: "", //作业环节编码
                plotName: "", //地块名称
                createName: "", //申请人
                applyAuditStatus: "1", //审核状态
                page: 1,
                rows: 10,
            },
            // 表单参数
            form: {
                orgCode: "", //组织机构编码
                plotNo: "", //地块代码
                plotName: "", //地块名称
                raiseCropsCd: "", //作物编码
                prodProcessCode: "", //生产流程编码
                raiseCropsNm: "",
                prodProcessName: "",
                linkCode: "", //作业环节编码
                linkName: "", //作业环节名称
                applyPicUrl: [],
                applyPicUrl1: "", //作业前地块照片1
                applyPicUrl2: "", //作业前地块照片2
                applyPicUrl3: "",
                attrList: [],
            },
            // 年度字典
            yearNoOptions: [],
            raiseCropsCdOptions: [],
            prodProcessOptions: [],
            apply_status: [],
            apply_audit_status: [],
            // 作业环节
            linkCodeOptions: [],
            // 作业环节
            settingQueryFormlinkCodeOptions: [],
            logs: [],
            logdialog: false,
            apply_audit_reason: [],
            reasondialog: false,
            formReason: {
                applyAuditReason: null,
                reasonContent: null,
            },
            formReasonrules: {
                reasonContent: [
                    {
                        required: true,
                        message: "请输入原因",
                        trigger: "blur",
                    },
                ],
            },
            islook: false,
        };
    },
    watch: {
        'queryParams.statYear': function (val, oldVal) {
            if (val != null && val !== '') {
                this.linkCodeOptions = [];
                this.queryParams.linkCode = '';
                this.getRaise();
            }
        },
        'queryParams.orgCode': function (val, oldVal) {
            if (val != null && val !== '') {
                this.linkCodeOptions = [];
                this.queryParams.linkCode = '';
                this.getRaise();
            }
        },
        "queryParams.raiseCropsCd": function (val, oldVal) {
            this.queryParams.linkCode = null;
            if (
                val != null &&
                val !== "" &&
                this.queryParams.prodProcessCode != null &&
                this.queryParams.prodProcessCode !== ""
            ) {
                //环节字典
                cropProcess({
                    raiseCrops: val,
                    prodProcessCode: this.queryParams.prodProcessCode,
                }).then((response) => {
                    this.linkCodeOptions = response.data;
                });
            } else {
                this.linkCodeOptions = [];
            }
        },
        "queryParams.prodProcessCode": function (val, oldVal) {
            this.queryParams.linkCode = null;
            if (
                val != null &&
                val !== "" &&
                this.queryParams.raiseCropsCd != null &&
                this.queryParams.raiseCropsCd !== ""
            ) {
                //查机具品目
                cropProcess({
                    raiseCrops: this.queryParams.raiseCropsCd,
                    prodProcessCode: val,
                }).then((response) => {
                    this.linkCodeOptions = response.data;
                });
            } else {
                this.linkCodeOptions = [];
            }
        },
    },
    created() {
        getDicts("apply_audit_reason").then((response) => {
            this.apply_audit_reason = response.data;
        });
        // getDicts("raise_crops").then((response) => {
        //     this.raiseCropsCdOptions = response.data;
        // });
        getDicts("prod_process").then((response) => {
            this.prodProcessOptions = response.data;
        });
        getDicts("apply_status").then((response) => {
            this.apply_status = response.data;
        });
        getDicts("apply_audit_status").then((response) => {
            this.apply_audit_status = response.data;
        });
        this.getRaise()
        this.getList();
    },
    methods: {
      orgDictsUrl,
        getRaise() {
            //作物
            this.raiseCropsCdOptions = [];
            this.queryParams.raiseCropsCd = '';
            getRaiseCrops({
                orgCode: this.queryParams.orgCode,
                year: this.queryParams.statYear
            }).then(response => {
                this.raiseCropsCdOptions = response.data;
            })

        },
        //控制父级Column是否展示根据子show是否都是true
        showParentsColumn(item) {
            if (item.childs && item.childs.length > 0) {
                return item.childs.some((it) => {
                    return it.show == true;
                });
            } else {
                return item.show;
            }
        },
        reasonChange() {
            this.formReason.reasonContent = null;
        },
        subminReason() {
            this.$refs["formReason"].validate((valid) => {
                if (valid) {
                    this.formReason.applyId = this.form.applyId;
                    var formReason = Object.assign({}, this.formReason);
                    if (formReason.applyAuditReason != 3) {
                        this.apply_audit_reason.forEach((audit_reason) => {
                            if (
                                audit_reason.code == formReason.applyAuditReason
                            ) {
                                formReason.reasonContent = audit_reason.name;
                            }
                        });
                    }
                    turn(formReason).then((response) => {
                       this.$modal.msgSuccess("驳回成功");
                        this.reasondialog = false;
                        this.open = false;
                        this.getList();
                    });
                }
            });
        },
        openReason() {
            this.formReason.applyAuditReason = "1";
            this.reasondialog = true;
        },
        handleApplyLog(row) {
            this.logdialog = true;
            queryApproveLog({ applyId: row.applyId }).then((response) => {
                response.data.forEach((data) => {
                    data.updateTimeF = this.parseTime(
                        data.updateTime,
                        "{y}-{m}-{d} {h}:{i}:{s}"
                    );
                });
                this.logs = response.data;
            });
        },
        arrowleft() {
            if (this.picIndex == 0) {
                return;
            } else {
                this.picIndex -= 1;
            }
        },
        arrowright() {
            if (this.picIndex == this.pics.length - 1) {
                return;
            } else {
                this.picIndex += 1;
            }
        },
        openpic(item, i) {
            this.picIndex = i;
            this.pics = [];
            if (item.applyPicUrl1) {
                this.pics.push(item.applyPicUrl1);
            }
            if (item.applyPicUrl2) {
                this.pics.push(item.applyPicUrl2);
            }
            if (item.applyPicUrl3) {
                this.pics.push(item.applyPicUrl3);
            }

            this.dialogVisible = true;
        },
        handleOrgChange({orgCode}) {
            this.queryParams.orgCode = orgCode
        },
        formatter(row, column, cellValue, index) {
            if (column.property == "applyStatus") {
                return selectDictLabels(this.apply_status, cellValue);
            } else if (column.property == "applyAuditStatus") {
                return selectDictLabels(
                    this.apply_audit_status,
                    cellValue
                );
            } else {
                // 返回其他不需要处理的正常参数
                return row[column.property];
            }
        },
        /** 查询列表 */
        getList() {
            getList(this.queryParams).then((response) => {
                response.data.records.forEach((record) => {
                    if (record.doneLinkName == "") {
                        record.doneLinkNameArr = [];
                    } else {
                        record.doneLinkNameArr = record.doneLinkName.split(",");
                    }
                });
                this.amcompanyList = response.data.records;
                this.total = response.data.total;
            });
        },
        // 取消按钮
        cancel() {
            this.open = false;
            this.reset();
        },
        // 表单重置
        reset() {
            this.resetForm("form");
        },
        /** 搜索按钮操作 */
        handleQuery() {
            this.queryParams.page = 1;
            this.getList();
        },
        /** 重置按钮操作 */
        resetQuery() {
            this.resetForm("queryForm");
            this.handleQuery();
        },
        /** 编辑按钮操作 */
        handleUpdate(row) {
            this.reset();
            const applyId = row.applyId || this.ids;
            if (row.applyAuditStatus == 1) {
                this.islook = false;
                this.title = "作业申请审核";
            } else {
                this.islook = true;
                this.title = "作业申请审核结果";
            }
            getAmcompany(applyId).then((response) => {
                this.form = response.data;
                this.form.applyPicUrl = [];
                if (this.form.applyPicUrl1) {
                    this.form.applyPicUrl.push({ url: this.form.applyPicUrl1 });
                }
                if (this.form.applyPicUrl2) {
                    this.form.applyPicUrl.push({ url: this.form.applyPicUrl2 });
                }
                if (this.form.applyPicUrl3) {
                    this.form.applyPicUrl.push({ url: this.form.applyPicUrl3 });
                }
                this.open = true;
            });
        },
        /** 提交按钮 */
        submitForm() {
            this.$confirm('请确认是否提交审核结果"', "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
            }).then(() => {
                turnPass(this.form.applyId).then((response) => {
                   this.$modal.msgSuccess("审核成功");
                    this.open = false;
                    this.getList();
                });
            });
        },
    },
};
</script>

<style lang="scss" scoped>
// @import '@/views/systemagriculturalmachineryv2/assets/styles/index.scss';

.el-table .el-checkbox__label {
    display: none;
}

// .protoForm .el-dialog__header {
//     background: #5a98de;
// }

// .protoForm .el-dialog__header .el-dialog__title {
//     color: #fff;
// }

// .protoForm .el-dialog__header .el-dialog__headerbtn .el-dialog__close {
//     color: #fff;
// }

.applyPicUrl {
    width: 80px;
    height: 60px;
    object-fit: cover;
}

.bac {
    margin-bottom: 20px;
    border-bottom: 1px solid #5a98de;
    position: relative;
    margin-top: 10px;
}

.bac .bactitle {
    width: 110px;
    height: 0;
    border-width: 0px 30px 30px 0px;
    border-style: none solid solid;
    border-color: transparent transparent rgb(90 152 222);
    position: relative;
}

.bac .bactitle .title {
    color: white;
    position: absolute;
    top: 6px;
    left: 10px;
}

.bac_btn {
    position: absolute;
    top: -7px;
    right: 0;
}

.bac_btn2 {
    position: absolute;
    top: -7px;
    right: 90px;
}

.picboc {
    display: flex;
    margin-top: 10px;
    height: 80px;
    padding: 0 20px;
    position: relative;
    align-items: center;
}

.picbocimg {
    width: 80px;
    height: 60px;
    margin-right: 10px;
    cursor: pointer;
}

.picboc .el-icon-arrow-left {
    position: absolute;
    top: 25px;
    left: -10px;
    font-size: 30px;
    cursor: pointer;
}

.picboc .el-icon-arrow-right {
    position: absolute;
    font-size: 30px;
    top: 25px;
    right: 10px;
    cursor: pointer;
}

.picture {
    display: block;
    width: 800px;
    height: 500px;
    margin: 0 auto;
    object-fit: contain;
}

.pictureparent .el-dialog__body {
    display: flex;
    flex-direction: column;
}

.actice {
    border: 1px solid #006be1;
}

.bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    position: relative;
}

.el-radio {
    display: block;
}

.box_m {
    display: flex;
    align-items: center;
}

.box_m img {
    background-color: #3eacef;
    width: 60px;
    margin-right: 10px;
}

.pass {
    position: absolute;
    top: -37px;
    left: 48px;
    width: 98px;
    height: 98px;
    text-align: center;
    line-height: 98px;
    border: 4px solid #5ef412;
    transform: rotate(323deg);
    font-weight: 700;
    font-size: 20px;
    border-radius: 50%;
    color: #5ef412;
    z-index: 1;
}

.turn {
    position: absolute;
    top: -37px;
    left: 48px;
    width: 98px;
    height: 98px;
    text-align: center;
    line-height: 98px;
    border: 4px solid #f70000;
    transform: rotate(323deg);
    font-weight: 700;
    font-size: 20px;
    border-radius: 50%;
    color: #f70000;
    z-index: 1;
}

.con-search-form {
    margin-bottom: 10px;
}
</style>
