<template>
    <el-dialog title="" v-model="visible" width="630px" :append-to-body="false" :modal-append-to-body="false"
        @close="onCloseDialog">
        <div>
            <img src="@/views/systemagriculturalmachineryv2/assets/images/goko.png" alt="">
        </div>
        <div class="tabs" style="width: 368px;">
            <div class="tabItem" :class="{ 'tabActive': form.ji == 1 }" @click="form.ji = 1">作业信息</div>
            <div class="tabItem" :class="{ 'tabActive': form.ji == 2 }" @click="form.ji = 2">设备信息</div>
            <div class="tabItem" :class="{ 'tabActive': form.ji == 3 }" @click="form.ji = 3">导航信息</div>
        </div>
        <div class="form" style="overflow: hidden;padding-bottom: 0px;">
            <el-form ref="form" :model="form" label-width="148px" label-position="left" disabled style="padding-bottom: 50px;">
                <template v-if="form.ji == 1">
                    <el-form-item label-width="90px" label="作业状态">
                        <el-select v-model="form.workState" placeholder="" style="width: 100%;"
                            :popper-append-to-body="false">
                            <el-option
                                v-for="(item, index) in mtOptions"
                                :key="index"
                                :label="item.name"
                                :value="+item.code"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label-width="90px" label="作业名称">
                        <el-input v-model="form.workName"/>
                    </el-form-item>
                    <el-form-item label-width="90px" label="作业模式">
                        <el-select v-model="form.workMode" placeholder="" style="width: 100%;"
                            :popper-append-to-body="false">
                            <el-option
                                v-for="(item, index) in operationmodeOptions"
                                :key="index"
                                :label="item.name"
                                :value="+item.code"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label-width="90px" label="作业类型">
                        <el-select v-model="form.workType" placeholder="" style="width: 100%;"
                            :popper-append-to-body="false">
                            <el-option
                                v-for="(item, index) in assignTypeOptions"
                                :key="index"
                                :label="item.name"
                                :value="+item.code"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label-width="90px" label="机具宽度">
                        <el-input v-model="form.mtWidth" class="append">
                            <template #append>米</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label-width="90px" label="接行宽度" prop="abutWidth">
                        <el-input v-model="form.abutWidth" class="append">
                            <template #append>厘米</template>
                        </el-input>
                    </el-form-item>
                    <!-- <el-form-item>
                        <el-button class="cancel">取消</el-button>
                        <el-button class="submit" @click="onSubmit">立即创建</el-button>
                    </el-form-item> -->
                </template>
                <template v-if="form.ji == 2">
                    <el-form-item label-width="120px" label="发动机转速">
                        <el-input v-model="form.enginespeed" class="append">
                            <template #append>转</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label-width="120px" label="燃油位百分比">
                        <el-input v-model="form.b7" class="append">
                            <template #append>%</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label-width="120px" label="冷却液温度">
                        <el-input v-model="form.gk" class="append">
                            <template #append>℃</template>
                        </el-input>
                    </el-form-item>
                    <el-form-item label-width="120px" label="机油压力">
                        <el-input v-model="form.gl"></el-input>
                    </el-form-item>
                    <!-- <el-form-item>
                        <el-button class="cancel">取消</el-button>
                        <el-button class="submit" @click="onSubmit">立即创建</el-button>
                    </el-form-item> -->
                </template>

                <template v-if="form.ji == 3">
                    <el-scrollbar style="height:600px">
                        <el-form-item label="导航系统状态">
                            <el-select v-model="form.navigationsystemstatusinformation" placeholder="" style="width: 100%;"
                                :popper-append-to-body="false">
                                <el-option
                                v-for="(item, index) in nsformationOptions"
                                :key="index"
                                :label="item.name"
                                :value="+item.code"
                            ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="GNSS差分状态">
                            <el-select v-model="form.gnssdifferentialstate" placeholder="" style="width: 100%;"
                                :popper-append-to-body="false">
                                <el-option
                                v-for="(item, index) in gdifferentialstateOptions"
                                :key="index"
                                :label="item.name"
                                :value="+item.code"
                            ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="累计作业亩数">
                            <el-input v-model="form.cumulativeoperationpermu" class="append">
                                <template #append>亩</template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="累计作业时间">
                            <el-input v-model="form.cumulativejobtime" class="append">
                                <template #append>分</template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="开始作业时间">
                            <el-input v-model="form.cucrrnetjobstartstime"></el-input>
                        </el-form-item>
                        <el-form-item label="已作业亩数">
                            <el-input v-model="form.currentworkarea" class="append">
                                <template #append>亩</template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="当前行数">
                            <el-input v-model="form.currentjobline" class="append">
                                <template #append>行</template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="差分信号延迟时间">
                            <el-input v-model="form.differentialsignaldelaytime"></el-input>
                        </el-form-item>
                        <el-form-item label="控制模式设置">
                            <el-select v-model="form.x1" placeholder="" style="width: 100%;"
                                :popper-append-to-body="false">
                                <el-option
                                v-for="(item, index) in xctypeOptions"
                                :key="index"
                                :label="item.name"
                                :value="+item.code"
                            ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="上线灵敏度值">
                            <el-input v-model="form.onlinesensitivityvalue"></el-input>
                        </el-form-item>
                        <el-form-item label="转向灵敏度值">
                            <el-input v-model="form.steeringsensitivityvalue"></el-input>
                        </el-form-item>
                        <el-form-item label="前进灵敏度值">
                            <el-input v-model="form.forwardsensitivityvalue"></el-input>
                        </el-form-item>
                        <el-form-item label="后退灵敏度值">
                            <el-input v-model="form.backwardsensitivityvalue"></el-input>
                        </el-form-item>
                        <el-form-item label="手动方向灵敏度值">
                            <el-input v-model="form.sensitivityvalueofmanualdirection"></el-input>
                        </el-form-item>
                    </el-scrollbar>
                </template>
            </el-form>
        </div>
    </el-dialog>
</template>
<script>
import { getNavData, getRealTimeInfo, } from "@/api/systemagriculturalmachineryv2/unmanned/unmanned";
import { getDicts } from "@/api/systemagriculturalmachineryv2/dict";
export default {
    name: 'opInfoDialog',
    props: {
        modelValue: {
            type: Boolean,
            default: false,
        },
        requestHeadLovol:{
            type: Object,
            default: {
                
            },
        }
    },
    data() {
        return {
            form: {
                ji: true,
                workState: '', // 作业状态
                workName: '', // 作业名称
                workMode: '', // 作业模式
                workType: '', // 作业类型
                mtWidth: '', // 机具宽度
                abutWidth: '', // 接行宽度
                enginespeed: '', // 发动机转速
                b7: '', // 燃油百分比
                gk: '', // 冷却液温度 
                gl: '', // 机油压力
                navigationsystemstatusinformation: '', // 导航系统状态
                gnssdifferentialstate: '', // GNSS差分状态
                cumulativeoperationpermu: '', // 累计作业亩数
                cumulativejobtime: '', // 累计作业时间
                cucrrnetjobstartstime: '', // 作业开始时间
                currentworkarea: '', // 已作业亩数
                currentjobline: '', // 当前行数
                differentialsignaldelaytime: '', // 差分信号延迟时间
                x1: '', // 控制模式设置
                onlinesensitivityvalue: '', // 上线灵敏度
                steeringsensitivityvalue: '', // 转向灵敏度
                forwardsensitivityvalue: '', // 前进灵敏度
                backwardsensitivityvalue: '', // 后退灵敏度
                sensitivityvalueofmanualdirection: '', // 手动方向灵敏度
            },
            mtOptions: [], // 作业状态
            operationmodeOptions: [], // 作业模式
            assignTypeOptions: [], // 作业类型
            nsformationOptions: [], // 导航系统状态
            gdifferentialstateOptions: [], // gnss差分状态
            xctypeOptions: [], //控制模式设置
        }
    },
    computed: {
        visible: {
          get() {
            return this.modelValue
          },
          set(val) {
            this.$emit('update:modelValue', val)
          }
        },
        isHand() {
            return this.form.pointMode == '1'
        },
    },
    created() {
        this.getAllDict()
    },
    mounted() {
        // this.$bus.$on('navData', (val) => {
        //     console.log('>>>>>>>>>>工况信息弹窗获取实时数据：', val);
            

        // })
    },
    methods: {
        async getAllDict() {
            try {
                const [{data: mtRes}, {data: operationmodeRes}, { data: assignTypeRes}, {data: nsformationRes}, {data: gdifferentialstateRes}, {data: xctypeRes}] = await Promise.all([
                    getDicts('mt'), // 作业状态
                    getDicts('operationmode'), // 作业模式
                    getDicts('assign_type'), // 作业类型
                    getDicts('nsformation'), // 导航系统状态
                    getDicts('gdifferentialstate'), // gnss差分状态
                    getDicts('xctype'), // 控制模式设置

                ]);
                this.mtOptions = mtRes;
                this.operationmodeOptions = operationmodeRes;
                this.assignTypeOptions = assignTypeRes;
                this.nsformationOptions = nsformationRes;
                this.gdifferentialstateOptions = gdifferentialstateRes;
                this.xctypeOptions = xctypeRes;
                console.log('>>>>>>>gnss: ', gdifferentialstateRes)
            } catch (error) {
                console.log('>>>>>>工况-err: ', error)
            }
        },
        async getInfo(obj){
            try {
                console.log('>>>>>>getInfo: ', obj)
                this.requestHeadLovol.deviceNo = obj.deviceCode || obj.uuid;
                const result = await getNavData({...this.requestHeadLovol});
                const result2 = await getRealTimeInfo({
                        requestHeadLovol: this.requestHeadLovol,
                        terminalId: obj.terminalId,
                    }); // TBOX实时数据
                const val =  {...result.data, tboxData: result2.data};
                console.log('>>>>>>>getInfo-val: ', val)
                this.form.workState = val.mt;
            this.form.workName = val.jobnameid;
            this.form.workMode = val.operationmode;
            this.form.workType = val.operationtype;
            this.form.mtWidth = val.machinewidth;
            this.form.abutWidth = val.linewidth;
            this.form.enginespeed = val.tboxData.b8;
            this.form.b7 = val?.tboxData.b7;
            this.form.gk = val?.tboxData.gk;
            this.form.gl = val?.tboxData.gl;
            this.form.navigationsystemstatusinformation = val.navigationsystemstatusinformation;
            this.form.gnssdifferentialstate = val.gnssdifferentialstate;
            this.form.cumulativeoperationpermu = val.cumulativeoperationpermu;
            this.form.cumulativejobtime = val.cumulativejobtime;
            this.form.cucrrnetjobstartstime = val.cucrrnetjobstartstime;
            this.form.currentworkarea = val.currentworkarea;
            this.form.currentjobline = val.currentjobline;
            this.form.differentialsignaldelaytime = val.differentialsignaldelaytime;
            this.form.x1 = val.x1;
            this.form.onlinesensitivityvalue = val.onlinesensitivityvalue;
            this.form.steeringsensitivityvalue = val.steeringsensitivityvalue;
            this.form.forwardsensitivityvalue = val.forwardsensitivityvalue;
            this.form.backwardsensitivityvalue = val.backwardsensitivityvalue;
            this.form.sensitivityvalueofmanualdirection = val.sensitivityvalueofmanualdirection;
            } catch (error) {
                console.log('>>>>>>>>getInfo-err: ', error)
            }
        },
        tabChange(bool) {
            this.form.ji = bool
        },
        handleChange() {

        },
        onCloseDialog() {
            this.visible = false
        },
    },
}
</script>
<style lang="scss" scoped>
.underline {
    border-bottom: 1px solid rgba(47, 169, 255, 0.2);
    padding-bottom: 10px;
}

.form {
    padding: 0 30px !important;
    margin-top: 20px;
    :deep(.el-input.is-disabled .el-input__inner) {
        // background-color: rgba(8, 105, 204, 0.15) !important;
        color: #7fbef8 !important;
        border-color: rgba(23, 133, 255, 0.27) !important;
    }
    :deep(.el-input-group__append) {
        border: 1px solid rgba(23, 133, 255, 0.27) !important;
        border-left-color: transparent !important;
    }
    :deep(.el-form-item__label) {
        color: #c2e1fe;
        font-size: 16px;
    }

    :deep(.el-radio) {
        color: #c2e1fe;
        width: 60px;
    }

    :deep(.el-radio__inner) {
        border: 1px solid rgba(23, 133, 255, 0.4);
        background: rgba(8, 105, 204, 0.2);

        &::after {
            background: #1193fd;
        }
    }

    :deep(.el-radio__input.is-checked+.el-radio__label) {
        color: #c2e1fe;
    }

    :deep(.el-input__wrapper), :deep(.el-input.is-disabled .el-input__wrapper),:deep(.el-select__wrapper){
        background: rgba(8, 105, 204, 0.15) !important;
        border: 1px solid rgba(23, 133, 255, 0.27) !important;
        color: #7fbef8 !important;
    }

    :deep(.el-input__inner) {
        color: #7fbef8;
    }

    :deep(.el-input-number__decrease) {
        width: 66px;
        height: 34px;
        background: #0056a9;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #ffffff;
        border-right: none;
    }

    :deep(.el-input-number__increase) {
        width: 66px;
        height: 34px;
        background: #0056a9;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #ffffff;
        border-left: none;
    }

    :deep(.el-input-number--medium) {
        width: 260px;
    }

    :deep(.el-input-number .el-input) {
        margin-left: 74px;
        width: 106px;
        height: 36px;
        background: rgba(8, 105, 204, 0.15);
        border: 1px solid rgba(23, 133, 255, 0.27);
        border-radius: 4px;
    }

    .cancel {
        width: 121px;
        border: 1px solid #1193fd;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        background-color: transparent;
        color: #6896d1;
    }

    .submit {
        width: 120px;
        background: linear-gradient(-46deg, #0056a9, #0a83e5);
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        font-size: 16px;
        font-weight: 500;
        color: #e3f1ff;
        border: none;
    }

    .click {
        width: 60px;
        height: 28px;
        background: #0056a9;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #c2e1fe;
        border: none;
        text-align: center;
        line-height: 28px;
        padding: 0;
    }

    .reset {
        width: 60px;
        height: 28px;
        background: #0056a9;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #c2e1fe;
        border: none;
        text-align: center;
        line-height: 28px;
        padding: 0;
    }
}

.newbottom {
    padding: 20px 20px 1px;
    width: 506px;
    background: rgba(8, 105, 204, 0.15);
    border-radius: 4px;
}

.newpoint {
    width: 464px;
    background: rgba(7, 15, 29, 0.55);
    border-radius: 4px;
    padding: 10px 0px 0px;
    margin-bottom: 10px;

    .newtop {
        // border-left: 2px solid #0075E6;
        margin-left: -20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        color: #c2e1fe;
        padding-left: 20px;
        padding-right: 20px;

        .borderLeft {
            width: 2px;
            height: 20px;
            background: #0075e6;
            flex-shrink: 0;
        }

        .new-top-right {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-around;
        }

        .newtoppic {
            margin-left: -10px;
        }
    }

    .newtbottom {
        padding-left: 20px;
        padding-bottom: 1px;
        padding-right: 20px;
    }
}

.con-bottom-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin-top: 20px;
    margin-bottom: 30px;

    .btn-cancel {
        width: 121px;
        height: 41px;
        border: 1px solid #1193fd;
        border-radius: 4px;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        color: #6896d1;
        text-align: center;
        line-height: 41px;
    }

    .btn-save {
        width: 120px;
        height: 40px;
        background: linear-gradient(-46deg, #0056a9, #0a83e5);
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #e3f1ff;
        text-align: center;
        line-height: 40px;
        margin-left: 20px;
    }
}

.tabs {
    width: 248px;
    height: 40px;
    background: rgba(91, 162, 227, 0.05);
    border: 1px solid #08599d;
    border-radius: 4px;
    margin: 0px auto 0;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .tabItem {
        width: 120px;
        height: 32px;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #e4f2ff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #7fbef8;
        opacity: 0.6;

        &.tabActive {
            background: rgba(1, 125, 255, 0.5);
            border: 0px solid #070002;
            opacity: 1;
            color: #ffffff;
        }
    }


}
</style>
