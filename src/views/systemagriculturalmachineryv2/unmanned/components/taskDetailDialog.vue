<template>
    <el-dialog
        title=""
        v-model="visible"
        width="630px"
        :append-to-body="false"
        :modal-append-to-body="false"
        @close="onCloseDialog"
    >
        <div>
            <img src="@/views/systemagriculturalmachineryv2/assets/images/img_task_detail.png" alt="" />
        </div>
      
        <div class="form" style="overflow: hidden; padding-bottom: 50px">
            <el-scrollbar :style="{ height: isHand ? '600px' : '' }">
                <el-form
                    ref="formRef"
                    :model="form"
                    label-width="90px"
                    label-position="left"
                    :show-message="false"
                    :rules="rules"
                    disabled
                >
                        <div style="padding: 0 20px 0 0">
                        <el-form-item label="任务编号" prop="assignTaskNo">
                            <el-input
                                v-model="taskDetail.assignTaskNo"
                                maxlength="30"
                                
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="任务名称" prop="assignTaskName">
                            <el-input
                                v-model="taskDetail.assignTaskName"
                                maxlength="30"
                            ></el-input>
                        </el-form-item>
                        <el-form-item label="作业类型" prop="assignType">
                            <el-select
                                v-model="taskDetail.assignType"
                                placeholder="请选择作业类型"
                                style="width: 100%"
                                :popper-append-to-body="false"
                            >
                                <el-option
                                    v-for="(item, index) in assignTypeOptions"
                                    :key="index"
                                    :label="item.name"
                                    :value="item.code"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="机具宽度" prop="mtWidth">
                            <el-input v-model.trim="taskDetail.mtWidth" class="append">
                                <template  #append>米</template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="接行宽度" prop="abutWidth">
                            <el-input v-model="taskDetail.abutWidth" class="append">
                                <template #append>厘米</template>
                            </el-input>
                        </el-form-item>
                        <el-form-item label="打点模式" prop="pointMode">
                            <el-radio-group v-model="form.pointMode">
                                <el-radio label="1">手动打点</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                        </div>
                        <div class="newbottom" v-if="isHand">
                            <!-- <el-form-item label="是否复用历史数据" label-width="140px" prop="useHistory">
                                <el-radio-group v-model="form.useHistory" @change="onChangeUseHistory">
                                    <el-radio label="2">否</el-radio>
                                    <el-radio label="1">是</el-radio>
                                </el-radio-group>
                            </el-form-item> -->
                            <!-- <el-form-item label="复用历史任务" label-width="140px" v-if="form.useHistory == '1'" prop="historyTask">
                                <el-select
                                    v-model="form.historyTask"
                                    placeholder="请选择历史任务"
                                    style="width: 100%"
                                    :popper-append-to-body="false"
                                    @change="onChangeHistoryTask"
                                >
                                    <el-option
                                        v-for="(item, index) in taskList"
                                        :key="index"
                                        :label="item.assignTaskNo"
                                        :value="index"
                                    ></el-option>
                                </el-select>
                            </el-form-item> -->
                            <el-form-item label="作业模式" label-width="140px" v-show="form.useHistory == '2'" prop="assignMode">
                                <el-radio-group v-model="taskDetail.assignMode">
                                    <!-- <el-radio v-for="(item, index) in assignModeOptions" :key="index" label="item.code">{{ item.name }}</el-radio> -->
                                    <el-radio label="0">AB点</el-radio>
                                    <el-radio label="3">ABCD点</el-radio>
                                    <!-- <el-radio label="9">多点</el-radio> -->
                                </el-radio-group>
                            </el-form-item>
                            <!-- <el-form-item class="underline" align="right" v-show="form.useHistory == '2'">
                                <el-button class="click" @click="setPoint">打点</el-button>
                                <el-button class="reset" @click="onReset"
                                    >重置</el-button
                                >
                            </el-form-item> -->
                                <dot-lon-lat-item v-for="(item, index) in taskDetail.pointList" :key="index" :label="item.label" :lon="item.lon" :lat="item.lat" v-model="item.safeDistance"
                             :icon="item.icon"/>
                            <div v-if="taskDetail.assignMode == '3' || taskDetail.assignMode == '9'">
                            <el-form-item label="路径模式" prop="routeMode">
                                <el-select
                                    v-model="taskDetail.routeMode"
                                    placeholder="请选择路径模式"
                                    style="width: 100%"
                                    :popper-append-to-body="false"
                                >
                                <el-option
                                    v-for="(item, index) in routeModeOptions"
                                    :key="index"
                                    :label="item.name"
                                    :value="item.code"
                                ></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="转弯方式" prop="turnMode">
                                <el-select
                                    v-model="taskDetail.turnMode"
                                    placeholder="请选择转弯方式"
                                    style="width: 100%"
                                    :popper-append-to-body="false"
                                >
                                <el-option
                                    v-for="(item, index) in turnModeOptions"
                                    :key="index"
                                    :label="item.name"
                                    :value="item.code"
                                ></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="进口点" prop="importPlace">
                                <el-select
                                    v-model="taskDetail.importPlace"
                                    placeholder="请选择进口点"
                                    style="width: 100%"
                                    :popper-append-to-body="false"
                                >
                                    <el-option
                                    v-for="(item, index) in importExitPlaceOptions"
                                        :key="index"
                                        :label="item.name"
                                        :value="item.code"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item label="出口点" prop="exportPlace">
                                <el-select
                                    v-model="taskDetail.exportPlace"
                                    placeholder="请选择出口点"
                                    style="width: 100%"
                                    :popper-append-to-body="false"
                                >
                                <el-option
                                    v-for="(item, index) in importExitPlaceOptions"
                                        :key="index"
                                        :label="item.name"
                                        :value="item.code"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                            <!-- <el-form-item label="是否收边" prop="sideType">
                                <el-select
                                    v-model="form.sideType"
                                    placeholder="请选择是否收边"
                                    style="width: 100%"
                                    :popper-append-to-body="false"
                                >
                                <el-option
                                    v-for="(item, index) in sideTypeOptions"
                                    :key="index"
                                    :label="item.name"
                                    :value="item.code"
                                ></el-option>
                                </el-select>
                            </el-form-item> -->
                            <el-form-item label="转弯半径" prop="turnRadius">
                                <el-input
                                    v-model="taskDetail.turnRadius"
                                    class="append"
                                    placeholder="请输入转弯半径"
                                >
                                    <template #append>米</template></el-input
                                >
                            </el-form-item>
                            </div>
                        </div>
                        <!-- <div class="con-bottom-btn">
                            <div class="btn-cancel" @click="onCloseDialog">取消</div>
                            <div class="btn-save" @click="onSave">保存并下发</div>
                        </div> -->
                </el-form>
            </el-scrollbar>
        </div>
    </el-dialog>
</template>
<script>
import dotLonLatItem from './dotLonLatItem.vue';
import { sendTask, apiGetHistTaskDetail, taskQueryByPage } from '@/api/systemagriculturalmachineryv2/unmanned/unmanned';
import { formatDate } from '@/views/systemagriculturalmachineryv2/utils';
const LETTER = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'; 
import { getDicts } from "@/api/systemagriculturalmachineryv2/dict";
export default {
    name: 'taskDetailDialog',
    components: { dotLonLatItem },
    props: {
        modelValue: {
            type: Boolean,
            default: false,
        },
        // 当前农机信息
        currentMachine: {
            type: Object,
            default: () => ({})
        },
        taskDetail: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        const numReg = /^[0-9]+(\.?[0-9]{1,2})?$/;
        const validateAssignMode = (rule, value, callback) => {
            if(value === '') {
                callback(new Error('请选择作业模式'))
            } else if(this.form.umMachineAssignTaskPlaces.length) {
                callback()
            } else {
                callback(new Error('请打点'))
            }
        }
        const validateHistoryTask = (rule, value, callback) => {
            if(value === '') {
                callback(new Error('请选择历史任务'))
            } else {
                callback()
            }
        }
        const validateRequired = (value, callback, msg) => {
            if(value === '') {
                callback(new Error(msg))
            } else {
                callback();
            }
        }
        const validateNum = (rule, value, callback) => {
            if (value === null || value === "" || typeof value == "undefined") {
                callback();
            } else if (value.toString().split(".")[0].length > 10) {
                callback(new Error("数字过大，请确认"));
            } else if (numReg.test(value)) {
                callback();
            } else {
                callback(new Error("请输入数字(最多带两位小数)"));
            }
        };
        const validateMtWidth = (rule, value, callback) => {
            const num = Number(value);
            if(Number.isNaN(num) || !numReg.test(value)) {
                callback(new Error('请输入数字(最多带两位小数)'))
            } else if(num<0 || num>99.99) {
                callback(new Error('机具宽度范围0～99.99'))
            } else {
                callback()
            }
        }
        const validateAbutWidth = (rule, value, callback) => {
            const reg = /^[1-9]\d*$/
            const num = Number(value);
            if(Number.isNaN(num) || !reg.test(value)) {
                callback(new Error('请输入正整数'))
            } else if(num<0 || num>999) {
                callback(new Error('接行宽度范围0～999'))
            } else {
                callback()
            }
        }
        const validateTurnRadius = (rule, value, callback) => {
            const reg = /^[0-9]+(.?[0-9])?$/;
            const num = Number(value);
            if(Number.isNaN(num) || !reg.test(value)) {
                callback(new Error('请输入数字(最多保留1位小数)'))
            } else {
                callback();
            }
        }
        return {
            saveLoading: false,
            taskList: [

            ],
            historyTaskDetail: {}, // 存储历史任务的点位信息
            historyTaskList: [
                {
                    assignTaskNo: 'TASK-1111',
                    assignMode: '0',
                    umMachineAssignTaskPlaces: [
                    {
                        "placeCode": "A",
                        "longitude": 126.627935428255,
                        "latitude": 48.15802604740293,
                        "safeDistance": ""
                    },
                    {
                        "placeCode": "B",
                        "longitude": 126.63792564370556,
                        "latitude": 48.1426950363327,
                        "safeDistance": ""
                    }
                    ]
                },
                {
                    assignTaskNo: 'TASK-2222',
                    assignMode: '3',
                    routeMode: '1',
                    turnMode: '1',
                    importPlace: 'A',
                    exportPlace: 'D',
                    sideType: '1',
                    turnRadius: '20',
                    umMachineAssignTaskPlaces: [
                        {
                            "placeCode": "A",
                            "longitude": 126.6302807765424,
                            "latitude": 48.14794162691291,
                            "safeDistance": "11"
                        },
                        {
                            "placeCode": "B",
                            "longitude": 126.63482110019481,
                            "latitude": 48.129166213826664,
                            "safeDistance": "22"
                        },
                        {
                            "placeCode": "C",
                            "longitude": 126.66086032509799,
                            "latitude": 48.13016421685839,
                            "safeDistance": "33"
                        },
                        {
                            "placeCode": "D",
                            "longitude": 126.65807121298297,
                            "latitude": 48.1600759369058,
                            "safeDistance": "44"
                        }
                    ]
                },
                {
                    assignTaskNo: 'TASK-3333',
                    assignMode: '9',
                    routeMode: '1',
                    turnMode: '1',
                    importPlace: 'A',
                    exportPlace: 'E',
                    sideType: '1',
                    turnRadius: '30',
                    umMachineAssignTaskPlaces: [
                        {
                            "placeCode": "A",
                            "longitude": 126.63038040906173,
                            "latitude": 48.15662524401645,
                            "safeDistance": "12"
                        },
                        {
                            "placeCode": "B",
                            "longitude": 126.63698946165056,
                            "latitude": 48.13699537781653,
                            "safeDistance": "13"
                        },
                        {
                            "placeCode": "C",
                            "longitude": 126.65811223231589,
                            "latitude": 48.13646610647089,
                            "safeDistance": "14"
                        },
                        {
                            "placeCode": "D",
                            "longitude": 126.66389798990923,
                            "latitude": 48.15849629863936,
                            "safeDistance": "15"
                        },
                        {
                            "placeCode": "E",
                            "longitude": 126.64954081880427,
                            "latitude": 48.186825357280185,
                            "safeDistance": "16"
                        }
                    ]
                }
                
            ],
            isAllReset: true, // 标识是否需要重置表单
            pointList: [],
            assignTypeOptions: [], // 作业类型选项
            assignModeOptions: [], // 作业模式选项
            routeModeOptions: [], // 路径模式选项
            turnModeOptions: [], // 转弯模式选项
            sideTypeOptions: [], // 是否收边选项
            importPlaceOptions: [], // 进出口选项
            form: {
                machineId: '', // 农机id
                assignTaskNo: '', // 任务编号
                assignTaskName: '', // 任务名称
                assignType: '', // 作业类型
                mtWidth: '3.90', // 机具宽度（米）
                abutWidth: '66', // 接行宽度（厘米）
                assignMode: '', // 作业模式
                routeMode: '0', // 路径模式
                turnMode: '0', // 转弯模式
                importPlace: '', // 进口
                exportPlace: '', // 出口
                sideType: '0', // 是否收边
                turnRadius: '', // 转弯半径（米）
                taskStartTime: '', // 任务开始时间
                taskEndTime: '', // 任务结束时间
                umMachineAssignTaskPlaces: [
                    // {
                    //     placeCode: '', // 位置代码
                    //     longitude: '', // 经度
                    //     latitude: '', // 纬度
                    //     safeDistance: '', // 距下一位置安全距离(米)
                    // }
                ], // 位置信息
                pointMode: '1', // 打点模式
                useHistory: '2', // 是否复用历史数据
                historyTask: '',
            },
            rules: {
                assignTaskNo: [
                    {
                        required: true,
                        message: '请输入任务编号',
                        trigger: 'change',
                    },
                ],
                assignTaskName: [
                    {
                        required: true,
                        message: '请输入任务名称',
                        trigger: 'change',
                    },
                ],
                assignType: [
                    {
                        required: true,
                        message: '请选择作业类型',
                        trigger: 'change',
                    },
                ],
                mtWidth: [
                    {
                        required: true,
                        message: '请输入机具宽度',
                        trigger: 'change',
                    },
                    {
                        validator: validateMtWidth,
                    }
                ],
                abutWidth: [
                    {
                        required: true,
                        message: '请输入接行宽度',
                        trigger: 'change',
                    },
                    {
                        validator: validateAbutWidth,
                    }
                ],
                pointMode: [
                    {
                        required: true,
                        message: '请选择打点模式',
                        trigger: 'change',
                    },
                ],
                assignMode: [
                    {
                        validator: validateAssignMode,
                        trigger: ['change', 'blur']
                    }
                ],
                historyTask: [
                    {
                        validator: validateHistoryTask,
                        trigger: 'change'
                    }
                ],
                routeMode: [
                    {
                        validator: (rule, value, callback) => validateRequired(value, callback, '请选择路径模式')
                    }
                ],
                turnMode: [
                    {
                        validator: (rule, value, callback) => validateRequired(value, callback, '请选择转弯模式')
                    }
                ],
                importPlace: [
                    {
                        validator: (rule, value, callback) => validateRequired(value, callback, '请选择进口点')
                    }
                ],
                exportPlace: [
                    {
                        validator: (rule, value, callback) => validateRequired(value, callback, '请选择出口点')
                    }
                ],
                sideType: [
                    {
                        validator: (rule, value, callback) => validateRequired(value, callback, '请选择是否收边')
                    }
                ],
                turnRadius: [
                    {
                        validator: (rule, value, callback) => validateRequired(value, callback, '请输入转弯半径')
                    },
                    {
                        validator: validateTurnRadius,
                    }
                ],
            },
            requestHeadLovol: {}, // 雷沃请求信息
            currentNavData: {}, // 当前农机实时数据
        }
    },
    computed: {
        visible: {
          get() {
            return this.modelValue
          },
          set(val) {
            this.$emit('update:modelValue', val)
          }
        },
        isHand() {
            return this.form.pointMode == '1'
        },
        importExitPlaceOptions() {
            // const list = this.importPlaceOptions.slice(0, this.pointList.length);
            // console.log('>>>>>进出口list：', list)
            // this.form.importPlace = list[0].code;
            // this.form.exportPlace = list[list.length -1].code;
            // return this.importPlaceOptions.slice(0, this.pointList.length);
            return this.importPlaceOptions
        },
    },
    watch: {
        importExitPlaceOptions(list) {
            console.log('>>>>>>>watch-importExitPlaceOptions: ', list)
            if(list.length > 2 && this.form.importPlace == '' && this.form.exportPlace == '') {
              this.form.importPlace = list[0].code;
              this.form.exportPlace = list[list.length -1].code;
                
            } else if(list.length <= 2){
                this.form.importPlace = '';
                this.form.exportPlace = '';
            }
        },
    },
    created() {
        this.getAllDicts();
    },
    methods: {
        async getAllDicts() {
            try {
                const [{data: assignTypeRes}, {data: assignModeRes}, {data: routeModeRes}, {data: turnModeRes}, {data: sideTypeRes}, { data: importPlace }] = await Promise.all([getDicts("assign_type"), getDicts("assign_mode"), getDicts('route_mode'), getDicts('turn_mode'), getDicts('side_type'), getDicts('import_place')]);
                console.log('>>>>>>>>>当前农机信息：', this.currentMachine)
                this.assignTypeOptions = assignTypeRes;
                this.assignModeOptions = assignModeRes;
                this.routeModeOptions = routeModeRes;
                this.turnModeOptions = turnModeRes;
                this.sideTypeOptions = sideTypeRes;
                this.importPlaceOptions = importPlace;
                console.log('>>>>>>>>>进出口字典：', importPlace)
                console.log('>>>>>>>>>assignModeRes: ', assignModeRes)
            } catch (error) {
                console.log('>>>>>>>>getAllDicts-err: ', error)
            }
        },
        onCloseDialog() {
            // if(this.isAllReset) {
            //     console.log('>>>>>>>>关闭弹窗并重置表单')
            //     this.$nextTick(() => {
            //         try {
                        
            //             this.$refs['formRef'].resetFields();
            //         } catch (error) {
            //            console.log('>>>>>>.重置表单err: ', error) 
            //         }
            //         this.onReset();
            //     })
            // } else {
            //     this.isAllReset = true;
            // }
            this.visible = false
            // this.$emit('update:visible', false)
        }, 
    },
}
</script>
<style lang="scss" scoped>
.underline {
  border-bottom: 1px solid rgba(47, 169, 255, 0.2);
  padding-bottom: 10px;
}

.form {
    padding: 0 10px 20px 30px;
    margin-top: 20px;

    :deep(.el-form-item__label) {
        color: #c2e1fe;
        font-size: 16px;
    }

    :deep(.el-radio) {
        color: #c2e1fe !important;
        width: 60px;
    }

    :deep(.el-radio__inner) {
        border: 1px solid rgba(23, 133, 255, 0.4) !important;
        background: rgba(8, 105, 204, 0.2) !important;

        &::after {
            background: #1193fd !important;
        }
    }

    :deep(.el-radio__input.is-checked + .el-radio__label) {
        color: #c2e1fe;
    }

    :deep(.el-input__wrapper), :deep(.el-input.is-disabled .el-input__wrapper),:deep(.el-select__wrapper){
        background: rgba(8, 105, 204, 0.15) !important;
        border: 1px solid rgba(23, 133, 255, 0.27) !important;
        color: #7fbef8 !important;
    }
    
    :deep(.el-input__inner) {
        color: #7fbef8;
    }

    :deep(.el-input-number__decrease) {
        width: 66px;
        height: 34px;
        background: #0056a9;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #ffffff;
        border-right: none;
    }

    :deep(.el-input-number__increase) {
        width: 66px;
        height: 34px;
        background: #0056a9;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #ffffff;
        border-left: none;
    }

    :deep(.el-input-number--medium) {
        width: 260px;
    }

    :deep(.el-input-number .el-input) {
        margin-left: 74px;
        width: 106px;
        height: 36px;
        background: rgba(8, 105, 204, 0.15);
        border: 1px solid rgba(23, 133, 255, 0.27);
        border-radius: 4px;
    }

    .cancel {
        width: 121px;
        border: 1px solid #1193fd;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        background-color: transparent;
        color: #6896d1;
    }

    .submit {
        width: 120px;
        background: linear-gradient(-46deg, #0056a9, #0a83e5);
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        font-size: 16px;
        font-weight: 500;
        color: #e3f1ff;
        border: none;
    }

    .click {
        width: 60px;
        height: 28px;
        background: #0056a9;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #c2e1fe;
        border: none;
        text-align: center;
        line-height: 28px;
        padding: 0;
    }

    .reset {
        width: 60px;
        height: 28px;
        background: #0056a9;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #c2e1fe;
        border: none;
        text-align: center;
        line-height: 28px;
        padding: 0;
    }
        //鼠标经过下拉框
    :deep(.el-select-dropdown__item.hover),
    :deep(.el-select-dropdown__item:hover) {
        color: #00d3e9;
        background-color: #0f3360;
    }
}

.newbottom {
    padding: 20px 20px 1px;
    width: 530px;
    background: rgba(8, 105, 204, 0.15);
    border-radius: 4px;
}

.newpoint {
    width: 464px;
    background: rgba(7, 15, 29, 0.55);
    border-radius: 4px;
    padding: 10px 0px 0px;
    margin-bottom: 10px;

    .newtop {
        // border-left: 2px solid #0075E6;
        margin-left: -20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        color: #c2e1fe;
        padding-left: 20px;
        padding-right: 20px;

        .borderLeft {
            width: 2px;
            height: 20px;
            background: #0075e6;
            flex-shrink: 0;
        }
      .new-top-right {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-around;
      }
        .newtoppic {
            margin-left: -10px;
        }
    }

    .newtbottom {
        padding-left: 20px;
        padding-bottom: 1px;
        padding-right: 20px;
    }
}
.con-bottom-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin-top: 20px;
    margin-bottom: 30px;
    .btn-cancel {
        width: 121px;
        height: 41px;
        border: 1px solid #1193fd;
        border-radius: 4px;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        color: #6896d1;
        text-align: center;
        line-height: 41px;
    }
    .btn-save {
        width: 120px;
        height: 40px;
        background: linear-gradient(-46deg, #0056a9, #0a83e5);
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #e3f1ff;
        text-align: center;
        line-height: 40px;
        margin-left: 20px;
    }
}
</style>
