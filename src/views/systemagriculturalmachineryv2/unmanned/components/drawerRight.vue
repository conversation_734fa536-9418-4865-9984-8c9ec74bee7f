<template>
    <div class="drawerright">
        <div class="close" @click="changedrawerRight">
            <img src="@/views/systemagriculturalmachineryv2/assets/images/rightmore.png" alt="" v-show="drawerRight">
            <img src="@/views/systemagriculturalmachineryv2/assets/images/right_more.png" alt="" v-show="!drawerRight">
        </div>
        <div class="top">
            <img src="@/views/systemagriculturalmachineryv2/assets/images/top.png" class="img" alt="">
        </div>
        <img src="@/views/systemagriculturalmachineryv2/assets/images/side_light.png" class="side_light" alt="">
        <img src="@/views/systemagriculturalmachineryv2/assets/images/light.png" class="light" alt="">
        <div class="list">
            <div class="title">
                <img src="@/views/systemagriculturalmachineryv2/assets/images/Homework.png" alt="">
            </div>
            <div class="box mapbox">
                <div style="width: 100%;height: 100%;">
                    <!-- <monitor-center-map ref="map" :isBig="false" @mapChange="mapChange" mapContainerId="c2781101-34a8-4eef-af77-b010e73473c77" /> -->
                    <track-map ref="trackMapRef" @dblclick.native="onDblclickMap" @mapChange="mapChange"/>
                </div>
            </div>
        </div>
        <div class="list">
            <div class="title">
                <img src="@/views/systemagriculturalmachineryv2/assets/images/realtime.png" alt="">
            </div>
            <div class="box" style="height: calc(100% - 73px);">
                <el-scrollbar style="height:100%">
                    <div class="boxheader" style="margin-bottom: 10px;">
                        <div class="headeritem RegularEnglish left">发动机工作时长</div>
                        <div class="headeritem RegularEnglish right">{{ currentNavData.tboxData ? currentNavData.tboxData.c0 : '' }}h</div>
                    </div>
                    <div class="boxheader" style="margin-bottom: 10px;">
                        <div class="headeritem RegularEnglish left">经度</div>
                        <div class="headeritem RegularEnglish right">{{ currentNavData.lon }}</div>
                    </div>
                    <div class="boxheader" style="margin-bottom: 10px;">
                        <div class="headeritem RegularEnglish left">纬度</div>
                        <div class="headeritem RegularEnglish right">{{ currentNavData.lat }}</div>
                    </div>
                    <div class="boxheader" style="margin-bottom: 10px;">
                        <div class="headeritem RegularEnglish left">油位</div>
                        <div class="headeritem RegularEnglish right">{{ currentNavData.tboxData ? currentNavData.tboxData.b7 : '' }}%</div>
                    </div>
                    <div class="boxheader" style="margin-bottom: 10px;">
                        <div class="headeritem RegularEnglish left">冷却液温度</div>
                        <div class="headeritem RegularEnglish right">{{ currentNavData.tboxData ? currentNavData.tboxData.gk : '' }}­°C</div>
                    </div>

                </el-scrollbar>
            </div>
        </div>
        <div class="list">
            <div class="title">
                <img src="@/views/systemagriculturalmachineryv2/assets/images/carVideo.png" alt="">
            </div>
            <div class="box" style="height: calc(100% - 73px);">
                <el-scrollbar style="height:100%">
                    <div class="videoparent" style="height:100%">
                        <video id="flv-video1" class="video" muted="muted" autoplay  @click="videoChange"></video>
                        <!-- <video :src="liveUrl" autoplay loop muted class="video" @click="videoChange"></video>
                        <video :src="mp4" autoplay loop muted class="video" @click="videoChange"></video>
                        <video :src="mp4" autoplay loop muted class="video" @click="videoChange"></video> -->
                    </div>
                </el-scrollbar>
            </div>
        </div>
    </div>
</template>

<script>
import {
    Map as MonitorCenterMap,
} from "./index";
import TrackMap from "./trackMap.vue"
import flvJs from 'flv.js';
import { getLookAroundOnlineStatus, playLookAroundInfo, closeLookAround } from '@/api/systemagriculturalmachineryv2/unmanned/unmanned'
const debounce = (function () {
    let timer = 0
    return function (fn, timeout) {
        if (timer) {
            clearTimeout(timer)
            timer = null
        }
        clearTimeout(timer)
        timer = setTimeout(() => {
            fn.apply(this, arguments)
        }, timeout || 300)
    }
})()
export default {
    name: 'drawerRight',
    components: { MonitorCenterMap, TrackMap },
    props: {
        drawerRight: {
            type: Boolean,
            default: true
        },
        currentMachine: {
            type: Object,
            default: () => ({}),
        }
    },
    data() {
        return {
            player1: null,
            liveUrl: '',
            // liveUrl: 'https://iot.lovol.com:8892/video/013496388889-1',
            // liveUrl: 'https://sf1-hscdn-tos.pstatp.com/obj/media-fe/xgplayer_doc_video/flv/xgplayer-demo-360p.flv',
            clientId: '445566', // 360设备编号
            requestHeadLovol: {},
            currentNavData: {
                "ts": null, // 时间戳
                "lon": 126.************, // 经度
                "lat": 48.15802604740293, // 纬度
                "status": null, // 作业状态
                "process": null, // 进度
                "currentheadingofthemachine": null, // 航向
                "currentjobspeed": null, // 当前速度
                "enginespeed": null, // 发动机转速
                "uuid": null, // 设备唯一标识
                "controlModel": null, // 控制模式
                "numberofsatellitescaptured": null, // 捕获卫星数目
                "rtksignalidentification": null, // RTK信号标识
                "simNum": null, // sim卡个数
                "sim1OPERATOR": null, // sim1运营商
                "sim1NETWORKSYSTEM": null, // sim1网络制式
                "sim1SIGNALSTRENGTH": null, // sim1信号强度
                "sim2OPERATOR": null,
                "sim2NETWORKSYSTEM": null,
                "sim2SIGNALSTRENGTH": null,
                "cucrrnetjobstartstime": null, // 当前作业开始时间
                "operationmode": null, // 作业模式
                "operationtype": null, // 作业类型
                "machinewidth": null, // 机具宽度
                "linewidth": null, // 接行宽度
                "navigationsystemstatusinformation": 0, // 导航系统状态信息
                "gnssdifferentialstate": null, // GNSS差分状态
            }
        }
    },
    mounted() {
    },
    computed: {
        currentDrawerRight: {
            get() {
                return this.drawerRight
            },
            set(val) {
                this.$emit('update:drawerRight', val)
            }
        },
    },
    watch: {

    },
    mounted() {
        let lon = 126.************;
        let lat = 48.15802604740293;
        this.$bus.$on('navData', (val) => {
            console.log('>>>>>>右侧弹窗接收到的实时数据：', val)
            this.currentNavData = val;
            // // TODO 演示数据
            // lon += 0.000001;
            // lat += 0.000001;
            // this.$refs['trackMapRef']?.setMachinePlace([lon, lat], +val.currentheadingofthemachine)

            this.$refs['trackMapRef']?.setMachinePlace([+val.lon, +val.lat], +val.currentheadingofthemachine)
            if(val.requestHeadLovol.deviceNo != this.requestHeadLovol.deviceNo) {
                console.log('>>>>>>设备号不一致-开始赋值')
                this.requestHeadLovol = val.requestHeadLovol;
                this.getFlvUrl()
            }
        })
    },
    methods: {
        onDblclickMap() {
            console.log('>>>>>>>>双击轨迹地图')
            this.$emit('dblclickTrackMap')
        },
        showMessage(msg) {
            this.$message({
                dangerouslyUseHTMLString: true,
                customClass: 'messageboxcustomClass',
                duration: 3000,
                offset: 200,
                message: '<div class="content"><div class="title">消息提示</div><div class="text">' + msg + '</div></div>'
            });
        },
        async getFlvUrl() {
            try {
                console.log('>>>>>获取flv')
                const { deviceNo, account, controlDeviceId, controlDeviceType, expire,  } = this.requestHeadLovol;
                const { clientId } = this.currentMachine;
                const res1 = await getLookAroundOnlineStatus({
                    deviceNo, account, controlDeviceId, controlDeviceType, expire, clientId 
                });
                console.log('>>>>>>>>360设备是否在线: ', res1);

                if(res1.data) {
                    // 360环视在线
                    const res2 = await playLookAroundInfo({
                        deviceNo, account, controlDeviceId, controlDeviceType, expire, clientId 
                    })
                    if(res2.data) {
                        this.liveUrl = res2.data;
                        this.destroyVideos();
                        this.createPlay('flv-video1', this.liveUrl);

                    }
                    console.log('>>>>>>>>>播放360设备：', res2);
                } else {
                    this.showMessage('视频设备处于离线状态')
                }
            } catch (error) {
                console.log('>>>>>>>>360err: ', error)
            }
        },
        async close360() {
            try {
                const { deviceNo, account, controlDeviceId, controlDeviceType, expire, clientId } = this.requestHeadLovol;
                const res = await closeLookAround({
                    deviceNo, account, controlDeviceId, controlDeviceType, expire, clientId 
                })
                console.log('>>>>>>>>>关闭360: ', res)
            } catch (err) {
                console.log('>>>>>>关闭360err：', err)
            }
        },
        mapChange() {
            this.$emit('mapChange')
        },
        videoChange() {
            debounce(() => {
                this.$emit('videoChange', this.liveUrl)
            })
        },
        changedrawerRight() {
            this.currentDrawerRight = !this.currentDrawerRight
        },
        destroyVideos(){
            if(!this.player1) return
            this.player1.pause()
            this.player1.unload()
            this.player1.detachMediaElement()
            this.player1.destroy()
            this.player1 = null
        },
        createPlay(id, url) {
            if(flvJs.isSupported()) {
                console.log('>>>>>>.支持flv')
                this.player1 = null;
                const videoDom = document.getElementById(id);
                const player = flvJs.createPlayer({
                    type: 'flv', // 媒体类型
                    isLive: true, // 是否为直播流
                    hasAudio: false, // 是否有音频
                    hasVideo: true, // 是否有视频
                    url,
                }, {
                    autoCleanupMinBackwardDuration: true, // 清除缓存 对SourceBuffer进行自动清理
                });
                player.attachMediaElement(videoDom);
                player.load();
                player.play();
                this.player1 = player;
                this.player1.on(flvJs.Events.ERROR, (errorType, errorDetail, errorInfo) => {
                    console.log('>>>>>>errorType: ', errorType)
                    console.log('>>>>>>errorDetail: ', errorDetail)
                    console.log('>>>>>>errorInfo: ', errorInfo)
                    if(this.player1) {
                        this.player1.pause();
                        this.player1.unload();
                        this.player1.detachMediaElement();
                        this.player1.destroy();
                        this.player1 = null;
                        setTimeout(() => {
                            // this.createPlay(id, url)
                            this.getFlvUrl()
                        }, 0)
                    }
                })
            }
        },
    }
}
</script>

<style scoped lang="scss">
::-webkit-scrollbar-corner {
    background-color: inherit !important;
}

:deep(.input .el-input__inner) {
    background-color: transparent;
    border: none;
    color: #fff;
}

    :deep(.el-select-dropdown__list) {
        background-color: #030a27;
    }

    :deep(.el-select-dropdown) {
        border: 1px solid #08599D;
    }

    :deep(.el-popper[x-placement^=bottom] .popper__arrow::after) {
        border-bottom-color: #08599D;
    }

    :deep(.el-popper[x-placement^=bottom] .popper__arrow) {
        border-bottom-color: #08599D;
    }

    :deep(.el-scrollbar__wrap) {
        overflow-x: hidden;
    }

    :deep(.el-input-group__append) {
        background-color: rgba(8, 105, 204, 0.15);
        border: 1px solid rgba(23, 133, 255, 0.27);
        border-left: none;
    }

    :deep(.append .el-input__inner) {
        border-right: none !important;
    }

.form {
    :deep(.el-select-dropdown__list) {
        background-color: #030a27;
    }

    //下拉框
    :deep(.el-select-dropdown) {
        border: none;
        background-color: rgba(1, 28, 82, 0.8);
    }

    //输入框
    :deep(.el-input__inner) {
        color: #eee;
        border-color: #00fff6;
        background-color: rgba(1, 28, 82, 0.8);
    }

    //聚焦时的样式
    :deep(.el-select .el-input.is-focus .el-input__inner) {
        border-color: #0B61AA;
        background-color: rgba(1, 28, 82, 0.8);
        color: #00D3E9;
    }

    //下拉框选中
    :deep(.el-select-dropdown__item) {
        color: #eee;
    }

    //鼠标经过下拉框
    :deep(.el-select-dropdown__item.hover),
    :deep(.el-select-dropdown__item:hover) {
        color: #00D3E9;
        background-color: #0F3360;
    }
}


:deep(.el-slider__button-wrapper) {
    border: 1px solid #62AAFE;
    opacity: 0.5;
    border-radius: 50%;
    width: 27px;
    height: 27px;
    top: -10px;
    margin-left: -4px;
}

:deep(.el-slider__button) {
    width: 12px;
    height: 12px;
    background: #99C8FF;
    border-radius: 50%;
}
:deep(.el-scrollbar__view){
    height: 100%;
}

:deep(.el-slider__runway) {
    background: rgba(8, 124, 255, 0.1);
    border-radius: 4px;
    height: 9px;
}

:deep(.el-slider__bar) {
    background: linear-gradient(90deg, #061733, #1C8AFF);
    border-radius: 4px;
    height: 9px;
}

:deep(.el-slider__stop) {
    display: none;
}

:deep(.el-slider__marks-text) {
    width: max-content;
    margin-top: 20px;
}

.drawerright {
    transition: all 1s;
    width: 500px;
    background-color: rgba(3, 10, 39, 0.7);
    position: absolute;
    right: 0;
    top: 80px;
    height: calc(100% - 90px);
    border: 1px solid #196ab9;
    display: flex;
    flex-direction: column;
    z-index: 40;

    .close {
        position: absolute;
        left: -29px;
        top: calc(50% - 55px);
        cursor: pointer;
    }

    .top {
        position: absolute;
        width: 100%;
        left: 0;
        top: 0;

        .img {
            width: 100%;
            height: auto;
        }
    }

    .side_light {
        position: absolute;
        left: -12px;
        top: 0;
    }

    .light {
        position: absolute;
        bottom: -2px;
        right: 95px;
    }

    .list {
        flex: 1;
        overflow: hidden;

        .title {
            margin-left: -15px;
        }

        .search {
            margin: 0 auto;
            width: 439px;
            height: 36px;
            background: rgba(8, 105, 204, 0.15);
            border: 1px solid rgba(23, 133, 255, 0.27);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding-right: 4px;

            .input {}

            .btn {
                width: 70px;
                height: 28px;
                background: linear-gradient(-46deg, #0056A9, #0A83E5);
                border-radius: 4px;
                font-size: 16px;
                font-weight: 500;
                color: #C2E1FE;
                line-height: 28px;
                text-align: center;
                cursor: pointer;
            }
        }

        .box {
            margin-top: 20px;

            .boxheader {
                width: 467px;
                min-height: 46px;
                margin: 0 auto;
                background-image: url("@/views/systemagriculturalmachineryv2/assets/images/lat.png");
                background-size: cover;
                display: flex;
                align-items: center;
                font-size: 16px;
                color: #C2E1FE;
                padding: 0 40px;

                .headeritem {
                    flex: 1;
                    text-align: center;
                }

                .left {
                    text-align: left;
                }

                .right {
                    text-align: right;
                }
            }

            // .boxbody {
            //     background-image: url("@/views/systemagriculturalmachineryv2/assets/images/lat.png");
            //     color: #C2E1FE;
            //     font-size: 14px;

            // }

            .videoparent {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                justify-content: space-around;
                padding: 0 20px;

                .video {
                    width: 420px;
                    height: calc(100% - 20px);
                    background-color: rgba(3, 10, 39, 0.7);
                    margin-bottom: auto;
                    object-fit: cover;

                    &:hover{
                        cursor: pointer;
                    }
                }
            }

        }

        .mapbox {
            height: calc(100% - 63px);
            width: calc(100% - 40px);
            margin-top: 0px;
            margin-left: 20px;
            border: 3px solid rgba(13, 142, 254, 0.25);
            border-radius: 8px;
            box-sizing: border-box;
            overflow: hidden;
        }
    }
}



:deep(.el-dialog__wrapper) {
    position: absolute;
}

:deep(.el-dialog) {
    background: url('@/views/systemagriculturalmachineryv2/assets/images/bg.png') no-repeat !important;
    background-size: 100% 100% !important;
    padding-bottom: 20px;
}

:deep(.el-dialog__title) {
    position: absolute;
    top: 5%;
    left: 0%;
    display: block;
    width: 100%;
    text-align: center;
    color: #fff !important;
    font-weight: 700;
    font-size: 23px !important;
}

:deep(.el-dialog__body ){}

:deep(.el-dialog__headerbtn) {
    top: 75px;
    right: 40px;
}

:deep(.el-dialog__body .abc) {
    height: 565px;
    overflow: auto;
}

:deep(.el-dialog__body .myCon p) {
    background-color: #0d4e68 !important;
    color: #fff
}

.tabs {
    width: 248px;
    height: 40px;
    background: rgba(91, 162, 227, 0.05);
    border: 1px solid #08599D;
    border-radius: 4px;
    margin: 0px auto 0;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .tabItem {
        width: 120px;
        height: 32px;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #E4F2FF;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #7FBEF8;
        opacity: 0.6;
    }

    .tabActive {
        background: rgba(1, 125, 255, 0.5);
        border: 0px solid #070002;
        opacity: 1;
        color: #ffffff;
    }

}

.form {
    padding: 0 50px 0 30px;
    margin-top: 20px;

    :deep(.el-form-item__label) {
        color: #C2E1FE;
        font-size: 16px;
    }

    :deep(.el-radio) {
        color: #C2E1FE;
        width: 60px;
    }

    :deep(.el-radio__inner) {
        border: 1px solid rgba(23, 133, 255, 0.4);
        background: rgba(8, 105, 204, 0.2);

        &::after {
            background: #1193FD;
        }
    }

    :deep(.el-radio__input.is-checked+.el-radio__label) {
        color: #C2E1FE;
    }

    :deep(.el-input__inner) {
        background: rgba(8, 105, 204, 0.15);
        border: 1px solid rgba(23, 133, 255, 0.27);
        color: #7FBEF8;
    }

    :deep(.el-input-number__decrease){
        width: 66px;
        height: 34px;
        background: #0056A9;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #ffffff;
        border-right: none;
    }

    :deep(.el-input-number__increase) {
        width: 66px;
        height: 34px;
        background: #0056A9;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #ffffff;
        border-left: none;
    }

    :deep(.el-input-number--medium) {
        width: 260px;
    }

    :deep(.el-input-number .el-input) {
        margin-left: 74px;
        width: 106px;
        height: 36px;
        background: rgba(8, 105, 204, 0.15);
        border: 1px solid rgba(23, 133, 255, 0.27);
        border-radius: 4px;
    }

    .cancel {
        width: 121px;
        border: 1px solid #1193FD;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        background-color: transparent;
        color: #6896D1;
    }

    .submit {
        width: 120px;
        background: linear-gradient(-46deg, #0056A9, #0A83E5);
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        font-size: 16px;
        font-weight: 500;
        color: #E3F1FF;
        border: none;
    }

    .click {
        width: 60px;
        height: 28px;
        background: #0056A9;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #C2E1FE;
        border: none;
        text-align: center;
        line-height: 28px;
        padding: 0;
    }

    .reset {
        width: 60px;
        height: 28px;
        background: #0056A9;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #C2E1FE;
        border: none;
        text-align: center;
        line-height: 28px;
        padding: 0;
    }

}

.newbottom {
    padding: 20px;
    width: 506px;
    height: 1011px;
    background: rgba(8, 105, 204, 0.15);
    border-radius: 4px;
}

.newpoint {
    width: 464px;
    background: rgba(7, 15, 29, 0.55);
    border-radius: 4px;
    padding: 10px 0px 0px;
    margin-bottom: 10px;

    .newtop {
        // border-left: 2px solid #0075E6;
        margin-left: -20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        color: #C2E1FE;
        padding-left: 20px;
        padding-right: 20px;

        .borderLeft {
            width: 2px;
            height: 20px;
            background: #0075E6;
        }

        .newtoppic {
            margin-left: -10px;
        }
    }

    .newtbottom {
        padding-left: 20px;
        padding-bottom: 1px;
        padding-right: 20px;
    }
}

.RegularEnglish {
    font-family: RegularEnglish;
}
</style>
