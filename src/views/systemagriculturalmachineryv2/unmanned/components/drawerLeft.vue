<template>
    <div class="drawerLeft">
        <div class="top">
            <img src="@/views/systemagriculturalmachineryv2/assets/images/top.png" class="img" alt="" />
        </div>
        <img
            src="@/views/systemagriculturalmachineryv2/assets/images/side_light.png"
            class="side_light"
            alt=""
        />
        <img src="@/views/systemagriculturalmachineryv2/assets/images/light.png" class="light" alt="" />
        <div class="close" @click="changedrawerLeft">
            <img
                src="@/views/systemagriculturalmachineryv2/assets/images/leftmore.png"
                alt=""
                v-show="drawerLeft"
            />
            <img
                src="@/views/systemagriculturalmachineryv2/assets/images/left_more.png"
                alt=""
                v-show="!drawerLeft"
            />
        </div>
        <div class="list">
            <div class="title">
                <img src="@/views/systemagriculturalmachineryv2/assets/images/noongjilist.png" alt="" />
            </div>
            <div class="search">
                <el-input
                    v-model="machineValue"
                    placeholder="请输入牌照号或车主姓名"
                    class="input"
                    clearable
                    @clear="getMachineList"
                ></el-input>
                <div class="btn" @click="getMachineList">搜索</div>
            </div>
            <div class="box" style="height: calc(100% - 118px)">
                <div class="boxheader">
                    <div class="headeritem">农机牌照</div>
                    <div class="headeritem">农机类型</div>
                    <div class="headeritem">农机品牌</div>
                    <div class="headeritem">车主姓名</div>
                    <div class="headeritem">状态</div>
                </div>
                <el-scrollbar style="height: calc(100% - 46px)">
                    <div
                        class="boxheader boxbody"
                        :class="{
                            active: item.deviceCode === currentItem.deviceCode && item.licenseNo === currentItem.licenseNo,
                        }"
                        v-for="item in machineList"
                        @dblclick="machineryClick(item)"
                        style="width: 488px"
                    >
                        <div class="headeritem">{{ item.licenseNo }}</div>
                        <div class="headeritem">{{ item.amTypeName3 }}</div>
                        <div class="headeritem">{{ item.deviceBrand }}</div>
                        <div class="headeritem">{{ item.amOwnerName }}</div>
                        <div class="headeritem" :class="classObject(item)">
                            {{ textObject(item) }}
                        </div>
                    </div>
                </el-scrollbar>
            </div>
        </div>
        <div class="list">
            <div class="title">
                <img src="@/views/systemagriculturalmachineryv2/assets/images/tasklist.png" alt="" />
            </div>
            <div class="search">
                <el-input
                    v-model="taskValue"
                    :placeholder="currentItem.deviceCode?'请输入任务名称':'请输入牌照号或任务名称'"
                    class="input"
                    clearable
                    @clear="getTaskList"
                ></el-input>
                <div class="btn" @click="getTaskList">搜索</div>
            </div>
            <div class="box" style="height: calc(100% - 118px)">
                <div class="boxheader">
                    <div class="headeritem" v-show="!currentItem.deviceCode">
                        农机牌照
                    </div>
                    <div class="headeritem">任务名称</div>
                    <div class="headeritem">作业模式</div>
                    <div class="headeritem">开始时间</div>
                    <div class="headeritem">结束时间</div>
                    <div class="headeritem">操作</div>
                </div>
                <el-scrollbar style="height: calc(100% - 46px)">
                    <div
                        class="boxheader boxbody"
                        v-for="item in taskList"
                        :class="{
                            active:
                                item.assignTaskNo == currentTask.assignTaskNo,
                        }"
                        style="width: 488px"
                    >
                        <div
                            class="headeritem"
                            v-show="!currentItem.deviceCode"
                        >
                            {{ item.licenseNo }}
                        </div>
                        <div class="headeritem">{{ item.assignTaskName }}</div>
                        <div class="headeritem">{{ formatMode(item) }}</div>
                        <div class="headeritem">{{ item.taskStartTime }}</div>
                        <div class="headeritem">{{ item.taskEndTime }}</div>
                        <div
                            class="headeritem"
                            style="display: flex; justify-content: center"
                        >
                            <div
                                class=""
                                @dblclick="showDetail(item)"
                                style="margin-right: 6px"
                            >
                                详情
                            </div>
                            <div
                                class=""
                                v-if="item.taskEndTime"
                                @dblclick="tasClick(item)"
                            >
                                回放
                            </div>
                        </div>
                    </div>
                </el-scrollbar>
            </div>
        </div>
    </div>
</template>

<script>
import {
    machineQueryByPage,
    taskQueryByPage,
    getDeviceOnlineStatus,
    getOrExtendControlAuth,
    batchGetDeviceOnlineStatus,
} from '@/api/systemagriculturalmachineryv2/unmanned/unmanned'
import { getDicts } from "@/api/systemagriculturalmachineryv2/dict";
export default {
    name: 'drawerLeft',
    components: {},
    props: {
        currentMachine: {
            type: Object,
            default: {
                deviceCode: '',
                controlAuth: false,
                cfgvalue6: null,
            },
        },
        currentTask: {
            type: Object,
            default: {
                assignTaskNo: '',
            },
        },
        drawerLeft: {
            type: Boolean,
            default: true,
        },
        currentState: {
            type: Number,
            default: 0,
        },
        // 当前回放的任务编号
        currentReplayNo: {
            type: String,
            default: '',
        },
        requestHeadLovol: {
            type: Object,
            default: {},
        },
        currentCarControlMode: {
            type: [String, Number],
            default: ''
        }
    },
    data() {
        return {
            isDestroy: false,
            assign_mode: [],
            deviceNos: [], //已经获取到权限的设备
            machineValue: '',
            machineList: [],
            taskValue: '',
            taskList: [],
            timerNav: null,
            authTimer: null,
            currentItem: {
                deviceCode: '',
            },
        }
    },
    computed: {
        currentDrawerLeft: {
            get() {
                return this.drawerLeft
            },
            set(val) {
                this.$emit('update:drawerLeft', val)
            },
        },
    },
    mounted() {
        this.getMachineList()
        this.getTaskList()
        // 作业模式
        getDicts('assign_mode').then((response) => {
            this.assign_mode = response.data
        })
    },
    beforeDestroy() {
        this.isDestroy = true
    },
    watch: {},
    methods: {
        deleteAuth() {
            clearTimeout(this.authTimer)
        },
        formatMode(obj) {
            let name = ''
            let o = this.assign_mode.find((item) => {
                return item.code == obj.assignMode
            })
            if (o) {
                name = o.name
            }
            return name
        },
        classObject(obj) {
            if (!obj.machineStatus || obj.machineStatus == '0') {
                return ['offline']
            }
            if (obj.machineStatus == '1') {
                return ['online']
            }
            if (obj.machineStatus == '2') {
                return ['job']
            }
        },
        textObject(obj) {
            if (!obj.machineStatus || obj.machineStatus == '0') {
                return '离线'
            }
            if (obj.machineStatus == '1') {
                return '在线'
            }
            if (obj.machineStatus == '2') {
                return '作业中'
            }
        },
        async getMachineList() {
            const result = await machineQueryByPage({
                searchText: this.machineValue,
                rows: 100000,
                page: 1,
            })
            result.data.records.forEach((item) => {
                if (!item.machineStatus) {
                    item.machineStatus = '0'
                }
            })
            this.machineList = result.data.records.map((item) => ({
                ...item,
                controlAuth: false,
                deviceCode: item.cfgvalue1,
                terminalId: item.cfgvalue2,
                clientId: item.cfgvalue3,
                deviceBrand: item.cfgvalue4,
            }))
            this.loopMachineState(10000)
        },
        async getTaskList() {
            const result = await taskQueryByPage({
                searchText: this.taskValue,
                rows: 100000,
                page: 1,
                machineId:this.currentItem.machineId
            })
            this.taskList = result.data.records
        },
        async loopMachineState(interval) {
            clearTimeout(this.timerNav)
            let isPolling = false
            let poll = async () => {
                if (isPolling) return
                if (this.isDestroy) {
                    return
                }
                isPolling = true
                try {
                    let obj = {
                        requestHeadLovol: this.requestHeadLovol,
                        deviceNos: [],
                    }
                    this.machineList.forEach((item) => {
                        if (item.deviceCode) {
                            obj.deviceNos.push(item.deviceCode)
                        }
                    })
                    const result = await batchGetDeviceOnlineStatus(obj)
                    let data = result.data
                    if (data) {
                        for (const key in data) {
                            let value = data[key]
                            this.machineList.forEach((item) => {
                                if (item.deviceCode == key) {
                                    if (value != item.machineStatus) {
                                        if (value == 0) {
                                            this.$emit('offline', item)
                                        }
                                        if (value == 1) {
                                            this.$emit('online', item)
                                        }
                                        if (value == 2) {
                                            this.$emit('job', item)
                                        }
                                    }
                                    item.machineStatus = value
                                }
                            })
                        }
                    }
                } catch (error) {
                } finally {
                    isPolling = false
                    clearTimeout(this.timerNav)
                    this.timerNav = setTimeout(poll, interval)
                }
            }
            if (!isPolling) {
                await poll()
            }
        },
        changedrawerLeft() {
            this.currentDrawerLeft = !this.currentDrawerLeft
        },
        showMessage(msg) {
            this.$message({
                dangerouslyUseHTMLString: true,
                customClass: 'messageboxcustomClass',
                duration: 3000,
                offset: 200,
                message:
                    '<div class="content"><div class="title">消息提示</div><div class="text">' +
                    msg +
                    '</div></div>',
            })
        },
        async loopGetOrExtendControlAuth(item, interval) {
            //持续获取权限
            clearTimeout(this.authTimer)
            if (this.isDestroy) {
                return
            }
            let isPolling = false
            let poll = async () => {
                if (isPolling) return
                if (this.isDestroy) {
                    return
                }
                isPolling = true
                try {
                    var obj = Object.assign({}, this.requestHeadLovol)
                    obj.deviceNo = item.deviceCode
                    const result = await getOrExtendControlAuth(obj)
                } catch (error) {
                } finally {
                    isPolling = false
                    clearTimeout(this.authTimer)
                    this.authTimer = setTimeout(poll, interval)
                }
            }
            setTimeout(async () => {
                if (!isPolling) {
                    await poll()
                }
            }, interval)
        },
        async machineryClick(item) {
            if (item.machineStatus == 0) {
                this.currentItem = item
                this.getTaskList()
                this.$emit("hideBar")
                return
            }
            if (item.machineStatus != 1) {
                //必须是在线 不是作业中
                return
            }
            // if (
            //     item.deviceCode == this.currentMachine.deviceCode &&
            //     item.controlAuth == true
            // ) {
            //     return
            // }
            if(this.currentMachine.deviceCode && (this.currentCarControlMode == '1' || this.currentCarControlMode == '2')) {
                try {
                    await  this.$confirm('请选择', '提示', {
                    confirmButtonText: '结束控制',
                    cancelButtonText: '暂时退出',
                    showClose: false,
                    closeOnClickModal: false,
                    closeOnPressEscape: false,
                    type: 'warning'
                    })
                    this.$emit('endControl')
                } catch (error) {
                    this.$emit('pauseControl')
                }
            }
            this.currentItem = item
            this.getTaskList()
            var obj = Object.assign({}, this.requestHeadLovol)
            obj.deviceNo = item.deviceCode
            getOrExtendControlAuth(obj).then((res) => {
                let data = res.data.data
                item.controlAuth = true
                this.$emit('changeMachinery', item)
                this.showMessage('权限获取成功')
                this.loopGetOrExtendControlAuth(item,5000)
            })
        },
        tasClick(item) {
            this.$emit('changeTask', item)
        },
        showDetail(item) {
            this.$emit('detail', item)
        },
    },
}
</script>

<style scoped lang="scss">
::-webkit-scrollbar-corner {
    background-color: inherit !important;
}

:deep(.input .el-input__inner) {
    background-color: transparent;
    border: none;
    color: #606266 !important;
}

.drawerLeft {
    transition: all 1s;
    width: 500px;
    // background-color: #030a27;
    background-color: rgba(3, 10, 39, 0.7);
    // background: linear-gradient(to bottom, #F4FAFF 0%, #358de1 10%);
    position: absolute;
    left: 0;
    top: 80px;
    height: calc(100% - 90px);
    // opacity: 0.7;
    // border-top: 2px solid #b1f2fd;
    // border-image: linear-gradient(to right, #030a27, #b1f2fd, #030a27) 2;
    // border-right: 1px solid #b1f2fd;
    // border-bottom: 2px solid #b1f2fd;
    // background-image: linear-gradient(to right, #10142d, #32384e);
    border: 1px solid #196ab9;
    display: flex;
    flex-direction: column;
    z-index: 40;

    .close {
        position: absolute;
        right: -29px;
        top: calc(50% - 55px);
        cursor: pointer;
    }

    .top {
        position: absolute;
        width: 100%;
        left: 0;
        top: 0;
        z-index: -1;

        .img {
            width: 100%;
            height: auto;
        }
    }

    .side_light {
        position: absolute;
        right: -12px;
        top: 0;
    }

    .light {
        position: absolute;
        bottom: -2px;
        left: 95px;
    }

    .list {
        flex: 1;
        overflow: hidden;

        .title {
            margin-left: -15px;
        }

        .search {
            margin: 0 auto;
            width: 470px;
            height: 36px;
            background: rgba(8, 105, 204, 0.15);
            border: 1px solid rgba(23, 133, 255, 0.27);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding-right: 4px;
            margin-left: 15px;
            .input {
            }

            .btn {
                width: 70px;
                height: 28px;
                background: linear-gradient(-46deg, #0056a9, #0a83e5);
                border-radius: 4px;
                font-size: 16px;
                font-weight: 500;
                color: #c2e1fe;
                line-height: 28px;
                text-align: center;
                cursor: pointer;
            }
        }

        .box {
            margin-top: 20px;

            .boxheader {
                width: 490px;
                // min-height: 46px;
                background-image: url('@/views/systemagriculturalmachineryv2/assets/images/table_bg.png');
                background-size: cover;
                display: flex;
                align-items: center;
                font-size: 16px;
                color: #c2e1fe;
                padding: 14px 0px;
                margin-left: 10px;
                background-repeat: no-repeat;
                background-position-x: -7px;
                .headeritem {
                    flex: 1;
                    text-align: center;
                    word-break: break-all;
                }

                .online {
                    color: #7ff8eb;
                }

                .job {
                    color: #f39c5b;
                }

                .Offline {
                    color: #c2e1fe;
                }
            }

            .boxbody {
                background-image: url('@/views/systemagriculturalmachineryv2/assets/images/list_bg.png');
                color: #c2e1fe;
                font-size: 14px;
                cursor: pointer;
                transition: all 0.5s;

                &.active {
                    background-image: url('@/views/systemagriculturalmachineryv2/assets/images/list_hover.png');
                }

                &:hover {
                    background-image: url('@/views/systemagriculturalmachineryv2/assets/images/list_hover.png');
                }
            }
        }
    }
}

:deep(
    .el-select-dropdown__list ){
        background-color: #030a27;
    }
:deep(
    .el-select-dropdown) {
        border: 1px solid #08599d;
    }
:deep(
    .el-popper[x-placement^='bottom'] .popper__arrow::after) {
        border-bottom-color: #08599d;
    }

    :deep(.el-popper[x-placement^='bottom'] .popper__arrow) {
        border-bottom-color: #08599d;
    }

    :deep(.el-scrollbar__wrap) {
        overflow-x: hidden;
    }

    :deep(.el-input-group__append) {
        background-color: rgba(8, 105, 204, 0.15);
        border: 1px solid rgba(23, 133, 255, 0.27);
        border-left: none;
    }

    :deep(.append .el-input__inner) {
        border-right: none !important;
    }

.form {
    :deep(.el-select-dropdown__list) {
        background-color: #030a27;
    }

    //下拉框
    :deep(.el-select-dropdown) {
        border: none;
        background-color: rgba(1, 28, 82, 0.8);
    }

    //输入框
    :deep(.el-input__inner) {
        color: #eee;
        border-color: #00fff6;
        background-color: rgba(1, 28, 82, 0.8);
    }

    //聚焦时的样式
    :deep(.el-select .el-input.is-focus .el-input__inner) {
        border-color: #0b61aa;
        background-color: rgba(1, 28, 82, 0.8);
        color: #00d3e9;
    }

    //下拉框选中
    :deep(.el-select-dropdown__item) {
        color: #eee;
    }

    //鼠标经过下拉框
    :deep(.el-select-dropdown__item.hover),
    :deep(.el-select-dropdown__item:hover) {
        color: #00d3e9;
        background-color: #0f3360;
    }
}

:deep(.el-slider__button-wrapper) {
    border: 1px solid #62aafe;
    opacity: 0.5;
    border-radius: 50%;
    width: 27px;
    height: 27px;
    top: -10px;
    margin-left: -4px;
}

:deep(.el-slider__button) {
    width: 12px;
    height: 12px;
    background: #99c8ff;
    border-radius: 50%;
}

:deep(.el-slider__runway) {
    background: rgba(8, 124, 255, 0.1);
    border-radius: 4px;
    height: 9px;
}

:deep(.el-slider__bar) {
    background: linear-gradient(90deg, #061733, #1c8aff);
    border-radius: 4px;
    height: 9px;
}

:deep(.el-slider__stop) {
    display: none;
}

:deep(.el-slider__marks-text) {
    width: max-content;
    margin-top: 20px;
}

:deep(.el-dialog__wrapper) {
    position: absolute;
}

:deep(.el-dialog) {
    background: url('@/views/systemagriculturalmachineryv2/assets/images/bg.png') no-repeat !important;
    background-size: 100% 100% !important;
    padding-bottom: 20px;
}

:deep(.el-dialog__title){
    position: absolute;
    top: 5%;
    left: 0%;
    display: block;
    width: 100%;
    text-align: center;
    color: #fff !important;
    font-weight: 700;
    font-size: 23px !important;
}

:deep(.el-dialog__body) {
}

:deep(.el-dialog__headerbtn) {
    top: 75px;
    right: 40px;
}

:deep(.el-dialog__body .abc) {
    height: 565px;
    overflow: auto;
}

:deep(.el-dialog__body .myCon p) {
    background-color: #0d4e68 !important;
    color: #fff;
}

.tabs {
    width: 248px;
    height: 40px;
    background: rgba(91, 162, 227, 0.05);
    border: 1px solid #08599d;
    border-radius: 4px;
    margin: 0px auto 0;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .tabItem {
        width: 120px;
        height: 32px;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #e4f2ff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #7fbef8;
        opacity: 0.6;
    }

    .tabActive {
        background: rgba(1, 125, 255, 0.5);
        border: 0px solid #070002;
        opacity: 1;
        color: #ffffff;
    }
}

.form {
    padding: 0 50px 0 30px;
    margin-top: 20px;

    :deep(.el-form-item__label) {
        color: #c2e1fe;
        font-size: 16px;
    }

    :deep(.el-radio) {
        color: #c2e1fe;
        width: 60px;
    }

    :deep(.el-radio__inner) {
        border: 1px solid rgba(23, 133, 255, 0.4);
        background: rgba(8, 105, 204, 0.2);

        &::after {
            background: #1193fd;
        }
    }

    :deep(.el-radio__input.is-checked + .el-radio__label) {
        color: #c2e1fe;
    }

    :deep(.el-input__inner) {
        background: rgba(8, 105, 204, 0.15);
        border: 1px solid rgba(23, 133, 255, 0.27);
        color: #7fbef8;
    }

    :deep(.el-input-number__decrease) {
        width: 66px;
        height: 34px;
        background: #0056a9;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #ffffff;
        border-right: none;
    }

    :deep(.el-input-number__increase) {
        width: 66px;
        height: 34px;
        background: #0056a9;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #ffffff;
        border-left: none;
    }

    :deep(.el-input-number--medium) {
        width: 260px;
    }

    :deep(.el-input-number .el-input) {
        margin-left: 74px;
        width: 106px;
        height: 36px;
        background: rgba(8, 105, 204, 0.15);
        border: 1px solid rgba(23, 133, 255, 0.27);
        border-radius: 4px;
    }

    .cancel {
        width: 121px;
        border: 1px solid #1193fd;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        background-color: transparent;
        color: #6896d1;
    }

    .submit {
        width: 120px;
        background: linear-gradient(-46deg, #0056a9, #0a83e5);
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        font-size: 16px;
        font-weight: 500;
        color: #e3f1ff;
        border: none;
    }

    .click {
        width: 60px;
        height: 28px;
        background: #0056a9;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #c2e1fe;
        border: none;
        text-align: center;
        line-height: 28px;
        padding: 0;
    }

    .reset {
        width: 60px;
        height: 28px;
        background: #0056a9;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #c2e1fe;
        border: none;
        text-align: center;
        line-height: 28px;
        padding: 0;
    }
}

.newbottom {
    padding: 20px;
    width: 506px;
    height: 1011px;
    background: rgba(8, 105, 204, 0.15);
    border-radius: 4px;
}

.newpoint {
    width: 464px;
    background: rgba(7, 15, 29, 0.55);
    border-radius: 4px;
    padding: 10px 0px 0px;
    margin-bottom: 10px;

    .newtop {
        // border-left: 2px solid #0075E6;
        margin-left: -20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        color: #c2e1fe;
        padding-left: 20px;
        padding-right: 20px;

        .borderLeft {
            width: 2px;
            height: 20px;
            background: #0075e6;
        }

        .newtoppic {
            margin-left: -10px;
        }
    }

    .newtbottom {
        padding-left: 20px;
        padding-bottom: 1px;
        padding-right: 20px;
    }
}
</style>
