<template>
    <div class="footer">
        <div class="footerPanel">
            <div class="controlcolumn">
                <!-- <div class="content_arg" v-show="command.controlModel == 1">

                </div> -->
                <div
                    class="content_add"
                    @click="openCreateTaskDialog"
                    v-show="command.controlModel == 1"
                >
                    <img
                        src="@/views/systemagriculturalmachineryv2/assets/images/add.png"
                        alt=""
                        style="margin-right: 4px; width: 17px"
                    />
                    新建任务
                </div>
                <div
                    class="content_add"
                    @click="openParameterConfigurationDialog"
                    v-show="command.controlModel == 1"
                >
                    <img
                        src="@/views/systemagriculturalmachineryv2/assets/images/arg.png"
                        alt=""
                        style="margin-right: 4px; width: 17px"
                    />
                    参数配置
                </div>
                <div
                    class="content"
                    v-show="command.controlModel == 2"
                    @click="openControlDisplayDialog"
                >
                    <img
                        src="@/views/systemagriculturalmachineryv2/assets/images/cont.png"
                        alt=""
                        style="margin-right: 4px; width: 17px"
                    />
                    控制显示
                </div>
                <div
                    class="content_switch"
                    v-show="command.controlModel == 0"
                    style="width: 126px"
                >
                    <div>本地控制</div>
                    <el-switch
                        v-model="localModel"
                        :active-text="localModel ? '开' : '关'"
                        inactive-text=""
                        @change="localChange"
                        active-color="#94C2FF"
                    >
                    </el-switch>
                </div>
                <div class="content_show" @click="changeShow">
                    <img
                        src="@/views/systemagriculturalmachineryv2/assets/images/shouqi.png"
                        alt=""
                        v-show="showFooterBar"
                    />
                    <img
                        src="@/views/systemagriculturalmachineryv2/assets/images/zhankai.png"
                        alt=""
                        v-show="!showFooterBar"
                    />
                    {{ showFooterBar ? '收起' : '展开' }}
                </div>
            </div>
            <div class="controlTextBox">
                <div class="maskleft" :style="{ 'clip-path': clipPathValue }">
                    <img
                        src="@/views/systemagriculturalmachineryv2/assets/images/left.png"
                        alt=""
                        class="mask"
                    />
                    <img
                        src="@/views/systemagriculturalmachineryv2/assets/images/left1.png"
                        alt=""
                        class="mask"
                        style="left: 7px"
                    />
                </div>
                <div class="maskleftPoint">
                    <div class="scale0" :class="{ active: speed >= 0 }">0</div>
                    <div
                        class="pointerBor0"
                        :class="{ active: speed >= 0 }"
                    ></div>
                    <div class="scale30" :class="{ active: speed >= 10 }">
                        10
                    </div>
                    <div
                        class="pointerBor30"
                        :class="{ active: speed >= 10 }"
                    ></div>
                    <div class="scale60" :class="{ active: speed >= 20 }">
                        20
                    </div>
                    <div
                        class="pointerBor60"
                        :class="{ active: speed >= 20 }"
                    ></div>
                    <div class="scale100" :class="{ active: speed >= 30 }">
                        30
                    </div>
                    <div
                        class="pointerBor100"
                        :class="{ active: speed >= 30 }"
                    ></div>
                    <div class="scale140" :class="{ active: speed >= 40 }">
                        40
                    </div>
                    <div
                        class="pointerBor140"
                        :class="{ active: speed >= 40 }"
                    ></div>
                    <div class="scale170" :class="{ active: speed >= 50 }">
                        50
                    </div>
                    <div
                        class="pointerBor170"
                        :class="{ active: speed >= 50 }"
                    ></div>
                    <div class="scale200" :class="{ active: speed >= 60 }">
                        60
                    </div>
                    <div
                        class="pointerBor200"
                        :class="{ active: speed >= 60 }"
                    ></div>
                    <div class="scale230" :class="{ active: speed >= 70 }">
                        70
                    </div>
                    <div
                        class="pointerBor230"
                        :class="{ active: speed >= 70 }"
                    ></div>

                    <div class="scale260" :class="{ active: speed >= 80 }">
                        80
                    </div>
                    <div
                        class="pointerBor260"
                        :class="{ active: speed >= 80 }"
                    ></div>
                    <div
                        class="pointer"
                        :style="{ rotate: rotateLeft + 'deg' }"
                    >
                        <img
                            src="@/views/systemagriculturalmachineryv2/assets/images/pointerLeft.png"
                            alt=""
                            style="margin-top: 3px"
                        />
                    </div>
                </div>
                <div
                    class="maskright"
                    :style="{ 'clip-path': clipPathValueRight }"
                >
                    <img
                        src="@/views/systemagriculturalmachineryv2/assets/images/right.png"
                        alt=""
                        class="mask"
                    />
                    <img
                        src="@/views/systemagriculturalmachineryv2/assets/images/right2.png"
                        alt=""
                        class="mask"
                        style="right: 7px"
                    />
                </div>
                <div class="maskrightPoint">
                    <div class="scale00" :class="{ active: rotate >= 0 }">
                        0
                    </div>
                    <div
                        class="pointerBor00"
                        :class="{ active: rotate >= 0 }"
                    ></div>
                    <div class="scale1000" :class="{ active: rotate >= 1000 }">
                        1000
                    </div>
                    <div
                        class="pointerBor1000"
                        :class="{ active: rotate >= 1000 }"
                    ></div>
                    <div class="scale2000" :class="{ active: rotate >= 2000 }">
                        2000
                    </div>
                    <div
                        class="pointerBor2000"
                        :class="{ active: rotate >= 2000 }"
                    ></div>
                    <div class="scale3000" :class="{ active: rotate >= 3000 }">
                        3000
                    </div>
                    <div
                        class="pointerBor3000"
                        :class="{ active: rotate >= 3000 }"
                    ></div>
                    <div class="scale4000" :class="{ active: rotate >= 4000 }">
                        4000
                    </div>
                    <div
                        class="pointerBor4000"
                        :class="{ active: rotate >= 4000 }"
                    ></div>
                    <div class="scale5000" :class="{ active: rotate >= 5000 }">
                        5000
                    </div>
                    <div
                        class="pointerBor5000"
                        :class="{ active: rotate >= 5000 }"
                    ></div>
                    <div
                        class="pointer"
                        :style="{ rotate: -rotateRight + 'deg' }"
                    >
                        <img
                            src="@/views/systemagriculturalmachineryv2/assets/images/pointerRight.png"
                            alt=""
                            style="margin-top: 3px"
                        />
                    </div>
                </div>

                <div class="speed">
                    {{ speed }}
                </div>
                <div class="rotational">
                    {{ rotate }}
                </div>
                <transition name="el-fade-in-linear">
                    <img
                        src="@/views/systemagriculturalmachineryv2/assets/images/control.png"
                        alt=""
                        v-show="command.controlModel == 2"
                        class="controlText"
                    />
                </transition>
                <img
                    src="@/views/systemagriculturalmachineryv2/assets/images/controltop.png"
                    v-show="command.controlModel == 2 && topMove == 1"
                    alt=""
                    class="controltop"
                />
                <img
                    src="@/views/systemagriculturalmachineryv2/assets/images/controlright.png"
                    v-show="command.controlModel == 2 && leftMove == 2"
                    alt=""
                    class="controlright"
                />
                <img
                    src="@/views/systemagriculturalmachineryv2/assets/images/controlback.png"
                    v-show="command.controlModel == 2 && topMove == 2"
                    alt=""
                    class="controlback"
                />
                <img
                    src="@/views/systemagriculturalmachineryv2/assets/images/controlleft.png"
                    v-show="command.controlModel == 2 && leftMove == 1"
                    alt=""
                    class="controlleft"
                />
                <div
                    class="gear"
                    @click="resetMove(true)"
                    v-show="command.controlModel == 2"
                >
                    N
                </div>
                <div
                    slot="reference"
                    class="controltopText"
                    @click="topMoveChange($event, 1)"
                    v-show="command.wy != 1 && command.controlModel == 2"
                >
                    前
                </div>
                <div
                    slot="reference"
                    class="controlrightText"
                    @click="leftMoveChange(2)"
                    v-show="command.wy != 1 && command.controlModel == 2"
                >
                    右
                </div>
                <div
                    slot="reference"
                    class="controlbackText"
                    @click="topMoveChange($event, 2)"
                    v-show="command.wy != 1 && command.controlModel == 2"
                >
                    后
                </div>
                <div
                    slot="reference"
                    class="controlleftText"
                    @click="leftMoveChange(1)"
                    v-show="command.wy != 1 && command.controlModel == 2"
                >
                    左
                </div>
                <el-popover
                    placement="top"
                    title=""
                    width="480"
                    v-popover
                    trigger="click"
                    popper-class="top_wrap"
                    v-model="popoverVisible"
                    :disabled="command.wy != 1"
                >
                <div class="popovermove"></div>
                    <i
                        class="el-icon-circle-close popoverclose"
                        @click="popoverVisible = false"
                    ></i>
                    <div
                        v-if="currentMachine.cfgvalue6 == 'b'"
                    >
                        <div class="popovertop">行驶速度</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.speedControl"
                                @change="sliderChange"
                                :step="1"
                                :min="0"
                                :max="25"
                                :marks="form.marks1"
                            >
                            </el-slider>
                        </div>
                    </div>
                    <div
                        style="margin-top: 30px"
                        v-if="
                            currentMachine.cfgvalue6 == 'c' ||
                            currentMachine.cfgvalue6 == 'd' ||
                            currentMachine.cfgvalue6 == 'e' ||
                            currentMachine.cfgvalue6 == 'f'
                        "
                    >
                        <div class="popovertop">行驶速度</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.speedControl"
                                @change="sliderChange"
                                :step="1"
                                :min="0"
                                :max="100"
                                :format-tooltip="formatTooltipmarks4"
                                :marks="form.marks4"
                            >
                            </el-slider>
                        </div>
                    </div>
                    <div style="margin-top: 30px">
                        <div class="popovertop">转向角度</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.yns"
                                @change="sliderChange"
                                :step="10"
                                :min="-100"
                                :max="100"
                                :format-tooltip="formatTooltip"
                                :marks="form.marks2"
                            >
                            </el-slider>
                        </div>
                    </div>
                    <div style="margin-top: 30px">
                        <div class="popovertop">发动机转速</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.zg"
                                @change="sliderChange"
                                :step="10"
                                :min="0"
                                :max="4000"
                                :marks="form.marks3"
                            >
                            </el-slider>
                        </div>
                    </div>
                    <div
                        slot="reference"
                        class="controltopText"
                        @click="topMoveChange($event, 1)"
                        v-show="command.wy == 1 && command.controlModel == 2"
                    >
                        前
                    </div>
                </el-popover>
                <el-popover
                    v-popover
                    placement="top"
                    title=""
                    width="480"
                    trigger="click"
                    popper-class="top_wrap"
                    ref="right"
                    :offset="55"
                    v-model="popoverVisible2"
                    :disabled="command.wy != 1"
                >
                <div class="popovermove"></div>
                    <i
                        class="el-icon-circle-close popoverclose"
                        @click="popoverVisible = false"
                    ></i>
                    <div
                        v-if="currentMachine.cfgvalue6 == 'b'"
                    >
                        <i
                            class="el-icon-circle-close popoverclose"
                            @click="popoverVisible2 = false"
                        ></i>
                        <div class="popovertop">行驶速度</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.speedControl"
                                @change="sliderChange"
                                :step="1"
                                :min="0"
                                :max="80"
                                :marks="form.marks1"
                            >
                            </el-slider>
                        </div>
                    </div>
                    <div
                        style="margin-top: 30px"
                        v-if="
                            currentMachine.cfgvalue6 == 'c' ||
                            currentMachine.cfgvalue6 == 'd' ||
                            currentMachine.cfgvalue6 == 'e' ||
                            currentMachine.cfgvalue6 == 'f'
                        "
                    >
                        <div class="popovertop">行驶速度</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.speedControl"
                                @change="sliderChange"
                                :step="1"
                                :min="0"
                                :max="100"
                                :format-tooltip="formatTooltipmarks4"
                                :marks="form.marks4"
                            >
                            </el-slider>
                        </div>
                    </div>
                    <div style="margin-top: 30px">
                        <div class="popovertop">转向角度</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.yns"
                                @change="sliderChange"
                                :step="10"
                                :min="-100"
                                :max="100"
                                :format-tooltip="formatTooltip"
                                :marks="form.marks2"
                            >
                            </el-slider>
                        </div>
                    </div>
                    <div style="margin-top: 30px">
                        <div class="popovertop">发动机转速</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.zg"
                                @change="sliderChange"
                                :step="10"
                                :min="0"
                                :max="4000"
                                :marks="form.marks3"
                            >
                            </el-slider>
                        </div>
                    </div>

                    <div
                        slot="reference"
                        class="controlrightText"
                        @click="leftMoveChange($event, 2)"
                        v-show="command.wy == 1 && command.controlModel == 2"
                    >
                        右
                    </div>
                </el-popover>
                <el-popover
                    v-popover
                    placement="top"
                    title=""
                    width="480"
                    trigger="click"
                    popper-class="top_wrap"
                    v-model="popoverVisible3"
                    :disabled="command.wy != 1"
                    ref="popover"
                >
                <div class="popovermove"></div>
                    <i
                        class="el-icon-circle-close popoverclose"
                        @click="popoverVisible = false"
                    ></i>
                    <div
                        v-if="currentMachine.cfgvalue6 == 'b'"
                    >
                        <i
                            class="el-icon-circle-close popoverclose"
                            @click="popoverVisible3 = false"
                        ></i>
                        <div class="popovertop">行驶速度</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.speedControl"
                                @change="sliderChange"
                                :step="1"
                                :min="0"
                                :max="80"
                                :marks="form.marks1"
                            >
                            </el-slider>
                        </div>
                    </div>
                    <div
                        style="margin-top: 30px"
                        v-if="
                            currentMachine.cfgvalue6 == 'c' ||
                            currentMachine.cfgvalue6 == 'd' ||
                            currentMachine.cfgvalue6 == 'e' ||
                            currentMachine.cfgvalue6 == 'f'
                        "
                    >
                        <div class="popovertop">行驶速度</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.speedControl"
                                @change="sliderChange"
                                :step="1"
                                :min="0"
                                :max="100"
                                :format-tooltip="formatTooltipmarks4"
                                :marks="form.marks4"
                            >
                            </el-slider>
                        </div>
                    </div>
                    <div style="margin-top: 30px">
                        <div class="popovertop">转向角度</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.yns"
                                @change="sliderChange"
                                :step="10"
                                :min="-100"
                                :max="100"
                                :format-tooltip="formatTooltip"
                                :marks="form.marks2"
                            >
                            </el-slider>
                        </div>
                    </div>
                    <div style="margin-top: 30px">
                        <div class="popovertop">发动机转速</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.zg"
                                @change="sliderChange"
                                :step="10"
                                :min="0"
                                :max="4000"
                                :marks="form.marks3"
                            >
                            </el-slider>
                        </div>
                    </div>
                    <div
                        slot="reference"
                        class="controlbackText"
                        @click="topMoveChange($event, 2)"
                        v-show="command.wy == 1 && command.controlModel == 2"
                    >
                        后
                    </div>
                </el-popover>
                <el-popover
                    v-popover
                    placement="top"
                    title=""
                    width="480"
                    trigger="click"
                    popper-class="top_wrap"
                    ref="left"
                    :offset="-55"
                    v-model="popoverVisible4"
                    :disabled="command.wy != 1"
                >
                <div class="popovermove"></div>
                    <i
                        class="el-icon-circle-close popoverclose"
                        @click="popoverVisible = false"
                    ></i>
                    <div
                        v-if="currentMachine.cfgvalue6 == 'b'"
                    >
                        <i
                            class="el-icon-circle-close popoverclose"
                            @click="popoverVisible4 = false"
                        ></i>
                        <div class="popovertop">行驶速度</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.speedControl"
                                @change="sliderChange"
                                :step="1"
                                :min="0"
                                :max="25"
                                :marks="form.marks1"
                            >
                            </el-slider>
                        </div>
                    </div>
                    <div
                        style="margin-top: 30px"
                        v-if="
                            currentMachine.cfgvalue6 == 'c' ||
                            currentMachine.cfgvalue6 == 'd' ||
                            currentMachine.cfgvalue6 == 'e' ||
                            currentMachine.cfgvalue6 == 'f'
                        "
                    >
                        <div class="popovertop">行驶速度</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.speedControl"
                                @change="sliderChange"
                                :step="1"
                                :min="0"
                                :max="100"
                                :format-tooltip="formatTooltipmarks4"
                                :marks="form.marks4"
                            >
                            </el-slider>
                        </div>
                    </div>
                    <div style="margin-top: 30px">
                        <div class="popovertop">转向角度</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.yns"
                                @change="sliderChange"
                                :step="10"
                                :min="-100"
                                :max="100"
                                :format-tooltip="formatTooltip"
                                :marks="form.marks2"
                            >
                            </el-slider>
                        </div>
                    </div>
                    <div style="margin-top: 30px">
                        <div class="popovertop">发动机转速</div>
                        <div class="popoverform">
                            <el-slider
                                v-model="form.zg"
                                @change="sliderChange"
                                :step="10"
                                :min="0"
                                :max="4000"
                                :marks="form.marks3"
                            >
                            </el-slider>
                        </div>
                    </div>
                    <div
                        slot="reference"
                        class="controlleftText"
                        @click="leftMoveChange($event, 1)"
                        v-show="command.wy == 1 && command.controlModel == 2"
                    >
                        左
                    </div>
                </el-popover>
            </div>
            <div class="autonomous_box">
                <div
                    class="autonomous"
                    :class="{ active: command.controlModel == 1 }"
                    @click="controlModelChange(1)"
                >
                    自主作业
                </div>
                <div
                    class="hand"
                    :class="{ active: command.controlModel == 2 }"
                    @click="controlModelChange(2)"
                >
                    手动遥控
                </div>
                <div
                    class="locality"
                    :class="{ active: command.controlModel == 0 }"
                    @click="controlModelChange(0)"
                >
                    本地控制
                </div>
            </div>
            <div class="emergent_box">
                <div
                    class="emergent"
                    :class="{ active: command.yh != 0 }"
                    @click="yhChange()"
                    v-show="command.controlModel != 0"
                >
                    {{
                        command.yh == 2 || command.yh == 0
                            ? '紧急停止'
                            : '解除停止'
                    }}
                </div>
                <div
                    class="navigation"
                    :class="{ active: command.abu == 1 || command.abu == 2 }"
                    v-if="command.controlModel != 0"
                    v-show="
                        command.controlModel == 1 || command.controlModel == 3
                    "
                    @click="abuChange()"
                >
                    {{
                        command.abu == 2 || command.abu == 0
                            ? '导航开始'
                            : '导航暂停'
                    }}
                </div>
                <div
                    class="fire"
                    v-longpress="handfire"
                    :class="{ active: command.wy != 0 }"
                    v-show="command.controlModel != 0"
                >
                    {{
                        command.wy == 2 || command.wy == 0
                            ? '长按点火'
                            : '长按熄火'
                    }}
                </div>
                <div
                    class="fire"
                    @click="authChange()"
                    :class="{ active: currentMachine.controlAuth == false }"
                >
                    释放农机
                </div>
            </div>
        </div>
        <audio
            ref="open"
            :src="open"
            volume="0.5"
            style="visibility: hidden"
        ></audio>
        <audio
            ref="fire"
            :src="fire"
            volume="0.5"
            loop
            style="visibility: hidden"
        ></audio>
    </div>
</template>

<script>
import fire from '@/views/systemagriculturalmachineryv2/assets/images/fire.mp3'
import open from '@/views/systemagriculturalmachineryv2/assets/images/open.mp3'
import { h } from 'vue';
console.log('h',h)
import {
    commonSetting,
    commonRemoteControl,
    taskQueryByPage,
    getDeviceOnlineStatus,
    getOrExtendControlAuth,
    getNavData,
    deleteControlAuth,
} from '@/api/systemagriculturalmachineryv2/unmanned/unmanned'
import { getDicts } from "@/api/systemagriculturalmachineryv2/dict";
export default {
    name: 'footerBar',
    components: {},
    props: {
        currentMachine: {
            type: Object,
            default: {
                deviceCode: '',
                controlAuth: false,
                cfgvalue6: null,
            },
        },
        currentState: {
            type: Number,
            default: 0,
        },
        showFooterBar: {
            type: Boolean,
            default: false,
        },
        requestHeadLovol: {
            type: Object,
            default: {},
        },
    },
    watch: {
        'currentMachine.deviceCode': {
            handler(newValue, oldValue) {
                this.destroyYh()
                this.reset()
            },
        },
        currentState: {
            handler(newValue, oldValue) {
                this.destroyYh()
            },
        },
    },
    data() {
        return {
            fire: fire,
            open: open,
            popoverVisible: false,
            popoverVisible2: false,
            popoverVisible3: false,
            popoverVisible4: false,
            localModel: false,
            directionType: 0,
            topMove: 0, //0 空状态 1 前进 2 后退
            leftMove: 0, //0 空状态 1 左转 2 右转
            rotateLeft: -45,
            rotateRight: -45,
            speed: 0,
            rotate: 0,
            clipPathValue:
                'polygon(100% 100%, 100% 59%, 100% 100%, 100% 100%, 0 100%)',
            clipPathValueRight:
                'polygon(100% 100%, 100% 100%, 0 100%, 0 55%, 0 100%)',
            form: {
                speedControl: 0,
                yns: 0,
                zg: 0,
                marks1: {
                    0: {
                        style: {
                            color: '#1989FA',
                        },
                        label: h('span', '0'),
                    },
                    25: {
                        style: {
                            color: '#1989FA',
                        },
                        label: h('span', '25km/h'),
                    },
                },
                marks2: {
                    '-100': {
                        style: {
                            color: '#1989FA',
                        },
                        label: h('span', '100左转'),
                    },
                    0: {
                        style: {
                            color: '#1989FA',
                        },
                        label: h('span', '0'),
                    },
                    100: {
                        style: {
                            color: '#1989FA',
                        },
                        label: h('span', '100右转'),
                    },
                },
                marks3: {
                    0: {
                        style: {
                            color: '#1989FA',
                        },
                        label: h('span', '0'),
                    },
                    2000: {
                        style: {
                            color: '#1989FA',
                        },
                        label: h('span', '2000'),
                    },
                    4000: {
                        style: {
                            color: '#1989FA',
                        },
                        label: h('span', '4000'),
                    },
                },
                marks4: {
                    0: {
                        style: {
                            color: '#1989FA',
                        },
                        label: h('span', '0'),
                    },
                    100: {
                        style: {
                            color: '#1989FA',
                        },
                        label: h('span', '100%'),
                    },
                },
            },
            command: {
                //设备号
                deviceNo: '',
                /**
                 * 控制模式
                 * 0: 本地控制模式
                 *    1：自主作业模式
                 *    2：手动遥控模式
                 *    3：无效(默认)
                 */
                controlModel: 3,
                /**
                 * 导航启动设置
                 * 0：无动作(默认)
                 *    1：导航开始
                 *    2：导航暂停
                 *    3：导航禁止
                 */
                abu: 0,
                /**
                 * 手机断连
                 * 0：无动作(默认)
                 *    1：断开连接
                 *
                 */
                appDisconnect: 0,
                /**
                 * 整机启动设置
                 * 0：无动作(默认)
                 *    1：启动
                 *    2：熄火
                 *    3：无效
                 */
                wy: 0,
                /**
                 * 系统通电设置
                 * 0：无动作(默认)
                 *    1：通电
                 *    2：断电
                 *    3：无效
                 */
                x2: 0,
                /**
                 * 紧急制动
                 * 0：无动作(默认)
                 *    1：紧急制动
                 *    2：解除制动
                 *    3：无效
                 */
                yh: 0,
                auth: 1,
            },
            assign_type: [],
            assign_mode: [],
            route_mode: [],
            turn_mode: [],
            side_type: [],
            pto_trol: [],
            pto_rpm: [],
            gear_type: [],
            fwd_install: [],
            eds_install: [],
            mw_install: [],
            pro_console: [],
            assign_drift: [],
            mw_action: [],
            currentData: {},
        }
    },
    mounted() {
        window.addEventListener('beforeunload', this.destroyYh)
        // 作业类型
        getDicts('assign_type').then((response) => {
            this.assign_type = response.data
        })
        // 作业模式
        getDicts('assign_mode').then((response) => {
            this.assign_mode = response.data
        })
        // 路径模式
        getDicts('route_mode').then((response) => {
            this.route_mode = response.data
        })
        // 转弯方式
        getDicts('turn_mode').then((response) => {
            this.turn_mode = response.data
        })
        // 是否收边
        getDicts('side_type').then((response) => {
            this.side_type = response.data
        })
        // PTO控制
        getDicts('pto_trol').then((response) => {
            this.pto_trol = response.data
        })
        // PTO转速
        getDicts('pto_rpm').then((response) => {
            this.pto_rpm = response.data
        })
        // 档位
        getDicts('gear_type').then((response) => {
            this.gear_type = response.data
        })
        // 四驱设置
        getDicts('fwd_install').then((response) => {
            this.fwd_install = response.data
        })
        // 差速锁设置
        getDicts('eds_install').then((response) => {
            this.eds_install = response.data
        })
        // 多路阀使能
        getDicts('mw_install').then((response) => {
            this.mw_install = response.data
        })
        // 后提升控制
        getDicts('pro_console').then((response) => {
            this.pro_console = response.data
        })
        // 作业机具浮动
        getDicts('assign_drift').then((response) => {
            this.assign_drift = response.data
        })
        // 多路阀动作
        getDicts('mw_action').then((response) => {
            this.mw_action = response.data
        })
        this.$bus.$on('navData', (data) => {
            this.currentData = data
            // if (!data.currentsteeringwheelangle) {
            //     data.currentsteeringwheelangle = 0
            // }
            // if (data.currentsteeringwheelangle < 0) {
            //     this.leftMove = 1
            // } else if (data.currentsteeringwheelangle > 0) {
            //     this.leftMove = 2
            // } else {
            //     this.leftMove = 0
            // }
            // this.form.yns = data.currentsteeringwheelangle
            //作业状态
            // if (data.mu == 0) {
            //     this.end()
            // } else if (data.mu == 1) {
            //     this.start()
            // } else if (data.mu == 2) {
            //     this.end()
            // }
            //速度
            let currentjobspeed = Math.abs(Number(data.currentjobspeed))
            if (!currentjobspeed || currentjobspeed < 0) {
                currentjobspeed = 0
                // this.rotateLeft = 0
            }
            // this.form.speedControl = currentjobspeed
            this.speed = currentjobspeed // (Math.floor(Math.random() * 30)) + 5
            //发动机转速
            let enginespeed = this.currentMachine.cfgvalue6 == 'd' ? data.tboxData.b8 : data.aaaa
            if (!enginespeed || enginespeed < 0) {
                enginespeed = 0
                // this.rotateRight = 0
            }

            // this.form.zg = enginespeed
            this.rotate = enginespeed // (Math.floor(Math.random() * 3000)) + 500
            //发动机熄火状态
            if (enginespeed == 0) {
                if (this.command.wy != 0) {
                    this.command.wy = 2
                }
                this.stopAudio()
            } else {
                if (this.command.wy != 1) {
                    this.playAudio()
                }
                this.command.wy = 1
            }
            // if (data.mt == 3) {//倒车
            //     this.topMove = 2
            // } else {
            //     if (enginespeed == 0) {
            //         this.topMove = 0
            //     } else {
            //         this.topMove = 1
            //     }
            // }
            // 紧急制动
            // if (!data.yh) {
            //     data.yh = 0
            // }
            // this.command.yh = data.yh
            //控制模式
            // if (data.x1 === null || data.x1 === undefined) {
            //     data.x1 = 3
            // }
            // if (data.x1 == 0) {
            //     this.localModel = true
            // } else {
            //     this.localModel = false
            // }
            // this.command.controlModel = data.x1

            //导航系统状态信息
            // if (!data.navigationsystemstatusinformation) {
            //     data.navigationsystemstatusinformation = 3
            // }
            // if (data.navigationsystemstatusinformation == 3) {
            //     this.command.abu = 0
            // }
            // if (data.navigationsystemstatusinformation == 0 || data.navigationsystemstatusinformation == 2) {
            //     this.command.abu = 2
            // }
            // if (data.navigationsystemstatusinformation == 1) {
            //     this.command.abu = 1
            // }

            this.speedToRotate()
            this.rotateToRotate()
        })
    },
    beforeDestroy() {
        this.destroyYh()
    },
    methods: {
        getControlModel() {
            return this.command.controlModel;
        },
        formatTooltip(val) {
            if (val < 0) {
                this.leftMove = 1
                return `左${Math.abs(val)}度`
            } else if (val > 0) {
                this.leftMove = 2
                return `右${Math.abs(val)}度`
            } else {
                this.leftMove = 0
                return ''
            }
        },
        formatTooltipmarks4(val) {
            return `${val}%`
        },
        changeShow() {
            this.$emit('changeShow')
        },
        start() {
            this.$emit('update:currentState', 1)
        },
        end() {
            this.$emit('update:currentState', 0)
        },
        playAudio() {
            if (this.$refs.open) {
                this.$refs.open.play()
            }
            if (this.$refs.fire) {
                this.$refs.fire.play()
            }
        },
        stopAudio() {
            if (this.$refs.open) {
                this.$refs.open.pause()
                this.$refs.open.currentTime = 0
            }
            if (this.$refs.fire) {
                this.$refs.fire.pause()
                this.$refs.fire.currentTime = 0
            }
        },
        localChange(val) {
            if (val) {
                let obj = {
                    requestHeadLovol: this.requestHeadLovol,
                    command: {
                        deviceNo: this.currentMachine.deviceCode,
                        controlModel: 0,
                    },
                }
                commonSetting(obj)
                    .then((res) => {
                        // this.start()
                        this.showMessage('本地控制切换成功')
                        this.command.controlModel = 0
                    })
                    .catch((err) => {
                        this.localModel = !this.localModel
                    })
            } else {
                let obj = {
                    requestHeadLovol: this.requestHeadLovol,
                    command: {
                        deviceNo: this.currentMachine.deviceCode,
                        controlModel: 3,
                    },
                }
                commonSetting(obj)
                    .then((res) => {
                        // this.end()
                        this.showMessage('本地控制切换成功')
                        // this.command.controlModel = 3
                    })
                    .catch((err) => {
                        this.localModel = !this.localModel
                    })
            }
        },
        resetMove(showMsg) {
            let obj = {
                requestHeadLovol: this.requestHeadLovol,
                commonRemoteControlCommand: {
                    deviceNo: this.currentMachine.deviceCode,
                    controlModel: 2,
                    yk: 1,
                    speedControl: 0,
                    yns: 0,
                    yn: 0,
                    zg: 0,
                },
            }
            this.popoverVisible = false
            this.popoverVisible2 = false
            this.popoverVisible3 = false
            this.popoverVisible4 = false
            commonRemoteControl(obj).then((res) => {
                this.topMove = 0
                this.leftMove = 0
                this.form.speedControl = 0
                this.form.yns = 0
                this.form.zg = 0
                if (showMsg) {
                    this.showMessage('N档切换成功')
                }
            })
        },
        showMessage(msg) {
            this.$message({
                dangerouslyUseHTMLString: true,
                customClass: 'messageboxcustomClass',
                duration: 3000,
                offset: 200,
                message:
                    '<div class="content"><div class="title">消息提示</div><div class="text">' +
                    msg +
                    '</div></div>',
            })
        },
        sliderChange() {
            if (this.command.wy == 1) {
                let yn = 1
                if (this.form.yns > 0) {
                    yn = 3
                }
                if (this.form.yns < 0) {
                    yn = 2
                }
                let speedControl
                if (this.currentMachine.cfgvalue6 == 'b') {
                    speedControl = this.form.speedControl * 10
                } else {
                    speedControl = this.form.speedControl
                }
                let obj = {
                    requestHeadLovol: this.requestHeadLovol,
                    commonRemoteControlCommand: {
                        deviceNo: this.currentMachine.deviceCode,
                        controlModel: 2,
                        speedControl: speedControl,
                        yk: this.topMove + 1,
                        yns: Math.abs(this.form.yns),
                        yn,
                        zg: this.form.zg,
                    },
                }
                commonRemoteControl(obj).then((res) => {})
            } else {
                this.showMessage('请长按点火')
            }
        },
        destroyYh() {
            if (this.command.yh == 1) {
                let yh = 2
                let obj = {
                    requestHeadLovol: this.requestHeadLovol,
                    command: {
                        deviceNo: this.currentMachine.deviceCode,
                        yh: yh,
                    },
                }
                commonSetting(obj).then((res) => {})
            }
        },
        yhChange() {
            //切换模式需要将导航状态置为暂停
            this.command.abu = 2;
            let yh = this.command.yh == 1 ? 2 : 1
            let obj = {
                requestHeadLovol: this.requestHeadLovol,
                command: {
                    deviceNo: this.currentMachine.deviceCode,
                    yh: yh,
                    abu: 0,
                    wy: 0,
                    x2: 0,
                    controlModel: this.command.controlModel,
                },
            }
            commonSetting(obj).then((res) => {
                this.command.yh = yh
                setTimeout(() => {
                    if (yh == 1) {
                        this.showMessage('制动成功')
                    } else {
                        this.showMessage('解除制动成功')
                    }
                }, 500)
            })
        },
        authChange() {
            let obj = {
                ...this.requestHeadLovol,
                deviceNo: this.currentMachine.deviceCode,
            }
            deleteControlAuth(obj).then((res) => {
                this.showMessage('释放成功')
                this.$emit('deleteAuth', this.currentMachine.deviceCode)
            })
        },
        abuChange() {
            if (this.command.wy == 1) {
                let abu = this.command.abu == 1 ? 2 : 1
                let obj = {
                    requestHeadLovol: this.requestHeadLovol,
                    command: {
                        deviceNo: this.currentMachine.deviceCode,
                        controlModel: this.command.controlModel,
                        abu: abu,
                    },
                }
                commonSetting(obj).then((res) => {
                    this.command.abu = abu
                    // this.start()
                    if (this.command.abu == 1) {
                        this.showMessage('开始作业')
                    } else {
                        this.showMessage('暂停作业')
                    }
                })
            } else {
                this.showMessage('请长按点火')
            }
        },

        topMoveChange(event, type) {
            if (this.command.wy == 1) {
                this.topMove = type
                if (type == 2) {
                    const popover = this.$refs.popover
                    let timer = setTimeout(() => {
                        const { top } = event.target.getBoundingClientRect()
                        // 找到气泡元素
                        const { popperElm } = popover
                        popperElm.style.top = top - 530 + 'px'
                        clearTimeout(timer)
                    }, 100)
                }
            } else {
                this.showMessage('请长按点火')
            }
        },
        leftMoveChange(event, type) {
            if (this.command.wy == 1) {
                this.leftMove = type
                if (type == 1) {
                    const popover = this.$refs.left
                    let timer = setTimeout(() => {
                        const { top } = event.target.getBoundingClientRect()
                        // 找到气泡元素
                        const { popperElm } = popover
                        popperElm.style.top = top - 458 + 'px'
                        clearTimeout(timer)
                    }, 100)
                }
                if (type == 2) {
                    const popover = this.$refs.right
                    let timer = setTimeout(() => {
                        const { top } = event.target.getBoundingClientRect()
                        // 找到气泡元素
                        const { popperElm } = popover
                        popperElm.style.top = top - 458 + 'px'
                        clearTimeout(timer)
                    }, 100)
                }
            } else {
                this.showMessage('请长按点火')
            }
        },
        controlModelChange(type) {
            if (this.command.controlModel == type) {
                return
            }
            //切换模式需要将导航状态置为暂停
            this.command.abu = 2;
            //后端有判断 先取消
            // if (this.command.yh == 1) {
            //     this.showMessage('请先解除制动')
            //     return
            // }
            //前端现在没有这个同步这个状态
            // if (this.currentState != 0) {
            //     this.showMessage('正在作业不可切换作业模式')
            //     return
            // }
            let controlModel = type
            let obj = {
                requestHeadLovol: this.requestHeadLovol,
                command: {
                    deviceNo: this.currentMachine.deviceCode,
                    controlModel: controlModel,
                },
            }
            if (controlModel == 0) {
                //本地控制时 需要点击switch触发指令
                //传一个空指令 为了验证农机否处于紧急制动 防止是紧急制动时 本地控制解除不了的问题
                let obj = {
                    requestHeadLovol: this.requestHeadLovol,
                    command: {
                        deviceNo: this.currentMachine.deviceCode,
                    },
                }
                return commonSetting(obj).then((res) => {
                    this.command.controlModel = controlModel
                })
            }
            commonSetting(obj).then((res) => {
                if (controlModel == 1 || controlModel == 2) {
                    this.localModel = false
                }
                this.command.controlModel = controlModel
                this.$emit('onClearPoint')
            })
        },
        speedToRotate() {
            // this.speed = 0
            let total = 120
            let oldRotate = 180
            var rotate = (this.speed * oldRotate) / total
            let offset = 45 //偏移量
            this.rotateLeft = rotate - 90 + offset
            let currentRotation = rotate + offset
            let long = 140
            if (this.speed > 90) {
                this.clipPathValue = ''
            } else {
                if (currentRotation <= 45) {
                    let dis = long * Math.tan(Math.PI / (180 / currentRotation))
                    this.clipPathValue = `polygon(100% 100%, 100% 50%, ${
                        213 - dis
                    }px 100%, 100% 100%, 0 100%)`
                } else if (currentRotation <= 135) {
                    let dis =
                        213 * Math.tan(Math.PI / (180 / (90 - currentRotation)))
                    this.clipPathValue = `polygon(100% 100%, 100% 50%, 0 ${
                        280 - (long - dis)
                    }px, 0 100%, 0 100%)`
                } else if (currentRotation <= 180) {
                    let dis = long * Math.tan(Math.PI / (180 / currentRotation))
                    this.clipPathValue = `polygon(100% 100%, 100% 50%, ${
                        213 + dis
                    }px 0, 0 0, 0 100%)`
                }
            }
            if (this.rotateLeft >= 80) {
                this.rotateLeft = 80
            }
        },
        rotateToRotate() {
            // this.rotate = 10
            let total = 9000
            let oldRotate = 180
            var rotate = (this.rotate * oldRotate) / total
            let offset = 45 //偏移量
            this.rotateRight = rotate - 90 + offset
            let currentRotation = rotate + offset
            let long = 140
            if (this.rotate > 6000) {
                this.clipPathValueRight = ''
            } else {
                if (currentRotation <= 45) {
                    let dis = long * Math.tan(Math.PI / (180 / currentRotation))
                    this.clipPathValueRight = `polygon( 100% 100%, 100% 100%, 0 100%, 0 50%, ${dis}px 100%)`
                } else if (currentRotation <= 135) {
                    let dis =
                        213 * Math.tan(Math.PI / (180 / (90 - currentRotation)))
                    this.clipPathValueRight = `polygon(100% 100%, 100% 100%, 0 100%, 0 50%, 100% ${
                        280 - (140 - dis)
                    }px)`
                } else if (currentRotation <= 180) {
                    let dis =
                        long *
                        Math.tan(Math.PI / (180 / (180 - currentRotation)))
                    this.clipPathValueRight = `polygon( 100% 0, 100% 100%, 0 100%, 0 50%, ${dis}px 0)`
                }
            }

            if (this.rotateRight >= 80) {
                this.rotateRight = 80
            }
        },
        changedrawerRight() {
            this.currentDrawerRight = !this.currentDrawerRight
        },
        openControlDisplayDialog() {
            this.$emit('openControlDisplayDialog')
        },
        openCreateTaskDialog() {
            this.$emit('openCTDialog')
        },
        openParameterConfigurationDialog() {
            this.$emit('openParameterConfigurationDialog')
        },
        handfire() {
            if (
                this.command.controlModel == 1 ||
                this.command.controlModel == 2
            ) {
                if (
                    this.command.controlModel == 1 &&
                    !this.currentData.jobnameid
                ) {
                    this.showMessage('请新建任务')
                    return
                }
                // if (this.command.yh == 1) {
                //     this.showMessage('请先解除制动')
                //     return
                // }
                let wy = this.command.wy == 1 ? 2 : 1
                let abu = this.command.abu
                if (wy == 2) {
                    abu = 2
                }
                let obj = {
                    requestHeadLovol: this.requestHeadLovol,
                    command: {
                        deviceNo: this.currentMachine.deviceCode,
                        wy: wy,
                        abu: abu,
                        controlModel: this.command.controlModel,
                    },
                }
                commonSetting(obj).then((res) => {
                    this.command.wy = wy
                    if (this.command.wy == 1) {
                        this.playAudio()
                        if (this.command.controlModel == 2) {
                            //手动摇控时 点火即开始作业
                            // this.start()
                            this.showMessage('手动遥控点火成功')
                        } else {
                            this.showMessage('自主作业点火成功')
                        }
                    } else {
                        this.command.abu = 2
                        this.stopAudio()
                        this.showMessage('熄火成功')
                        this.resetMove()
                    }
                })
            } else {
                this.showMessage('请选择操作模式')
            }
        },
        reset() {
            // this.end()
            this.stopAudio()
            this.controlModel = false
            this.topMove = 0
            this.leftMove = 0
            this.rotateLeft = -45
            this.rotateRight = -45
            this.speed = 0
            this.rotate = 0
            this.form.speedControl = 0
            this.form.yns = 0
            this.form.zg = 0
            this.clipPathValue =
                'polygon(100% 100%, 100% 59%, 100% 100%, 100% 100%, 0 100%)'
            this.clipPathValueRight =
                'polygon(100% 100%, 100% 100%, 0 100%, 0 55%, 0 100%)'
            this.command = {
                //设备号
                deviceNo: this.currentMachine.deviceCode,
                /**
                 * 控制模式
                 * 0: 本地控制模式
                 *    1：自主作业模式
                 *    2：手动遥控模式
                 *    3：无效(默认)
                 */
                controlModel: 3,
                /**
                 * 导航启动设置
                 * 0：无动作(默认)
                 *    1：导航开始
                 *    2：导航暂停
                 *    3：导航禁止
                 */
                abu: 0,
                /**
                 * 手机断连
                 * 0：无动作(默认)
                 *    1：断开连接
                 *
                 */
                appDisconnect: 0,
                /**
                 * 整机启动设置
                 * 0：无动作(默认)
                 *    1：启动
                 *    2：熄火
                 *    3：无效
                 */
                wy: 0,
                /**
                 * 系统通电设置
                 * 0：无动作(默认)
                 *    1：通电
                 *    2：断电
                 *    3：无效
                 */
                x2: 0,
                /**
                 * 紧急制动
                 * 0：无动作(默认)
                 *    1：紧急制动
                 *    2：解除制动
                 *    3：无效
                 */
                yh: 0,
            }
        },
    },
}
</script>

<style scoped lang="scss">
.popoverclose {
    position: absolute;
    top: 10px;
    right: 10px;
    color: #1c8aff;
    font-size: 20px;
    cursor: pointer;
}

.gear {
    color: #95d7ff;
    font-size: 20px;
    position: absolute;
    left: 422px;
    top: 166px;
    width: 55px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 50%;
}

::-webkit-scrollbar-corner {
    background-color: inherit !important;
}

:deep(.input .el-input__inner) {
    background-color: transparent;
    border: none;
    color: #fff;
}

    :deep(.el-select-dropdown__list){
        background-color: #030a27;
    }

    :deep(.el-select-dropdown) {
        border: 1px solid #08599d;
    }

    :deep(.el-popper[x-placement^='bottom'] .popper__arrow::after) {
        border-bottom-color: #08599d;
    }

    :deep(.el-popper[x-placement^='bottom'] .popper__arrow) {
        border-bottom-color: #08599d;
    }

    :deep(.el-scrollbar__wrap) {
        overflow-x: hidden;
    }

    :deep(.el-input-group__append) {
        background-color: rgba(8, 105, 204, 0.15);
        border: 1px solid rgba(23, 133, 255, 0.27);
        border-left: none;
    }

    :deep(.append .el-input__inner) {
        border-right: none !important;
    }

.form {
    :deep(.el-select-dropdown__list) {
        background-color: #030a27;
    }

    //下拉框
    :deep(.el-select-dropdown) {
        border: none;
        background-color: rgba(1, 28, 82, 0.8);
    }

    //输入框
    :deep(.el-input__inner) {
        color: #eee;
        border-color: #00fff6;
        background-color: rgba(1, 28, 82, 0.8);
    }

    //聚焦时的样式
    :deep(.el-select .el-input.is-focus .el-input__inner) {
        border-color: #0b61aa;
        background-color: rgba(1, 28, 82, 0.8);
        color: #00d3e9;
    }

    //下拉框选中
    :deep(.el-select-dropdown__item) {
        color: #eee;
    }

    //鼠标经过下拉框
    :deep(.el-select-dropdown__item.hover),
    :deep(.el-select-dropdown__item:hover) {
        color: #00d3e9;
        background-color: #0f3360;
    }
}

:deep(
    .el-switch__label) {
        color: #adbfe6;
    }

    :deep(.el-switch__core) {
        height: 4px;

        &:after {
            border: 1px solid #94c2ff;
            top: -7px;
            left: -1px;
        }
    }

    :deep(.el-switch.is-checked .el-switch__core::after) {
        left: 104%;
    }

:deep(.el-slider__button-wrapper) {
    border: 1px solid #62aafe;
    opacity: 0.5;
    border-radius: 50%;
    width: 27px;
    height: 27px;
    top: -10px;
    margin-left: -4px;
}

:deep(.el-slider__button) {
    width: 12px;
    height: 12px;
    background: #99c8ff;
    border-radius: 50%;
}

:deep(.el-slider__runway) {
    background: rgba(8, 124, 255, 0.1);
    border-radius: 4px;
    height: 9px;
}

:deep(.el-slider__bar) {
    background: linear-gradient(90deg, #061733, #1c8aff);
    border-radius: 4px;
    height: 9px;
}

:deep(.el-slider__stop) {
    display: none;
}

:deep(.el-slider__marks-text) {
    width: max-content;
    margin-top: 20px;
}

.popovertop {
    background-image: url('@/views/systemagriculturalmachineryv2/assets/images/topTitle.png');
    background-repeat: no-repeat;
    background-size: cover;
    width: 177px;
    height: 38px;
    font-size: 16px;
    font-weight: 500;
    color: #c2e1ff;
    line-height: 38px;
    text-align: center;
    margin: 0 auto;
}

.popoverform {
    padding: 0 40px;
}

.footer {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 0 auto;
    width: 912px;
    z-index: 50;
    user-select: none;
    transition: all 1s;

    .footerPanel {
        position: relative;
        width: 912px;
        height: 372px;
        margin: 0 auto;
        background-image: url('@/views/systemagriculturalmachineryv2/assets/images/nomannedbg.png');
        background-size: cover;
        // clip-path: polygon(55% 13%, 76% 53px, 80% 55px, 752px 61px, 85% 20%, 793px 86px, 801px 96px, 813px 108px, 93% 144px, 95% 47%, 95% 54%, 95% 56%, 94% 69%, 94% 85%, 93% 80%, 92% 86%, 90% 90%, 91% 92%, 8% 93%, 6% 77%, 2% -18%, -13% 62%, 1% 90%, -11% 74%, -10% 43%, -2% 39%, 7% 39%, 10% 31%, 14% 22%, 17% 17%, 21% 15%);

        .controlcolumn {
            position: absolute;
            left: 276px;
            bottom: 320px;
            background-image: url('@/views/systemagriculturalmachineryv2/assets/images/controlcolumn.png');
            background-size: cover;
            width: 364px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-evenly;
            padding: 0 20px;

            .content {
                font-size: 14px;
                font-weight: 500;
                color: #95d7ff;
                cursor: pointer;
                text-align: center;
                height: 40px;
                line-height: 40px;
                background-position: center;
                margin-top: 14px;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .content_switch {
                width: 200px;
                font-size: 14px;
                font-weight: 500;
                color: #95d7ff;
                cursor: pointer;
                text-align: center;
                height: 40px;
                line-height: 40px;
                background-position: center;
                margin-top: 14px;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .content_show {
                height: 40px;
                line-height: 40px;
                color: #95d7ff;
                margin-top: 14px;
                font-size: 14px;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;

                & > img {
                    width: 16px;
                    height: 16px;
                    margin-right: 8px;
                }
            }

            .content_arg {
                width: 180px;
                font-size: 14px;
                font-weight: 500;
                color: #95d7ff;
                cursor: pointer;
                text-align: center;
                height: 40px;
                line-height: 40px;
                background-position: center;
                margin-top: 14px;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .content_add {
                display: flex;
                align-items: center;
                padding-top: 14px;
                height: 40px;
                line-height: 40px;
                color: #95d7ff;
                font-size: 14px;
                cursor: pointer;
            }
        }

        .maskleft {
            position: absolute;
            left: 47px;
            bottom: 31px;
            width: 213px;
            height: 280px;
            overflow: hidden;
            transition: all 1s;

            .mask {
                position: absolute;
                left: -1px;
                bottom: 1px;
            }
        }

        .maskleftPoint {
            position: absolute;
            left: 47px;
            bottom: 31px;
            width: 213px;
            height: 280px;

            // overflow: hidden;
            // clip-path: polygon(0 38%, 15% 17%, 35% 0, 85% 0%, 100% 0, 100% 38%, 100% 85%, 100% 100%, 85% 100%, 11% 100%, 6% 89%, 3% 70%);
            .pointermask {
                position: absolute;
                top: 10px;
                left: 10px;
                width: 240px;
                height: 121px;
            }

            .scale0 {
                font-size: 16px;
                font-weight: 500;
                color: #8590a3;
                position: absolute;
                left: 79px;
                bottom: 12px;
                font-family: RegularEnglish;

                &.active {
                    color: #e5f1f9;
                }
            }

            .pointerBor0 {
                width: 4px;
                height: 4px;
                background-color: #8590a3;
                border-radius: 50%;
                position: absolute;
                left: 82px;
                bottom: 8px;
                font-family: RegularEnglish;

                &.active {
                    background-color: #e5f1f9;
                    box-shadow: 0px 3px 2px 0px rgba(4, 10, 29, 0.38);
                }
            }

            .scale30 {
                font-size: 16px;
                font-weight: 500;
                color: #8590a3;
                position: absolute;
                left: 42px;
                bottom: 28px;
                font-family: RegularEnglish;

                &.active {
                    color: #e5f1f9;
                }
            }

            .pointerBor30 {
                width: 4px;
                height: 4px;
                background-color: #8590a3;
                border-radius: 50%;
                position: absolute;
                left: 34px;
                bottom: 34px;
                font-family: RegularEnglish;

                &.active {
                    background-color: #e5f1f9;
                    box-shadow: 0px 3px 2px 0px rgba(4, 10, 29, 0.38);
                }
            }

            .scale60 {
                font-size: 16px;
                font-weight: 500;
                color: #8590a3;
                position: absolute;
                left: 27px;
                bottom: 78px;
                font-family: RegularEnglish;

                &.active {
                    color: #e5f1f9;
                }
            }

            .pointerBor60 {
                width: 4px;
                height: 4px;
                background-color: #8590a3;
                border-radius: 50%;
                position: absolute;
                left: 19px;
                bottom: 84px;
                font-family: RegularEnglish;

                &.active {
                    background-color: #e5f1f9;
                    box-shadow: 0px 3px 2px 0px rgba(4, 10, 29, 0.38);
                }
            }

            .scale100 {
                font-size: 16px;
                font-weight: 500;
                color: #8590a3;
                position: absolute;
                left: 17px;
                bottom: 132px;
                font-family: RegularEnglish;

                &.active {
                    color: #e5f1f9;
                }
            }

            .pointerBor100 {
                width: 4px;
                height: 4px;
                background-color: #8590a3;
                border-radius: 50%;
                position: absolute;
                left: 10px;
                bottom: 137px;
                font-family: RegularEnglish;

                &.active {
                    background-color: #e5f1f9;
                    box-shadow: 0px 3px 2px 0px rgba(4, 10, 29, 0.38);
                }
            }

            .scale140 {
                font-size: 16px;
                font-weight: 500;
                color: #8590a3;
                position: absolute;
                left: 33px;
                bottom: 179px;
                font-family: RegularEnglish;

                &.active {
                    color: #e5f1f9;
                }
            }

            .pointerBor140 {
                width: 4px;
                height: 4px;
                background-color: #8590a3;
                border-radius: 50%;
                position: absolute;
                left: 24px;
                bottom: 186px;
                font-family: RegularEnglish;

                &.active {
                    background-color: #e5f1f9;
                    box-shadow: 0px 3px 2px 0px rgba(4, 10, 29, 0.38);
                }
            }

            .scale170 {
                font-size: 16px;
                font-weight: 500;
                color: #8590a3;
                position: absolute;
                left: 69px;
                bottom: 217px;
                font-family: RegularEnglish;

                &.active {
                    color: #e5f1f9;
                }
            }

            .pointerBor170 {
                width: 4px;
                height: 4px;
                background-color: #8590a3;
                border-radius: 50%;
                position: absolute;
                left: 60px;
                bottom: 223px;
                font-family: RegularEnglish;

                &.active {
                    background-color: #e5f1f9;
                    box-shadow: 0px 3px 2px 0px rgba(4, 10, 29, 0.38);
                }
            }

            .scale200 {
                font-size: 16px;
                font-weight: 500;
                color: #8590a3;
                position: absolute;
                left: 104px;
                bottom: 245px;
                font-family: RegularEnglish;

                &.active {
                    color: #e5f1f9;
                }
            }

            .pointerBor200 {
                width: 4px;
                height: 4px;
                background-color: #8590a3;
                border-radius: 50%;
                position: absolute;
                left: 95px;
                bottom: 251px;
                font-family: RegularEnglish;

                &.active {
                    background-color: #e5f1f9;
                    box-shadow: 0px 3px 2px 0px rgba(4, 10, 29, 0.38);
                }
            }

            .scale230 {
                font-size: 16px;
                font-weight: 500;
                color: #8590a3;
                position: absolute;
                left: 128px;
                bottom: 255px;
                font-family: RegularEnglish;

                &.active {
                    color: #e5f1f9;
                }
            }

            .pointerBor230 {
                width: 4px;
                height: 4px;
                background-color: #8590a3;
                border-radius: 50%;
                position: absolute;
                left: 132px;
                bottom: 273px;
                font-family: RegularEnglish;

                &.active {
                    background-color: #e5f1f9;
                    box-shadow: 0px 3px 2px 0px rgba(4, 10, 29, 0.38);
                }
            }

            .scale260 {
                font-size: 16px;
                font-weight: 500;
                color: #8590a3;
                position: absolute;
                left: 164px;
                bottom: 255px;
                font-family: RegularEnglish;

                &.active {
                    color: #e5f1f9;
                }
            }

            .pointerBor260 {
                width: 4px;
                height: 4px;
                background-color: #8590a3;
                border-radius: 50%;
                position: absolute;
                left: 173px;
                bottom: 273px;
                font-family: RegularEnglish;

                &.active {
                    background-color: #e5f1f9;
                    box-shadow: 0px 3px 2px 0px rgba(4, 10, 29, 0.38);
                }
            }

            .pointer {
                width: 225px;
                height: 1px;
                position: absolute;
                top: 140px;
                right: 1px;
                // background-color: #3498db;
                transform-origin: right bottom;
                transition: all 1s;
                display: flex;
                align-items: center;
                z-index: 2;
            }
        }

        .maskright {
            position: absolute;
            left: 652px;
            bottom: 31px;
            width: 213px;
            height: 280px;
            overflow: hidden;
            transition: all 1s;

            .mask {
                position: absolute;
                right: 0;
                bottom: 0;
            }
        }

        .maskrightPoint {
            position: absolute;
            left: 652px;
            bottom: 31px;
            width: 213px;
            height: 280px;

            .scale00 {
                font-size: 16px;
                font-weight: 500;
                color: #8590a3;
                position: absolute;
                left: 124px;
                bottom: 12px;
                font-family: RegularEnglish;

                &.active {
                    color: #e5f1f9;
                }
            }

            .pointerBor00 {
                width: 4px;
                height: 4px;
                background-color: #8590a3;
                border-radius: 50%;
                position: absolute;
                left: 127px;
                bottom: 8px;
                font-family: RegularEnglish;

                &.active {
                    background-color: #e5f1f9;
                    box-shadow: 0px 3px 2px 0px rgba(4, 10, 29, 0.38);
                }
            }

            .scale1000 {
                font-size: 16px;
                font-weight: 500;
                color: #8590a3;
                position: absolute;
                left: 141px;
                bottom: 44px;
                font-family: RegularEnglish;

                &.active {
                    color: #e5f1f9;
                }
            }

            .pointerBor1000 {
                width: 4px;
                height: 4px;
                background-color: #8590a3;
                border-radius: 50%;
                position: absolute;
                left: 183px;
                bottom: 51px;
                font-family: RegularEnglish;

                &.active {
                    background-color: #e5f1f9;
                    box-shadow: 0px 3px 2px 0px rgba(4, 10, 29, 0.38);
                }
            }

            .scale2000 {
                font-size: 16px;
                font-weight: 500;
                color: #8590a3;
                position: absolute;
                left: 145px;
                bottom: 112px;
                font-family: RegularEnglish;

                &.active {
                    color: #e5f1f9;
                }
            }

            .pointerBor2000 {
                width: 4px;
                height: 4px;
                background-color: #8590a3;
                border-radius: 50%;
                position: absolute;
                left: 194px;
                bottom: 119px;
                font-family: RegularEnglish;

                &.active {
                    background-color: #e5f1f9;
                    box-shadow: 0px 3px 2px 0px rgba(4, 10, 29, 0.38);
                }
            }

            .scale3000 {
                font-size: 16px;
                font-weight: 500;
                color: #8590a3;
                position: absolute;
                left: 132px;
                bottom: 178px;
                font-family: RegularEnglish;

                &.active {
                    color: #e5f1f9;
                }
            }

            .pointerBor3000 {
                width: 4px;
                height: 4px;
                background-color: #8590a3;
                border-radius: 50%;
                position: absolute;
                left: 181px;
                bottom: 185px;
                font-family: RegularEnglish;

                &.active {
                    background-color: #e5f1f9;
                    box-shadow: 0px 3px 2px 0px rgba(4, 10, 29, 0.38);
                }
            }

            .scale4000 {
                font-size: 16px;
                font-weight: 500;
                color: #8590a3;
                position: absolute;
                left: 87px;
                bottom: 225px;
                font-family: RegularEnglish;

                &.active {
                    color: #e5f1f9;
                }
            }

            .pointerBor4000 {
                width: 4px;
                height: 4px;
                background-color: #8590a3;
                border-radius: 50%;
                position: absolute;
                left: 137px;
                bottom: 233px;
                font-family: RegularEnglish;

                &.active {
                    background-color: #e5f1f9;
                    box-shadow: 0px 3px 2px 0px rgba(4, 10, 29, 0.38);
                }
            }

            .scale5000 {
                font-size: 16px;
                font-weight: 500;
                color: #8590a3;
                position: absolute;
                left: 38px;
                bottom: 256px;
                font-family: RegularEnglish;

                &.active {
                    color: #e5f1f9;
                }
            }

            .pointerBor5000 {
                width: 4px;
                height: 4px;
                background-color: #8590a3;
                border-radius: 50%;
                position: absolute;
                left: 89px;
                bottom: 263px;
                font-family: RegularEnglish;

                &.active {
                    background-color: #e5f1f9;
                    box-shadow: 0px 3px 2px 0px rgba(4, 10, 29, 0.38);
                }
            }

            .pointer {
                width: 225px;
                height: 1px;
                position: absolute;
                top: 140px;
                left: 1px;
                transform-origin: left bottom;
                transition: all 1s;
                display: flex;
                align-items: center;
                justify-content: flex-end;
                z-index: 2;
            }
        }

        .speed {
            font-size: 68px;
            font-family: RegularEnglish;
            font-weight: 600;
            color: #ffffff;
            background: linear-gradient(to bottom, #ffffff 35%, #358de1 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: absolute;
            left: 115px;
            bottom: 116px;
            width: 202px;
            text-align: center;
            letter-spacing: 2px;
            // text-shadow: 0px 3px 2px #fff;
        }

        .rotational {
            font-size: 68px;
            font-family: RegularEnglish;
            font-weight: 600;
            color: #ffffff;
            background: linear-gradient(to bottom, #ffffff 35%, #358de1 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            position: absolute;
            left: 591px;
            bottom: 116px;
            width: 210px;
            text-align: center;
            letter-spacing: 2px;
        }

        .controlText {
            position: absolute;
            left: 311px;
            bottom: 0px;
        }

        .controltop {
            position: absolute;
            left: 388px;
            bottom: 137px;
        }

        .controlright {
            position: absolute;
            left: 409px;
            bottom: 114px;
        }

        .controlback {
            position: absolute;
            left: 385px;
            bottom: 96px;
        }

        .controlleft {
            position: absolute;
            left: 369px;
            bottom: 114px;
        }

        .controltopText {
            position: absolute;
            left: 411px;
            bottom: 215px;
            color: #d7ebff;
            width: 80px;
            text-align: center;
            line-height: 30px;
            cursor: pointer;
        }

        .controlrightText {
            position: absolute;
            left: 491px;
            bottom: 140px;
            color: #d7ebff;
            height: 70px;
            width: 30px;
            text-align: center;
            line-height: 70px;
            cursor: pointer;
        }

        .controlbackText {
            position: absolute;
            left: 410px;
            bottom: 108px;
            color: #d7ebff;
            width: 80px;
            text-align: center;
            line-height: 30px;
            cursor: pointer;
        }

        .controlleftText {
            position: absolute;
            left: 379px;
            bottom: 140px;
            color: #d7ebff;
            height: 70px;
            width: 30px;
            text-align: center;
            line-height: 70px;
            cursor: pointer;
        }

        .autonomous_box {
            width: 340px;
            height: 31px;
            position: absolute;
            left: 280px;
            top: 55px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .autonomous {
                font-size: 14px;
                font-weight: 500;
                color: #95d7ff;
                cursor: pointer;
                background-size: cover;
                flex: 1;
                text-align: center;
                height: 28px;
                line-height: 28px;
                background-position: center;

                &.active {
                    background-image: url('@/views/systemagriculturalmachineryv2/assets/images/hover.png');
                }
            }

            .hand {
                font-size: 14px;
                font-weight: 500;
                color: #95d7ff;
                cursor: pointer;
                background-size: cover;
                flex: 1;
                text-align: center;
                height: 28px;
                line-height: 28px;
                background-position: center;

                &.active {
                    background-image: url('@/views/systemagriculturalmachineryv2/assets/images/hover.png');
                }
            }

            .locality {
                font-size: 14px;
                font-weight: 500;
                color: #95d7ff;
                cursor: pointer;
                background-size: cover;
                flex: 1;
                text-align: center;
                height: 28px;
                line-height: 28px;
                background-position: center;

                &.active {
                    background-image: url('@/views/systemagriculturalmachineryv2/assets/images/hover.png');
                }
            }
        }

        .emergent_box {
            width: 300px;
            height: 31px;
            position: absolute;
            left: 300px;
            bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;

            .emergent {
                font-size: 14px;
                font-weight: 500;
                color: #95d7ff;
                cursor: pointer;
                flex: 1;
                text-align: center;
                height: 28px;
                line-height: 28px;
                background-size: cover;
                background-position: center;
                background-repeat: no-repeat;
                &.active {
                    background-image: url('@/views/systemagriculturalmachineryv2/assets/images/hover.png');
                }
            }

            .navigation {
                font-size: 14px;
                font-weight: 500;
                color: #95d7ff;
                cursor: pointer;
                flex: 1;
                text-align: center;
                height: 28px;
                line-height: 28px;
                background-size: cover;
                background-position: center;

                &.active {
                    background-image: url('@/views/systemagriculturalmachineryv2/assets/images/hover.png');
                }
            }

            .fire {
                font-size: 14px;
                font-weight: 500;
                color: #95d7ff;
                cursor: pointer;
                flex: 1;
                text-align: center;
                height: 28px;
                line-height: 28px;
                background-size: cover;
                background-position: center;
                max-width: 100px;
                background-repeat: no-repeat;
                &.active {
                    background-image: url('@/views/systemagriculturalmachineryv2/assets/images/hover.png');
                }
            }
        }
    }
}

:deep(.el-dialog__wrapper) {
    position: absolute;
}

:deep(.el-dialog) {
    background: url('@/views/systemagriculturalmachineryv2/assets/images/bg.png') no-repeat !important;
    background-size: 100% 100% !important;
    padding-bottom: 20px;
}

:deep(.el-dialog__title) {
    position: absolute;
    top: 5%;
    left: 0%;
    display: block;
    width: 100%;
    text-align: center;
    color: #fff !important;
    font-weight: 700;
    font-size: 23px !important;
}

:deep(.el-dialog__body) {
}

:deep(.el-dialog__headerbtn) {
    top: 75px;
    right: 40px;
}

:deep(.el-dialog__body .abc) {
    height: 565px;
    overflow: auto;
}

:deep(.el-dialog__body .myCon p) {
    background-color: #0d4e68 !important;
    color: #fff;
}

.tabs {
    width: 248px;
    height: 40px;
    background: rgba(91, 162, 227, 0.05);
    border: 1px solid #08599d;
    border-radius: 4px;
    margin: 0px auto 0;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .tabItem {
        width: 120px;
        height: 32px;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #e4f2ff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #7fbef8;
        opacity: 0.6;
    }

    .tabActive {
        background: rgba(1, 125, 255, 0.5);
        border: 0px solid #070002;
        opacity: 1;
        color: #ffffff;
    }
}

.form{
    padding: 0 50px 0 30px;
    margin-top: 20px;

    :deep(.el-form-item__label) {
        color: #c2e1fe;
        font-size: 16px;
    }

    :deep(.el-radio) {
        color: #c2e1fe;
        width: 60px;
    }

    :deep(.el-radio__inner) {
        border: 1px solid rgba(23, 133, 255, 0.4);
        background: rgba(8, 105, 204, 0.2);

        &::after {
            background: #1193fd;
        }
    }

    :deep(.el-radio__input.is-checked + .el-radio__label) {
        color: #c2e1fe;
    }

    :deep(.el-input__inner) {
        background: rgba(8, 105, 204, 0.15);
        border: 1px solid rgba(23, 133, 255, 0.27);
        color: #7fbef8;
    }

    :deep(.el-input-number__decrease) {
        width: 66px;
        height: 34px;
        background: #0056a9;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #ffffff;
        border-right: none;
    }

    :deep(.el-input-number__increase) {
        width: 66px;
        height: 34px;
        background: #0056a9;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #ffffff;
        border-left: none;
    }

    :deep(.el-input-number--medium) {
        width: 260px;
    }

    :deep(.el-input-number .el-input) {
        margin-left: 74px;
        width: 106px;
        height: 36px;
        background: rgba(8, 105, 204, 0.15);
        border: 1px solid rgba(23, 133, 255, 0.27);
        border-radius: 4px;
    }

    .cancel {
        width: 121px;
        border: 1px solid #1193fd;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        background-color: transparent;
        color: #6896d1;
    }

    .submit {
        width: 120px;
        background: linear-gradient(-46deg, #0056a9, #0a83e5);
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        font-size: 16px;
        font-weight: 500;
        color: #e3f1ff;
        border: none;
    }

    .click {
        width: 60px;
        height: 28px;
        background: #0056a9;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #c2e1fe;
        border: none;
        text-align: center;
        line-height: 28px;
        padding: 0;
    }

    .reset {
        width: 60px;
        height: 28px;
        background: #0056a9;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #c2e1fe;
        border: none;
        text-align: center;
        line-height: 28px;
        padding: 0;
    }
}

.newbottom {
    padding: 20px;
    width: 506px;
    height: 1011px;
    background: rgba(8, 105, 204, 0.15);
    border-radius: 4px;
}

.newpoint {
    width: 464px;
    background: rgba(7, 15, 29, 0.55);
    border-radius: 4px;
    padding: 10px 0px 0px;
    margin-bottom: 10px;

    .newtop {
        // border-left: 2px solid #0075E6;
        margin-left: -20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        color: #c2e1fe;
        padding-left: 20px;
        padding-right: 20px;

        .borderLeft {
            width: 2px;
            height: 20px;
            background: #0075e6;
        }

        .newtoppic {
            margin-left: -10px;
        }
    }

    .newtbottom {
        padding-left: 20px;
        padding-bottom: 1px;
        padding-right: 20px;
    }
}
.popovermove{
    width: 100%;
    height: 30px;
    cursor: move;
}
</style>
