<template>
    <div class="select">
        <div class="select-input">
            <div class="select-input-name">出厂编号：</div>
            <el-input
                placeholder="请输入出厂编号"
                clearable
                v-model="keyword"
                @change="updateSelectDialogVisible(true)"
                @focus="updateSelectDialogVisible(true)"
            >
                <template #append>
                    <el-button
                        icon="CaretBottom"
                        @click="updateSelectDialogVisible(!isSelectDialogVisible)"
                    ></el-button>
                </template>
            </el-input>
        </div>
        <div class="select-dialog" v-if="isSelectDialogVisible" v-loading="isLoading">
            <el-tree
                class="filter-tree"
                :data="filteredTreeData"
                :props="defaultTreeProps"
                :filter-node-method="filterNode"
                node-key="codeLabel"
                :default-expanded-keys="defaultExpandedKeys"
                @node-click="handleTreeNodeClick"
                ref="tree"
            >
                <template class="custom-tree-node" #default="{node,data}">
                    <!-- <span>{{ data.name + "（" + data.label+ "）" }}</span> -->
                    <span :title="data.name + `（` + data.label + `）`">{{
                        data.name + '（' + data.label + '）'
                    }}</span>
                    <img
                        class="locate"
                        title="定位"
                        :src="location"
                        style="width: 15px;"
                        v-if="data.lng && data.lat"
                    />
                </template>
            </el-tree>
        </div>
    </div>
</template>

<script>
/**
 * 组件说明: 车辆位置地图组件
 */
import '@bdh-gis/mapbox-gl/dist/mapbox-gl.css'
import { mapWritableState, mapActions } from 'pinia'
import useMapStore from "@/store/modules/map"
const DEFAULT_TIME = 1000 * 300
import location from '@/views/systemagriculturalmachineryv2/assets/static/icon/location.svg'
const debounce = (function () {
    let timer = 0
    return function (fn, timeout) {
        if (timer) {
            clearTimeout(timer)
            timer = null
        }
        clearTimeout(timer)
        timer = setTimeout(() => {
            fn.apply(this, arguments)
        }, timeout || 300)
    }
})()

export default {
    name: 'Select',
    components: {},
    mixins: [],
    data() {
        return {
            location,
            timer: null,
            keyword: '',
            isSelectDialogVisible: false,
            treeData: [],
            filteredTreeData: [],
            defaultExpandedKeys: [],
            defaultTreeProps: {
                children: 'children',
                label: 'name',
            },
            isLoading: false
        }
    },
    computed: {
        ...mapWritableState(useMapStore, ['vehicleTree','isOfflineCount','isOnlineCount']),
        states() {
            let states = []
            if (this.isOfflineCount) {
                states.push('2')
            }
            if (this.isOnlineCount) {
                states.push('1')
            }
            return states.join(',')
        },
    },
    watch: {
        vehicleTree(value) {
            this.isLoading = false
            this.treeData = value
            this.filteredTreeData = value
            // this.$nextTick(() =>  this.$refs.tree?.filter(this.keyword));
        },
        keyword(val) {
            this.isSelectDialogVisible = false
            debounce(() => {
                //延时器
                // this.$refs.tree.filter(val)
                if (!val?.trim()) {
                    this.filteredTreeData = this.treeData
                    this.defaultExpandedKeys = []
                    return
                }
                let _keys = []
                const filter = (arr) => {
                    const result = []
                    for (let index = 0; index < arr.length; index++) {
                        const item = arr[index]
                        const obj = { ...item }
                        obj.codeLabel = obj.code + obj.label
                        if (obj.children?.length) {
                            obj.children = filter(obj.children)
                        }
                        if (
                            obj.children?.length > 0 ||
                            obj.label.includes(val)
                        ) {
                            result.push(obj)
                            // _keys.push(obj.label)
                            _keys.push(obj.codeLabel)
                        }
                    }
                    return result
                }
                this.filteredTreeData = filter(this.treeData)

                if(_keys.length < 7){
                    this.defaultExpandedKeys = [..._keys]
                } else {
                    this.defaultExpandedKeys = [this.filteredTreeData[0].codeLabel]
                }

            })
            this.isSelectDialogVisible = true
        },
        states(value) {
            this.init()
        },
    },
    created() {
        this.init()
    },
    mounted() {},
    beforeDestroy() {
        this.clearTimer()
    },
    methods: {
        ...mapActions(useMapStore, ['getVehicleTree']),
        init() {
            this.initTree()
        },
        initTree() {
            const { states } = this
            this.getVehicleTree({ states })
            this.isLoading = true
            this.clearTimer()
            this.timer = setInterval(() => {
                //创建定时器
                this.getVehicleTree({ states })
            }, DEFAULT_TIME)
        },
        clearTimer() {
            // 清除定时器
            this.timer && clearInterval(this.timer)
            this.timer = null
        },
        // filterNode(value, data) {
        //     if (!value) return true;
        //     return data.label.indexOf(value) !== -1;
        // },
        filterNode(value, data, node) {
            if (!value) return true
            let parentNode = node.parent,
                labels = [data.label],
                level = 1
            while (level - data.level < 0) {
                labels = [...labels, parentNode.data.label]
                parentNode = parentNode.parent
                level++
            }
            return labels.some((label) => label.indexOf(value) !== -1)
        },

        updateSelectDialogVisible(isVisible) {
            this.isSelectDialogVisible = isVisible
        },
        handleTreeNodeClick(data, node, tree) {
            if (node.isLeaf) this.keyword = data.label
            this.$emit('onTreeNodeClick', data, node, tree)
        },
    },
}
</script>

<style rel="stylesheet" lang="scss" scoped>
@import '@/views/systemagriculturalmachineryv2/assets/styles/variables.scss';
.select {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: start;
    .select-input {
        background-color: rgba(var(--background-color), 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 5px;
        border-left: 4px solid rgba(var(--active-color), 1);
        .select-input-name {
            width: 80px;
        }
        .el-input {
            width: 15vw;
            :deep(.el-input__inner) {
                /*background: none;*/
            }
            :deep(.el-button) {
                .el-icon-caret-bottom {
                    color: rgba(var(--active-color), 1);
                    font-size: 20px;
                }
            }
        }
    }
    .select-dialog {
        background-color: rgba(var(--background-color), 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 10px;
        padding: 5px;
        border-left: 4px solid rgba(var(--active-color), 0);
        :deep(.el-tree) {
            width: calc(64px + 15vw);
            max-height: 35vh;
            overflow-y: auto;
            background: none;
            color: rgba(var(--color), 1);
            :deep(.el-tree-node:focus > .el-tree-node__content) {
                background-color: rgba(var(--active-color), 0.5);
                color: rgba(var(--active-color), 1);
            }
            :deep(.el-tree-node__content:hover) {
                background-color: rgba(var(--active-color), 0.5);
                color: rgba(var(--active-color), 1);
            }
        }
        & :deep(.el-loading-mask) {
            background: rgba(var(--background-color), 1);
        }
    }
    :deep(.el-tree) {
        .custom-tree-node {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            .locate {
                width: 15px;
                height: 17px;
            }
        }
    }
}
</style>
