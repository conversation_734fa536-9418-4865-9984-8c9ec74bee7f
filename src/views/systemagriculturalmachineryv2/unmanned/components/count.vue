<template>
    <div class="count">
        <div class="count-item">
            <div class="count-item-label">总设备</div> <div class="count-item-value">{{ total }}</div>
        </div>
        <div class="count-item" @click="TOGGLE_Is_Offline_count" :class="isOfflineCount?'':'off'">
            <div class="count-item-label">离线</div> <div class="count-item-value">{{ offlineCount }}</div>
        </div>
        <div class="count-item" @click="TOGGLE_Is_Online_Count" :class="isOnlineCount?'':'off'">
            <div class="count-item-label">在线</div> <div class="count-item-value">{{ onlineCount }}</div>
        </div>
        <div class="count-item">
            <div class="count-item-label">当月在线</div> <div class="count-item-value">{{ monthOnlineCount }}</div>
        </div>
    </div>
</template>

<script>
    /**
     * 组件说明: 车辆位置地图组件
     */
    import '@bdh-gis/mapbox-gl/dist/mapbox-gl.css';
    import { mapWritableState, mapActions } from 'pinia'
    import useVehicleMonitoringStore from "@/store/modules/vehicleMonitoring"
    import useMapStore from "@/store/modules/map"

    const DEFAULT_TIME = 1000 * 30
    export default {
        name: 'Count',
        components: {

        },
        mixins: [],
        data() {
            return {
                timer: null,
            }
        },
        computed: {
            ...mapWritableState(useVehicleMonitoringStore, ['total','offlineCount','onlineCount','monthOnlineCount']),
            ...mapWritableState(useMapStore, ['isOfflineCount','isOnlineCount']),
        },
        watch: {

        },
        created() {
            this.init()
        },
        mounted() {
            this.initEvent_visibilitychange(this.query,this.clearTimer)
        },
        beforeDestroy() {
            this.clearTimer()
            this.clearEvent_visibilitychange()
        },
        methods: {
            ...mapActions(useVehicleMonitoringStore, ['getAmount']),
            ...mapActions(useMapStore, ['TOGGLE_Is_Offline_count','TOGGLE_Is_Online_Count']),
            init() {
                this.query()
            },
            query(){
                this.getAmount()
                this.clearTimer()
                this.timer = setInterval(() => { //创建定时器
                    this.getAmount()
                }, DEFAULT_TIME)
            },
            clearTimer() { // 清除定时器
                this.timer && clearInterval(this.timer)
                this.timer = null
            },
            initEvent_visibilitychange(){
                document.addEventListener("visibilitychange", this.event_visibilitychange,true) // 浏览器窗口切换监听
            },
            clearEvent_visibilitychange(){
                document.removeEventListener('visibilitychange', this.event_visibilitychange,true)
            },
            // 浏览器窗口切换监听
            event_visibilitychange(){
                if (document.hidden === true) {
                    // 页面被挂起
                    this.clearTimer() // 关闭定时器
                } else {
                    // 页面由挂起被激活
                    this.query()
                }
            }
        }
    }
</script>

<style rel="stylesheet" lang="scss" scoped>
    @import "@/views/systemagriculturalmachineryv2/assets/styles/variables.scss";
    .count {
        /*background-color: rgba(var(--color-bg), 0.5);*/
        width: 100%;
        height: 100%;
        background: url("@/views/systemagriculturalmachineryv2/assets/images/bg_count.png") no-repeat;
        background-size: 100% 100%;
        display: flex;
        justify-content: space-evenly;
        align-items: center;
        .count-item{
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            &.off{
                opacity: 0.5
            }
            .count-item-label{
                padding-bottom: 5px;
            }
            .count-item-value{

            }
            &:nth-child(1){
                .count-item-value{
                    color: #F6FA43;
                }
            }
            &:nth-child(2){
                .count-item-value{
                    color: #FF4646;
                }
            }
            &:nth-child(3){
                .count-item-value{
                    color: #46FF64;
                }
            }
            &:nth-child(4){
                .count-item-value{
                    color: #1BC8FF;
                }
            }

        }
    }
</style>
