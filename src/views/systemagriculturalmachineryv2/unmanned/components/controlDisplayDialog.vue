<template>
    <el-dialog title="" v-model="visible" width="630px" :append-to-body="false" :modal-append-to-body="false" :class="{'isHeight':type}" class="noHeight"
        @close="onCloseDialog">
        <div>
            <img src="@/views/systemagriculturalmachineryv2/assets/images/controlshow.png" alt="" />
        </div>
        <div class="tabs" v-if="currentMachine. cfgvalue6=='b'||currentMachine. cfgvalue6=='c'||currentMachine. cfgvalue6=='d'">
            <div class="tabItem" :class="{ tabActive: type }" @click="tabChange(true)" v-if="currentMachine. cfgvalue6=='b'">
                机具设置 
            </div>
            <div class="tabItem" :class="{ tabActive: type }" @click="tabChange(true)" v-if="currentMachine. cfgvalue6=='c'||currentMachine. cfgvalue6=='d'">
                收割设置
            </div>
            <div class="tabItem" :class="{ tabActive: !type }" @click="tabChange(false)">
                农机设置
            </div>
        </div>
        <!-- P类型 -->
        <div class="form" v-if="currentMachine.cfgvalue6=='b'">
            <el-form ref="form" :model="form" label-width="180px" label-position="left">
                <template v-if="type">
                    <el-form-item label="后提升控制">
                        <el-radio-group v-model="form.proConsole">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in pro_console">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="PTO控制">
                        <el-radio-group v-model="form.ptoTrol">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in pto_trol">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="PTO转速">
                        <el-radio-group v-model="form.ptoRpm">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in pto_rpm">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="作业机具实际高度(%）">
                        <el-input v-model="form.x8" :disabled="true" :precision="0"
                            v-if="form.realDeepSet === null || form.realDeepSet === undefined" step-strictly
                            style="width: 260px;"></el-input>
                        <el-input v-model="form.realDeepSet" :disabled="true" v-else :precision="0" step-strictly
                            style="width: 260px;"></el-input>
                    </el-form-item>
                    <el-form-item label="作业机具浮动">
                        <div class="realDeepSet" :class="[color]"
                            v-if="form.realDeepSet === null || form.realDeepSet === undefined" @click="floatClick">浮动
                        </div>
                        <div class="realDeepSet" :class="[color2]" v-else @click="floatClick">浮动</div>
                    </el-form-item>
                   
                    <el-form-item label="作业机具高度(%）">
                        <el-input-number v-model="form.plowDeepSet" :disabled="disabled" step-strictly :min="0"
                            :max="100"></el-input-number>
                    </el-form-item>
                    <el-form-item>
                        <el-button class="cancel" @click="onCloseDialog">取消</el-button>
                        <el-button class="submit" @click="submit">立即创建</el-button>
                    </el-form-item>
                </template>
                <template v-if="!type">
                    <el-form-item label="四驱设置">
                        <el-radio-group v-model="form.fwdInstall">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in fwd_install">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="差速锁设置">
                        <el-radio-group v-model="form.edsInstall">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in eds_install">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="多路阀使能">
                        <el-radio-group v-model="form.mwInstall">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in mw_install">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="多路阀动作">
                        <el-radio-group v-model="form.mwAction">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in mw_action">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="多路阀油量(%)">
                        <el-input-number v-model="form.mwOil" step-strictly :min="0"
                            :max="100"></el-input-number>
                    </el-form-item>
                    <el-form-item>
                        <el-button class="cancel" @click="onCloseDialog">取消</el-button>
                        <el-button class="submit" @click="submit">立即创建</el-button>
                    </el-form-item>
                </template>
            </el-form>
        </div>
        <!-- M类型 -->
        <div class="form" v-if="currentMachine.cfgvalue6=='a'">
            <el-form ref="form" :model="form" label-width="180px" label-position="left">
                <template>
                    <el-form-item label="后提升控制">
                        <el-radio-group v-model="form.proConsole">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in pro_console">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="PTO控制">
                        <el-radio-group v-model="form.ptoTrol">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in pto_trol">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item>
                        <el-button class="cancel" @click="onCloseDialog">取消</el-button>
                        <el-button class="submit" @click="submit">立即创建</el-button>
                    </el-form-item>
                </template>
            </el-form>
        </div>
        <!-- 小麦 -->
        <div class="form" v-if="currentMachine.cfgvalue6=='c'">
                <template v-if="type">
                    <el-form ref="form" :model="form" label-width="180px" label-position="left">
                    <el-form-item label="拨禾轮控制模式">
                        <el-radio-group v-model="form.reelControlMode">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in basket_mode">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="拨禾轮控制">
                        <el-radio-group v-model="form.reelControl">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in reel_control">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="割台控制模式">
                        <el-radio-group v-model="form.headerControlMode">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in earbox_mode">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="割台控制" v-if="form.headerControlMode==1">
                        <el-radio-group v-model="form.yx">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in unload_drumh">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="割台高度(%)"v-if="form.headerControlMode==2">
                        <el-input-number v-model="form.headerHeight" step-strictly :min="0"
                                :max="100"></el-input-number>
                    </el-form-item>
                    <el-form-item label="卸粮筒控制">
                        <el-radio-group v-model="form.xk">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in umxk">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="卸粮控制">
                        <el-radio-group v-model="form.xm">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in umxm">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="滚筒转速">
                        <el-radio-group v-model="form.drumSpeed">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in drum_speed">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="一键伸出/收回">
                        <el-radio-group v-model="form.oneKeyOutIn">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in onekey_outin">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="一键卸粮">
                        <el-radio-group v-model="form.oneKeyUnloadingGrain">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in onekey_unload">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item>
                        <el-button class="cancel" @click="onCloseDialog">取消</el-button>
                        <el-button class="submit" @click="submit">立即创建</el-button>
                    </el-form-item>
                </el-form>
                </template>
                <template v-if="!type">
                    <el-form ref="form" :model="form" label-width="180px" label-position="left">
                    <el-form-item label="主离合设置">
                        <el-radio-group v-model="form.yw">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in umyw">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="机械档位设置">
                        <el-radio-group v-model="form.mechanicalGearSetting">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in mechan_gear">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="电子驻车">
                        <el-radio-group v-model="form.electricParkingClutchStatus">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in electric_parking">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="凹板间隙">
                        <el-radio-group v-model="form.concaveClearance">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in concave_clearance">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                   
                    <el-form-item>
                        <el-button class="cancel" @click="onCloseDialog">取消</el-button>
                        <el-button class="submit" @click="submit">立即创建</el-button>
                    </el-form-item>
                    </el-form>
                </template>
        </div>
        <!-- 水稻机 -->
        <div class="form" v-if="currentMachine.cfgvalue6=='d'">
                <template v-if="type">
                    <el-form ref="form" :model="form" label-width="180px" label-position="left">
                        <el-form-item label="卸粮筒高度控制模式">
                            <el-radio-group v-model="form.unloadingDrumHeightControlMode">
                                <el-radio :label="Number(it.code)" :key="it.code" v-for="it in earbox_mode">{{ it.name
                                }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="卸粮筒高度控制" v-if="form.unloadingDrumHeightControlMode==1">
                            <el-radio-group v-model="form.unloadingDrumHeightControl">
                                <el-radio :label="Number(it.code)" :key="it.code" v-for="it in unload_drumh">{{ it.name
                                }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                    </el-form>
                    <el-form ref="form" :model="form" label-width="180px" label-position="left">
                    <el-form-item label="卸粮筒高度(-90°-90°)" v-if="form.unloadingDrumHeightControlMode==2">
                            <div style="padding: 0 10px">
                                <el-slider v-model="form.unloadingHeight" :step="1" :min="-90" :max="90"
                                >
                                </el-slider>
                            </div>
                        </el-form-item>
                    </el-form>
                    <el-form ref="form" :model="form" label-width="180px" label-position="left">
                    <el-form-item label="卸粮筒旋转控制模式">
                        <el-radio-group v-model="form.unloadingDrumRotationControlMode">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in earbox_mode">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="卸粮筒旋转控制" v-if="form.unloadingDrumRotationControlMode==1">
                        <el-radio-group v-model="form.unloadingDrumRotationControl">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in unload_drumheight">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="卸粮筒旋转角度(%)" v-if="form.unloadingDrumRotationControlMode==2">
                            <el-input-number v-model="form.unloadingRotateAngle" step-strictly :min="0"
                                :max="100"></el-input-number>
                    </el-form-item>
                    <el-form-item label="割台控制模式">
                        <el-radio-group v-model="form.headerControlMode">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in earbox_mode">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="割台控制" v-if="form.headerControlMode==1">
                        <el-radio-group v-model="form.yx">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in unload_drumh">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="割台高度(%)"v-if="form.headerControlMode==2">
                        <el-input-number v-model="form.headerHeight" step-strictly :min="0"
                                :max="100"></el-input-number>
                    </el-form-item>
                    <el-form-item label="拨禾轮控制模式">
                        <el-radio-group v-model="form.reelControlMode">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in basket_mode">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="拨禾轮控制">
                        <el-radio-group v-model="form.reelControl">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in reel_control">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="卸粮筒控制">
                        <el-radio-group v-model="form.xk">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in umxk">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="卸粮控制">
                        <el-radio-group v-model="form.xm">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in umxm">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="一键伸出/收回">
                        <el-radio-group v-model="form.oneKeyOutIn">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in onekey_outin">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="一键卸粮">
                        <el-radio-group v-model="form.oneKeyUnloadingGrain">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in onekey_unload">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item>
                        <el-button class="cancel" @click="onCloseDialog">取消</el-button>
                        <el-button class="submit" @click="submit">立即创建</el-button>
                    </el-form-item>
                </el-form>
                </template>
                <template v-if="!type">
                    <el-form ref="form" :model="form" label-width="180px" label-position="left">
                    <el-form-item label="主离合设置">
                        <el-radio-group v-model="form.yw">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in umyw">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="过桥离合设置">
                        <el-radio-group v-model="form.bridgeClutchStateSet">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in bridge_clutch">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                   
                    <el-form-item>
                        <el-button class="cancel" @click="onCloseDialog">取消</el-button>
                        <el-button class="submit" @click="submit">立即创建</el-button>
                    </el-form-item>
                </el-form>
                </template>
            
        </div>
         <!-- 玉米果穗机 -->
        <div class="form" v-if="currentMachine.cfgvalue6=='e'">
            <el-form ref="form" :model="form" label-width="180px" label-position="left">
                <template>
                    <el-form-item label="主离合设置">
                        <el-radio-group v-model="form.yw">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in umyw">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="割台控制模式">
                        <el-radio-group v-model="form.headerControlMode">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in earbox_mode">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="割台控制" v-if="form.headerControlMode==1">
                        <el-radio-group v-model="form.yx">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in unload_drumh">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="割台高度(%)"v-if="form.headerControlMode==2">
                        <el-input-number v-model="form.headerHeight" step-strictly :min="0"
                                :max="100"></el-input-number>
                    </el-form-item>
                    <el-form-item label="果穗箱控制模式">
                        <el-radio-group v-model="form.earBoxControlMode">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in earbox_mode">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="果穗箱控制" v-if="form.earBoxControlMode==1">
                        <el-radio-group v-model="form.earBoxControl">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in earbox_control">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="果穗箱高度(%)" v-if="form.earBoxControlMode==2">
                        <el-input-number v-model="form.earBoxHeight" step-strictly :min="0"
                                :max="100"></el-input-number>
                    </el-form-item>
                    <el-form-item label="一键卸粮">
                        <el-radio-group v-model="form.oneKeyUnloadingGrain">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in onekey_unload">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>

                    <el-form-item label="还田机">
                        <el-radio-group v-model="form.fieldReturningMachine">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in field_returning">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="对行模式">
                        <el-radio-group v-model="form.autoAlignment">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in auto_alignment">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    
                   
                    <el-form-item>
                        <el-button class="cancel" @click="onCloseDialog">取消</el-button>
                        <el-button class="submit" @click="submit">立即创建</el-button>
                    </el-form-item>
                </template>
            </el-form>
        </div>
         <!-- 玉米籽粒机 -->
        <div class="form" v-if="currentMachine.cfgvalue6=='f'">
            <el-form ref="form" :model="form" label-width="180px" label-position="left">
                <template>
                    <el-form-item label="主离合设置">
                        <el-radio-group v-model="form.yw">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in umyw">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="割台控制模式">
                        <el-radio-group v-model="form.headerControlMode">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in earbox_mode">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="割台控制" v-if="form.headerControlMode==1">
                        <el-radio-group v-model="form.yx">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in unload_drumh">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="割台高度(%)"v-if="form.headerControlMode==2">
                        <el-input-number v-model="form.headerHeight" step-strictly :min="0"
                                :max="100"></el-input-number>
                    </el-form-item>
                    <el-form-item label="卸粮筒控制">
                        <el-radio-group v-model="form.xk">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in umxk">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="卸粮控制">
                        <el-radio-group v-model="form.xm">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in umxm">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="滚筒转速">
                        <el-radio-group v-model="form.drumSpeed">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in drum_speed">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="一键伸出/收回">
                        <el-radio-group v-model="form.oneKeyOutIn">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in onekey_outin">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="一键卸粮">
                        <el-radio-group v-model="form.oneKeyUnloadingGrain">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in onekey_unload">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="对行模式">
                        <el-radio-group v-model="form.autoAlignment">
                            <el-radio :label="Number(it.code)" :key="it.code" v-for="it in auto_alignment">{{ it.name
                            }}</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item>
                        <el-button class="cancel" @click="onCloseDialog">取消</el-button>
                        <el-button class="submit" @click="submit">立即创建</el-button>
                    </el-form-item>
                </template>
            </el-form>
        </div>
    </el-dialog>
</template>
<script>
import { tractorRemoteControl } from '@/api/systemagriculturalmachineryv2/unmanned/unmanned'
import {
    getNavData,
    consoleSave,
    getConsole
} from '@/api/systemagriculturalmachineryv2/unmanned/unmanned'
import { getDicts } from "@/api/systemagriculturalmachineryv2/dict";
export default {
    name: 'controlDisplayDialog',
    props: {
        modelValue: {
            type: Boolean,
            default: false,
        },
        currentMachine: {
            type: Object,
            default: {
                deviceCode: '',
                controlAuth: false,
                cfgvalue6: null,
            },
        },
        requestHeadLovol:{
            type: Object,
            default: {
                
            },
        }
    },
     computed: {
        visible: {
            get() {
                return this.modelValue
            },
            set(val) {
                this.$emit('update:modelValue', val)
            }
        },
    },
    watch:{
        type(){
            let dialog = document.querySelector(".noHeight .el-dialog")
            let initH = dialog.offsetHeight
            let realdialog
            this.$nextTick(()=>{
                if(!this.type){
                    realdialog = document.querySelector(".noHeight .el-dialog")
                    let dialogH = dialog.offsetHeight
                    let height = (initH -  dialogH) / 24
                    realdialog.style.backgroundPositionY = height+'px'
                }else{
                    realdialog = document.querySelector(".isHeight .el-dialog")
                    realdialog.style.backgroundPositionY ='0px'
                }
            })
        }
    },
    mounted() {
        // 作业类型
        getDicts('assign_type').then((response) => {
            this.assign_type = response.data
        })
        // 作业模式
        getDicts('assign_mode').then((response) => {
            this.assign_mode = response.data
        })
        // 路径模式
        getDicts('route_mode').then((response) => {
            this.route_mode = response.data
        })
        // 转弯方式
        getDicts('turn_mode').then((response) => {
            this.turn_mode = response.data
        })
        // 是否收边
        getDicts('side_type').then((response) => {
            this.side_type = response.data
        })
        // PTO控制
        getDicts('pto_trol').then((response) => {
            this.pto_trol = response.data
        })
        // PTO转速
        getDicts('pto_rpm').then((response) => {
            this.pto_rpm = response.data
        })
        // 档位
        getDicts('gear_type').then((response) => {
            this.gear_type = response.data
        })
        // 四驱设置
        getDicts('fwd_install').then((response) => {
            this.fwd_install = response.data
        })
        // 差速锁设置
        getDicts('eds_install').then((response) => {
            this.eds_install = response.data
        })
        // 多路阀使能
        getDicts('mw_install').then((response) => {
            this.mw_install = response.data
        })
        // 后提升控制
        getDicts('pro_console').then((response) => {
            this.pro_console = response.data
        })
        // 作业机具浮动
        getDicts('assign_drift').then((response) => {
            this.assign_drift = response.data
        })
        // 多路阀动作
        getDicts('mw_action').then((response) => {
            this.mw_action = response.data
        })



        // 过桥离合设置
        getDicts('bridge_clutch').then((response) => {
            this.bridge_clutch = response.data
        })
        // 凹板间隙
        getDicts('concave_clearance').then((response) => {
            this.concave_clearance = response.data
        })
        // 滚筒转速
        getDicts('drum_speed').then((response) => {
            this.drum_speed = response.data
        })
        // 果穗箱控制
         getDicts('earbox_control').then((response) => {
            this.earbox_control = response.data
        })
        // 电子驻车
        getDicts('electric_parking').then((response) => {
            this.electric_parking = response.data
        })
        // 还田机
         getDicts('field_returning').then((response) => {
            this.field_returning = response.data
        })
        // 对行模式
         getDicts('auto_alignment').then((response) => {
            this.auto_alignment = response.data
        })
        // 机械档位设置
        getDicts('mechan_gear').then((response) => {
            this.mechan_gear = response.data
        })
        // 收回
        getDicts('onekey_outin').then((response) => {
            this.onekey_outin = response.data
        })
        // 拨禾轮控制
        getDicts('reel_control').then((response) => {
            this.reel_control = response.data
        })
        // 卸粮控制
         getDicts('umxm').then((response) => {
            this.umxm = response.data
        })
        // 果穗箱控制模式
        getDicts('earbox_mode').then((response) => {
            this.earbox_mode = response.data
        })
        // 主离合设置
         getDicts('umyw').then((response) => {
            this.umyw = response.data
        })
        // 卸粮筒控制
        getDicts('umxk').then((response) => {
            this.umxk = response.data
        })
        // 卸粮筒高度控制
        getDicts('unload_drumh').then((response) => {
            this.unload_drumh = response.data
        })
        // 一键卸粮
         getDicts('onekey_unload').then((response) => {
            this.onekey_unload = response.data
        })
        // 卸粮筒旋转控制
        getDicts('unload_drumheight').then((response) => {
            this.unload_drumheight = response.data
        })
        // 拨禾轮控制模式
        getDicts('basket_mode').then((response) => {
            this.basket_mode = response.data
        })
        this.getx8()
        this.$bus.$on('navData', (data) => {
            this.index++
            if (this.index == 1) {

            } else {
                let x8 = data.aaac
                if (x8 !== null && x8 !== undefined) {
                    this.form.realDeepSet = x8
                    this.classObject2()
                }
            }
        })
    },
    data() {
        return {
            basket_mode:[],
            unload_drumheight:[],
            onekey_unload:[],
            unload_drumh:[],
            umxk:[],
            umyw:[],
            earbox_mode:[],
            umxm:[],
            reel_control:[],
            onekey_outin:[],
            mechan_gear:[],
            auto_alignment:[],
            field_returning:[],
            electric_parking:[],
            earbox_control:[],
            drum_speed:[],
            concave_clearance:[],
            bridge_clutch:[],
            index: 0,
            color: '',
            color2: '',
            disabled: false,
            timerNav: null,
            type: true,
            form: {
                x8: null,//机具高度 用于控制浮动显示

                consoleId:null,
                machineId:null,
                realDeepSet: null,
                deviceNo: null,
                mwOil: 0,
                ptoRpm: 0,
                proConsole: 0,
                ptoTrol: 0,

                plowDeepSet: 0,
                yx:0,
                oneKeyUnloadingGrain:0,
                oneKeyOutIn:0,
                drumSpeed:0,
                xm:0,
                xk:0,
                reelControlMode:1,
                reelControl:0,
                unloadingDrumHeightControlMode:1,
                unloadingHeight:0,
                
                unloadingDrumHeightControl:0,
                unloadingDrumRotationControlMode:1,
                unloadingDrumRotationControl:0,
                headerControlMode:1,
                earBoxControlMode:1,
                earBoxControl:0,
                fieldReturningMachine:0,
                autoAlignment:0,
                headerHeight:0,
                earBoxHeight:0,

                unloadingRotateAngle:0,
                mwInstall: 0,
                edsInstall: 0,
                fwdInstall: 0,
                mwAction: 0,
                yw:0,
                mechanicalGearSetting:"0",
                electricParkingClutchStatus:0,
                concaveClearance:0,
                bridgeClutchStateSet:0
            },
            rules: {
               
            },
            assign_type: [],
            assign_mode: [],
            route_mode: [],
            turn_mode: [],
            side_type: [],
            pto_trol: [],
            pto_rpm: [],
            gear_type: [],
            fwd_install: [],
            eds_install: [],
            mw_install: [],
            pro_console: [],
            assign_drift: [],
            mw_action: [],
        }
    },
    methods: {
        floatClick() {
            if (this.form.realDeepSet !== null && this.form.realDeepSet !== undefined) {
                if (this.color2 == 'green') {
                    this.form.plowDeepSet = 0
                    this.disabled = true
                    let count = 100
                    this.color2 = 'red'
                    var obj = {
                        requestHeadLovol: this.requestHeadLovol,
                        tractorRemoteControlCommand: {
                            deviceNo: this.currentMachine.deviceCode,
                            plowDeepSet: count
                        },
                    }
                    tractorRemoteControl(obj).then((res) => { })

                }
                else if (this.color2 == 'red') {
                    let count = 0
                    this.disabled = false
                    this.color2 = 'green'
                    var obj = {
                        requestHeadLovol: this.requestHeadLovol,
                        tractorRemoteControlCommand: {
                            deviceNo: this.currentMachine.deviceCode,
                            plowDeepSet: count
                        },
                    }
                    tractorRemoteControl(obj).then((res) => { })

                }
                else if (this.color2 == 'gray') {
                    // this.disabled = false
                }
            } else {
                if (this.color == 'green') {
                    this.form.plowDeepSet = 0
                    this.disabled = true
                    let count = 100
                    this.color = 'red'
                    var obj = {
                        requestHeadLovol: this.requestHeadLovol,
                        tractorRemoteControlCommand: {
                            deviceNo: this.currentMachine.deviceCode,
                            plowDeepSet: count
                        },
                    }
                    tractorRemoteControl(obj).then((res) => { })

                }
                else if (this.color == 'red') {
                    let count = 0
                    this.disabled = false
                    this.color = 'green'
                    var obj = {
                        requestHeadLovol: this.requestHeadLovol,
                        tractorRemoteControlCommand: {
                            deviceNo: this.currentMachine.deviceCode,
                            plowDeepSet: count
                        },
                    }
                    tractorRemoteControl(obj).then((res) => { })
                }
                else if (this.color == 'gray') {
                    // this.disabled = false
                }
            }

        },
        async getx8() {
            let obj = this.requestHeadLovol
            const result = await getNavData(obj)
            const res = await getConsole({ machineId: this.currentMachine.machineId })
            if (res.data) {
                let data = res.data
                this.form = data
                if(this.currentMachine.cfgvalue6=='b'){
                    this.form.plowDeepSet = (data.mtHeight - 100) / 9
                }
                this.form.x8 = result.data.aaad
            }
            this.classObject()
        },
        // 异步轮询函数 获取速度 转速
        async getNavData(interval = 2000) {
            clearTimeout(this.timerNav)
            let isPolling = false
            let poll = async () => {
                if (isPolling) return
                if (!this.currentMachine.controlAuth) {
                    return
                }
                isPolling = true
                try {
                    let obj = this.requestHeadLovol
                    const result = await getNavData(obj) // 执行异步操作
                    let x8 = result.data.x8
                    x8 = 15
                    if (x8 !== null && x8 !== undefined) {
                        this.form.realDeepSet = x8
                        this.classObject2()
                    }

                } catch (error) {
                } finally {
                    isPolling = false
                    clearTimeout(this.timerNav)
                    this.timerNav = setTimeout(poll, interval) // 重新触发轮询
                }
            }
            if (!isPolling) {
                await poll()
            }
        },
        classObject() {
            if (this.form.x8 <= 80) {
                this.color = 'green'
            }
            else if (this.form.x8 <= 100) {
                this.color = 'red'
            }
            else if (this.form.x8 <= 1000) {
                this.color = 'gray'
            }

        },
        classObject2() {
            if (this.form.realDeepSet <= 20) {
                this.color2 = 'red'
            }
            else if (this.form.realDeepSet <= 100) {
                this.color2 = 'gray'
            }
        },
        showMessage(msg) {
            this.$message({
                dangerouslyUseHTMLString: true,
                customClass: 'messageboxcustomClass',
                duration: 3000,
                offset: 200,
                message:
                    '<div class="content"><div class="title">消息提示</div><div class="text">' +
                    msg +
                    '</div></div>',
            })
        },
        submit() {
            this.form.deviceNo = this.currentMachine.deviceCode
            this.form.smartType = this.currentMachine.cfgvalue5
            var obj = {
                ...this.form,
                machineId: this.currentMachine.machineId,
                headLovol: this.requestHeadLovol,
            }
            delete obj.x8
            delete obj.realDeepSet
            delete obj.deviceNo
            delete obj.plowDeepSet
            
            if(this.currentMachine.cfgvalue6=='b'){
                obj.mtHeight = 100 + (this.form.plowDeepSet * 9)
            }
            consoleSave(obj).then((res) => {
                this.showMessage("配置成功")
                setTimeout(() => {
                    this.onCloseDialog()
                }, 300)
            })
        },
        tabChange(bool) {
            this.type = bool
        },
        onCloseDialog() {
            this.visible = false
        },
    },
}
</script>
<style lang="scss" scoped>
.realDeepSet {
    width: 80px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    color: #ffffff;

    &.green {
        background-color: rgb(29, 116, 29);
        cursor: pointer;
    }

    &.red {
        background-color: rgb(151, 10, 10);
        cursor: pointer;
    }

    &.gray {
        background-color: #dfdfdf;
        cursor: not-allowed;
    }
}

.underline {
    border-bottom: 1px solid rgba(47, 169, 255, 0.2);
    padding-bottom: 10px;
}

.form {
    padding: 0 10px 0 30px !important;
    margin-top: 20px;

    :deep(.el-form-item__label) {
        color: #c2e1fe;
        font-size: 16px;
    }

    :deep(.el-input.is-disabled .el-input__inner) {
        background-color: rgba(8, 105, 204, 0.15) !important;
    }

    :deep(.el-radio) {
        color: #c2e1fe;
    }

    :deep(.el-radio__inner) {
        border: 1px solid rgba(23, 133, 255, 0.4);
        background: rgba(8, 105, 204, 0.2);

        &::after {
            background: #1193fd;
        }
    }

    :deep(.el-radio__input.is-checked+.el-radio__label) {
        color: #c2e1fe;
    }

    :deep(.el-input__inner) {
        background: rgba(8, 105, 204, 0.15);
        border: 1px solid rgba(23, 133, 255, 0.27);
        color: #7fbef8;
        padding-left: 10px;
        padding-right: 10px;
    }

    :deep(.el-input-number__decrease) {
        width: 66px;
        height: 34px;
        background: #0056a9;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #ffffff;
        border-right: none;
    }

    :deep(.el-input-number__increase) {
        width: 66px;
        height: 34px;
        background: #0056a9;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #ffffff;
        border-left: none;
    }

    :deep(.el-input-number--medium) {
        width: 260px;
    }

    :deep(.el-input-number .el-input) {
        margin-left: 74px;
        width: 106px;
        height: 36px;
        background: rgba(8, 105, 204, 0.15);
        border: 1px solid rgba(23, 133, 255, 0.27);
        border-radius: 4px;
    }

    .cancel {
        width: 121px;
        border: 1px solid #1193fd;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        background-color: transparent;
        color: #6896d1;
    }

    .submit {
        width: 120px;
        background: linear-gradient(-46deg, #0056a9, #0a83e5);
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        font-size: 16px;
        font-weight: 500;
        color: #e3f1ff;
        border: none;
    }

    .click {
        width: 60px;
        height: 28px;
        background: #0056a9;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #c2e1fe;
        border: none;
        text-align: center;
        line-height: 28px;
        padding: 0;
    }

    .reset {
        width: 60px;
        height: 28px;
        background: #0056a9;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #c2e1fe;
        border: none;
        text-align: center;
        line-height: 28px;
        padding: 0;
    }
}

.newbottom {
    padding: 20px 20px 1px;
    width: 506px;
    background: rgba(8, 105, 204, 0.15);
    border-radius: 4px;
}

.newpoint {
    width: 464px;
    background: rgba(7, 15, 29, 0.55);
    border-radius: 4px;
    padding: 10px 0px 0px;
    margin-bottom: 10px;

    .newtop {
        // border-left: 2px solid #0075E6;
        margin-left: -20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        color: #c2e1fe;
        padding-left: 20px;
        padding-right: 20px;

        .borderLeft {
            width: 2px;
            height: 20px;
            background: #0075e6;
            flex-shrink: 0;
        }

        .new-top-right {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-around;
        }

        .newtoppic {
            margin-left: -10px;
        }
    }

    .newtbottom {
        padding-left: 20px;
        padding-bottom: 1px;
        padding-right: 20px;
    }
}

.con-bottom-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin-top: 20px;
    margin-bottom: 30px;

    .btn-cancel {
        width: 121px;
        height: 41px;
        border: 1px solid #1193fd;
        border-radius: 4px;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        color: #6896d1;
        text-align: center;
        line-height: 41px;
    }

    .btn-save {
        width: 120px;
        height: 40px;
        background: linear-gradient(-46deg, #0056a9, #0a83e5);
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #e3f1ff;
        text-align: center;
        line-height: 40px;
        margin-left: 20px;
    }
}

.tabs {
    width: 248px;
    height: 40px;
    background: rgba(91, 162, 227, 0.05);
    border: 1px solid #08599d;
    border-radius: 4px;
    margin: 0px auto 0;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .tabItem {
        width: 120px;
        height: 32px;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #e4f2ff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #7fbef8;
        opacity: 0.6;

        &.tabActive {
            background: rgba(1, 125, 255, 0.5);
            border: 0px solid #070002;
            opacity: 1;
            color: #ffffff;
        }
    }
}
</style>
