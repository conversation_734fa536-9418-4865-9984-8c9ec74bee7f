<template>
    <div class="controls">
        <div class="verhicle-controls">
            <el-button
                type="primary"
                
                @click="speedDown"
                :disabled="!isRout"
                >减速</el-button
            >
            <el-button
                type="primary"
                
                @click="startStop"
                :disabled="!isRout"
                >开始/暂停（x{{ playTimes }}）</el-button
            >
            <el-button
                type="primary"
                
                @click="speedUp"
                :disabled="!isRout"
                >加速</el-button
            >
            <el-button
                type="primary"
                
                @click="reset"
                :disabled="!isRout"
                >重置</el-button
            >
            <el-input
                :value="curPercent + '%'"
                
                readonly
                class="percent-input"
            />
        </div>
    </div>
</template>
<script>
import { mapWritableState, mapActions } from 'pinia'
import useReplayStore from "@/store/modules/replay"
export default {
    name: 'ReplayCtrl',
    props: {},
    data() {
        return {}
    },
    computed: {
        ...mapWritableState(useReplayStore,['curPercent','playTimes','curWork','disabled']),
        isRout() {
            return !this.disabled
            // return this.curWork && this.curWork.geom
        },
    },
    methods: {
        speedDown() {
            this.$emit('speedDown')
        },
        startStop() {
            this.$emit('startStop')
        },
        speedUp() {
            this.$emit('speedUp')
        },
        reset() {
            this.$emit('reset')
        },
    },
}
</script>
<style lang="scss" scoped>
.controls {
    position: absolute;
    left: 50%;
    bottom: 0;
    z-index: 99;
    transform: translateX(-50%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    // width: 440px;
    padding: 20px;
    background-color: rgb(255 255 255 / 0.2);
    .verhicle-controls {
        display: flex;
        justify-content: left;
        align-items: center;
        :deep(.el-button) {
            &:not(:first-child) {
                margin-left: 10px;
            }
        }
        :deep(.el-input) {
            margin-left: 10px;
            width: 70px;
            :deep(.el-input__inner) {
                text-align: center;
            }
        }
    }
    .table-controls {
        i {
            font-size: 30px;
            cursor: pointer;
        }
    }
}
</style>
