<template>
    <el-dialog
        title=""
        v-model="visible"
        width="630px"
        :append-to-body="false"
        :modal-append-to-body="false"
        @close="onCloseDialog"
    >
        <div>
            <img src="@/views/systemagriculturalmachineryv2/assets/images/parameter.png" alt="" />
        </div>
        <!-- P类型 -->
        <div
            class="form"
            style="overflow: hidden; padding-bottom: 50px"
            v-if="currentMachine.cfgvalue6 == 'b'"
        >
            <el-scrollbar style="height: 600px">
                <el-form
                    ref="form"
                    :model="form"
                    label-width="188px"
                    label-position="left"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="作业机具高度(%)">
                            <el-input-number
                                v-model="form.mtHeight"
                                step-strictly
                                :min="0"
                                :max="1000"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="直线作业速度（km/h）">
                            <el-input-number
                                v-model="form.straightSpeed"
                                :min="0"
                                :max="25"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="地头转弯速度（km/h）">
                            <el-input-number
                                v-model="form.turnSpeed"
                                :min="0"
                                :max="25"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="地头转弯机具高度(%)">
                            <el-input-number
                                v-model="form.turnMtHeight"
                                step-strictly
                                :min="0"
                                :max="1000"
                            ></el-input-number>
                        </el-form-item>
                        <!-- <el-form-item label="PTO高度使能（%）">
                            <el-input-number v-model="form.ptoVol" :min="0" :max="100" step-strictly></el-input-number>
                        </el-form-item> -->
                    </div>
                </el-form>
                <el-form
                    ref="form"
                    :model="form"
                    label-width="188px"
                    label-position="top"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="作业发动机转速（0-4000）">
                            <div style="padding: 0 10px">
                                <el-slider
                                    v-model="form.engineSpeed"
                                    :step="10"
                                    :min="0"
                                    :max="4000"
                                    :marks="form.marks3"
                                >
                                </el-slider>
                            </div>
                        </el-form-item>
                        <el-form-item label="地头转弯发动机转速（0-4000）">
                            <div style="padding: 0 10px">
                                <el-slider
                                    v-model="form.turnEngineSpeed"
                                    :step="10"
                                    :min="0"
                                    :max="4000"
                                    :marks="form.marks3"
                                >
                                </el-slider>
                            </div>
                        </el-form-item>
                    </div>
                </el-form>
                <el-form
                    ref="form"
                    :model="form"
                    label-width="128px"
                    label-position="left"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="PTO控制">
                            <el-radio-group v-model="form.ptoTrol">
                                <el-radio
                                    :label="Number(it.code)"
                                    :key="it.code"
                                    v-for="it in pto_trol"
                                    >{{ it.name }}</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="PTO转速">
                            <el-radio-group v-model="form.ptoRpm">
                                <el-radio
                                    :label="Number(it.code)"
                                    :key="it.code"
                                    v-for="it in pto_rpm"
                                    >{{ it.name }}</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>

                        <!-- <el-form-item label="地头转弯档位">
                            <el-radio-group v-model="form.turnGear">
                                <el-radio :label="Number(it.code)" :key="it.code" v-for="it in gear_type">{{ it.name }}</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="直线作业档位">
                            <el-radio-group v-model="form.straightGear">
                                <el-radio :label="Number(it.code)" :key="it.code" v-for="it in gear_type">{{ it.name }}</el-radio>
                            </el-radio-group>
                        </el-form-item> -->
                        <el-form-item label="四驱设置">
                            <el-radio-group v-model="form.fwdInstall">
                                <el-radio
                                    :label="Number(it.code)"
                                    :key="it.code"
                                    v-for="it in fwd_install"
                                    >{{ it.name }}</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="差速锁设置">
                            <el-radio-group v-model="form.edsInstall">
                                <el-radio
                                    :label="Number(it.code)"
                                    :key="it.code"
                                    v-for="it in eds_install"
                                    >{{ it.name }}</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="多路阀使能">
                            <el-radio-group v-model="form.mwInstall">
                                <el-radio
                                    :label="Number(it.code)"
                                    :key="it.code"
                                    v-for="it in mw_install"
                                    >{{ it.name }}</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item>
                            <el-button class="cancel" @click="onCloseDialog"
                                >取消</el-button
                            >
                            <el-button class="submit" @click="submit"
                                >保存并下发</el-button
                            >
                        </el-form-item>
                    </div>
                </el-form>
            </el-scrollbar>
        </div>
        <!-- M类型 -->
        <div
            class="form"
            style="overflow: hidden; padding-bottom: 0px"
            v-if="currentMachine.cfgvalue6 == 'a'"
        >
            <el-scrollbar style="height: 450px">
                <el-form
                    ref="form"
                    :model="form"
                    label-width="188px"
                    label-position="left"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="作业机具高度(%)">
                            <el-input-number
                                v-model="form.mtHeight"
                                step-strictly
                                :min="0"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="地头转弯机具高度(%)">
                            <el-input-number
                                v-model="form.turnMtHeight"
                                step-strictly
                                :min="0"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                    </div>
                </el-form>
                <el-form
                    ref="form"
                    :model="form"
                    label-width="188px"
                    label-position="top"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="作业发动机转速（0-4000）">
                            <div style="padding: 0 10px">
                                <el-slider
                                    v-model="form.engineSpeed"
                                    :step="10"
                                    :min="0"
                                    :max="4000"
                                    :marks="form.marks3"
                                >
                                </el-slider>
                            </div>
                        </el-form-item>
                        <el-form-item label="地头转弯发动机转速（0-4000）">
                            <div style="padding: 0 10px">
                                <el-slider
                                    v-model="form.turnEngineSpeed"
                                    :step="10"
                                    :min="0"
                                    :max="4000"
                                    :marks="form.marks3"
                                >
                                </el-slider>
                            </div>
                        </el-form-item>
                    </div>
                </el-form>
                <el-form
                    ref="form"
                    :model="form"
                    label-width="128px"
                    label-position="left"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="PTO控制">
                            <el-radio-group v-model="form.ptoTrol">
                                <el-radio
                                    :label="Number(it.code)"
                                    :key="it.code"
                                    v-for="it in pto_trol"
                                    >{{ it.name }}</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item>
                            <el-button class="cancel" @click="onCloseDialog"
                                >取消</el-button
                            >
                            <el-button class="submit" @click="submit"
                                >保存并下发</el-button
                            >
                        </el-form-item>
                    </div>
                </el-form>
            </el-scrollbar>
        </div>
        <!-- 小麦 -->
        <div
            class="form"
            style="overflow: hidden; padding-bottom: 0px"
            v-if="currentMachine.cfgvalue6 == 'c'"
        >
            <el-scrollbar style="height: 600px">
                <el-form
                    ref="form"
                    :model="form"
                    label-width="188px"
                    label-position="left"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="直线作业速度（%）">
                            <el-input-number
                                v-model="form.xe"
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="地头转弯速度（%）">
                            <el-input-number
                                v-model="form.xf"
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="作业割台高度(%)">
                            <el-input-number
                                v-model="form.xg"
                                step-strictly
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="地头转弯割台高度(%)">
                            <el-input-number
                                v-model="form.xh"
                                step-strictly
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                    </div>
                </el-form>
                <el-form
                    ref="form"
                    :model="form"
                    label-width="188px"
                    label-position="top"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="作业发动机转速（0-4000）">
                            <div style="padding: 0 10px">
                                <el-slider
                                    v-model="form.xc"
                                    :step="10"
                                    :min="0"
                                    :max="4000"
                                    :marks="form.marks3"
                                >
                                </el-slider>
                            </div>
                        </el-form-item>
                        <el-form-item label="地头转弯发动机转速（0-4000）">
                            <div style="padding: 0 10px">
                                <el-slider
                                    v-model="form.xd"
                                    :step="10"
                                    :min="0"
                                    :max="4000"
                                    :marks="form.marks3"
                                >
                                </el-slider>
                            </div>
                        </el-form-item>
                    </div>
                </el-form>
                <el-form
                    ref="form"
                    :model="form"
                    label-width="128px"
                    label-position="left"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="主离合设置">
                            <el-radio-group v-model="form.mainClutchSetting">
                                <el-radio
                                    :label="Number(it.code)"
                                    :key="it.code"
                                    v-for="it in umyw"
                                    >{{ it.name }}</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item>
                            <el-button class="cancel" @click="onCloseDialog"
                                >取消</el-button
                            >
                            <el-button class="submit" @click="submit"
                                >保存并下发</el-button
                            >
                        </el-form-item>
                    </div>
                </el-form>
            </el-scrollbar>
        </div>
        <!-- 水稻机 -->
        <div
            class="form"
            style="overflow: hidden; padding-bottom: 0px"
            v-if="currentMachine.cfgvalue6 == 'd'"
        >
            <el-scrollbar style="height: 600px">
                <el-form
                    ref="form"
                    :model="form"
                    label-width="188px"
                    label-position="left"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="直线作业速度百分比(%)">
                            <el-input-number
                                v-model="form.xe"
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="地头转弯速度百分比(%)">
                            <el-input-number
                                v-model="form.xf"
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="作业割台高度(%)">
                            <el-input-number
                                v-model="form.xg"
                                step-strictly
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="地头转弯割台高度(%)">
                            <el-input-number
                                v-model="form.xh"
                                step-strictly
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                    </div>
                </el-form>
                <el-form
                    ref="form"
                    :model="form"
                    label-width="188px"
                    label-position="top"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="作业发动机转速（0-4000）">
                            <div style="padding: 0 10px">
                                <el-slider
                                    v-model="form.xc"
                                    :step="10"
                                    :min="0"
                                    :max="4000"
                                    :marks="form.marks3"
                                >
                                </el-slider>
                            </div>
                        </el-form-item>
                        <el-form-item label="地头转弯发动机转速（0-4000）">
                            <div style="padding: 0 10px">
                                <el-slider
                                    v-model="form.xd"
                                    :step="10"
                                    :min="0"
                                    :max="4000"
                                    :marks="form.marks3"
                                >
                                </el-slider>
                            </div>
                        </el-form-item>
                    </div>
                </el-form>
                <el-form
                    ref="form"
                    :model="form"
                    label-width="128px"
                    label-position="left"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="主离合设置">
                            <el-radio-group v-model="form.mainClutchSetting">
                                <el-radio
                                    :label="Number(it.code)"
                                    :key="it.code"
                                    v-for="it in umyw"
                                    >{{ it.name }}</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="过桥离合设置">
                            <el-radio-group v-model="form.bridgeClutchSetting">
                                <el-radio
                                    :label="Number(it.code)"
                                    :key="it.code"
                                    v-for="it in bridge_clutch"
                                    >{{ it.name }}</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item>
                            <el-button class="cancel" @click="onCloseDialog"
                                >取消</el-button
                            >
                            <el-button class="submit" @click="submit"
                                >保存并下发</el-button
                            >
                        </el-form-item>
                    </div>
                </el-form>
            </el-scrollbar>
        </div>
        <!-- 玉米果穗机 -->
        <div
            class="form"
            style="overflow: hidden; padding-bottom: 0px"
            v-if="currentMachine.cfgvalue6 == 'e'"
        >
            <el-scrollbar style="height: 600px">
                <el-form
                    ref="form"
                    :model="form"
                    label-width="188px"
                    label-position="left"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="直线作业速度百分比(%)">
                            <el-input-number
                                v-model="form.xe"
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="地头转弯速度百分比(%)">
                            <el-input-number
                                v-model="form.xf"
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="作业割台高度(%)">
                            <el-input-number
                                v-model="form.xg"
                                step-strictly
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="地头转弯割台高度(%)">
                            <el-input-number
                                v-model="form.xh"
                                step-strictly
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                    </div>
                </el-form>
                <el-form
                    ref="form"
                    :model="form"
                    label-width="188px"
                    label-position="top"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="作业发动机转速（0-4000）">
                            <div style="padding: 0 10px">
                                <el-slider
                                    v-model="form.xc"
                                    :step="10"
                                    :min="0"
                                    :max="4000"
                                    :marks="form.marks3"
                                >
                                </el-slider>
                            </div>
                        </el-form-item>
                        <el-form-item label="地头转弯发动机转速（0-4000）">
                            <div style="padding: 0 10px">
                                <el-slider
                                    v-model="form.xd"
                                    :step="10"
                                    :min="0"
                                    :max="4000"
                                    :marks="form.marks3"
                                >
                                </el-slider>
                            </div>
                        </el-form-item>
                    </div>
                </el-form>
                <el-form
                    ref="form"
                    :model="form"
                    label-width="128px"
                    label-position="left"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="主离合设置">
                            <el-radio-group v-model="form.mainClutchSetting">
                                <el-radio
                                    :label="Number(it.code)"
                                    :key="it.code"
                                    v-for="it in umyw"
                                    >{{ it.name }}</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="对行模式">
                            <el-radio-group v-model="form.autoAlignment">
                                <el-radio
                                    :label="Number(it.code)"
                                    :key="it.code"
                                    v-for="it in auto_alignment"
                                    >{{ it.name }}</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="还田机">
                            <el-radio-group
                                v-model="form.fieldReturningMachine"
                            >
                                <el-radio
                                    :label="Number(it.code)"
                                    :key="it.code"
                                    v-for="it in field_returning"
                                    >{{ it.name }}</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item>
                            <el-button class="cancel" @click="onCloseDialog"
                                >取消</el-button
                            >
                            <el-button class="submit" @click="submit"
                                >保存并下发</el-button
                            >
                        </el-form-item>
                    </div>
                </el-form>
            </el-scrollbar>
        </div>
        <!-- 玉米籽粒机 -->
        <div
            class="form"
            style="overflow: hidden; padding-bottom: 0px"
            v-if="currentMachine.cfgvalue6 == 'f'"
        >
            <el-scrollbar style="height: 600px">
                <el-form
                    ref="form"
                    :model="form"
                    label-width="188px"
                    label-position="left"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="直线作业速度百分比(%)">
                            <el-input-number
                                v-model="form.xe"
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="地头转弯速度百分比(%)">
                            <el-input-number
                                v-model="form.xf"
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="作业割台高度(%)">
                            <el-input-number
                                v-model="form.xg"
                                step-strictly
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                        <el-form-item label="地头转弯割台高度(%)">
                            <el-input-number
                                v-model="form.xh"
                                step-strictly
                                :min="1"
                                :max="100"
                            ></el-input-number>
                        </el-form-item>
                    </div>
                </el-form>
                <el-form
                    ref="form"
                    :model="form"
                    label-width="188px"
                    label-position="top"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="作业发动机转速（0-4000）">
                            <div style="padding: 0 10px">
                                <el-slider
                                    v-model="form.xc"
                                    :step="10"
                                    :min="0"
                                    :max="4000"
                                    :marks="form.marks3"
                                >
                                </el-slider>
                            </div>
                        </el-form-item>
                        <el-form-item label="地头转弯发动机转速（0-4000）">
                            <div style="padding: 0 10px">
                                <el-slider
                                    v-model="form.xd"
                                    :step="10"
                                    :min="0"
                                    :max="4000"
                                    :marks="form.marks3"
                                >
                                </el-slider>
                            </div>
                        </el-form-item>
                    </div>
                </el-form>
                <el-form
                    ref="form"
                    :model="form"
                    label-width="128px"
                    label-position="left"
                >
                    <div style="padding: 0 20px">
                        <el-form-item label="主离合设置">
                            <el-radio-group v-model="form.mainClutchSetting">
                                <el-radio
                                    :label="Number(it.code)"
                                    :key="it.code"
                                    v-for="it in umyw"
                                    >{{ it.name }}</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="对行模式">
                            <el-radio-group v-model="form.autoAlignment">
                                <el-radio
                                    :label="Number(it.code)"
                                    :key="it.code"
                                    v-for="it in auto_alignment"
                                    >{{ it.name }}</el-radio
                                >
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item>
                            <el-button class="cancel" @click="onCloseDialog"
                                >取消</el-button
                            >
                            <el-button class="submit" @click="submit"
                                >保存并下发</el-button
                            >
                        </el-form-item>
                    </div>
                </el-form>
            </el-scrollbar>
        </div>
    </el-dialog>
</template>
<script>
import { tractorWorkSetting, getCfg, cfgSave } from '@/api/systemagriculturalmachineryv2/unmanned/unmanned'
import { getDicts } from "@/api/systemagriculturalmachineryv2/dict";
export default {
    name: 'parameterConfigurationDialog',
    props: {
        modelValue: {
            type: Boolean,
            default: false,
        },
        currentMachine: {
            type: Object,
            default: {
                deviceCode: '',
                controlAuth: false,
                cfgvalue6: null,
            },
        },
        requestHeadLovol: {
            type: Object,
            default: {},
        },
    },
    mounted() {
        // 作业类型
        getDicts('assign_type').then((response) => {
            this.assign_type = response.data
        })
        // 作业模式
        getDicts('assign_mode').then((response) => {
            this.assign_mode = response.data
        })
        // 路径模式
        getDicts('route_mode').then((response) => {
            this.route_mode = response.data
        })
        // 转弯方式
        getDicts('turn_mode').then((response) => {
            this.turn_mode = response.data
        })
        // 是否收边
        getDicts('side_type').then((response) => {
            this.side_type = response.data
        })
        // PTO控制
        getDicts('pto_trol').then((response) => {
            this.pto_trol = response.data
        })
        // PTO转速
        getDicts('pto_rpm').then((response) => {
            this.pto_rpm = response.data
        })
        // 档位
        getDicts('gear_type').then((response) => {
            this.gear_type = response.data
        })
        // 四驱设置
        getDicts('fwd_install').then((response) => {
            this.fwd_install = response.data
        })
        // 差速锁设置
        getDicts('eds_install').then((response) => {
            this.eds_install = response.data
        })
        // 多路阀使能
        getDicts('mw_install').then((response) => {
            this.mw_install = response.data
        })
        // 后提升控制
        getDicts('pro_console').then((response) => {
            this.pro_console = response.data
        })
        // 作业机具浮动
        getDicts('assign_drift').then((response) => {
            this.assign_drift = response.data
        })
        // 多路阀动作
        getDicts('mw_action').then((response) => {
            this.mw_action = response.data
        })
        // 主离合设置
        getDicts('umyw').then((response) => {
            this.umyw = response.data
        })
        // 对行模式
         getDicts('auto_alignment').then((response) => {
            this.auto_alignment = response.data
        })
        // 还田机
        getDicts('field_returning').then((response) => {
            this.field_returning = response.data
        })
        // 过桥离合设置
        getDicts('bridge_clutch').then((response) => {
            this.bridge_clutch = response.data
        })
        this.getCfg()
    },
    data() {
        return {
            bridge_clutch:[],
            field_returning:[],
            auto_alignment:[],
            umyw:[],
            assign_type: [],
            assign_mode: [],
            route_mode: [],
            turn_mode: [],
            side_type: [],
            pto_trol: [],
            pto_rpm: [],
            gear_type: [],
            fwd_install: [],
            eds_install: [],
            mw_install: [],
            pro_console: [],
            assign_drift: [],
            mw_action: [],
            num: '',
            form: {
                //农机id
                machineId: '',
                // 参数id
                cfgationId: '',
                /**
                 * 作业机具高度
                 *
                 * P平台：0-1000
                 *    M平台：0-100
                 *    默认值：0
                 */
                mtHeight: 0,
                /**
                 * 直线作业速度 P平台支持
                 * 乘以10发送
                 *    默认值：0
                 */
                straightSpeed: 0,
                /**
                 * 地头转弯速度 P平台支持
                 *  乘以10发送
                 *    默认值：0
                 */
                turnSpeed: 0,
                /**
                 * 曲线作业速度
                 * 乘以10发送
                 *    默认值：0
                 */
                curveSpeed: 0,
                /**
                 * 地头转弯机具高度
                 * P平台：0-1000
                 *    M平台：0-100
                 *    默认值：0
                 *
                 */
                turnMtHeight: 0,
                /**
                 * PTO使能高度
                 * 0-100%
                 *    默认值：0
                 */
                ptoVol: 0,
                /**
                 * 作业发动机转速
                 * 0-4000
                 *    默认值：0
                 */
                engineSpeed: 0,
                /**
                 * 地头转弯发动机转速
                 * 0-4000
                 *    默认值：0
                 */
                turnEngineSpeed: 0,
                /**
                 * PTO控制
                 * 0: 无动作
                 *    1：啮合
                 *    2：分离
                 *    3：出错
                 */
                ptoTrol: 0,
                /**
                 * PTO转速
                 * 0：无动作
                 *    1: 空档
                 *    2：540
                 *    3：760
                 *    6：1000
                 *    7：无效
                 *    默认值：0
                 */
                ptoRpm: 0,
                /**
                 * 地头转弯档位
                 * 0: 档L(默认)
                 *    1：档M
                 *    2：档H
                 *    3：档D
                 */
                turnGear: 0,
                /**
                 *
                 *  直线作业档位
                 *  0: 档L(默认)
                 *    1：档M
                 *    2：档H
                 *    3：档D
                 */
                straightGear: 0,
                /**
                 * 四驱设置
                 * 0：无动作(默认)
                 *    1：四驱
                 *    2：两驱
                 *    3：无效
                 */
                fwdInstall: 0,
                /**
                 * 差速锁设置
                 * 0：无动作(默认)
                 *    1：结合
                 *    2：分离
                 *    3：无效
                 */
                edsInstall: 0,
                /**
                 * 多路阀使能
                 * 0：无动作(默认)
                 *    1：使能
                 *    2：不使能
                 */
                mwInstall: 0,
                mainClutchSetting: 0,
                autoAlignment: 0,
                fieldReturningMachine: 0,
                bridgeClutchSetting: 0,
                xg: 0,
                xh: 0,
                xe: 0,
                xf: 0,
                xc: 0,
                xd: 0,
                smartType: '',
            },
            rules: {},
        }
    },
    computed: {
        visible: {
            get() {
                return this.modelValue
            },
            set(val) {
                this.$emit('update:modelValue', val)
            }
        },
    },
    methods: {
        getCfg() {
            getCfg({ machineId: this.currentMachine.machineId }).then((res) => {
                if (res.data) {
                    let data = res.data
                    this.form = data
                    // p类型 直线作业速度 地头转弯速度 单独处理
                    if (this.currentMachine.cfgvalue6 == 'b') {
                        this.form.straightSpeed = data.straightSpeed / 10
                        this.form.turnSpeed = data.turnSpeed / 10
                    }
                    // this.form = {
                    //     cfgationId: data.machineId,
                    //     machineId: data.machineId,
                    //     mtHeight: data.mtHeight,
                    //     straightSpeed: data.straightSpeed / 10,
                    //     turnSpeed: data.turnSpeed / 10,
                    //     curveSpeed: data.curveSpeed,
                    //     turnMtHeight: data.turnMtHeight,
                    //     ptoVol: data.ptoVol,
                    //     engineSpeed: data.engineSpeed,
                    //     turnEngineSpeed: data.turnEngineSpeed,
                    //     ptoTrol: data.ptoTrol,
                    //     ptoRpm: data.ptoRpm,
                    //     turnGear: data.turnGear,
                    //     straightGear: data.straightGear,
                    //     fwdInstall: data.fwdInstall,
                    //     edsInstall: data.edsInstall,
                    //     mwInstall: data.mwInstall,
                    // }
                } else {
                    if (this.currentMachine.cfgvalue6 == 'b') {
                        this.form.mtHeight = 0
                        this.form.straightSpeed = 0
                        this.form.turnSpeed = 0
                        this.form.turnMtHeight = 0
                    }
                    if (this.currentMachine.cfgvalue6 == 'a') {
                        this.form.mtHeight = 0
                        this.form.turnMtHeight = 50
                    }

                    if (this.currentMachine.cfgvalue6 == 'c') {
                        this.form.xe = 1
                        this.form.xf = 1
                        this.form.xg = 1
                        this.form.xh = 1
                    }
                    if (this.currentMachine.cfgvalue6 == 'e') {
                        this.form.xe = 1
                        this.form.xf = 1
                        this.form.xg = 1
                        this.form.xh = 1
                    }
                    if (this.currentMachine.cfgvalue6 == 'd') {
                        this.form.xe = 1
                        this.form.xf = 1
                        this.form.xg = 1
                        this.form.xh = 1
                    }
                    if (this.currentMachine.cfgvalue6 == 'f') {
                        this.form.xe = 1
                        this.form.xf = 1
                        this.form.xg = 1
                        this.form.xh = 1
                    }
                }
            })
        },
        onCloseDialog() {
            this.visible = false
        },
        showMessage(msg) {
            this.$message({
                dangerouslyUseHTMLString: true,
                customClass: 'messageboxcustomClass',
                duration: 3000,
                offset: 200,
                message:
                    '<div class="content"><div class="title">消息提示</div><div class="text">' +
                    msg +
                    '</div></div>',
            })
        },
        submit() {
            this.requestHeadLovol.deviceNo = this.currentMachine.deviceCode
            this.form.machineId = this.currentMachine.machineId
            this.form.smartType = this.currentMachine.cfgvalue5
            var obj = {
                ...this.form,
                headLovol: this.requestHeadLovol,
                x1: 1,
            }
            if (this.currentMachine.cfgvalue6 == 'b') {
                obj.straightSpeed = this.form.straightSpeed * 10
                obj.turnSpeed = this.form.turnSpeed * 10
            }
            cfgSave(obj).then((res) => {
                this.showMessage('配置成功')
                setTimeout(() => {
                    this.onCloseDialog()
                }, 300)
            })
        },
    },
}
</script>
<style lang="scss" scoped>
.underline {
    border-bottom: 1px solid rgba(47, 169, 255, 0.2);
    padding-bottom: 10px;
}

.form {
    padding: 0 10px 0 30px !important;
    margin-top: 20px;

    :deep(.el-form-item__label) {
        color: #c2e1fe;
        font-size: 16px;
    }

    :deep(.el-radio) {
        color: #c2e1fe;
    }

    :deep(.el-radio__inner) {
        border: 1px solid rgba(23, 133, 255, 0.4);
        background: rgba(8, 105, 204, 0.2);

        &::after {
            background: #1193fd;
        }
    }

    :deep(.el-radio__input.is-checked + .el-radio__label) {
        color: #c2e1fe;
    }
    
    :deep(.el-input__wrapper), :deep(.el-input.is-disabled .el-input__wrapper),:deep(.el-select__wrapper){
        background: rgba(8, 105, 204, 0.15) !important;
        border: 1px solid rgba(23, 133, 255, 0.27) !important;
        color: #7fbef8 !important;
    }

    :deep(.el-input__inner) {
        color: #7fbef8;
        padding-left: 10px;
        padding-right: 10px;
    }

    :deep(.el-input-number__decrease) {
        width: 66px;
        height: 34px;
        background: #0056a9;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #ffffff;
        border-right: none;
    }

    :deep(.el-input-number__increase) {
        width: 66px;
        height: 34px;
        background: #0056a9;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #ffffff;
        border-left: none;
    }

    :deep(.el-input-number--medium) {
        width: 260px;
    }

    :deep(.el-input-number .el-input) {
        margin-left: 74px;
        width: 106px;
        height: 36px;
        background: rgba(8, 105, 204, 0.15);
        border: 1px solid rgba(23, 133, 255, 0.27);
        border-radius: 4px;
    }

    .cancel {
        width: 121px;
        border: 1px solid #1193fd;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        background-color: transparent;
        color: #6896d1;
    }

    .submit {
        width: 120px;
        background: linear-gradient(-46deg, #0056a9, #0a83e5);
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        font-size: 16px;
        font-weight: 500;
        color: #e3f1ff;
        border: none;
    }

    .click {
        width: 60px;
        height: 28px;
        background: #0056a9;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #c2e1fe;
        border: none;
        text-align: center;
        line-height: 28px;
        padding: 0;
    }

    .reset {
        width: 60px;
        height: 28px;
        background: #0056a9;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #c2e1fe;
        border: none;
        text-align: center;
        line-height: 28px;
        padding: 0;
    }
}

.newbottom {
    padding: 20px 20px 1px;
    width: 506px;
    background: rgba(8, 105, 204, 0.15);
    border-radius: 4px;
}

.newpoint {
    width: 464px;
    background: rgba(7, 15, 29, 0.55);
    border-radius: 4px;
    padding: 10px 0px 0px;
    margin-bottom: 10px;

    .newtop {
        // border-left: 2px solid #0075E6;
        margin-left: -20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        color: #c2e1fe;
        padding-left: 20px;
        padding-right: 20px;

        .borderLeft {
            width: 2px;
            height: 20px;
            background: #0075e6;
            flex-shrink: 0;
        }

        .new-top-right {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: space-around;
        }

        .newtoppic {
            margin-left: -10px;
        }
    }

    .newtbottom {
        padding-left: 20px;
        padding-bottom: 1px;
        padding-right: 20px;
    }
}

.con-bottom-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    margin-top: 20px;
    margin-bottom: 30px;

    .btn-cancel {
        width: 121px;
        height: 41px;
        border: 1px solid #1193fd;
        border-radius: 4px;
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        color: #6896d1;
        text-align: center;
        line-height: 41px;
    }

    .btn-save {
        width: 120px;
        height: 40px;
        background: linear-gradient(-46deg, #0056a9, #0a83e5);
        box-shadow: 0px 3px 6px 0px rgba(2, 114, 230, 0.3);
        border-radius: 4px;
        color: #e3f1ff;
        text-align: center;
        line-height: 40px;
        margin-left: 20px;
    }
}

.tabs {
    width: 248px;
    height: 40px;
    background: rgba(91, 162, 227, 0.05);
    border: 1px solid #08599d;
    border-radius: 4px;
    margin: 0px auto 0;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .tabItem {
        width: 120px;
        height: 32px;
        border-radius: 2px;
        font-size: 16px;
        font-weight: 400;
        color: #e4f2ff;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        color: #7fbef8;
        opacity: 0.6;

        &.tabActive {
            background: rgba(1, 125, 255, 0.5);
            border: 0px solid #070002;
            opacity: 1;
            color: #ffffff;
        }
    }
}
</style>
