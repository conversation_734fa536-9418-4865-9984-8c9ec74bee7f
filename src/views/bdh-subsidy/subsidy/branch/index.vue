<!--分公司上报业务-->
<template>
  <div class="app-container">
    <el-tabs v-model="activeName">
      <el-tab-pane
        label="申报"
        name="first"
        v-if="checkPermi(['subsidyApplyUpreportBranch:show'])"
      >
        <subsidy-apply v-show="activeName === 'first'"></subsidy-apply>
      </el-tab-pane>
      <el-tab-pane label="发放" name="second" v-if="checkPermi(['subsidybranch:show'])">
        <subsidy-branch v-show="activeName === 'second'"></subsidy-branch>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup name="/subsidy/branch/queryByPage">
import { ref } from "vue";
import subsidyBranch from "./components/subsidyBranch.vue";
import subsidyApply from "./components/subsidyApply.vue";
import { checkPermi } from "@/utils/permission";
const activeName = ref("first");
if (
  !checkPermi(["subsidyApplyUpreportBranch:show"]) &&
  checkPermi(["subsidybranch:show"])
) {
  // this.activeName = "second";
  activeName.value = "second";
}
</script>

<style lang="scss"></style>
