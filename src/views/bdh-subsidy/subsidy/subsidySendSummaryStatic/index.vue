<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-form :model="queryParams" class="queryClass"  ref="queryForm" v-show="showSearch" label-width="110px">
        <el-row style="display: flex; flex-wrap: wrap" :gutter="20">

          <el-col :span="6">
            <el-form-item label="年度" prop="subsidyYear">
              <el-select v-model="queryParams.subsidyYear"  placeholder="请选择年度" clearable>
                <el-option v-for="dict in yearNoOptions" :key="dict.code" :label="dict.name" :value="parseInt(dict.code)">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="所在单位" prop="organizationNo">
              <new-org-select
                  @firstTrip="firstTripFun"
                  :isclearable="false"
                  ref="newOrgSelect"
                  style="width: 100%"
                  @handleOrgChange="handleOrgChange"
                  :showLevel="3"
                  :currentOrgValue="queryParams.organizationNo"
                  :defaultType=true
              ></new-org-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="补贴分类" prop="subsidyClassify">
              <el-select v-model="queryParams.subsidyClassify" placeholder="请选择补贴分类" clearable>
                <el-option v-for="dict in subsidyClassifyOptions" :key="dict.key" :label="dict.value"
                           :value="dict.key"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="补贴项目" prop="subsidyConfigId">
              <el-select v-model="queryParams.subsidyConfigId" placeholder="请选择补贴项目"   @change="querygetSelectBudget">
                <el-option
                    v-for="dict in querySubsidyProjectList"
                    :key="dict.subsidyConfigId"
                    :label="dict.subsidyItemName"
                    :value="dict.subsidyConfigId"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="补贴类型" prop="subsidyType">
              <el-select v-model="queryParams.subsidyType" placeholder="请选择补贴类型" @clear="querygetSelectBudget" clearable  @change="querygetSelectBudget">
                <el-option
                    v-for="dict in subsidyTypeOptions"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="农场审批状态" prop="approvalStatusNo">
              <el-select v-model="queryParams.approvalStatusNo" placeholder="请选择农场审批状态" clearable>
                <el-option
                    v-for="dict in auditNoOptions"
                    :key="dict.code"
                    :label="dict.name"
                    :value="parseInt(dict.code)"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="分公司审批状态" prop="approvalBranchStatus">
              <el-select v-model="queryParams.approvalBranchStatus" placeholder="请选择分公司审批状态" clearable>
                <el-option
                    v-for="dict in auditNoOptions"
                    :key="dict.code"
                    :label="dict.name"
                    :value="parseInt(dict.code)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="发放状态" prop="paymentStatus">
              <el-select v-model="queryParams.paymentStatus" clearable placeholder="请选择发放状态"
              >
                <el-option
                    v-for="dict in paymentStatusOptions"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6" v-if="queryParams.subsidyClassify == 2">
            <el-form-item label="预算名称" prop="subsidyBudgetTitle">
              <el-select v-model="queryParams.subsidyBudgetTitle"  clearable   placeholder="请选择预算名称" >
                <el-option
                    v-for="dict in querysubsidyBudgetIdOptions"
                    :key="dict.subsidyBudgetTitle"
                    :label="dict.subsidyBudgetTitle"
                    :value="dict.subsidyBudgetTitle"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="发放开始时间" prop="paymentStartTime">
              <el-date-picker
                  style="width: 100%"
                  v-model="queryParams.paymentStartTime"

                  type="date"
                  placeholder="请选择发放开始时间"
                  format="YYYY 年 MM 月 DD 日"
                  value-format="YYYY-MM-DD"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="发放结束时间" prop="paymentEndTime">
              <el-date-picker
                  style="width: 100%"
                  v-model="queryParams.paymentEndTime"

                  type="date"
                  placeholder="请选择发放结束时间"
                  format="YYYY 年 MM 月 DD 日"
                  value-format="YYYY-MM-DD"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="补贴发放类型" prop="subsidySendType">
              <el-select v-model="queryParams.subsidySendType" clearable  placeholder="请选择补贴发放类型" >
                <el-option
                    v-for="dict in subsidySendTypeOptions"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6" align="right" v-if="queryParams.subsidyClassify == 1">
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          </el-col>

        </el-row>
      </el-form>
    </div>

    <el-row :gutter="10" class="mb8">

      <el-col :span="1.5" v-if="queryParams.subsidyClassify == 2">
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-col>

      <el-col :span="1.5" v-if="queryParams.subsidyClassify == 2">
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
      </el-col>

      <el-col :span="1.5" v-if="queryParams.subsidyClassify == 2">
        <el-button @click="handleExport" icon="Download" v-hasPermi="['subsidySendSummaryStatic:exportExcel']">
          导出
        </el-button>
      </el-col>

      <el-col :span="1.5" v-if="queryParams.subsidyClassify == 1">
        <el-button type="primary" @click="handleExport" icon="Download" v-hasPermi="['subsidySendSummaryStatic:exportExcel']">
          导出
        </el-button>
      </el-col>

      <right-toolbar v-model:showSearch="showSearch"  @queryTable="getList"></right-toolbar>

    </el-row>

    <el-table :height="tableHeight" border :data="getTableDataList" @selection-change="handleSelectionChange">
      <el-table-column
          :fixed="fixedFunName(item.name)"
          v-for="(item, index) in tableHeaderList.filter(datas => !datas.isHidden)"
          :width="getWidth(item.name)"
          :key="index"
          :align="fixedFunAlign(item.align)"
          :prop="item.name"
          :formatter="formatter"
          :label="item.label">
        <el-table-column
            v-if="item.child"
            v-for="(ite, ids) in item.child.filter(datas => !datas.isHidden)"
            :align="ite.align"
            :key="ids"
            :prop="ite.name"
            :formatter="formatter"
            :label="ite.label">
        </el-table-column>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup name="/subsidy/subsidySendSummaryStatic/queryAll">

import { getDicts } from "@/api/bdh-subsidy/system/dict/data";
import { selectDictLabel, selectDictLabels } from "@/utils/cop";
import {numFormat} from '@/views/bdh-subsidy/utils/cop.js'
import { listSubsidyProject } from "@/api/bdh-subsidy/subsidy/subsidyProject";
import { listSubsidyConfigAll } from "@/api/bdh-subsidy/subsidy/subsidyConfig";
import { getColumnModel, queryByPage} from "@/api/bdh-subsidy/subsidy/subsidySendSummaryStatic";
import {selectGroupBudgetTitle } from "@/api/bdh-subsidy/subsidy/agmachinedetail";
import {getSelectSubsidyPermission} from "@/api/bdh-subsidy/subsidy/subsidyAnnounceConfig";
import {postForExcel} from "@/api/bdh-subsidy/project/excel";
import NewOrgSelect from "@/views/bdh-subsidy/components/NewOrgSelect/index.vue";




const searchNum = ref(0); // 首次查询计数器
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const searchDom = ref(); // 搜索栏dom
const { proxy } = getCurrentInstance();


const subsidyTypeOptions = ref([]);//补贴类型字典
const getTableDataList = ref([]);
const tableHeaderList = ref([]);
const querysubsidyBudgetIdOptions = ref([]);
const querySubsidyProjectList = ref([]);
const ids = ref([]);
const single = ref(true);//非单选禁用
const multiple = ref(true);//非多选禁用
const showSearch = ref(true);//搜索是否展示
const title = ref("");//弹窗标题
const open = ref(false);//弹窗是否开启

const subsidyItemNames = ref([]);
const subsidyConfigList = ref([]);
const subsidyCropCodeOptions = ref([]);
const subsidyClassifyOptions = ref([]);
const subsidyUnitOptions = ref([]);
const yearNoOptions = ref([]);
const chargeTypeNoOptions = ref([]);
const subsidyCropCode = ref([]);
const bankNameOptions = ref([]);
const chargeCategoryTypeOptions = ref([]);
const ifCertificationOptions = ref([]);
const chargeSourceOptions = ref([]);
const paymentStatusOptions = ref([]);
const subsidySendTypeOptions = ref([]);

const subsidyUnit = ref("");
const auditNoOptions = ref("");
const item = ref("");
const showDetail = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    rows: 10,
    subsidyYear: String(new Date().getFullYear()),
    organizationNo: null,
    subsidyConfigId: null,
    subsidyType:null,
    approvalStatusNo: null,
    subsidyClassify: null,
    subsidyBudgetTitle: null
  },
  // 表单参数
  form: {},
  // 表单校验
  rules: {
    subsidyYear: [{required: true, message: '请选择年度', trigger: 'change'}],
    subsidyClassify: [{required: true, message: '请选择补贴分类', trigger: 'change'}],
    // subsidyConfigId: [{required: true, message: '请选择补贴项目', trigger: 'change'}],
  },
});

const { queryParams, form, rules } = toRefs(data);


onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
      ? window.innerHeight - searchHeight.value - 220
      : window.innerHeight - 220;

  geSubsidyConfigList();

  getDicts("year_cd").then((response) => {
    yearNoOptions.value = response.data;
  });
  getDicts('subsidy_audit_status').then((response) => {
    auditNoOptions.value = response.data
  });
  getDicts("charge_type_no").then(response => {
    chargeTypeNoOptions.value = response.data;
  });
  getDicts("sys_subsidy_crop_code").then((response) => {
    subsidyCropCodeOptions.value = response.data;
  });
  getDicts("subsidy_classify").then((response) => {
    subsidyClassifyOptions.value = response.data;
  }); //补贴分类
  getDicts("subsidy_unit").then((response) => {
    subsidyUnitOptions.value = response.data;
  }); //补贴统计单位
  getDicts("bank_name").then(response => {
    bankNameOptions.value = response.data;
  });
  getDicts("charge_category_type").then((response) => {
    chargeCategoryTypeOptions.value = response.data;
  });
  getDicts("pub_if").then((response) => {
    ifCertificationOptions.value = response.data;
  });
  getDicts("charge_source").then((response) => {
    chargeSourceOptions.value = response.data;
  });
  getDicts("sys_subsidy_crop_code").then(response => {
    subsidyCropCodeOptions.value = response.data;
  });
  getDicts("sys_subsidy_type").then(response => {
    subsidyTypeOptions.value = response.data;
  });
  getDicts("sys_payment_status").then(response => {
    paymentStatusOptions.value = response.data;
  });
  getDicts("subsidy_send_type").then(response => {
    subsidySendTypeOptions.value = response.data;
  });

});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
      ? window.innerHeight - searchHeight.value - 280
      : window.innerHeight - 280;
});
watch(() => queryParams.value.subsidyClassify, (val) => {
  queryParams.value.subsidyItemName = null;
  queryParams.value.subsidyConfigId = null; // 清空补贴项目的选择值
  querySubsidyProjectList.value = []; // 清空补贴项目的选项列表
  if (!val) {
    return;
  }
  listSubsidyConfigAll({ subsidyClassify: val }).then((response) => {
    querySubsidyProjectList.value = response.data;
  });
});

async function getAdvanceHeader() {
  tableHeaderList.value = []
  getColumnModel(queryParams.value).then(res => {
        let {data, success} = res
        if (success) {
          tableHeaderList.value = data
        }
      }
  )
}

/** 查询年度补贴项目表列表 */
function getList() {
  if(queryParams.value.paymentStartTime && queryParams.value.paymentEndTime){
    if(queryParams.value.paymentStartTime > queryParams.value.paymentEndTime){
      return msgError("发放开始时间需小于等于发放结束时间");
    }
  }


  queryByPage(queryParams.value).then(response => {
    getTableDataList.value = response.data;
    getAdvanceHeader();
  });
};

function geSubsidyConfigList() {
  form.value.subsidyProjectCode = null;
  showDetail.value = false;
  const queryParams = {};
  queryParams.statusCd = 1;
  queryParams.enableFlag = 1;
  queryParams.subsidyClassify = form.value.subsidyClassify;
  listSubsidyConfigAll(queryParams).then((response) => {
    subsidyConfigList.value = response.data;
  });
};

//查询中获取预算
function querygetSelectBudget(){
  queryParams.value.subsidyBudgetTitle = null
  querysubsidyBudgetIdOptions.value = []
  if( !queryParams.value.subsidyConfigId || !queryParams.value.subsidyYear){
    return
  }
  let params = {
    organizationNo: queryParams.value.organizationNo,
    subsidyType:queryParams.value.subsidyType,
    subsidyConfigId: queryParams.value.subsidyConfigId,
    subsidyYear:queryParams.value.subsidyYear
  }
  selectGroupBudgetTitle(params).then((response)=>{
    querysubsidyBudgetIdOptions.value = response.data
  })
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  getList();
};
/** 重置按钮操作 */
function resetQuery() {
  queryParams.value.page =  1;
  queryParams.value.rows = 10;
  queryParams.value.subsidyYear = String(new Date().getFullYear()),
  queryParams.value.organizationNo =  null,
  queryParams.value.subsidyConfigId = null,
  queryParams.value.subsidyType = null,
  queryParams.value.approvalStatusNo = null
  queryParams.value.subsidyBudgetTitle = null
  if( subsidyClassifyOptions.value.length > 0){
    queryParams.value.subsidyClassify =  subsidyClassifyOptions.value[0].key
  }
  proxy.resetForm("queryForm");
  handleQuery();
};


// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.projectConfigId);
  subsidyItemNames.value = selection.map((item) => item.subsidyItemName);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
};


//补贴作物字典翻译
function subsidyCropCodeFormatter(row, column) {
  return selectDictLabels(
      subsidyCropCodeOptions.value,
      row.subsidyCropCode != null && row.subsidyCropCode != undefined
          ? row.subsidyCropCode.toString()
          : ""
  );
};

//补贴分类字典翻译
function subsidyClassifyFormatter(row, column) {
  return selectDictLabels(
      subsidyClassifyOptions.value,
      row.subsidyClassify != null && row.subsidyClassify != undefined
          ? row.subsidyClassify.toString()
          : ""
  );
};

//补贴统计单位字典翻译
function subsidyUnitFormatter(row, column) {
  return selectDictLabels(
      subsidyUnitOptions.value,
      row.subsidyUnit != null && row.subsidyUnit != undefined
          ? row.subsidyUnit.toString()
          : ""
  );
};

function subsidyStdDesc1Formatter(row, column) {
  const projectId = row.subsidyProjectCode;
  for (let index in subsidyConfigList.value) {
    let sc = subsidyConfigList.value[index];
    if (sc.subsidyConfigId == projectId) {
      return sc.subsidyStdDesc1;
    }
  }
};

function subsidyStdDesc2Formatter(row, column) {
  const projectId = row.subsidyProjectCode;
  for (let index in subsidyConfigList.value) {
    let sc = subsidyConfigList.value[index];
    if (sc.subsidyConfigId == projectId) {
      return sc.subsidyStdDesc2;
    }
  }
};

function projectCodeHandler() {
  const projectId = form.value.subsidyProjectCode;
  for (let index in subsidyConfigList.value) {
    let sc = subsidyConfigList.value[index];
    if (sc.subsidyConfigId == projectId) {
      const codes = sc.subsidyCropCode?.split(",");
      let names = [];
      for (let c in codes) {
        let name = selectDictLabel(
            subsidyCropCodeOptions.value,
            codes[c] != null && codes[c] != undefined ? codes[c] : ""
        );
        names.push(name);
      }
      subsidyCropCode.value = names;
      subsidyUnit.value = sc.subsidyUnit.toString();
      showDetail.value = true;
      break;
    }
  }
};

function getSubsidyConfig(code) {
  const projectId = code;
  for (let index in subsidyConfigList.value) {
    let sc = subsidyConfigList.value[index];
    if (sc.subsidyConfigId == projectId) {
      return sc;
    }
  }
};


function formatter(row, column, cellValue, index) {
  if (column.property == 'subsidyProjectCode') {
    return selectDictLabel.value(querySubsidyProjectList.value, row.subsidyProjectCode);
  } else if (column.property == 'subsidyType') {
    return selectDictLabel.value(subsidyTypeOptions.value, row.subsidyType);
  }else if (
      !!row[column.property] &&
      (
          column.property.indexOf('Sum') != -1 ||
          column.property.indexOf('Fee') != -1 )
  ) {
    return numFormat(row[column.property])
  }
  else {
    // 返回其他不需要处理的正常参数
    return row[column.property]
  }
}

function fixedFunName(name){
  // console.log("fixedfunName--->>>",name)
  let names = ['organizationName','farmerName','farmerIdNumber']
  if (names.includes(name)) {
    return 'left';
  }
};

function fixedFunAlign(name){
  // console.log("fixedFunAlign--->>>",name)
  return 'center';
};

function getWidth(name) {
  let names = ['organizationNo','organizationName','orgName']
  let namemin = ['farmerIdNumber']
  if (names.includes(name)) {
    return 260;
  }else if(namemin.includes(name)){
    return 180;
  }
};

/** 组织机构查询 */
const firstTripFun = (orgCode) => {
  // handleQuery();
};
/** 组织机构查询 */
function handleOrgChange(orgCode) {
  queryParams.value.organizationNo = orgCode
  form.value.organizationNo = orgCode;
  querygetSelectBudget();
  proxy.$nextTick(()=>{
    searchNum.value++;
    if(searchNum.value < 2 ){
      getSelectSubsidyPermission().then(response => {
        subsidyClassifyOptions.value = response.data
        if(response.data.length > 0){
          queryParams.value.subsidyClassify =  response.data[0].key
        }
      });
    }
  });
};



/** 导出按钮操作 */
function handleExport() {
  postForExcel(
      "/" + import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY + "/subsidy/subsidySendSummaryStatic/exportExcel",
      queryParams.value,
      "补贴发放汇总导出"
  );
}


</script>
