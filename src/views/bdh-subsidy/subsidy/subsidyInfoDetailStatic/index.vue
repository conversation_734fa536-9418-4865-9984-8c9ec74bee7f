<!--补贴信息明细统计-->
<template>
  <div class="app-container">
    <div ref="searchDom">
      <el-form :model="queryParams" class="form-line" :rules="rules" ref="queryForm" v-show="showSearch"
               label-width="100px">
        <el-row style="display: flex; flex-wrap: wrap" :gutter="20">
          <el-col :span="6">
            <el-form-item label="年度" prop="subsidyYear">
              <el-select v-model="queryParams.subsidyYear" placeholder="请选择年度" clearable>
                <el-option v-for="dict in yearNoOptions" :key="dict.code" :label="dict.name"
                           :value="parseInt(dict.code)">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="所在单位" prop="organizationNo">
              <new-org-select
                  :isclearable="false"
                  ref="newOrgSelect"
                  style="width: 100%"
                  @handleOrgChange="handleOrgChange"
                  :showLevel="3"
                  :currentOrgValue="queryParams.organizationNo"
                  :defaultType=true
              ></new-org-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="补贴分类" prop="subsidyClassify">
              <el-select v-model="queryParams.subsidyClassify" placeholder="请选择补贴分类" >
                <el-option v-for="dict in subsidyClassifyOptions" :key="dict.key" :label="dict.value"
                           :value="dict.key"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="补贴项目" prop="subsidyConfigId">
              <el-select v-model="queryParams.subsidyConfigId" placeholder="请选择补贴项目"
                         @change="querygetSelectBudget">
                <el-option
                    v-for="dict in querySubsidyProjectList"
                    :key="dict.subsidyConfigId"
                    :label="dict.subsidyItemName"
                    :value="dict.subsidyConfigId"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="补贴类型" prop="subsidyType">
              <el-select v-model="queryParams.subsidyType" placeholder="请选择补贴类型" @clear="querygetSelectBudget"
                         clearable @change="querygetSelectBudget">
                <el-option
                    v-for="dict in subsidyTypeOptions"
                    :key="dict.code"
                    :label="dict.name"
                    :value="dict.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="姓名" prop="farmerName">
              <el-input
                  v-model="queryParams.farmerName"
                  clearable
                  placeholder="请输入姓名"
                  @keyup.enter.native="handleQuery"/>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="身份证号码" prop="farmerIdNumber">
              <el-input
                  v-model="queryParams.farmerIdNumber"
                  clearable
                  placeholder="请输入身份证号码"
                  @keyup.enter.native="handleQuery"/>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="银行卡号" prop="bankAccount">
              <el-input
                  v-model="queryParams.bankAccount"
                  clearable
                  placeholder="请输入银行卡号"
                  @keyup.enter.native="handleQuery"/>
            </el-form-item>
          </el-col>

          <el-col :span="6" v-if="queryParams.subsidyClassify==1">
            <el-form-item label="合同编号" prop="serialNumber">
              <el-input
                  v-model="queryParams.serialNumber"
                  clearable
                  placeholder="请输入合同编号"
                  @keyup.enter.native="handleQuery"/>
            </el-form-item>
          </el-col>

          <el-col :span="6" v-if="queryParams.subsidyClassify==1">
            <el-form-item label="地号" prop="landNumber">
              <el-input
                  v-model="queryParams.landNumber"
                  clearable
                  placeholder="请输入地号"
                  @keyup.enter.native="handleQuery"/>
            </el-form-item>
          </el-col>

          <el-col :span="6">
            <el-form-item label="审核状态" prop="approvalStatusNo">
              <el-select
                  v-model="queryParams.approvalStatusNo"
                  placeholder="请选择审核状态"
                  clearable>
                <el-option
                    v-for="dict in auditNoOptions"
                    :key="dict.code"
                    :label="dict.name"
                    :value="parseInt(dict.code)"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="6" v-if="queryParams.subsidyClassify == 2">
            <el-form-item label="补贴预算名称" prop="subsidyBudgetTitle">
              <el-select v-model="queryParams.subsidyBudgetTitle" clearable placeholder="请选择补贴预算名称">
                <el-option
                    v-for="dict in querysubsidyBudgetIdOptions"
                    :key="dict.subsidyBudgetTitle"
                    :label="dict.subsidyBudgetTitle"
                    :value="dict.subsidyBudgetTitle"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12" align="right" v-if="queryParams.subsidyClassify == 2">
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          </el-col>
          <el-col :span="6" align="right" v-else>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" @click="handleExport" icon="Download" v-hasPermi="['subsidyInfoDetailStatic:exportExcel']">
          导出
        </el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="tables" :height="tableHeight" border :data="detailStaticdata"
              @selection-change="handleSelectionChange">
      <el-table-column type="index" width="55" align="center" v-if="titleListEn &&titleListEn.length !=0"/>
      <el-table-column
          v-for="(item,index) in titleListEn"
          :key="index"
          align=center
          :prop="item"
          :formatter="formatter"
          :label="titleListCh[index]">
      </el-table-column>
    </el-table>
    <pagination
        v-show="totalShow"
        :total="total"
        v-model:page="queryParams.page"
        v-model:limit="queryParams.rows"
        @pagination="getList"
    />
  </div>
</template>

<script>

import {getDicts} from "@/api/bdh-subsidy/system/dict/data";
import {numFormat} from '@/views/bdh-subsidy/utils/cop.js'
import {queryByPage} from "@/api/bdh-subsidy/subsidy/subsidyInfoDetailStatic";
import {subsidyStandardManagegetByOrgs} from "@/api/bdh-subsidy/subsidy/subsidyPaymentDetail";
import {selectGroupBudgetTitle} from "@/api/bdh-subsidy/subsidy/agmachinedetail";
import {getSelectSubsidyPermission} from "@/api/bdh-subsidy/subsidy/subsidyAnnounceConfig";
import {postForExcel} from "@/api/bdh-subsidy/project/excel.js";
import NewOrgSelect from "@/views/bdh-subsidy/components/NewOrgSelect/index.vue";
import {selectDictLabel} from "@/views/bdh-subsidy/utils/cop.js"

export default {
  name: "/subsidy/subsidyInfoDetailStatic/queryByPage",
  components: {
    NewOrgSelect,
    selectDictLabel
  },
  data() {
    return {
      totalShow: false,//是否显示分页
      //补贴分类属性
      subsidyClassifyOptions: [],
      querysubsidyBudgetIdOptions: [],
      //首次查询计数器
      searchNum: 0,
      // 是否单选
      defaultType: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 预、实收费汇总（全局）表格数据
      tableHeaderList: [],
      bankNameList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 年度字典
      yearNoOptions: [],
      //补贴项目字典
      querySubsidyProjectList: [],
      //补贴类型字典
      subsidyTypeOptions: [],
      // 收费项目字典
      chargeSubjectCodeList: [],
      // 收费方式字典
      transferTypeNo: [],
      detailStaticdata: [],
      cropNoOptions: [],
      //审核状态字典
      auditNoOptions: [],
      // 查询参数
      queryParams: {
        page: 1,
        rows: 10,
        subsidyYear: String(new Date().getFullYear()),
        organizationNo: null,
        subsidyConfigId: null,
        subsidyType: null,
        landNumber: null,
        bankAccount: null,
        serialNumber: null,
        farmerIdNumber: null,
        approvalStatusNo: null,
        subsidyClassify: null,
        subsidyBudgetTitle: null
      },
      //表格高度
      tableHeight: 200,
      searchHeight: 0,
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        subsidyYear: [{required: true, message: '请选择年度', trigger: 'change'}],
        subsidyConfigId: [{required: true, message: '请选择补贴项目', trigger: 'change'}],
        subsidyClassify: [{required: true, message: '请选择补贴分类', trigger: 'change'}]
      },
      titleListCh: [],//动态表头中文名
      titleListEn: [],//动态表头英文名
    };
  },
  created() {
    getDicts("bank_name").then(response => {
      this.bankNameList = response.data;
    });
    getDicts('subsidy_audit_status').then((response) => {
      this.auditNoOptions = response.data
    })
    getDicts("transfer_type_no").then(response => {
      this.transferTypeNo = response.data;
    });
    getDicts("year_cd").then(response => {
      this.yearNoOptions = response.data;
    });
    getDicts("charge_category_type").then((response) => {
      this.chargeCategoryTypeOptions = response.data;
    });
    getDicts("sys_subsidy_type").then(response => {
      this.subsidyTypeOptions = response.data;
    });
    getDicts("sys_subsidy_crop_code").then((response) => {
      this.cropNoOptions = response.data;
    });
    // this.getSubsidyClassifyOptions()
  },
  mounted() {
    this.searchHeight = this.$refs.searchDom.clientHeight;
    this.tableHeight = this.showSearch
        ? window.innerHeight - this.searchHeight - 220
        : window.innerHeight - 220;
  },
  watch: {
    'showSearch': {
      handler() {
        this.tableHeight = this.showSearch
            ? window.innerHeight - this.searchHeight - 220
            : window.innerHeight - 220;
      },
      immediate: true
    },
    'queryParams.subsidyYear': {
      handler(val, oldval) {
        this.querygetSelectBudget()
        const queryParams = {}
        if (!val || !this.queryParams.subsidyClassify) {
          this.querySubsidyProjectList = []
          return
        }
        queryParams.subsidyYear = this.queryParams.subsidyYear
        queryParams.subsidyClassify = this.queryParams.subsidyClassify
        this.queryParams.subsidyConfigId = null
        subsidyStandardManagegetByOrgs(queryParams).then(response => {
          this.querySubsidyProjectList = response.data;
        });
      },
      immediate: true
    },
    'queryParams.subsidyClassify': {
      handler(val, oldval) {
        this.queryParams.serialNumber = null
        this.queryParams.landNumber = null
        const queryParams = {}
        this.$nextTick(() => {
          this.$refs.tables.doLayout()
        })

        if (!val) {
          this.querySubsidyProjectList = []
          return
        }
        queryParams.subsidyYear = this.queryParams.subsidyYear
        queryParams.subsidyClassify = this.queryParams.subsidyClassify
        this.queryParams.subsidyConfigId = null
        subsidyStandardManagegetByOrgs(queryParams).then(response => {
          this.querySubsidyProjectList = response.data;
        });
      },
      immediate: true
    }
  },
  methods: {
    getSubsidyClassifyOptions() {
      getSelectSubsidyPermission().then(response => {
        this.subsidyClassifyOptions = response.data
      });
    },

    handleOrgChange(orgCode) {
      this.queryParams.organizationNo = orgCode
      this.querygetSelectBudget()
      this.$nextTick(() => {
        this.searchNum++;
        if (this.searchNum < 2) {
          getSelectSubsidyPermission().then(response => {
            this.subsidyClassifyOptions = response.data
            if (this.subsidyClassifyOptions.length > 0) {
              this.queryParams.subsidyClassify = this.subsidyClassifyOptions[0].key
            }
            this.getList();
          });
        }
      });
    },

    //查询中获取预算
    querygetSelectBudget() {
      this.queryParams.subsidyBudgetTitle = null
      this.querysubsidyBudgetIdOptions = []
      if (!this.queryParams.subsidyConfigId || !this.queryParams.subsidyYear) {
        return
      }
      let params = {
        organizationNo: this.queryParams.organizationNo,
        subsidyType: this.queryParams.subsidyType,
        subsidyConfigId: this.queryParams.subsidyConfigId,
        subsidyYear: this.queryParams.subsidyYear
      }
      selectGroupBudgetTitle(params).then((response) => {
        this.querysubsidyBudgetIdOptions = response.data
      })
    },

    //收费方式
    handleTypeChange(types) {
      this.queryParams.types = types.map(item => item[0]);
    },
    /** 查询收费办理列表 */
    getList() {
      this.titleListCh = []
      this.titleListEn = []
      this.detailStaticdata = []
      this.totalShow = false
      this.$refs["queryForm"].validate(valid => {
        if (valid) {
          queryByPage(this.queryParams).then(response => {
            this.detailStaticdata = response.data.pagelist.records;
            this.total = response.data.pagelist.total;
            this.totalShow = response.data.pagelist.total > 0
            this.titleListCh = response.data.titleListCh
            this.titleListEn = response.data.titleListEn
            this.$nextTick(() => {
              this.$refs.tables.doLayout()
            })
          });
        }
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.page = 1;
      this.queryParams.rows = 10;
      this.queryParams.subsidyYear = String(new Date().getFullYear()),
          this.queryParams.organizationNo = null,
          this.queryParams.subsidyConfigId = null,
          this.queryParams.subsidyType = null,
          this.queryParams.farmerName = null,
          this.queryParams.farmerIdNumber = null,
          this.queryParams.bankAccount = null,
          this.queryParams.serialNumber = null,
          this.queryParams.landNumber = null,
          this.queryParams.approvalStatusNo = null
      this.queryParams.subsidyBudgetTitle = null
      if (this.subsidyClassifyOptions.length > 0) {
        this.queryParams.subsidyClassify = this.subsidyClassifyOptions[0].key
      }
      this.$refs.newOrgSelect.getCascader('reset'),
          this.handleQuery();
    },
    formatter(row, column) {
      const nums = ['subsidyArea', 'subsidyFee', 'subsidySendFee']
      if (column.property == 'bankName') {
        return this.selectDictLabel(this.bankNameList, row.bankName);
      } else if (column.property == 'subsidyCropCode') {
        return this.selectDictLabel(this.cropNoOptions, row.subsidyCropCode);
      } else if (
          !!row[column.property] && nums.includes(column.property)
      ) {
        return numFormat(row[column.property])
      } else {
        // 返回其他不需要处理的正常参数
        return row[column.property]
      }
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.lcChargeId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    getSelectedSubsidyName() {
      const selected = this.querySubsidyProjectList.find(
          item => item.subsidyConfigId === this.queryParams.subsidyConfigId
      );
      return selected?.subsidyItemName || '';
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$refs["queryForm"].validate(valid => {
        if (valid) {
          postForExcel('/' + import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY + '/subsidy/subsidyInfoDetailStatic/exportExcel', this.queryParams, this.queryParams.subsidyYear + this.getSelectedSubsidyName() + '补贴信息明细表');
        }
      })
    },
    selectDictLabel(options, cellValue){
      return selectDictLabel(
          options,
          cellValue
      );
    },
  }
}


</script>

<style lang="scss" scoped>
.leftBox {
  .leftTitle {
    font-size: 18px;
    background: #F2F6FC;
    text-align: center;
    line-height: 40px;
  }

  .userInfo {
    font-size: 16px;
    text-align: left;
    line-height: 28px;
    color: #686868;
    padding: 16px 20px;
  }
}

.outcardcont {
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;

  .left {
    width: 70%;

    .buttons {
      display: flex;
      text-align: center;
      margin-top: 20px;
    }

    .title {
      font-weight: bold;
      font-size: 20px;
      margin-bottom: 20px;
      text-align: center;
    }

    .img {
      img {
        width: 100%;
        height: 100%;
      }
    }
  }

}

.rightTop {
  height: 800px;
  width: 850px;
  display: flex;
  flex-direction: column;
  align-content: stretch;
  flex-wrap: nowrap;
  justify-content: flex-start;
}

.el-dialog__body {
  padding: 10px !important;
}

.landContractSign {
  .el-form-item {
    width: 266px;

    .el-form-item__content {
      width: 188px !important;

      .el-input-number {
        width: 188px;
      }
    }
  }
}
</style>
<style scoped lang='scss'>
:deep {
  .el-table__body-wrapper {
    overflow-y: auto;
  }

  .el-table--border th:first-child .cell:before {
    content: '序号';
  }
}

.el-table .warning-row {
  background: oldlace;
}

.form-line .el-form-item {
  padding-bottom: 12px;
}
</style>
