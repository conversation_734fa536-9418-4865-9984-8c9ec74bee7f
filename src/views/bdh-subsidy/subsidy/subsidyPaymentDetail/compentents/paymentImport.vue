<template>
  <div v-loading.fullscreen.lock="loading">
    <el-dialog
      :title="title"
      :model-value="visible"
      width="680px"
      append-to-body
      :before-close="cancel"
      :close-on-click-modal="false"
    >
      <el-form
        ref="importFormRef"
        :model="importForm"
        label-width="100px"
        label-position="left"
      >
        <el-row
          style="color: red; font-size: small; margin-bottom: 10px"
          >请选择年度、组织机构、补贴项目、补贴类型、预算名称后再上传文件</el-row
        >
        <el-row style="display: flex; flex-wrap: wrap" :gutter="10">
          <el-col :span="12">
            <el-form-item label="年度" prop="subsidyYear">
              <el-select
                v-model="importForm.subsidyYear"
                @change="onYearChange"
                placeholder="请选择年度"
                clearable
              >
                <el-option
                  v-for="dict in yearNoOptions"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="组织机构" prop="organizationNo">
              <new-org-select
                :showLevel="4"
                :checkStrictly="true"
                style="width: 100%"
                :defaultType="false"
                ref="newOrgSelect"
                @handleOrgChange="handleOrgChange"
                :currentOrgValue="importForm.organizationNo"
              ></new-org-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="补贴项目" prop="subsidyConfigId">
              <el-select
                v-model="importForm.subsidyConfigId"
                placeholder="请选择补贴项目"
                @change="onProjectChange"
                clearable
              >
                <el-option
                  v-for="dict in subsidyProjectList"
                  :key="dict.subsidyProjectCode"
                  :label="dict.subsidyItemName"
                  :value="dict.subsidyProjectCode"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="补贴类型" prop="subsidyType">
              <el-select
                v-model="importForm.subsidyType"
                placeholder="请选择补贴类型"
                @change="onSubsidyTypeChange"
                clearable
              >
                <el-option
                  v-for="dict in subsidyTypeOptions"
                  :key="dict.code"
                  :label="dict.name"
                  :value="dict.code"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预算名称" prop="subsidyBudgetId">
              <el-select
                v-model="importForm.subsidyBudgetId"
                placeholder="请选择预算名称"
                clearable
              >
                <el-option
                  v-for="dict in budgetOptions"
                  :key="dict.lcSubsidyFarmBudgetId"
                  :label="dict.subsidyBudgetTitle"
                  :value="dict.lcSubsidyFarmBudgetId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="模板下载">
          <el-button 
          type="primary" 
            :underline="false"
            @click="handleDownloadTemplate"
            >下载模板</el-button>
        </el-form-item>

        <el-form-item label="上传文件">
          <el-upload
            v-if="visible"
            class="upload-demo"
            :auto-upload="false"
            ref="upload"
            :action="uploadFileUrl"
            :http-request="httpRequest"
            :limit="1"
            accept=".xls,.xlsx"
            :disabled="isUploadDisabled"
          >
            <el-button type="primary" :disabled="isUploadDisabled"
              >点击上传</el-button
            >
            <template #tip>
              <div class="el-upload__tip">
                只能上传xls/xlsx文件，且不超过500kb
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">提 交</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      title="导入失败"
      v-model="dialogTableVisible"
      width="800px"
    >
      <el-table border :data="errDataList">
        <el-table-column
          property="rowIndex"
          label="行数"
          width="50"
          :formatter="valueFormat"
        ></el-table-column>
        <el-table-column
          property="message"
          label="错误详细信息"
          :formatter="valueFormat"
        ></el-table-column>
      </el-table>
    </el-dialog>

    <el-dialog
      title="导入日志"
      v-model="importResultDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      :before-close="closeAndTimer"
    >
      <div class="import-log-container result-dialog">
        <div class="log-content">
          <el-scrollbar max-height="400px">
            <div v-for="(log, index) in importLogs" :key="index" class="log-item">
              {{ log }}
            </div>
          </el-scrollbar>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="closeAndTimer">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { getDicts } from "@/api/bdh-subsidy/system/dict/data";
import axios from 'axios'
import { queryAllSubsidyProject } from '@/api/bdh-subsidy/subsidy/budget'
import { selectBudget, importNewApplylog, updateNewApplylog, exportTemplate } from "@/api/bdh-subsidy/subsidy/subsidyPaymentDetail";
import NewOrgSelect from "@/views/bdh-subsidy/components/NewOrgSelect/index.vue";

const { proxy } = getCurrentInstance();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  yearNoOptions: {
    type: Array,
    default: () => []
  },
  subsidyTypeOptions: {
    type: Array,
    default: () => []
  },
  title: {
    type: String,
    default: ''
  },
  uploadFileUrl: {
    type: String,
    default: ''
  }
});

const data = reactive({
  importForm: {
    subsidyYear: null,
    subsidyType: null,
    subsidyConfigId: null,
    subsidyBudgetId: null,
    organizationNo: null
  },
});
const { importForm } = toRefs(data);

const loading = ref(false);
const dialogTableVisible = ref(false);
const errDataList = ref([]);
const name = ref('paymentImport');
const subsidyProjectList = ref([]);
const budgetOptions = ref([]);
const importResultDialogVisible = ref(false);
const importLogs = ref([]);
// 导入状态检查的计时器
let importStatusTimer = null;
// 导入UUID
let importUuid = null;

// 监听年度变化，获取补贴项目列表
watch(() => importForm.value.subsidyYear, (val) => {
  if (!val) {
    subsidyProjectList.value = []
    return
  }
  queryAllSubsidyProject({
    subsidyClassify: 1,
    subsidyYear: val,
    subsidyClassifyClass: 100
  }).then((response) => {
    subsidyProjectList.value = response.data
  })
});

// 控制上传按钮是否禁用
const isUploadDisabled = computed(() => {
  return !(
    importForm.value.subsidyYear &&
    importForm.value.subsidyType &&
    importForm.value.subsidyConfigId &&
    importForm.value.organizationNo &&
    importForm.value.subsidyBudgetId
  )
});

// 重置表单数据为初始值
function clearForm() {
  importForm.value = {
    subsidyYear: null,
    subsidyType: null,
    subsidyConfigId: null,
    subsidyBudgetId: null,
    organizationNo: null
  }
  subsidyProjectList.value = []
  budgetOptions.value = []
}

function handleOrgChange(orgCode) {
  importForm.value.organizationNo = orgCode;
  queryBudgetOptions()
}

function onYearChange() {
  importForm.value.subsidyConfigId = null
  importForm.value.subsidyBudgetId = null
  budgetOptions.value = []
}

function onProjectChange() {
  importForm.value.subsidyBudgetId = null
  queryBudgetOptions()
}

function onSubsidyTypeChange() {
  importForm.value.subsidyBudgetId = null
  queryBudgetOptions()
}

// 查询预算名称选项
function queryBudgetOptions() {
  budgetOptions.value = []
  if (!importForm.value.subsidyYear || 
      !importForm.value.subsidyType || 
      !importForm.value.subsidyConfigId || 
      !importForm.value.organizationNo) {
    return
  }

  let params = {
    organizationNo: importForm.value.organizationNo,
    subsidyType: importForm.value.subsidyType,
    subsidyConfigId: importForm.value.subsidyConfigId,
    subsidyYear: importForm.value.subsidyYear
  }
  
  selectBudget(params).then((response) => {
    budgetOptions.value = response.data || []
  }).catch(() => {
    budgetOptions.value = []
  })
}

function httpRequest(param) {
  errDataList.value = []
  let fileObj = param.file
  let fd = new FormData()
  fd.append('file', fileObj)
  // 添加 subsidyBudgetId 到导入参数中
  const uploadParams = {
    ...importForm.value
  }
  fd.append('param', JSON.stringify(uploadParams))
  
  let url = props.uploadFileUrl
  let config = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  }
  loading.value = true
  axios.post(url, fd, config).then((res) => {
    if (res.data.code === 0 && res.data.success === true) {
      // 成功获取UUID，开始轮询导入状态
      importUuid = res.data.msg;
      importLogs.value = [];
      importResultDialogVisible.value = true;
      
      // 启动轮询
      startImportStatusPolling();
      proxy.$message.success('文件上传成功，正在处理中...')
      
      // 隐藏导入界面
      proxy.$emit('close', name.value)
    } else {
      proxy.$modal.msgError(res.data.msg)
      if (res.data.code !== 40001 && res.data.code == 0) {
        dialogTableVisible.value = true
        errDataList.value = res.data.data
      }
    }
    proxy.$refs.upload.clearFiles()
    loading.value = false
  }).catch(() => {
    proxy.$message.error('上传失败')
    proxy.$refs.upload.clearFiles()
    loading.value = false
  })
}

function submitForm() {
  proxy.$refs.upload.submit()
}

function cancel() {
  reset()
}

function reset() {
  // 清理定时器
  if (importStatusTimer) {
    clearInterval(importStatusTimer);
    importStatusTimer = null;
  }
  
  proxy.resetForm('importFormRef')
  proxy.$nextTick(() => {
    setTimeout(() => {
      proxy.$refs.newOrgSelect.getCascader('reset')
      proxy.$emit('close', name.value)
    })
  })
}

function valueFormat(row, column, cellValue, index) {
  return cellValue == '-1' || cellValue == 'null' ? '' : cellValue
}

// 开始轮询导入状态
function startImportStatusPolling() {
  // 清除之前的定时器
  if (importStatusTimer) {
    clearInterval(importStatusTimer);
  }
  // 创建新的定时器，每5秒检查一次
  importStatusTimer = setInterval(checkImportStatus, 5000);

  // 立即检查一次
  checkImportStatus();
}

// 检查导入状态
function checkImportStatus() {
  if (!importUuid) {
    clearInterval(importStatusTimer);
    return;
  }

  importNewApplylog({ uuid: importUuid }).then((response) => {
    if (response.code === 0) {
      // 更新日志
      if (response.data && response.data.length > 0) {
        importLogs.value = importLogs.value.concat(
          response.data.map((item) => item.info)
        );
        updateNewApplylog({
          lcSubsidyImportInfoLogId: response.data[0].lcSubsidyImportInfoLogId,
        });
      }
    } else {
      // 处理错误
      importLogs.value.push("获取导入状态失败: " + response.msg);
    }
  }).catch(() => {
    importLogs.value.push("获取导入状态失败");
  });
}

//关闭定时器和弹窗
function closeAndTimer() {
  importResultDialogVisible.value = false;
  if (importStatusTimer) {
    clearInterval(importStatusTimer);
    importStatusTimer = null;
  }
  proxy.$emit('getList');
}

// 下载模板
function handleDownloadTemplate() {
  window.location.href = exportTemplate();
}

defineExpose({
  clearForm
});
</script>

<style lang="scss" scoped>



/* 导入日志样式 */
.import-log-container {
  padding: 10px;

  &.result-dialog {
    height: 400px;
    padding: 0;
  }
}

.log-title {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 10px;
  text-align: center;
}

.log-content {
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  background-color: #f5f7fa;
  padding: 10px;
  height: 100%;
  overflow-y: auto;
}

.log-item {
  font-size: 14px;
  line-height: 1.5;
  padding: 5px 0;
  border-bottom: 1px dashed #e6e6e6;
  word-break: break-all;
}
</style>