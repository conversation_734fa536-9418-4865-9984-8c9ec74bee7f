<!--补贴信息管理 农业-->
<template>
  <div class="app-container">
    <el-tabs v-model="activeName" @tab-click="tabClick">
      <el-tab-pane label="农业补贴" name="first">
        <div v-if="tabIndex == 0">
          <div ref="searchDom">
            <el-form
              :model="queryParams"
              ref="queryForm"
              v-show="showSearch"
              label-width="100px"
              class="form-line"
            >
              <el-row style="display: flex; flex-wrap: wrap" :gutter="20">
                <el-col :span="6">
                  <el-form-item label="年度" prop="subsidyYear">
                    <el-select
                      v-model="queryParams.subsidyYear"
                      placeholder="请选择年度"
                      clearable
                    >
                      <el-option
                        v-for="dict in yearNoOptions"
                        :key="dict.code"
                        :label="dict.name"
                        :value="parseInt(dict.code)"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="所在单位" prop="organizationNo">
                    <new-org-select
                      @firstTrip="firstTripFun"
                      style="width: 100%"
                      :defaultType="true"
                      ref="newOrgSelect"
                      @handleOrgChange="handleOrgChange"
                      :currentOrgValue="queryParams.organizationNo"
                    ></new-org-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="补贴项目" prop="subsidyConfigId">
                    <el-select
                      v-model="queryParams.subsidyConfigId"
                      placeholder="请选择补贴项目"
                      clearable
                      @change="handleQuery"
                    >
                      <el-option
                        v-for="dict in querySubsidyProjectList"
                        :key="dict.subsidyConfigId"
                        :label="dict.subsidyItemName"
                        :value="dict.subsidyConfigId"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="补贴类型" prop="subsidyType">
                    <el-select
                      v-model="queryParams.subsidyType"
                      placeholder="请选择补贴类型"
                      clearable
                    >
                      <el-option
                        v-for="dict in subsidyTypeOptions"
                        :key="dict.code"
                        :label="dict.name"
                        :value="dict.code"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="类型" prop="contractSignType">
                    <el-select
                      @clear="queryParams.contractSignType = null"
                      v-model="queryParams.contractSignType"
                      placeholder="请选择类型"
                      clearable
                    >
                      <el-option
                        v-for="dict in contractSignTypeOption"
                        :key="dict.code"
                        :label="dict.name"
                        :value="parseInt(dict.code)"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="补贴作物" prop="subsidyCropCode">
                    <el-select
                      v-model="queryParams.subsidyCropCode"
                      placeholder="请选择补贴作物"
                      clearable
                    >
                      <el-option
                        v-for="dict in subsidyCropCodeOptions"
                        :key="dict.code"
                        :label="dict.name"
                        :value="dict.code"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="合同编号" prop="contractSerialNumber">
                    <el-input
                      v-model="queryParams.contractSerialNumber"
                      placeholder="请输入合同编号"
                      clearable
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="农户姓名" prop="farmerName">
                    <el-input
                      v-model="queryParams.farmerName"
                      placeholder="请输入农户姓名"
                      clearable
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="身份证号" prop="farmerIdNumber">
                    <el-input
                      v-model="queryParams.farmerIdNumber"
                      placeholder="请输入身份证号"
                      clearable
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="地块号" prop="landNumber">
                    <el-input
                      v-model="queryParams.landNumber"
                      placeholder="请输入地块号"
                      clearable
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="审核状态" prop="approvalStatusNo">
                    <el-select
                      v-model="queryParams.approvalStatusNo"
                      placeholder="请选择审核状态"
                      clearable
                    >
                      <el-option
                        v-for="dict in approverNoOptions"
                        :key="dict.code"
                        :label="dict.name"
                        :value="parseInt(dict.code)"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="审核级别" prop="auditLevel">
                    <el-select
                      v-model="queryParams.auditLevel"
                      clearable
                      placeholder="请选择审核级别"
                      @clear="queryParams.auditLevel = ''"
                      :empty-values="[null, undefined]"
                    >
                      <el-option
                        v-for="dict in auditLevelOptions"
                        :key="dict.code"
                        :label="dict.name"
                        :value="dict.code"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                plain
                icon="Download"
                @click="handleExport"
                v-hasPermi="['subsidyPaymentDetail:exportExcel']"
                >导出
              </el-button>
            </el-col>

            <el-col :span="1.5">
              <el-button
                icon="Upload"
                @click="handleCardImport"
                v-hasPermi="['subsidyPaymentDetail:exportTemplateCard']"
                >银行卡导入
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                icon="Upload"
                @click="handleImport"
                v-hasPermi="['subsidyPaymentDetail:importExcel']"
                >导入
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                icon="Delete"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['subsidyPaymentDetail:logicDeleteByIds']"
                >删除
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                icon="Check"
                :disabled="multiple"
                @click="(dialogVisible = true), (textarea = '')"
                v-hasPermi="[
                  'subsidyPaymentDetail:auditPassByIds',
                  'subsidyPaymentDetail:auditCancelByIds',
                ]"
                >审核
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                icon="Edit"
                :disabled="bankclickdata.length != 1"
                @click="handlebank"
                v-hasPermi="['subsidyPaymentDetail:getBankCard']"
                >修改银行卡信息</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                icon="Check"
                @click="handlePassAll"
                v-hasPermi="['subsidyPaymentDetail:auditPassAll']"
                >全部审核
              </el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button
                icon="Upload"
                @click="applyhandleImport"
                v-hasPermi="['subsidyPaymentDetail:applyImport']"
                >补贴申请导入
              </el-button>
            </el-col>
            <right-toolbar
              v-model:showSearch="showSearch"
              @queryTable="getList"
            ></right-toolbar>
          </el-row>

          <el-table
            ref="table"
            v-if="header.length > 0"
            :height="tableHeight"
            border
            :data="subsidyPaymentDetailList"
            @selection-change="handleSelectionChange"
            :header-cell-style="{ 'text-align': 'center' }"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              :fixed="fixedfun(item.name)"
              v-for="(item, index) in header.filter((datas) => !datas.isHidden)"
              :width="getWidth(item.name)"
              :key="index"
              :align="item.align"
              :prop="item.name"
              :formatter="formatter"
              :label="item.label"
            >
              <template v-if="item.child">
                <el-table-column
                  v-for="(ite, ids) in item.child.filter((datas) => !datas.isHidden)"
                  :align="ite.align"
                  :key="ids"
                  :prop="ite.name"
                  :formatter="formatter"
                  :label="ite.label"
                >
                </el-table-column>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              fixed="right"
              width="140px"
            >
              <template #default="scope">
                <el-button
                  type="text"
                  @click="handleCheck(scope.row)"
                  v-hasPermi="['subsidyPaymentDetail:info']"
                  >查看详情
                </el-button>
                <el-button
                  v-if="scope.row.ifUpdate == 1 || scope.row.ifUpdate == '1'"
                  type="text"
                  @click="handlebank(scope.row)"
                  v-hasPermi="['subsidyPaymentDetail:getBankCard']"
                  >修改银行卡信息
                </el-button>
                <el-button
                  type="text"
                  @click="handleDeleteCheck(scope.row)"
                  v-hasPermi="['subsidyPaymentDetail:logicDeleteById']"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-table
            ref="table"
            v-else
            :height="tableHeight"
            border
            :data="subsidyPaymentDetailList"
            @selection-change="handleSelectionChange"
            :header-cell-style="{ 'text-align': 'center' }"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              label="补贴项目"
              align="center"
              prop="subsidyItemName"
              width="200"
            />
            <el-table-column
              label="补贴类型"
              align="center"
              prop="subsidyType"
              :formatter="subsidyTypeFormatter"
              width="200"
            />
            <el-table-column label="所在单位" align="center" prop="organizationName" />
            <el-table-column
              align="center"
              class-name="small-padding fixed-width"
              fixed="right"
              width="140px"
            >
              <template #default="scope">
                <el-button
                  type="primary"
                  icon="View"
                  @click="handleCheck(scope.row)"
                  v-hasPermi="['subsidyPaymentDetail:info']"
                  >查看详情
                </el-button>
                <el-button
                  v-if="scope.row.ifUpdate == 1 || scope.row.ifUpdate == '1'"
                  type="primary"
                  icon="Edit"
                  @click="handlebank(scope.row)"
                  v-hasPermi="['subsidyPaymentDetail:getBankCard']"
                  >修改银行卡信息
                </el-button>
                <el-button
                  type="primary"
                  icon="Delete"
                  @click="handleDeleteCheck(scope.row)"
                  v-hasPermi="['subsidyPaymentDetail:logicDeleteById']"
                  >删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.page"
            v-model:limit="queryParams.rows"
            @pagination="getList"
          />
          <!--    修改银行卡-->
          <el-dialog
            title="修改银行卡信息"
            :closeOnClickModal="false"
            v-model="orgshow"
            width="400px"
            top
          >
            <el-form
              :model="bankForm"
              :rules="bankrules"
              ref="bankFormRef"
              :inline="true"
              label-width="100px"
            >
              <el-row>
                <el-col :span="24">
                  <el-form-item
                    label="收费账户："
                    prop="bankAccount"
                    style="display: flex"
                  >
                    <el-select
                      class="bottom-shadows"
                      @change="countfun"
                      filterable
                      v-model="bankForm.bankAccount"
                      placeholder="请选择收费银行卡号"
                    >
                      <el-option
                        v-for="(item, index) in corporateBankList"
                        :key="index"
                        :label="item.bankAccount"
                        :value="item.bankAccount"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="开户行：" prop="bankName" style="display: flex">
                    <el-select
                      class="bottom-shadows"
                      filterable
                      v-model="bankForm.bankName"
                      placeholder="请选择开户行"
                    >
                      <el-option
                        v-for="dict in bankNameList"
                        :key="dict.code"
                        :label="dict.name"
                        :value="dict.code"
                      >
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="开户网点" prop="network">
                    <el-input
                      v-model="bankForm.network"
                      maxlength="50"
                      clearable
                      placeholder="请输入开户网点"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="联行号" prop="lineNumber">
                    <el-input
                      v-model="bankForm.lineNumber"
                      maxlength="50"
                      clearable
                      placeholder="请输入联行号"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <template #footer>
              <div class="dialog-footer">
                <el-button type="danger" @click="orgshow = false">关闭</el-button>
                <el-button type="primary" @click="onOK">确定</el-button>
              </div>
            </template>
          </el-dialog>
          <!-- 添加或修改补贴信息表对话框 -->
          <el-dialog
            :closeOnClickModal="false"
            :title="title"
            v-model="open"
            width="900px"
            append-to-body
          >
            <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
              <el-divider content-position="left">补贴配置</el-divider>
              <el-row :gutter="2">
                <el-col :span="24">
                  <el-form-item label="补贴标准">
                    <el-button type="primary" @click="selectManage()"
                      >选择补贴标准</el-button
                    >
                    <el-button type="info" plain @click="clearManage()">清空</el-button>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="2">
                <el-col :span="8">
                  <el-form-item label="年度" prop="subsidyYear">
                    <el-select
                      v-model="form.subsidyYear"
                      placeholder="请选择年度"
                      disabled
                      @change="yearChangeHandler"
                    >
                      <el-option
                        v-for="dict in yearNoOptions"
                        :key="dict.code"
                        :label="dict.name"
                        :value="parseInt(dict.code)"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="所在单位" prop="organizationNo">
                    <org-select
                      @handleOrgChange="handleFormOrgChange"
                      :currentOrgValue="form.organizationNo"
                      :isdisabled="true"
                    ></org-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="2">
                <el-col :span="8">
                  <el-form-item label="补贴项目">
                    <el-input
                      v-model="subsidyItemName"
                      placeholder=""
                      disabled
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="补贴类型" prop="subsidyType">
                    <el-select
                      v-model="form.subsidyType"
                      placeholder="请选择补贴类型"
                      disabled
                      clearable
                    >
                      <el-option
                        v-for="dict in subsidyTypeOptions"
                        :key="dict.code"
                        :label="dict.name"
                        :value="dict.code"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="补贴作物" prop="subsidyCropCode">
                    <el-select
                      v-model="form.subsidyCropCode"
                      placeholder="请选择补贴作物"
                      disabled
                      multiple
                      clearable
                    >
                      <el-option
                        v-for="dict in subsidyCropCodeOptions"
                        :key="dict.code"
                        :label="dict.name"
                        :value="dict.code"
                      />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-divider content-position="left">农户信息</el-divider>
              <el-row :gutter="2">
                <el-col :span="8">
                  <el-form-item label="补贴发放编码" prop="subsidyPaymentNo">
                    <el-input
                      v-model="form.subsidyPaymentNo"
                      placeholder="请输入补贴发放编码"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="银行卡号" prop="bankAccount">
                    <el-input v-model="form.bankAccount" placeholder="请输入银行卡号" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="合同编码" prop="contractId">
                    <el-input v-model="form.contractId" placeholder="请输入合同编码" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="2">
                <el-col :span="8">
                  <el-form-item label="身份证号码" prop="farmerIdNumber">
                    <el-input
                      v-model="form.farmerIdNumber"
                      placeholder="请输入身份证号码"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="地块编码" prop="landNumber">
                    <el-input v-model="form.landNumber" placeholder="请输入地块编码" />
                  </el-form-item>
                </el-col>
                <!--<el-col :span="8">
                  <el-form-item label="实际发放金额" prop="actualPaymentFee">
                    <el-input v-model="form.actualPaymentFee" placeholder="请输入实际发放金额"/>
                  </el-form-item>
                </el-col>-->
              </el-row>
              <el-divider content-position="left">补贴信息</el-divider>
              <el-row :gutter="2" v-if="showDesc1">
                <el-col :span="8">
                  <el-form-item
                    :label="labelFrom.subsidyStdDesc1"
                    prop="subsidyStandard1"
                  >
                    <el-input
                      v-model="form.subsidyStandard1"
                      disabled
                      placeholder="请输入补贴标准1"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item
                    label="补贴面积1"
                    prop="subsidyArea1"
                    min="0"
                    :controls="false"
                    :precision="2"
                  >
                    <el-input v-model="form.subsidyArea1" placeholder="请输入补贴面积1" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="发放金额1" prop="subsidyFee1">
                    <el-input v-model="form.subsidyFee1" placeholder="" disabled />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="2" v-if="showDesc2">
                <el-col :span="8">
                  <el-form-item
                    :label="labelFrom.subsidyStdDesc2"
                    prop="subsidyStandard2"
                  >
                    <el-input
                      v-model="form.subsidyStandard2"
                      disabled
                      placeholder="请输入补贴标准2"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="补贴面积2" prop="subsidyArea2">
                    <el-input
                      v-model="form.subsidyArea2"
                      placeholder="请输入补贴面积2"
                      min="0"
                      :controls="false"
                      :precision="2"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="发放金额2" prop="subsidyFee2">
                    <el-input v-model="form.subsidyFee2" placeholder="" disabled />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <template #footer>
              <div class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="cancel">取 消</el-button>
              </div>
            </template>
          </el-dialog>

          <el-dialog
            title="查看详情"
            :closeOnClickModal="false"
            v-model="openCheck"
            width="1000px"
            append-to-body
          >
            <el-table :data="childrenList" ref="tablePlanChild" fit>
              <el-table-column
                label="补贴作物"
                align="center"
                prop="subsidyCropCode"
                :formatter="subsidyCropCodeFormatter"
              />
              <el-table-column label="地块" align="center" prop="landNumber" />
              <el-table-column
                label="实际发放金额合计"
                align="center"
                prop="actualPaymentFee"
                :formatter="actualPaymentFeeFormat"
              />
              <el-table-column :label="subsidyStdDesc1" align="center">
                <el-table-column
                  label="补贴标准（元/亩）"
                  align="center"
                  prop="subsidyStandard1"
                  :formatter="subsidyStandard1Format"
                />
                <el-table-column
                  label="补贴面积（亩）"
                  align="center"
                  prop="subsidyArea1"
                  :formatter="subsidyArea1Format"
                />
                <el-table-column
                  label="发放金额（元）"
                  align="center"
                  prop="subsidyFee1"
                  :formatter="subsidyFee1Format"
                />
              </el-table-column>
              <el-table-column :label="subsidyStdDesc2" align="center">
                <el-table-column
                  label="补贴标准（元/亩）"
                  align="center"
                  prop="subsidyStandard2"
                  :formatter="subsidyStandard2Format"
                />
                <el-table-column
                  label="补贴面积（亩）"
                  align="center"
                  prop="subsidyArea2"
                  :formatter="subsidyArea2Format"
                />
                <el-table-column
                  label="发放金额（元）"
                  align="center"
                  prop="subsidyFee2"
                  :formatter="subsidyFee2Format"
                />
              </el-table-column>
              <el-table-column align="center">
                <template #default="scope">
                  <el-button
                    type="primary"
                    @click="handleEditUpdate(scope.row)"
                    v-hasPermi="['subsidyPaymentDetail:updateSub']"
                    >修改
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-dialog>
          <el-dialog
            title="审核操作"
            :closeOnClickModal="false"
            v-model="dialogVisible"
            width="30%"
            top
          >
            <span>备注：</span>
            <el-input
              type="textarea"
              :rows="2"
              placeholder="请输入备注"
              v-model="textarea"
            >
            </el-input>
            <template #footer>
              <div class="dialog-footer">
                <el-button
                  type="danger"
                  @click="handleNoAudit"
                  v-hasPermi="['subsidyPaymentDetail:auditCancelByIds']"
                  >审核拒绝</el-button
                >
                <el-button
                  type="primary"
                  @click="handleAudit"
                  v-hasPermi="['subsidyPaymentDetail:auditPassByIds']"
                  >审核通过</el-button
                >
              </div>
            </template>
          </el-dialog>
          <!--修改金额-->
          <el-dialog
            title="修改发放金额"
            :closeOnClickModal="false"
            v-model="editshow"
            width="1100px"
            top
          >
            <el-form
              :model="editForm"
              :rules="editrules"
              ref="editFormRef"
              :inline="true"
              label-width="160px"
            >
              <el-row>
                <el-col :span="8">
                  <el-form-item label="补贴作物" prop="subsidyCropCode">
                    <el-select
                      disabled
                      v-model="editForm.subsidyCropCode"
                      placeholder="请选择补贴作物"
                      clearable
                    >
                      <el-option
                        v-for="dict in subsidyCropCodeOptions"
                        :key="dict.code"
                        :label="dict.name"
                        :value="dict.code"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="地块" prop="landNumber">
                    <el-input
                      v-model="editForm.landNumber"
                      placeholder="请输入地块"
                      clearable
                      disabled
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="实际发放金额合计" prop="actualPaymentFee">
                    <el-input
                      v-model="editForm.actualPaymentFee"
                      placeholder="请输入实际发放金额合计"
                      clearable
                      disabled
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-divider content-position="center">{{ subsidyStdDesc1 }}</el-divider>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="补贴标准（元/亩）" prop="subsidyStandard1">
                    <el-input
                      v-model="editForm.subsidyStandard1"
                      placeholder="请输入补贴标准（元/亩）"
                      clearable
                      disabled
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="补贴面积（亩）" prop="subsidyArea1">
                    <el-input
                      v-model="editForm.subsidyArea1"
                      placeholder="请输入补贴面积（亩）"
                      clearable
                      disabled
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="发放金额（元）" prop="subsidyFee1">
                    <el-input
                      v-model="editForm.subsidyFee1"
                      placeholder="请输入发放金额（元）"
                      clearable
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-divider content-position="center">{{ subsidyStdDesc2 }}</el-divider>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="补贴标准（元/亩）" prop="subsidyStandard1">
                    <el-input
                      v-model="editForm.subsidyStandard2"
                      placeholder="请输入补贴标准（元/亩）"
                      clearable
                      disabled
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="补贴面积（亩）" prop="subsidyArea1">
                    <el-input
                      v-model="editForm.subsidyArea2"
                      placeholder="请输入补贴面积（亩）"
                      clearable
                      disabled
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="发放金额（元）" prop="subsidyFee2">
                    <el-input
                      v-model="editForm.subsidyFee2"
                      placeholder="请输入发放金额（元）"
                      :disabled="editFormclearable"
                      clearable
                      @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <template #footer>
              <div class="dialog-footer">
                <el-button
                  type="danger"
                  @click="
                    editshow = false;
                    openCheck = false;
                  "
                  >关闭</el-button
                >
                <el-button type="primary" @click="onEdit">确定</el-button>
              </div>
            </template>
          </el-dialog>
        </div>
      </el-tab-pane>
      <el-tab-pane label="粮改饲补贴" name="second">
        <subsidy-payment-grain2feed
          v-if="tabIndex == 1"
          :contractSignTypeOption="contractSignTypeOption"
          :subsidyCropCodeOptions="subsidyCropCodeOptions"
          :subsidyTypeOptions="subsidyTypeOptions"
          :yearNoOptions="yearNoOptions"
          :approverNoOptions="approverNoOptions"
          :bankNameList="bankNameList"
          :auditNoOptions="auditNoOptions"
          ref="subsidyPaymentGrain2feedRef"
        ></subsidy-payment-grain2feed>
      </el-tab-pane>
    </el-tabs>

    <excel-import
      ref="excelImportRef"
      :visible="excelImportOpen"
      title="银行卡导入"
      @close="handleClose"
      :yearNoOptions="yearNoOptions"
      :subsidyTypeOptions="subsidyTypeOptions"
      :uploadFileUrl="uploadFileUrlBank"
      :exportTemplateUrl="exportTemplateUrl"
      @getList="getList"
    ></excel-import>

    <apply-import
      ref="applyImportRef"
      :visible="applyImportOpen"
      title="补贴申请导入"
      @close="handleImportClose"
      :yearNoOptions="yearNoOptions"
      :subsidyTypeOptions="subsidyTypeOptions"
      :uploadFileUrl="uploadFileUrlBank"
      @getList="getList"
    ></apply-import>

    <payment-import
      ref="paymentImportRef"
      :visible="paymentImportOpen"
      title="补贴信息导入"
      @close="handlePaymentImportClose"
      :yearNoOptions="yearNoOptions"
      :subsidyTypeOptions="subsidyTypeOptions"
      :uploadFileUrl="uploadFileUrl"
      @getList="getList"
    ></payment-import>


  </div>
</template>

<script setup name="/subsidy/subsidyPaymentDetail/queryByPage">
import { getDicts } from "@/api/bdh-subsidy/system/dict/data";
import { selectDictLabel, selectDictLabels } from "@/utils/cop";
import {
  listSubsidyPaymentDetail,
  getSubsidyPaymentDetail,
  delSubsidyPaymentDetail,
  addSubsidyPaymentDetail,
  updateSubsidyPaymentDetail,
  delSubsidyPaymentDetails,

  details,
  auditPass,
  auditCancel,
  getColumnModel,
  getBankCard,
  updateBankCard,
  updateSub,
  subsidyStandardManagequeryAll,
  subsidyStandardManagegetByOrgs,
  auditPassAll,
} from "@/api/bdh-subsidy/subsidy/subsidyPaymentDetail";
import { listSubsidyConfigAll } from "@/api/bdh-subsidy/subsidy/subsidyConfig";
import {
  listSubsidyStandardManageAll,
  getSubsidyStandardManage,
} from "@/api/bdh-subsidy/subsidy/subsidyStandardManage";
import NewOrgSelect from "@/views/bdh-subsidy/components/NewOrgSelect/index.vue";
import excelImport from "@/views/bdh-subsidy/subsidy/subsidyPaymentDetail/compentents/excelImport.vue";
import applyImport from "@/views/bdh-subsidy/subsidy/subsidyPaymentDetail/compentents/applyImport.vue";
import paymentImport from "@/views/bdh-subsidy/subsidy/subsidyPaymentDetail/compentents/paymentImport.vue";
import subsidyPaymentGrain2feed from "@/views/bdh-subsidy/subsidy/subsidyPaymentGrain2feed/index";
import { postForExcel } from "@/api/bdh-subsidy/project/excel.js";
import { onMounted } from "vue";
const { proxy } = getCurrentInstance();
const searchDom = ref(null);
const searchHeight = ref(0); // 搜索栏高度
const tableHeight = ref(400); // 表格高度
const tabIndex = ref(0);
const activeName = ref("first");
const contractSignTypeOption = ref([]);
const auditNoOptions = ref([]);
const orgshow = ref(false);
const bankclickdata = ref([]);
const data = reactive({
  bankForm: {
    bankAccount: null,
    subsidyDetailId: null,
    bankName: null,
    network: null,
    lineNumber: null,
  },
  bankrules: {
    bankAccount: [
      {
        required: true,
        message: "请选择银行卡号",
        trigger: "change",
      },
    ],
    bankName: [
      {
        required: true,
        message: "请选择开户行",
        trigger: "change",
      },
    ],
    network: [
      {
        required: true,
        message: "请输入开户网点",
        trigger: "blur",
      },
    ],
    lineNumber: [
      {
        required: true,
        message: "请输入联行号",
        trigger: "blur",
      },
      { min: 12, max: 12, message: "长度需要12位", trigger: "blur" },

      { validator: checkIsNumber, trigger: "blur" },
    ],
  },
  editForm: {
    subsidyDetailSubId: null,
    subsidyCropCode: null,
    landNumber: null,
    actualPaymentFee: null,
    subsidyStandard1: null,
    subsidyArea1: null,
    subsidyFee1: null,
    subsidyStandard2: null,
    subsidyArea2: null,
    subsidyFee2: null,
  },
  editrules: {
    subsidyFee1: [
      {
        required: true,
        message: "请输入发放金额",
        trigger: "change",
      },
      { required: true, validator: validateNum, trigger: "blur" },
    ],
    subsidyFee2: [
      {
        required: true,
        message: "请输入发放金额",
        trigger: "change",
      },
      { required: true, validator: validateNum, trigger: "blur" },
    ],
  },
  queryParams: {
    page: 1,
    rows: 10,
    bankAccount: null,
    subsidyPaymentNo: null,
    subsidyYear: String(new Date().getFullYear()),
    subsidyConfigId: null,
    subsidyType: null,
    subsidyCropCode: null,
    organizationName: null,
    organizationNo: null,
    contractId: null,
    contractSerialNumber: null,
    farmerId: null,
    farmerName: null,
    farmerIdNumber: null,
    landNumber: null,
    actualPaymentFee: null,
    subsidyStandard1: null,
    subsidyArea1: null,
    subsidyFee1: null,
    subsidyStandard2: null,
    subsidyArea2: null,
    subsidyFee2: null,
    approvalStatusNo: 0,
    auditAFlag: null,
    auditAId: null,
    auditAName: null,
    auditATime: null,
    auditBFlag: null,
    auditBId: null,
    auditBName: null,
    auditBTime: null,
    auditCFlag: null,
    auditCId: null,
    auditCName: null,
    auditCTime: null,
    auditDId: null,
    auditDFlag: null,
    auditDName: null,
    auditDTime: null,
    auditEId: null,
    auditEFlag: null,
    auditEName: null,
    auditETime: null,
    auditFId: null,
    auditFFlag: null,
    auditFName: null,
    auditFTime: null,
    auditGId: null,
    auditGFlag: null,
    auditGName: null,
    auditGTime: null,
    auditHId: null,
    auditHFlag: null,
    auditHName: null,
    auditHTime: null,
    currentAuditRoleId: null,
    currentAuditRoleName: null,
    approvalRemark: null,
    auditLevel: "",
    statusCd: null,
    dataStatus: null,
    contractSignType: null,
  },
  newqueryParams: {
    page: 1,
    rows: 10,
    bankAccount: null,
    subsidyPaymentNo: null,
    subsidyYear: String(new Date().getFullYear()),
    subsidyConfigId: null,
    subsidyType: null,
    subsidyCropCode: null,
    organizationName: null,
    organizationNo: null,
    contractId: null,
    contractSerialNumber: null,
    farmerId: null,
    farmerName: null,
    farmerIdNumber: null,
    landNumber: null,
    actualPaymentFee: null,
    subsidyStandard1: null,
    subsidyArea1: null,
    subsidyFee1: null,
    subsidyStandard2: null,
    subsidyArea2: null,
    subsidyFee2: null,
    approvalStatusNo: 0,
    auditAFlag: null,
    auditAId: null,
    auditAName: null,
    auditATime: null,
    auditBFlag: null,
    auditBId: null,
    auditBName: null,
    auditBTime: null,
    auditCFlag: null,
    auditCId: null,
    auditCName: null,
    auditCTime: null,
    auditDId: null,
    auditDFlag: null,
    auditDName: null,
    auditDTime: null,
    auditEId: null,
    auditEFlag: null,
    auditEName: null,
    auditETime: null,
    auditFId: null,
    auditFFlag: null,
    auditFName: null,
    auditFTime: null,
    auditGId: null,
    auditGFlag: null,
    auditGName: null,
    auditGTime: null,
    auditHId: null,
    auditHFlag: null,
    auditHName: null,
    auditHTime: null,
    currentAuditRoleId: null,
    currentAuditRoleName: null,
    approvalRemark: null,
    auditLevel: "",
    statusCd: null,
    dataStatus: null,
  },
  labelFrom: {
    subsidyStdDesc1: "补贴标准1",
    subsidyStdDesc2: "补贴标准2",
  },
  // 表单参数
  form: {},
  // 表单校验
  rules: {},
});
const {
  bankForm,
  bankrules,
  editForm,
  editrules,
  queryParams,
  newqueryParams,
  labelFrom,
  form,
  rules,
} = toRefs(data);
const corporateBankList = ref([]);
const bankNameList = ref([]);
const excelImportOpen = ref(false);
const applyImportOpen = ref(false);
const paymentImportOpen = ref(false);
const childrenList = ref([]);
const editshow = ref(false);
const editFormclearable = ref(false);
const auditLevelOptions = ref([
  {
    code: "",
    name: "全部",
  },
  {
    code: "0",
    name: "一级审核",
  },
  {
    code: "1",
    name: "二级审核",
  },
  {
    code: "2",
    name: "三级审核",
  },
  {
    code: "3",
    name: "四级审核",
  },
  {
    code: "4",
    name: "五级审核",
  },
  {
    code: "5",
    name: "六级审核",
  },
  {
    code: "6",
    name: "七级审核",
  },
  {
    code: "7",
    name: "八级审核",
  },
]);
const subsidyStdDesc1 = ref("");
const subsidyStdDesc2 = ref("");
const loading = ref(true);
const subsidyPaymentDetailListChild = ref([]);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const showSearch = ref(true);
const total = ref(0);
const uploadFileUrl = ref(
  window.VITE_APP_BASE_API +
    "/" +
    import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY +
    "/subsidy/subsidyPaymentDetail/importExcel"
);
const uploadFileUrlBank = ref(
  window.VITE_APP_BASE_API +
    "/" +
    import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY +
    "/subsidy/subsidyPaymentDetail/importExcelCard"
);
const subsidyPaymentDetailList = ref([]);
const title = ref("");
const open = ref(false);
const openCheck = ref(false);
const header = ref([]);
const dialogVisible = ref(false);
const textarea = ref("");
const subsidyTypeOptions = ref([]);
const subsidyCropCodeOptions = ref([]);
const yearNoOptions = ref([]);
const querySubsidyProjectList = ref([]);
const querySubsidyConfigList = ref([]);
const querySubsidyStandardManageList = ref([]);
const subsidyProjectList = ref([]);
const approverNoOptions = ref([]);
const subsidyDetailFarmerNames = ref([]);
const subsidyItemName = ref([]);
const showDesc1 = ref([]);
const showDesc2 = ref([]);
const url = ref(
  window.VITE_APP_BASE_API +
    "/" +
    import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY +
    "/subsidy/subsidyPaymentDetail/exportExcel"
);
const maxAuditLevel = ref(1);
const exportTemplateUrl = ref(
  window.VITE_APP_BASE_API + "/subsidy/subsidyPaymentDetail/exportTemplateCard"
);

const getOptions = () => {
  getDicts("contract_sign_type").then((response) => {
    contractSignTypeOption.value = response.data;
  });
  getDicts("sys_subsidy_crop_code").then((response) => {
    subsidyCropCodeOptions.value = response.data;
  });
  getDicts("sys_subsidy_type").then((response) => {
    subsidyTypeOptions.value = response.data;
  });
  getDicts("year_cd").then((response) => {
    yearNoOptions.value = response.data;
  });
  getDicts("aproval_status_no").then((response) => {
    approverNoOptions.value = response.data;
  });
  getDicts("bank_name").then((response) => {
    bankNameList.value = response.data;
  });
  getDicts("aproval_status_no").then((response) => {
    auditNoOptions.value = response.data;
  });
};
onMounted(() => {
  searchHeight.value = searchDom.value?.clientHeight;
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 260
    : window.innerHeight - 260;
  getOptions();
  queryYearChangeHandler();
});
watch(showSearch, (value) => {
  tableHeight.value = showSearch.value
    ? window.innerHeight - searchHeight.value - 260
    : window.innerHeight - 260;
});

function validateNum(rule, value, callback) {
  const reg = /^([0-9]{1}|^[1-9]{1}\d{1,15})(\.\d{1,2})?$/;
  if (value) {
    if (value.toString().split(".")[0].length > 10) {
      callback(new Error("输入过长，请确认"));
    } else if (reg.test(value)) {
      callback();
    } else {
      callback(new Error("请输入数字(可带两位小数)"));
    }
  } else {
    if (rule.required) {
      callback(new Error("请输入发放金额"));
    } else {
      callback();
    }
  }
}

function checkIsNumber(rule, value, callback) {
  const patrn = /(^[0-9]*$)/; //数字校验
  let errorMsg = "";
  if (value) {
    if (!patrn.exec(value)) {
      errorMsg = "请输入正确的联行号";
    }
  }

  if (errorMsg) {
    callback(new Error(errorMsg));
  } else {
    callback();
  }
}
watch(
  () => form.value.subsidyArea1,
  (newVal, oldVal) => {
    if (newVal != oldVal) {
      queryParams.value.subsidyConfigId = null;
      queryYearChangeHandler();
    }
  }
);
watch(
  () => form.value.subsidyArea2,
  (newVal) => {
    if (form.value.subsidyStandard2 && newVal > 0) {
      form.value.subsidyFee2 = numMulti(newVal, form.value.subsidyStandard2);
    }
  }
);

watch(
  () => queryParams.value.subsidyYear,
  (val) => {
    queryParams.value.subsidyConfigId = null;
    queryYearChangeHandler();
  }
);

function handlePassAll() {
  proxy.$modal
    .confirm("此操作将全部审核通过, 是否继续?", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(() => {
      auditPassAll(newqueryParams.value).then(() => {
        proxy.$message({
          type: "success",
          message: "全部审核完成!",
        });
        getList();
      });
    });
}

async function handlebank(row) {
  bankrest();
  bankForm.value.subsidyDetailId =
    row.subsidyDetailId || bankclickdata.value[0].subsidyDetailId;
  orgshow.value = true;
  const param = {
    // farmerId:row.farmerId || this.bankclickdata[0].farmerId,
    subsidyDetailId: row.subsidyDetailId || bankclickdata.value[0].subsidyDetailId,
  };
  const bankres = await getBankCard(param);
  const { code, data } = bankres;
  if (code == 0) {
    corporateBankList.value = data.list;

    bankForm.value.bankAccount = data.detail.bankAccount || null;
    bankForm.value.bankName = data.detail.bankName || null;
    bankForm.value.network = data.detail.network || null;
    bankForm.value.lineNumber = data.detail.lineNumber || null;
    console.log("bankForm= " + JSON.stringify(bankForm));
  }
}

function actualPaymentFeeFormat(row, column) {
  return row.actualPaymentFee != undefined && row.actualPaymentFee != null
    ? row.actualPaymentFee.toString().replace(/(\d)(?=(\d{3})+\.)/g, "$1,")
    : "0.00";
}
function subsidyStandard1Format(row, column) {
  return row.subsidyStandard1 != undefined && row.subsidyStandard1 != null
    ? row.subsidyStandard1.toString().replace(/(\d)(?=(\d{3})+\.)/g, "$1,")
    : "0.00";
}
function subsidyArea1Format(row, column) {
  return row.subsidyArea1 != undefined && row.subsidyArea1 != null
    ? row.subsidyArea1.toString().replace(/(\d)(?=(\d{3})+\.)/g, "$1,")
    : "0.00";
}
function subsidyFee1Format(row, column) {
  return row.subsidyFee1 != undefined && row.subsidyFee1 != null
    ? row.subsidyFee1.toString().replace(/(\d)(?=(\d{3})+\.)/g, "$1,")
    : "0.00";
}
function subsidyStandard2Format(row, column) {
  return row.subsidyStandard2 != undefined && row.subsidyStandard2 != null
    ? row.subsidyStandard2.toString().replace(/(\d)(?=(\d{3})+\.)/g, "$1,")
    : "0.00";
}
function subsidyArea2Format(row, column) {
  return row.subsidyArea2 != undefined && row.subsidyArea2 != null
    ? row.subsidyArea2.toString().replace(/(\d)(?=(\d{3})+\.)/g, "$1,")
    : "0.00";
}
function subsidyFee2Format(row, column) {
  return row.subsidyFee2 != undefined && row.subsidyFee2 != null
    ? row.subsidyFee2.toString().replace(/(\d)(?=(\d{3})+\.)/g, "$1,")
    : "0.00";
}

function onEdit() {
  proxy.$refs["editFormRef"].validate((valid) => {
    if (valid) {
      if (editForm.value.subsidyFee1 === "0" && editForm.value.subsidyFee2 === null) {
        return proxy.$modal.msgError("发放金额不能为0");
      } else if (
        editForm.value.subsidyFee1 === "0" &&
        editForm.value.subsidyFee2 === "0"
      ) {
        return proxy.$modal.msgError("发放金额不能同时为0");
      }
      let param = {
        subsidyDetailSubId: editForm.value.subsidyDetailSubId,
        subsidyFee1: editForm.value.subsidyFee1,
        subsidyFee2: editForm.value.subsidyFee2,
      };
      updateSub(param).then((response) => {
        proxy.$modal.msgSuccess("修改成功");
        editshow.value = false;
        openCheck.value = false;
        getList();
      });
    }
  });
}

function onOK() {
  proxy.$refs["bankFormRef"].validate((valid) => {
    if (valid) {
      updateBankCard(bankForm.value).then((response) => {
        proxy.$modal.msgSuccess("修改成功");
        orgshow.value = false;
        getList();
      });
    }
  });
}

function bankrest() {
  proxy.resetForm("bankFormRef");
  corporateBankList.value = [];
}

function fixedfun(name) {
  let names = ["subsidyYear", "subsidyItemName", "subsidyTypeName", "subsidyCropCode"];
  if (names.includes(name)) {
    return "left";
  }
}
function getWidth(name) {
  let names = [
    "orgCode",
    "organizationName",
    "farmerIdNumber",
    "orgName",
    "subsidyItemName",
    "subsidyTypeName",
    "subsidyCropCode",
    "contractSerialNumber",
    "farmerIdNumber",
    "bankAccount",
    "bankName",
  ];
  if (names.includes(name)) {
    return 220;
  }
}

function formatter(row, column, cellValue, index) {
  const numeralCondition = ["amount", "amount_"];
  if (column.property === "chargeTypeNo") {
    // 收费类型
    return selectDictLabel(chargeTypeNoOptions.value, row.chargeTypeNo);
  } else if (column.property == "subsidyCropCode") {
    let rowdata = "";
    if (row.subsidyCropCode != null && row.subsidyCropCode != undefined) {
      subsidyCropCodeOptions.value.forEach((v, i) => {
        if (!row.subsidyCropCode.includes(",")) {
          if (v.code == row.subsidyCropCode) {
            rowdata = v.name;
          }
        } else {
          row.subsidyCropCode.split(",").forEach((v1, i1) => {
            if (v.code == v1) {
              rowdata += "," + v.name;
            }
          });
        }
      });
      return row.subsidyCropCode.includes(",") ? rowdata.substr(1) : rowdata;
    }
  } else if (column.property == "contractSignType") {
    return selectDictLabel(
      contractSignTypeOption.value,
      row.contractSignType != null && row.contractSignType != undefined
        ? row.contractSignType.toString()
        : ""
    );
  } else if (column.property == "approvalStatusNo") {
    return selectDictLabel(
      approverNoOptions.value,
      row.approvalStatusNo != null && row.approvalStatusNo != undefined
        ? row.approvalStatusNo.toString()
        : ""
    );
  } else if (column.property == "auditAFlag") {
    return selectDictLabel(
      approverNoOptions.value,
      row.auditAFlag != null && row.auditAFlag != undefined
        ? row.auditAFlag.toString()
        : ""
    );
  } else if (column.property == "auditBFlag") {
    return selectDictLabel(
      approverNoOptions.value,
      row.auditBFlag != null && row.auditBFlag != undefined
        ? row.auditBFlag.toString()
        : ""
    );
  } else if (column.property == "auditCFlag") {
    return selectDictLabel(
      approverNoOptions.value,
      row.auditCFlag != null && row.auditCFlag != undefined
        ? row.auditCFlag.toString()
        : ""
    );
  } else if (column.property == "auditDFlag") {
    return selectDictLabel(
      approverNoOptions.value,
      row.auditDFlag != null && row.auditDFlag != undefined
        ? row.auditDFlag.toString()
        : ""
    );
  } else if (column.property == "auditEFlag") {
    return selectDictLabel(
      approverNoOptions.value,
      row.auditEFlag != null && row.auditEFlag != undefined
        ? row.auditEFlag.toString()
        : ""
    );
  } else if (column.property == "auditFFlag") {
    return selectDictLabel(
      approverNoOptions.value,
      row.auditFFlag != null && row.auditFFlag != undefined
        ? row.auditFFlag.toString()
        : ""
    );
  } else if (column.property == "auditGFlag") {
    return selectDictLabel(
      approverNoOptions.value,
      row.auditGFlag != null && row.auditGFlag != undefined
        ? row.auditGFlag.toString()
        : ""
    );
  } else if (column.property == "auditHFlag") {
    return selectDictLabel(
      approverNoOptions.value,
      row.auditHFlag != null && row.auditHFlag != undefined
        ? row.auditHFlag.toString()
        : ""
    );
  } else if (column.property == "chargeCategoryType") {
    // 项目
    return selectDictLabel(chargeCategoryTypeOptions.value, row.chargeCategoryType);
  } else if (column.property == "bankName") {
    // 开户行
    return selectDictLabel(bankNameList.value, row.bankName);
  } else if (
    column.property.includes("subsidyStandard") ||
    column.property == "actualPaymentFee" ||
    column.property.includes("subsidyArea") ||
    column.property.includes("subsidyFee")
  ) {
    // 项目
    return row[column.property] != undefined && row[column.property] != null
      ? row[column.property].toString().replace(/(\d)(?=(\d{3})+\.)/g, "$1,")
      : "0.00";
  } else if (
    !!row[column.property] &&
    (numeralCondition.includes(column.property) ||
      column.property.indexOf("amount") != -1 ||
      column.property.indexOf("Amount") != -1)
  ) {
    let numeralStr = row[column.property].toString();
    let floatStr = "",
      floatArr = numeralStr.split(".");
    if (floatArr.length > 1) {
      let floatLength = floatArr[1].length;
      for (let i = 0; i < floatLength; i++) {
        floatStr += "0";
      }
    }

    return row[column.property] != undefined && row[column.property] != null
      ? row[column.property].toString().replace(/(\d)(?=(\d{3})+\.)/g, "$1,")
      : "0.00";
  } else {
    // 返回其他不需要处理的正常参数
    return row[column.property];
  }
}

/** 查询补贴信息表列表 */
function getList() {
  loading.value = true;
  getColumnModel(queryParams.value)
    .then((response) => {
      header.value = response.data;
      console.log("header.value = " + header.value.length);
      loading.value = false;
      getSubsidyPaymentDetailFun();
    })
    .catch((e) => {
      header.value = [];
      loading.value = false;
      proxy.$nextTick(() => {
        proxy.$refs.table.doLayout();
      });
      getSubsidyPaymentDetailFun();
    });
}

function getSubsidyPaymentDetailFun() {
  listSubsidyPaymentDetail(queryParams.value).then((response) => {
    total.value = response.data.page.total;
    subsidyPaymentDetailList.value = response.data.page.records;
    maxAuditLevel.value = response.data.maxAuditLevel;
  });
}
function querySubsidyConfigAll() {
  listSubsidyConfigAll({}).then((response) => {
    querySubsidyConfigList.value = response.data;
  });
}
// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  labelFrom.value = {
    subsidyStdDesc1: "补贴标准1",
    subsidyStdDesc2: "补贴标准2",
  };
  form.value = {
    bankAccount: null,
    subsidyDetailId: null,
    subsidyPaymentNo: null,
    subsidyYear: null,
    subsidyProjectCode: null,
    subsidyType: null,
    subsidyCropCode: null,
    organizationName: null,
    organizationNo: null,
    contractId: null,
    farmerId: null,
    farmerName: null,
    farmerIdNumber: null,
    landNumber: null,
    actualPaymentFee: null,
    subsidyStandard1: null,
    subsidyArea1: null,
    subsidyFee1: null,
    subsidyStandard2: null,
    subsidyArea2: null,
    subsidyFee2: null,
    approvalStatusNo: null,
    auditAFlag: null,
    auditAId: null,
    auditAName: null,
    auditATime: null,
    auditBFlag: null,
    auditBId: null,
    auditBName: null,
    auditBTime: null,
    auditCFlag: null,
    auditCId: null,
    auditCName: null,
    auditCTime: null,
    auditDId: null,
    auditDFlag: null,
    auditDName: null,
    auditDTime: null,
    auditEId: null,
    auditEFlag: null,
    auditEName: null,
    auditETime: null,
    auditFId: null,
    auditFFlag: null,
    auditFName: null,
    auditFTime: null,
    auditGId: null,
    auditGFlag: null,
    auditGName: null,
    auditGTime: null,
    auditHId: null,
    auditHFlag: null,
    auditHName: null,
    auditHTime: null,
    currentAuditRoleId: null,
    currentAuditRoleName: null,
    approvalRemark: null,
    auditLevel: "",
    createBy: null,
    createTime: null,
    updateBy: null,
    updateTime: null,
    statusCd: null,
    dataStatus: null,
  };
  proxy.resetForm("formRef");
}

/** 查看详情 */
function handleCheck(row) {
  details({
    subsidyDetailId: row.subsidyDetailId,
    subsidyConfigId: newqueryParams.value.subsidyConfigId,
    subsidyCropCode: newqueryParams.value.subsidyCropCode,
    landNumber: newqueryParams.value.landNumber,
  }).then((response) => {
    childrenList.value = response.data.page;
    subsidyStdDesc1.value = response.data.subsidyStdDesc1;
    subsidyStdDesc2.value = response.data.subsidyStdDesc2;
  });
  openCheck.value = true;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.page = 1;
  newqueryParams.value = JSON.parse(JSON.stringify(queryParams.value));
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryForm");
  if (proxy.$refs.newOrgSelect) {
    proxy.$refs.newOrgSelect.getCascader("reset");
  }
  newqueryParams.value = {
    page: 1,
    rows: 10,
    bankAccount: null,
    subsidyPaymentNo: null,
    subsidyYear: String(new Date().getFullYear()),
    subsidyConfigId: null,
    subsidyType: null,
    subsidyCropCode: null,
    organizationName: null,
    organizationNo: null,
    contractId: null,
    contractSerialNumber: null,
    farmerId: null,
    farmerName: null,
    farmerIdNumber: null,
    landNumber: null,
    actualPaymentFee: null,
    subsidyStandard1: null,
    subsidyArea1: null,
    subsidyFee1: null,
    subsidyStandard2: null,
    subsidyArea2: null,
    subsidyFee2: null,
    approvalStatusNo: 0,
    auditAFlag: null,
    auditAId: null,
    auditAName: null,
    auditATime: null,
    auditBFlag: null,
    auditBId: null,
    auditBName: null,
    auditBTime: null,
    auditCFlag: null,
    auditCId: null,
    auditCName: null,
    auditCTime: null,
    auditDId: null,
    auditDFlag: null,
    auditDName: null,
    auditDTime: null,
    auditEId: null,
    auditEFlag: null,
    auditEName: null,
    auditETime: null,
    auditFId: null,
    auditFFlag: null,
    auditFName: null,
    auditFTime: null,
    auditGId: null,
    auditGFlag: null,
    auditGName: null,
    auditGTime: null,
    auditHId: null,
    auditHFlag: null,
    auditHName: null,
    auditHTime: null,
    currentAuditRoleId: null,
    currentAuditRoleName: null,
    approvalRemark: null,
    auditLevel: "",
    statusCd: null,
    dataStatus: null,
  };
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.subsidyDetailId);
  subsidyDetailFarmerNames.value = selection.map((item) => item.subsidyItemName);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
  bankclickdata.value = selection.filter((item) => item.ifUpdate == 1);
}

//乘法
function numMulti(num1, num2) {
  let baseNum = 0;
  try {
    baseNum += num1.toString().split(".")[1].length;
  } catch (e) {}
  try {
    baseNum += num2.toString().split(".")[1].length;
  } catch (e) {}
  return (
    (Number(num1.toString().replace(".", "")) *
      Number(num2.toString().replace(".", ""))) /
    Math.pow(10, baseNum)
  );
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加补贴信息表";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const subsidyDetailId = row.subsidyDetailId || ids.value;
  getSubsidyPaymentDetail(subsidyDetailId).then((response) => {
    form.value = response.data;
    form.value.subsidyCropCode = form.value.subsidyCropCode.split(",");
    form.value.subsidyType = form.value.subsidyType + "";
    open.value = true;
    title.value = "修改补贴信息表";
    getSubsidyStandardManage(response.data.subsidyConfigId).then((response) => {
      const payload = [];
      payload.push(response.data);
      selectCallback(payload);
    });
  });
}

/** 修改金额操作 */
function handleEditUpdate(row) {
  resetedit();
  row.subsidyFee1 = row.subsidyFee1 === null ? null : String(row.subsidyFee1);
  row.subsidyFee2 = row.subsidyFee2 === null ? null : String(row.subsidyFee2);
  editForm.value = row;
  row.subsidyFee1 = row.subsidyFee1 === null ? null : String(row.subsidyFee1);
  row.subsidyFee2 = row.subsidyFee2 === null ? null : String(row.subsidyFee2);
  if (editForm.value.subsidyFee2 == null) {
    editrules.value.subsidyFee2[0].required = false;
    editrules.value.subsidyFee2[1].required = false;
    editFormclearable.value = true;
  } else {
    editrules.value.subsidyFee2[0].required = true;
    editrules.value.subsidyFee2[1].required = true;
    editFormclearable.value = false;
  }
  editshow.value = true;
}

function resetedit() {
  editForm.value = {
    subsidyDetailSubId: null,
    subsidyCropCode: null,
    landNumber: null,
    actualPaymentFee: null,
    subsidyStandard1: null,
    subsidyArea1: null,
    subsidyFee1: null,
    subsidyStandard2: null,
    subsidyArea2: null,
    subsidyFee2: null,
  };
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["formRef"].validate((valid) => {
    if (valid) {
      form.value.subsidyCropCode = form.value.subsidyCropCode.join(",");
      if (form.value.subsidyDetailId != null) {
        updateSubsidyPaymentDetail(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addSubsidyPaymentDetail(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const subsidyDetailIds = row.subsidyDetailId || ids.value;
  const farmerNames = row.subsidyItemName || subsidyDetailFarmerNames.value;
  proxy.$modal
    .confirm('是否确认删除补贴项目为"' + farmerNames + '"的数据项?', "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(function () {
      return Array.isArray(subsidyDetailIds)
        ? delSubsidyPaymentDetails(subsidyDetailIds)
        : delSubsidyPaymentDetail(subsidyDetailIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    });
}

/** 删除按钮操作 */
function handleDeleteCheck(row) {
  const subsidyDetailIds = row.subsidyDetailId;
  const farmerNames = row.subsidyItemName || subsidyDetailFarmerNames.value;
  proxy.$modal
    .confirm('是否确认删除补贴项目为"' + farmerNames + '"的数据项?', "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(function () {
      return Array.isArray(subsidyDetailIds)
        ? delSubsidyPaymentDetails(subsidyDetailIds)
        : delSubsidyPaymentDetail(subsidyDetailIds);
    })
    .then(() => {
      openCheck.value = false;
      getList();
      proxy.$modal.msgSuccess("删除成功");
    });
}


function handleOrgChange(orgCode) {
  queryParams.value.organizationNo = orgCode;
  newqueryParams.value.organizationNo = orgCode;
}
function handleFormOrgChange(orgCode) {
  form.value.organizationNo = orgCode;
  getSubsidyStandardManageList();
}

function subsidyTypeFormatter(row, colmn) {
  return selectDictLabel(
    subsidyTypeOptions.value,
    row.subsidyType != null && row.subsidyType != undefined
      ? row.subsidyType.toString()
      : ""
  );
}
function subsidyCropCodeFormatter(row, colmn) {
  return selectDictLabels(
    subsidyCropCodeOptions.value,
    row.subsidyCropCode != null && row.subsidyCropCode != undefined
      ? row.subsidyCropCode.toString()
      : ""
  );
}
function approvalStatusNoFormatter(row, column) {
  return selectDictLabel(
    approverNoOptions.value,
    row.approvalStatusNo != null && row.approvalStatusNo != undefined
      ? row.approvalStatusNo.toString()
      : ""
  );
}

function queryYearChangeHandler() {
  const queryParam = {};
  if (!queryParams.value.subsidyYear) {
    querySubsidyProjectList.value = [];
    return;
  }
  queryParams.value.subsidyConfigId = null;
  queryParam.subsidyYear = queryParams.value.subsidyYear;
  queryParam.subsidyClassifyClass = 100;
  subsidyStandardManagegetByOrgs(queryParam).then((response) => {
    querySubsidyProjectList.value = response.data;
  });
}

function yearChangeHandler() {
  getSubsidyStandardManageList();
}

// 获取补贴标准
function getSubsidyStandardManageList() {
  // 当年份与组织机构都选择的时候，去查询补贴项目
  if (form.value.subsidyYear && form.value.organizationNo) {
    // 查询补贴标准
    const query = {};
    query.subsidyYear = form.value.subsidyYear;
    query.organizationNo = form.value.organizationNo;
    listSubsidyStandardManageAll(query).then((response) => {
      querySubsidyStandardManageList.value = response.data;
    });
  }
}

function clearManage() {
  form.value.subsidyYear = null;
  form.value.organizationNo = null;
  subsidyItemName.value = null;
  form.value.subsidyType = null;
  form.value.subsidyCropCode = null;
  form.value.subsidyProjectCode = null;
  showDesc1.value = false;
  showDesc2.value = false;
}

function selectCallback(payload) {
  const p = payload[0];
  form.value.subsidyYear = p.subsidyYear;
  form.value.organizationNo = p.organizationNo;
  subsidyItemName.value = p.subsidyItemName;
  form.value.subsidyType = p.subsidyType + "";
  form.value.subsidyCropCode = p.subsidyCropCode.split(",");
  form.value.subsidyProjectCode = p.subsidyStdMgeId;

  //清空面积和金额
  form.value.subsidyArea1 = null;
  form.value.subsidyFee1 = null;
  form.value.subsidyArea2 = null;
  form.value.subsidyFee2 = null;

  if (p.subsidyStdDesc1) {
    form.value.subsidyStandard1 = p.subsidyStandard1;
    labelFrom.value.subsidyStdDesc1 = p.subsidyStdDesc1;
    showDesc1.value = true;
  } else {
    showDesc1.value = false;
    form.value.subsidyStandard1 = null;
    form.value.subsidyArea1 = null;
    form.value.subsidyFee1 = null;
  }
  if (p.subsidyStdDesc2) {
    form.value.subsidyStandard2 = p.subsidyStandard2;
    labelFrom.value.subsidyStdDesc2 = p.subsidyStdDesc2;

    showDesc2.value = true;
  } else {
    showDesc2.value = false;
    form.value.subsidyStandard2 = null;
    form.value.subsidyArea2 = null;
    form.value.subsidyFee2 = null;
  }
}

/** 审核通过按钮操作 */
function handleAudit() {
  const rationPlanIds = ids.value;
  auditPass({ rationPlanIds, approvalRemark: textarea.value }).then((res) => {
    if (res.code === 0) {
      getList();
      proxy.$modal.msgSuccess("操作成功");
    }
  });
  dialogVisible.value = false;
}

/** 审核拒绝按钮操作 */
function handleNoAudit() {
  if (textarea.value == "") return proxy.$modal.msgError("请填写备注");
  const rationPlanIds = ids.value;
  auditCancel({ rationPlanIds, approvalRemark: textarea.value }).then((res) => {
    if (res.code === 0) {
      getList();
      proxy.$modal.msgSuccess("操作成功");
    }
  });
  dialogVisible.value = false;
}

function countfun(e) {
  corporateBankList.value.forEach((v, i) => {
    if (v.bankAccount == e) {
      bankForm.value.bankName = v.bankName + "";
      bankForm.value.network = v.network;
      bankForm.value.lineNumber = v.lineNumber;
    }
  });
}

function tabClick(e) {
  tabIndex.value = e.index;
  if (e.index == 0) {
    resetQuery();
  }
}

/** 导入按钮操作 */
function handleImport() {
  proxy.$refs.paymentImportRef.clearForm();
  paymentImportOpen.value = true;
}

function handleCardImport() {
  proxy.$refs.excelImportRef.clearForm();
  excelImportOpen.value = true;
}

function applyhandleImport() {
  proxy.$refs.applyImportRef.clearForm();
  applyImportOpen.value = true;
}

//关闭导入模板的弹窗
function handleClose(modal) {
  excelImportOpen.value = false;
}
function handleImportClose(modal) {
  applyImportOpen.value = false;
  getList();
}

function handlePaymentImportClose(modal) {
  paymentImportOpen.value = false;
  getList();
}

function firstTripFun() {
  handleQuery();
}

function fixedFunAlign(name) {
  // console.log("fixedFunAlign--->>>",name)
  return "center";
}

/** 导出按钮操作 */
function handleExport() {
  postForExcel(
    "/" +
      import.meta.env.VITE_APP_GATEWAYPATH_SUBSIDY +
      "/subsidy/subsidyPaymentDetail/exportExcel",
    queryParams.value,
    "农业补贴信息"
  );
}


</script>
<style lang="scss" scoped>
:deep(.el-form--inline .el-form-item) {
  display: flex;
}
:deep {
  .el-table__fixed {
    height: calc(100% - 14px) !important;
  }
  .el-tabs__content {
    overflow: unset !important;
  }
}


</style>
