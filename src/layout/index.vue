<template>
  <div :class="classObj" class="app-wrapper" :style="{ '--current-color': theme }">
    <div v-if="device === 'mobile' && sidebar.opened" class="drawer-bg" @click="handleClickOutside"/>
    <sidebar v-if="!sidebar.hide && !poweredByQiankun" class="sidebar-container" />
    <div :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }" class="main-container" :style="mainContainerStyle">
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar v-if="!poweredByQiankun" @setLayout="setLayout" />
        <!-- 作为 qiankun 子应用时，这里只能隐藏而不能直接销毁 -->
        <tags-view v-if="needTagsView" :style="{ display: poweredByQiankun ? 'none' : undefined }" />
      </div>
      <app-main />
      <settings ref="settingRef" />
    </div>
  </div>
</template>

<script setup>
import { useWindowSize } from '@vueuse/core'
import Sidebar from './components/Sidebar/index.vue'
import { AppMain, Navbar, Settings, TagsView } from './components'
import { ElNotification } from 'element-plus'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import useUserStore from "@/store/modules/user"
const settingsStore = useSettingsStore()
const theme = computed(() => settingsStore.theme);
const sideTheme = computed(() => settingsStore.sideTheme);
const sidebar = computed(() => useAppStore().sidebar);
const device = computed(() => useAppStore().device);
const needTagsView = computed(() => settingsStore.tagsView);
const fixedHeader = computed(() => settingsStore.fixedHeader);

const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === 'mobile'
}))

const { width, height } = useWindowSize();
const WIDTH = 992; // refer to Bootstrap's responsive design

let subscription;
let poweredByQiankun = window.__POWERED_BY_QIANKUN__;
let mainContainerStyle = window.__POWERED_BY_QIANKUN__ ? {background: '#fff', marginLeft: 0} : {};
const { proxy } = getCurrentInstance();
import useMqtt from '@/views/systemagriculturalmachineryv2/utils/mqtt.js'
const { startMqtt, disconnect } = useMqtt()
onMounted(() => {
  subscription = window.bdhMicroMainEvents?.themeChange.subscribe((data) => {
    // 使用pinia做状态管理采用如下写法
    settingsStore.changeSetting({
        key: "theme",
        value: data.colorPrimary,
    })
  })
})

onUnmounted(() => {
  subscription.unsubscribe()
})

watch(() => device.value, () => {
  if (device.value === 'mobile' && sidebar.value.opened) {
    useAppStore().closeSideBar({ withoutAnimation: false })
  }
})

watchEffect(() => {
  if (width.value - 1 < WIDTH) {
    useAppStore().toggleDevice('mobile')
    useAppStore().closeSideBar({ withoutAnimation: true })
  } else {
    useAppStore().toggleDevice('desktop')
  }
})
// createdMqtt()
function createdMqtt() {
  disconnect()
  const mqtt = import.meta.env.VITE_APP_MQTT
  // const mqtt = window.VITE_APP_MQTT
  console.log('import.meta.env',import.meta.env)
  console.log('window',window)
  startMqtt(mqtt, 'machinery', '1234qwer', [`agmachine/async/${useUserStore().currentOrgCode}/${useUserStore().staffId}`], null, (topic, data) => {
    if (data) {
      let { type, msg } = JSON.parse(data)
      proxy.$bus.$emit('mqtt', JSON.parse(data))
      if (type == "basicinfo_aminfo_exportExcelAsync") {
        ElNotification({
          title: '提示',
          duration: 10000,
          message: msg || ''
        });
      } else if (type == "basicinfo_aminfo_exportExcelWithQrCode") {
        ElNotification({
          title: '提示',
          duration: 10000,
          message: msg || ''
        });
      }
    }
  })
}
function handleClickOutside() {
  useAppStore().closeSideBar({ withoutAnimation: false })
}

const settingRef = ref(null);
function setLayout() {
  settingRef.value.openSetting();
}
</script>

<style lang="scss" scoped>
  @import "@/assets/styles/mixin.scss";
  @import "@/assets/styles/variables.module.scss";

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.sidebarHide .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}
</style>