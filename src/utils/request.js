import axios from 'axios'
import {ElNotification, ElMessageBox, ElMessage, ElLoading} from 'element-plus'
import {getToken, removeToken,getCopToken} from "@/utils/auth"
import errorCode from "@/utils/errorCode"
import {tansParams, blobValidate} from "@/utils/ruoyi"
import {h} from "vue"
// import router from "@/router"
import cache from "@/plugins/cache"
import {saveAs} from "file-saver"
import useUserStore from "@/store/modules/user"

axios.defaults.headers["Content-Type"] = "application/json;charset=utf-8"

let downloadLoadingInstance
// 是否显示重新登录
export let isRelogin = {show: false}

let loading //定义loading变量
function startLoading() {
    //使用Element loading-start 方法
    loading = ElLoading.service({
        lock: true,
        target: document.getElementsByTagName("body")[0],
        fullscreen: true,
        background: "rgba(0, 0, 0, 0.3)",
        customClass: "creatLoading",
    })
}

function endLoading() {
    //使用Element loading-close 方法
    loading.close()
}

let needLoadingRequestCount = 0

export function showFullScreenLoading() {
    if (needLoadingRequestCount === 0) {
        startLoading()
    }
    needLoadingRequestCount++
}

export function tryHideFullScreenLoading() {
    if (needLoadingRequestCount <= 0) return
    needLoadingRequestCount--
    if (needLoadingRequestCount === 0) {
        endLoading()
    }
}

axios.defaults.headers["Content-Type"] = "application/json;charset=utf-8"
// 创建axios实例
const service = axios.create({
    // axios中请求配置有baseURL选项，表示请求URL公共部分
    // baseURL: window.VITE_APP_BASE_API,
    // 超时
    timeout: 60 * 1000,
})

function isTrim(data) {
    // 首先需要判断当前的config中是否存在data值
    if (data && data instanceof Object) {
        for (const key in data) {
            if (Object.hasOwnProperty.call(data, key)) {
                // 此处我们不要使用   let element = data[key] 注意  如果采用这种方式的话对应trim改变的值和data[key]将不再会是一个同一个内存地址
                // 在需要判断一下当前数据是否是数组
                if (Array.isArray(data[key])) {
                    // 就将数组放进去
                    data[key] = isTrim(data[key])
                } else if (data[key] && data[key] instanceof Object) {
                    // 如果对象里面套对象的话
                    data[key] = isTrim(data[key])
                } else if (
                    data[key] &&
                    Object.prototype.toString.call(data[key]) ===
                    "[object String]"
                ) {
                    // 如果对象里面的数据是String的话那么就直接trim只对String进行操作
                    data[key] = data[key].trim()
                }
            }
        }
        return data
    } else if (
        data &&
        Object.prototype.toString.call(data) === "[object String]"
    ) {
        // 如果是字符串说明是JSON.parse需要转换
        let dataObj = JSON.parse(data)
        // 转成对象之后在抛出去
        dataObj = isTrim(dataObj)
        return JSON.stringify(dataObj)
    } else if (data && data instanceof Array) {
        // 如果是数组  那就forin一下  判断里面的数据类型
        for (const key in data) {
            if (Object.hasOwnProperty.call(data, key)) {
                if (
                    (data && data instanceof Object) ||
                    (data && data instanceof Array)
                ) {
                    data[key] = isTrim(data[key])
                }
            }
        }
        return data
    }
}

// request拦截器
service.interceptors.request.use(
    (config) => {
        // 是否需要设置 token
        const isToken = (config.headers || {}).isToken === false
        // 是否需要防止数据重复提交
        const isRepeatSubmit = (config.headers || {}).repeatSubmit === false
        if(getCopToken()){
            config.headers["cop-token"] = getCopToken() // 让每个请求携带自定义token 请根据实际情况自行修改
            config.headers["app"] = 1
        }else if (getToken() && !isToken) {
            config.headers["access-token"] = getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
        }
        if (config.params) {
            isTrim(config.params)
        }
        if (config.data) {
            isTrim(config.data)
        }
        let shouLoading = config.showLoading || true
        if (config.showLoading != false) {
            showFullScreenLoading()
        }
        //debugger
        // get请求映射params参数
        if (config.method === "get" && config.params) {
            let url = config.url + "?" + tansParams(config.params)
            url = url.slice(0, -1)
            config.params = {}
            config.url = url
        }
        if (
            !isRepeatSubmit &&
            (config.method === "post" || config.method === "put")
        ) {
            const requestObj = {
                url: config.url,
                data:
                    typeof config.data === "object"
                        ? JSON.stringify(config.data)
                        : config.data,
                time: new Date().getTime(),
            }
            // const sessionObj = cache.session.getJSON('sessionObj')
            // if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
            //   cache.session.setJSON('sessionObj', requestObj)
            // } else {
            //   const s_url = sessionObj.url;                // 请求地址
            //   const s_data = sessionObj.data;              // 请求数据
            //   const s_time = sessionObj.time;              // 请求时间
            //   const interval = 1000;                       // 间隔时间(ms)，小于此时间视为重复提交
            //   if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {
            //     const message = '数据正在处理，请勿重复提交';
            //     console.warn(`[${s_url}]: ` + message)
            //     return Promise.reject(new Error(message))
            //   } else {
            //     cache.session.setJSON('sessionObj', requestObj)
            //   }
            // }
        }
        return config
    },
    (error) => {
        console.log(error)
        Promise.reject(error)
    }
)

// 响应拦截器
service.interceptors.response.use(
    (res) => {
        // 未设置状态码则默认成功状态
        const code = res.data.code || 200
        // 获取错误信息
        const msg = errorCode[code] || res.data.msg || errorCode["default"]
        tryHideFullScreenLoading()

        if (res.data.data?.pwdTip > 0 && !res.config.url.endsWith('/sso/rest/oauth2/userinfo')) {
            ElMessageBox.confirm(
                h("div", null, [
                    h("span", null, "您的密码还有"),
                    h("span", {style: "color: red"}, res.data.data.pwdTip),
                    h("span", null, "天过期，是否马上修改密码？"),
                ]),
                "系统提示",
                {
                    confirmButtonText: "修改密码",
                    cancelButtonText: "暂不修改",
                    type: "warning",
                }
            ).then(() => {
                window.router.push("/user/profile#modifyPwd")
            })
        } else if (res.data.data?.pwdTip < 0 && !res.config.url.endsWith('/sso/rest/oauth2/userinfo')) {
            ElMessageBox.alert(
                h("div", null, [
                    h("span", {style: "color: red"}, "您的密码已经过期，请修改密码!"),
                ]),
                "系统提示",
                {
                    showClose: false,
                    confirmButtonText: "修改密码",
                    type: "warning",
                }
            ).then(() => {
                window.router.push("/user/profile#modifyPwd")
            })
        }
        if ([100008, 100017, 200004, 100020, 300001].includes(code)) {
            ElMessageBox.confirm(
                msg || "登录状态已过期，您可以继续留在该页面，或者重新登录",
                "系统提示",
                {
                    confirmButtonText: "重新登录",
                    showCancelButton: false,
                    type: "warning",
                }
            ).then(() => {
                removeToken()
                //debugger
                // 处理登录相关(主动退出，LogOut;账号或token异常，跳转)
                if (
                    res.config.url.indexOf(process.env.VUE_APP_SSO_LOGOUT_URL) <
                    0
                ) {
                    //debugger
                    useUserStore().clearInfo(code)
                } else {
                    //debugger
                    useUserStore().logOut()
                }
            })
            return res.data
        } else if (code === 500) {
            ElMessage({
                message: msg,
                type: "error",
            })
            return Promise.reject(new Error(msg))
        } else if (code === 999 || code === 1) {
            //硬件通信失败特殊处理
            ElNotification.error({
                title: msg,
            })
            return res.data
        } else if (code === 400) {
            return Promise.reject(res.data)
        } else if (code !== 200) {
            ElNotification.error({
                title: msg,
            })
            return Promise.reject("error")
        } else {
            return res.data
        }
    },
    (error) => {
        tryHideFullScreenLoading()
        console.log("err" + error)
        let {message} = error
        if (message === "Network Error") {
            message = "后端接口连接异常"
        } else if (message.includes("timeout")) {
            message = "系统接口请求超时"
        } else if (message.includes("Request failed with status code")) {
            message = "系统接口" + message.substr(message.length - 3) + "异常"
        }
        ElMessage({
            message: message,
            type: "error",
            duration: 5 * 1000,
        })
        return Promise.reject(error)
    }
)

// 通用下载方法，如果提示请求类型不匹配：注意传参 contentType
export function download(url, params, filename, config) {
    downloadLoadingInstance = ElLoading.service({
        text: "正在下载数据，请稍候",
        background: "rgba(0, 0, 0, 0.7)",
    })
    return service
        .post(url, params, {
            // transformRequest: [
            //     (params) => {
            //         return tansParams(params)
            //     },
            // ],
            // headers: {"Content-Type": "application/x-www-form-urlencoded"},
            headers: { 'Content-Type': config?.contentType || 'application/x-www-form-urlencoded' },
            responseType: "blob",
            ...config,
        })
        .then(async (data) => {
            const isLogin = await blobValidate(data)
            if (isLogin) {
                const blob = new Blob([data])
                saveAs(blob, filename)
            } else {
                const resText = await data.text()
                const rspObj = JSON.parse(resText)
                const errMsg =
                    errorCode[rspObj.code] || rspObj.msg || errorCode["default"]
                ElMessage.error(errMsg)
            }
            downloadLoadingInstance.close()
        })
        .catch((r) => {
            console.error(r)
            ElMessage.error("下载文件出现错误，请联系管理员！")
            downloadLoadingInstance.close()
        })
}

export default service
