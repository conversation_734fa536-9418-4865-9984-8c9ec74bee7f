import Cookies from 'js-cookie'

const Token<PERSON>ey = 'access-token'
const Cop<PERSON>ey = 'cop-token'
const ExpiresInKey = 'Admin-Expires-In'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function getCopToken() {
  return Cookies.get(Co<PERSON><PERSON>ey)
}

export function setToken(token) {
  Cookies.set("access-token", token)
  return Cookies.set(Token<PERSON>ey, token)
}

export function setCopToken(token) {
  Cookies.set("cop-token", token)
  return Cookies.set(Cop<PERSON><PERSON>, token)
}

export function removeToken() {
  Cookies.remove("access-token")
  return Cookies.remove(TokenKey)
}

export function getExpiresIn() {
  return Cookies.get(ExpiresInKey) || -1
}

export function setExpiresIn(time) {
  return Cookies.set(ExpiresInKey, time)
}

export function removeExpiresIn() {
  return Cookies.remove(ExpiresInKey)
}
