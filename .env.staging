# 页面标题
VITE_APP_TITLE = 农业生产管理平台

# 生产环境配置
VITE_APP_ENV = 'staging'

# 农业生产管理平台/生产环境
VITE_APP_BASE_API = '/stage-api'

# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS = gzip

# 导航菜单-系统编码
VITE_SYSTEM_CODE = 'bdh-group-report,sysagriculturalsituation,sysanimalhusbandry,bdh-aviation-mission,bdh-agric-cost-analysis,bdh-samic-confirm,bdh-bpm,
bdh-question-feedback,bdh-info-publish,systemlandcontract,bdh-agric-invest,bdh-trace,systemagriculturalmachineryv2,sysforestresource,syslandresource,bdh-subsidy'

# mqtt 农机地址
VITE_APP_MQTT = 'ws://***********:8083/mqtt'
