apiVersion: apps/v1
kind: Deployment
metadata:
  annotations:
    kubesphere.io/alias-name: $ALIAS_NAME
  labels:
    app: kubesphere
    component: $APP_NAME
    tier: backend
  name: $APP_NAME
  namespace: $KS_SPACE
spec:
  progressDeadlineSeconds: 600
  replicas: 4
  selector:
    matchLabels:
      app: kubesphere
      component: $APP_NAME
      tier: backend
  template:
    metadata:
      labels:
        app: kubesphere
        component: $APP_NAME
        tier: backend
    spec:
      containers:
        - env:
          - name: PROJECT_PATH
            value: api
          - name: SSO_API
            value: https://10.7.10.100/
          - name: PROJECT_API
            value: http://10.7.13.1:31941/bdh-portal/api/
          - name: FANRUAN_URL
            value: http://10.7.10.20:8080/webroot/
          - name: FANRUAN_ACCESS_CODE
            value: bdh-front-group_pro
          - name: GEOSERVICE_API
            value: http://bdh-geoservice-service.pro/
          - name: GEOSERVICE_RVT_API
            value: http://bdh-tegola-rvt-service.pro/
          - name: OSS_STATIC_API
            value: http://wh-the-north.obsv3.hxl-bdhcloud-1.chinabdhcloud.com/
          image: $REGISTRY/$DOCKERHUB_NAMESPACE/$APP_NAME:$KS_SPACE-$BUILD_NUMBER
          imagePullPolicy: Always
          name: container-$APP_NAME
          ports:
            - containerPort: 80
              protocol: TCP
          resources:
            limits:
              cpu: 1
              memory: 1Gi
            requests:
              cpu: 0.1
              memory: 0.5Gi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    kubesphere.io/alias-name: $ALIAS_NAME-SVC
  labels:
    app: kubesphere
    component: $APP_NAME
  name: $APP_NAME
  namespace: $KS_SPACE
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
      nodePort: 31696
  selector:
    app: kubesphere
    component: $APP_NAME
    tier: backend
  sessionAffinity: None
  type: NodePort
